import fs from 'fs';

function generateDockerfiles() {
    const allApps = fs.readdirSync('./apps', { withFileTypes: true }).filter(dirent => dirent.isDirectory()).map(dirent => dirent.name);
    const allPackages = fs.readdirSync('./packages', { withFileTypes: true }).filter(dirent => dirent.isDirectory()).map(dirent => dirent.name);
    const copyStatements = allApps.map(app => `COPY apps/${app}/package.json ./apps/${app}/`);
    const copyStatementsPackages = allPackages.map(pkg => `COPY packages/${pkg}/package.json ./packages/${pkg}/`);
    const pkgJsonCopyStatements = [...copyStatements, ...copyStatementsPackages].join('\n');

    const containerAppsToCommands = [];
    for (const app of allApps) {
        const packageJson = JSON.parse(fs.readFileSync(`./apps/${app}/package.json`, 'utf-8'));
        if (packageJson.deployment?.containerize) {
            containerAppsToCommands.push([app, packageJson.deployment.startupCommand]);
        }
    }

    for (const [app, startupCommand] of containerAppsToCommands) {
        const existingDockerfile = fs.existsSync(`./Dockerfile.${app}`)
            ? fs.readFileSync(`./Dockerfile.${app}`, 'utf-8')
            : null;
        
        const appDockerfile = `FROM node:24-alpine AS base

        ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0
        RUN corepack enable
        RUN corepack prepare pnpm@9.0.0 --activate

        FROM base AS full_deps_builder
        WORKDIR /app

        COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

        ${pkgJsonCopyStatements}

        RUN pnpm install --ignore-scripts --frozen-lockfile --child-concurrency=4

        COPY . .

        ENV NODE_ENV=production

        RUN pnpm turbo run build --filter=${app}

        # --- Deploy Stage: Use pnpm deploy to create a lean package --- #
        FROM base AS deploy_package
        WORKDIR /app

        COPY --from=full_deps_builder /app .

        RUN pnpm --filter=${app} deploy --legacy /app/deployed_app --prod

        # --- Runner Stage: Final image (distroless, nonroot) --- #
        FROM gcr.io/distroless/nodejs24-debian12 AS runner
        WORKDIR /app

        ENV NODE_ENV=production

        COPY --from=deploy_package /app/deployed_app .

        EXPOSE 3000

        CMD [${startupCommand.split(' ').map(JSON.stringify).join(', ')}]
        `.replace(/^[ ]+/gm, '');

        if (existingDockerfile === appDockerfile) {
            console.log(`Dockerfile for ${app} is up to date`);
            continue;
        } else {
            fs.writeFileSync(`./Dockerfile.${app}`, appDockerfile);
            console.log(`Generated Dockerfile for ${app}`);
            console.log(`Dockerfile.${app} has been modified.`);
        }
    }
}

generateDockerfiles();
