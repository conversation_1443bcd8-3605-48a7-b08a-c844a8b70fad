{"variant_metafields": {"name": "Metafield della variante", "label": "Chiave del metafield della variante", "info": "Questo tema può mostrare un metafield della variante nella pagina del prodotto. [Per saperne di più](https://shopify-support.krownthemes.com/article/884-using-variant-metafields-in-product-pages)."}, "new_color_swatches": "Il tipo di selettore di varianti \"blocchi\" offre supporto per le varianti di colore create con i metafield di categoria. [Per saperne di più](https://help.shopify.com/en/manual/custom-data/metafields/category-metafields/using-category-metafields).", "video_controls": "Mostra i controlli video", "sticky_cart_actions": "Abilita azioni del carrello fisso", "currency_codes": {"header": "Formato valuta", "label": "Mostra codici valuta", "info": "Esempio: $1.00 USD."}, "a11": {"label": "Accessibilità", "show_sidebars_scrollbar": "Mostra la barra di scorrimento delle barre laterali", "disable_all_image_animations": "Disabilita tutte le animazioni delle immagini"}, "divider": {"label": "Divisore", "divider_design": "Design del divisore", "divider_style_solid": "Solido", "divider_style_dotted": "Puntina<PERSON>", "divider_style_dashed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divider_style_double": "<PERSON><PERSON><PERSON>", "divider_color": "Colore", "divider_image": "Immagine del divisore", "divider_image_info": "Un'immagine ripetitiva orizzontalmente. Sostituisce lo stile e il colore sopra."}, "cart_actions": {"label": "Azioni del carrello a cassetti", "option_1": "Mostrare il pulsante\"visualizzare il carrello\"", "option_2": "Mostrare il pulsante \"checkout\"", "option_3": "Mostrare entrambi"}, "sticky_atc": {"label": "Aggiungi al carrello sticky", "enable_sticky_atc": "Abilita aggiungi al carrello sticky", "sticky_atc_style_wide": "Wide", "sticky_atc_style_floating": "Floating"}, "performance": {"header": "SEO & prestazioni", "name": "Prestazioni", "label": "Precarica i link al passaggio del mouse", "info": "Aumenta la velocità di caricamento percepita delle pagine."}, "recently_viewed": {"enable_recently_viewed_products": "Abilita prodotti visualizzati di recente", "enable_recently_viewed_products_info": "Una volta abilitato, il tema registrerà i prodotti visualizzati, ma è necessario aggiungere la sezione nel tuo negozio per mostrare questi prodotti.", "recently_viewed_products": "Prodotti visualizzati di recente", "recently_viewed_products_info": "Questa sezione deve avere la funzionalità abilitata nelle Impostazioni Tema. Sarà visibile solo dopo che gli utenti hanno visitato almeno una pagina di prodotto.", "recently_viewed_products_limit": "Limite dei prodotti visualizzati di recente"}, "rating_apps_update": {"label": "App di valutazione", "info": "Le app di terze parti possono richiedere passaggi aggiuntivi per essere integrate correttamente."}, "local-220": {"preorder": "Mostra etichetta del pulsante \"preordine\"", "autorotate": {"heading": "Auto-ruotare", "info": "Scorri automaticamente tra le diapositive.", "enable": "Abilita auto-ruotare", "interval": "Intervallo", "pause_on_mouseover": "Pausa al passaggio del mouse"}}, "custom-social-icons": {"header": "<PERSON>", "info": "Carica un'icona personalizzata per il tuo social network preferito", "icon": {"label": "Icona", "info": "72 x 72px trasparente .png"}, "link": {"label": "Link"}}, "local-march-update": {"labels": {"dynamic_content": {"name": "Contenuto dinamico", "hide_block": "Nascondi blocco se contenuto dinamico non presente", "hide_section": "Nascondi sezione se contenuto dinamico non presente"}, "buttons": "<PERSON><PERSON><PERSON><PERSON>", "cards": "<PERSON><PERSON>", "heading": "Intestazione", "buttons_custom": "Colori personalizzati pulsante", "center_heading": "Centra intestazione", "section_design": "Struttura sezione", "bottom_margin": "Rimuovi margine inferiore", "text_spacing": "Spaziatura testo", "inherit_card_design": "E<PERSON>ita prop<PERSON>à design del biglietto", "align_button": "Allinea il pulsante acquista al bordo inferiore del biglietto", "custom_colors": "Colori personalizzati"}, "shadows": {"label": "Ombreggiatura", "label_plural": "Ombreggiature", "offset_x": "Spostamento orizzontale", "offset_y": "Spostamento verticale", "blur": "Sfocatura", "hide": "Nascondi ombreggiatura", "hide_button_shadows": "Nascondi ombreggiatura pulsante"}, "blocks": {"countdown_timer": {"name": "Timer conto alla rovescia", "label": "Fonte dinamica", "info": "Imposta una fonte di tempo dinamica per il timer di conto alla rovescia. [Per saperne di più](https://shopify-support.krownthemes.com/article/642-product-pages#countdown-timer)"}, "progress_slider": {"name": "Grafico a barre dell'avanzamento", "value": "Valore", "height": "Altezza del cursore", "width": "Larghezza del cursore", "dynamic_content": {"info": "Usa fonti dinamiche per definire valori unici creando metacampi per prodotti nel grafico di avanzamento. [Per saperne di più](https://shopify-support.krownthemes.com/article/804-product-page#Progress-chart-S8c6R)", "slider_label": "Metacampo del valore", "dots_label": "Metacampo dei punti evidenziati"}}, "progress_dots": {"name": "Grafico a punti dell'avanzamento", "highlight": "Punti evidenziati", "total": "Punti totali", "icon": "Icona del punto", "size": "Dimensione del punto", "inactive_color": "Colore inattivo", "active_color": "Colore evidenziato"}, "store_selector": {"default": "Default a primo negozio"}, "rating": {"app": "Recensioni app", "default_option": "<PERSON><PERSON><PERSON>"}, "space": {"name": "Spazio vuoto"}, "badges": {"name": "Badge prodotto"}, "nutritional": {"name": "Informazioni nutrizionali", "label_first": "Etichetta prima colonna", "label_second": "Etichetta seconda colonna", "label_third": "<PERSON>tic<PERSON>tta terza colonna", "information": {"label": "Informazioni", "info": "Separa etichetta e valore con una virgola. Usa interruzioni di riga per aggiungere una nuova riga. Usa un trattino per l'indentatura/rientro delle righe. [Per saperne di più](https://shopify-support.krownthemes.com/article/804-product-page#Nutritional-information-ypnLL)"}, "extra_information": "Informazioni aggiuntive"}}, "sections": {"progress_sliders": {"name": "Grafici a barre dell'avanzamento", "block_name": "Barr<PERSON>"}, "header": {"settings": {"promotion": {"header_1": "Promozione 1", "header_2": "Promozione 2", "header_3": "Layout menu", "show": "Mostra promozione", "image": "Immagine della promozione", "text": "Testo promozione", "width": "Larghezza colonna"}}}}}, "settings_schema": {"exit_intent": {"exit_intent_popup": "Popup di intenzione di uscita", "exit_intent_popup_info": "Questa sezione funziona solo su desktop"}, "colors": {"name": "Colori", "settings": {"header__1": {"content": "Barra laterale"}, "header__2": {"content": "Corpo"}, "header__3": {"content": "Piè di pagina"}, "bg_color": {"label": "Sfondo"}, "txt_color": {"label": "<PERSON><PERSON>"}, "link_color": {"label": "Collegamenti"}}}, "typography": {"name": "Tipografia", "settings": {"headings_font": {"label": "Intestazioni"}, "base_size": {"label": "Dimensione di base"}, "large_size": {"label": "Intestazioni grandi", "info": "Influisce sui titoli di grandi dimensioni dello slider, del testo formattato e delle immagini con sezioni di testo."}, "body_font": {"label": "Corpo"}, "nav_size": {"label": "Navigazione principale"}}}, "product-grid": {"name": "<PERSON><PERSON><PERSON>", "settings": {"aspect_ratio": {"label": "Proporzioni dei contenuti multimediali"}, "show_secondary_image": {"label": "Mostra il secondo media associato al prodotto al passaggio del mouse"}, "enhance_featured_products": {"label": "<PERSON><PERSON> in risalto i prodotti in primo piano", "info": "[Ulteriori informazioni](https://shopify-support.krownthemes.com/article/832-product-card)"}, "show_discount": {"label": "<PERSON><PERSON> sconto come...", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Percent<PERSON><PERSON>"}}, "caption_placement": {"label": "Posizionamento didascalia", "options__1": {"label": "In sovrimpressione", "group": "Visibile al passaggio del mouse"}, "options__2": {"label": "Sotto l'immagine", "group": "Sempre visibile"}}, "grid_color_bg": {"label": "Sfondo della didascalia in sovrimpressione"}, "grid_color_text": {"label": "Colore del testo della didascalia in sovrimpressione"}, "header__1": {"content": "Valutazione del prodotto", "info": "TPer visualizzare le valutazioni, aggiungi un'app di valutazione del prodotto. [Scopri di più]"}, "header__2": {"content": "Recensioni dei prodotti"}, "show_reviews": {"label": "Mostra valutazione"}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "48 x 48 pixel .png obbligatori"}}}, "cart-page": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Tipo di carrello", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Drawer"}}, "cart_notes": {"label": "Abilita le note del carrello"}, "cart_buttons": {"label": "Mostra pulsanti di pagamento aggiuntivi"}}}, "embellishments": {"name": "Decorazioni", "settings": {"show_preloader": {"label": "Indicatore di caricamento delle immagini", "info": "Mostra un piccolo indicatore di caricamento circolare mentre le immagini nel tuo negozio sono in fase di caricamento."}, "show_breadcrumb": {"label": "Mostra percorso di navigazione", "info": "Il percorso di navigazione aiuta gli utenti a navigare nel negozio e viene visualizzato solo nelle pagine delle collezioni, dei prodotti e di ricerca."}, "show_go_top": {"label": "Mostra il pulsante 'vai in alto'"}}}, "search": {"name": "Cerca", "settings": {"predictive_search": {"label": "Abilita la ricerca predittiva"}, "show_vendor": {"label": "<PERSON>ra fornitore"}, "show_price": {"label": "<PERSON>ra prezzo"}, "include_articles": {"label": "Includi gli articoli nei risultati di ricerca"}, "include_pages": {"label": "<PERSON>ludi le pagine nei risultati di ricerca"}}}, "social": {"name": "Social"}, "follow_on_shop": {"content": "<PERSON><PERSON><PERSON>", "info": "Shop Pay deve essere abilitato per consentire ai clienti di seguire il tuo negozio sull'app Shop dalla tua vetrina virtuale. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "label": "<PERSON><PERSON><PERSON>"}, "labels": {"hide_block_if_no_content_info": "Nascondi blocco se il contenuto non è definito", "popup_page_info": "Sostituisce il contenuto del testo se viene selezionata una pagina", "page": "<PERSON><PERSON><PERSON>", "popup": "Popup", "open_popup": "<PERSON><PERSON> popup"}}, "sections": {"main-404": {"name": "404 principale"}, "main-gift-card": {"name": "<PERSON>ta regalo"}, "main-page": {"name": "Pagina principale"}, "refactor_words": {"seo": {"name": "SEO", "label": "Tag intestazione", "info": "Specifica il livello di intestazione per aiutare i motori di ricerca a indicizzare la struttura della pagina.", "microdata": {"label": "Disabilita schema microdati", "info": "Questo rimuoverà il markup schema.org dalla pagina. Disabilita solo se usi un'app di terze parti per la SEO!"}}, "late_edits": {"image_with_hotspots": {"image_on_mobile": "Immagine per mobile", "position_on_mobile": "Posizione su mobile", "hotspot": {"mobile_info": "Solo con una immagine mobile impostata"}}, "product-card": {"thumbnails": {"border": "Colore del bordo dei media"}}, "labels": {"optional": "Opzionale"}, "before-after": {"layout": {"invert": "Inverti layout su dispositivi mobili"}}}, "labels": {"footer_group": "Gruppo piè di pagina", "header_group": "Gruppo intestazione", "overlay_group": "Gruppo sovrapposizione", "embellishments": "Decorazioni", "show_button": "Show button", "optional": "Optional"}, "text": {"icons_info": "Per visualizzare e scaricare altre icone, per favore visita [questo link](https://resources.krownthemes.com/icons/)."}, "borders": {"top_border": "<PERSON><PERSON><PERSON> in alto", "bottom_border": "<PERSON><PERSON><PERSON> in basso", "show_border": "Show border"}, "colors": {"heading_background": "Sfondo intestazione", "shadow": "Mostra ombra"}, "social": {"phone": "Telefono", "discord": "Discord"}, "settings": {"image_with_hotspots": {"label": "Immagine con hotspot", "hotspot": {"label": "Hotspot", "label_desktop_offset": "Hotspot desktop", "label_mobile_offset": "Hotspot mobile", "offset_horizontal": "Spostamento orizzontale", "offset_vertical": "Spostamento verticale", "tooltip": {"label": "<PERSON><PERSON><PERSON>", "position": {"label": "Posizione", "option_1": "Alto", "option_2": "<PERSON><PERSON>", "option_3": "A sinistra", "option_4": "A destra"}, "show_tooltip": "Show tooltip"}, "mobile_info": "Only if a mobile image is set"}, "image_on_mobile": "Mobile image", "position_on_mobile": "Position on mobile"}, "scrolling_images": {"label": "<PERSON><PERSON><PERSON> immagini", "image_size": "Dimensioni immagine", "columns": "Columns"}, "video": {"label": "Video", "info": "Formato MP4 richiesto, nessun suono"}, "variants_functionality": {"label": "Gestisci varianti non disponibili", "option_1": "Nascondi", "option_2": "Disabilita", "option_3": "Mostra"}, "auto_height": {"label": "Altezza automatica", "info_slider": "Selezionando questa opzione si sovrascriveranno le impostazioni di altezza precedenti e si renderà l'altezza della presentazione reattiva all'immagine contenuta in ogni diapositiva."}}, "header": {"promotion_block": {"image_link": "Link immagine promozionale"}, "sticky": {"label": "Intestazione adesiva", "option_1": "Disabilitato", "option_2": "Sempre", "option_3": "Solo scorrendo verso l'alto"}}, "inventory": {"name": "<PERSON><PERSON> scorte", "settings": {"show_progress_bar": "Mostra barra a<PERSON>o", "low_inventory_threshold": "Bassa soglia di scorte", "show_block": {"always": "Mostra sempre", "low": "Mostra solo quando le scorte scendono sotto la soglia"}}}, "breadcrumb": {"name": "Breadcrumb", "info": "La navigazione breadcrumb non appare nella home page"}, "announcement-bar": {"visibility": {"label": "Visibilità", "option_1": "<PERSON><PERSON> le pagini", "option_2": "Solo la home page", "option_3": "Tutte le pagine ad eccezione della home ", "option_4": "Solo pagine prodotti", "option_5": "Solo pagina carrello"}, "color": {"border": "Colore bordo"}}, "promotional_banner": {"name": "Banner promozionale", "enable": "Mostra banner"}, "cookies_banner": {"name": "<PERSON><PERSON>", "enable": "Mostra avviso cookie"}, "before_after": {"name": "Confronto immagine", "layout": {"label": "Layout", "option__1": "Orizzontale", "option__2": "Verticale"}, "style": {"label": "Stile colore", "option__1": "Chiaro", "option__2": "<PERSON><PERSON>"}, "image": {"label__1": "<PERSON><PERSON><PERSON><PERSON>", "label__2": "Immagine per mobile", "label__3": "<PERSON><PERSON><PERSON><PERSON>"}}, "cart_upsell": {"name": "Consigli prodotto individuale", "product": "<PERSON><PERSON><PERSON>", "info": "I consigli dinamici si basano sugli articoli presenti nel carrello. Cambiano e migliorano col tempo. [per maggiori info](https://help.shopify.com/en/themes/development/recommended-products)"}, "gift_wrapping": {"name": "Confezione regalo", "info": "La confezione regalo deve essere impostata come un prodotto. [maggiori info](https://shopify-support.krownthemes.com/article/696-cart-features#gift-wrap)", "settings": {"label": "<PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON> pulsante"}}, "custom_code": {"name": "HTML / Liquid personalizzato"}, "rating": {"name": "Recensioni app", "default": "<PERSON><PERSON><PERSON>"}, "product-page": {"size_guide": {"label": "<PERSON><PERSON><PERSON> taglie", "page": "<PERSON><PERSON>a guida taglie", "options": {"label": "Apri opzioni", "option_1": "Popup", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Nuova finestra"}}, "gallery_resize": {"label": "Proporzioni delle immagini", "info": "Video e altri tipi di media saranno visualizzati nel loro rapporto di aspetto originale.", "option_1": "<PERSON><PERSON><PERSON> immagini al contenitore"}, "gallery_padding": {"label": "Spaziatura interna nella galleria"}, "gallery_background": {"label": "Sfondo galleria", "info": "Visibile solo se le immagini sono impostate per rientrare nel contenitore."}}, "product-card": {"name": "Scheda del prodotto", "labels": {"thumbnail": "Miniatura prodotto", "caption": "<PERSON><PERSON><PERSON><PERSON> prodotto", "color_swatches": "Campioni di colore"}, "thumbnails": {"fit": "Adatta contenuti media nel contenitore", "padding": {"label": "Spaziatura nel contenitore", "info": "Funziona solo se i contenuti media sono impostati per rientrare nel contenitore."}, "background": {"label": "Sfondo contenitore", "info": "Funziona solo se i contenuti media sono impostati per rientrare nel contenitore."}, "border": "Colore bordo", "color_swatches": "Mostra campioni di colore nella scheda prodotto", "color_swatches_on_hover": "Mostra campioni di colore nella scheda prodotto (passandovi sopra)"}, "color_swatches_label": {"label": "Etichette campioni colore", "info": "Scrivi diversi titoli di varianti (separati da virgole) che vuoi far diventare campioni colore."}, "badges": {"name": "Badge prodotto", "show_badges": "Mostra badge", "settings": {"colors": {"text": "Colore del testo dei simboli", "sold_out": "Colore di sfondo per \"Esaurito\"", "sale": "Colore di sfondo per \"Sconto\""}}, "badge_sale": {"name": "Simbolo per lo sconto", "amount_saved": "Importo rispar<PERSON>"}, "regular_badges": {"info": "Maggiori informazioni sui distintivi prodotto [qui](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)."}, "sold_out": {"name": "Distintivo esaurito", "text_color": "'<PERSON><PERSON><PERSON>' colore testo", "sale_text": "'Sconto' colore testo"}, "custom_badges": {"name": "Simboli prodotti personalizzati", "info": "Questo tema utilizza distintivi di prodotto personalizzati che possono essere definiti qui. [Maggiori informazioni](https://shopify-support.krownthemes.com/article/564-product-pages#product-badges)", "split_info": "Questo tema utilizza badge di prodotto personalizzati che possono essere definiti qui. [Maggiori informazioni](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)", "name__1": "Simbolo personalizzato 1", "name__2": "Simbolo personalizzato 2", "name__3": "Simbolo personalizzato 3", "text": "<PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON>", "color": "Colore di sfondo", "text_color": "Colore del testo", "border_color": "Colore bordo"}}}, "seo_late": {"breadcrumbs": {"label": "Improve product breadcrumb navigation", "info": "Adds collection names to the breadcrumb navigation, by duplicating product url's. [Learn more](https://shopify-support.krownthemes.com/article/697-seo#breadcrumbs)"}}}, "local-extra-words": {"settings_schema": {"colors": {"headings": {"header": "Intestazione", "cards": "<PERSON><PERSON>"}, "settings": {"borders": "<PERSON><PERSON>", "hide_border": "Nascondi il bordo", "accent": "Accento", "overlay": "Overlay"}}, "typography": {"buttons": {"label": "Spessore carattere dei pulsanti", "option__1": "Normale", "option__2": "<PERSON><PERSON> grassetto"}, "menus": {"header": "<PERSON><PERSON>", "size": "Dimensione base", "weight": "Spessore del carattere", "weight_bold": "Grassetto"}}, "borders": {"name": "<PERSON><PERSON>", "main": {"name": "Sezioni", "info": "Questa impostazione controlla lo stile dei bordi su tutte le sezioni tramite il tema."}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "forms": {"name": "<PERSON><PERSON><PERSON>"}, "settings": {"width": "<PERSON><PERSON><PERSON><PERSON>", "radius": "Raggio"}}, "layout": {"name": "Disposizione", "sections": {"vertical_space": "Spazio verticale tra le sezioni", "remove_vertical_space": "Rimuovi il margine superiore", "remove_bottom_margin": "Rimuovi il margine inferiore"}, "grid": {"name": "Griglia", "info": "Interessa le aree con disposizione a più colonne.", "horizontal_space": "Spazio orizzontale", "vertical_space": "Spazio verticale"}}, "cart": {"shipping": {"name": "Spedizione", "show": {"label": "Mostra l'importo minimo per la spedizione gratuita", "info": "Per configurare le tariffe di spedizione, vai alle [impostazioni di spedizione](/amministratore/impostazioni/spedizione)."}, "amount": {"label": "Importo minimo per la spedizione gratuita", "info": "Scrivi un numero, niente lettere o caratteri speciali."}, "info": "This feature only works with a single currency and shipping method. If you're using multiple shipping methods or currencies, it might show false values to the customer."}, "settings": {"media": {"header": "Media aspect ratio", "label": "Aspect ratio", "info_cart": "Changes the aspect ratio for small product thumbnails used in the cart, search popup and some other areas that require small product thumbnails."}}}, "aspect_ratio": {"landscape": {"label": "<PERSON><PERSON> corto (3:2)"}}, "maps": {"name": "Mappe"}, "search": {"predictive_search": {"name": "Ricerca predittiva", "info": "La ricerca predittiva supporta suggerimenti per prodotti, collezioni, pagine e articoli."}}, "product-card": {"name": "Scheda del prodotto", "title-size": {"name": "Dimensione del titolo", "options__1": "<PERSON><PERSON><PERSON>", "options__2": "Grande"}, "local-pickup": {"name": "Disponibilità locale", "info": "Questo tema mostra la disponibilità locale dei prodotti. [Scopri di più](https://shopify-support.krownthemes.com/article/805-collection-page)"}, "badges": {"name": "Simboli predefiniti dei prodotti", "settings": {"colors": {"text": "Colore del testo dei simboli", "sold_out": "Colore di sfondo per \"Esaurito\"", "sale": "Colore di sfondo per \"Sconto\""}}, "badge_sale": {"name": "Simbolo per lo sconto"}, "custom_badges": {"name": "Simboli prodotti personalizzati", "info": "Questo tema usa simboli prodotti personalizzati che puoi definire qui. [Scop<PERSON> di più](https://shopify-support.krownthemes.com/article/795-product-card#Product-badges-_KIdK)", "name__1": "Simbolo personalizzato 1", "name__2": "Simbolo personalizzato 2", "name__3": "Simbolo personalizzato 3", "text": "<PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON>", "color": "Colore di sfondo", "text_color": "Text color"}}, "icons_list": "Elenco icone dinamiche", "products-list": {"name": "Products list"}}, "embellishments": {"settings": {"show_gotop": {"label": "Show go to top button"}}}}, "sections": {"video": {"name": "Video", "settings": {"video": {"label": "URL video"}, "image": {"label": "Immagine di sfondo"}}}, "contact-form": {"settings": {"form-fields": {"name": "Campi del modulo", "show-phone": "Mostra telefono", "show-subject": "<PERSON><PERSON> og<PERSON>"}}, "blocks": {"contact-info": {"name": "Informazioni di contatto", "settings": {"title": {"label": "<PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "text-columns-with-icons": {"blocks": {"icon": {"name": "Icona personalizzata", "info": "256 x 256px"}, "select_icon": {"info": "Per visualizzare e scaricare altre icone, visita [questo link](https://resources.krownthemes.com/icons/)"}, "icon_color": {"label": "Icona", "info": "Funziona solo con le icone incluse"}}}, "content-toggles": {"name": "Il contenuto attiva", "block": "<PERSON><PERSON><PERSON>"}, "text-columns-with-images": {"blocks": {"image": {"info": "640 x 640px"}}}, "announcement-bar": {"settings": {"social": {"header": "Icone social", "info": "Per configurare i profili social, vai su Impostazioni tema > Social.", "label": "Mostra le icone social"}}, "blocks": {"content": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": "<PERSON><PERSON>", "link": "Link", "target": "Apri il link in una nuova finestra"}}}}, "newsletter": {"show_icon": "Mostra l'icona"}, "cookies": {"name": "Pop-up dei cookie", "cookies_info": "Questo sito utilizza i cookie per garantire la migliore esperienza utente. [Per saperne di più](#)"}, "popups": {"name": "Pop-up", "blocks": {"model": {"model-1": "<PERSON><PERSON>", "model-2": "Newsletter", "model-3": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"size": {"label": "Dimensione del pop-up", "option_1": "<PERSON><PERSON><PERSON>", "option_2": "Grande"}}}}, "age-verification": {"name": "Verifica dell'età", "settings": {"button-text": "Testo del pulsante"}}, "stores-map": {"name": "Mappa dei negozi", "settings": {"map": {"title": "Mappa"}, "gallery": {"title": "Galleria del negozio"}}}, "store-selector": {"name": "Selettore del negozio", "settings": {"map": {"label": "Abilita la mappa dinamica", "info": "Accertati di avere la chiave API di Google Maps impostata in modo corretto nelle Impostazioni del tema"}, "zoom": {"label": "Ingrandimento mappa", "info": "Scegli un valore adeguato per visualizzare tutti i negozi desiderati contemporaneamente."}}, "blocks": {"map": {"name": "Posizione sulla mappa", "settings": {"address": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Scopri di più"}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Carica un'immagine statica se non vuoi usare la mappa dinamica."}, "style": {"label": "<PERSON><PERSON> della mappa", "option__1": "Normale", "option__2": "Argento", "option__3": "Retro", "option__4": "<PERSON><PERSON>", "option__5": "Notte", "option__6": "Melanzana"}, "pin": {"label": "<PERSON><PERSON><PERSON> sulla mappa", "info": "240 x 240px trasparente .png"}}}, "store": {"name": "Negozio", "settings": {"name": {"label": "Nome", "info": "Il nome del negozio deve corrispondere al nome del tuo negozio definito nelle [impostazioni di posizione](/amministratore/impostazioni/posizione)"}, "pickup_price": {"label": "Prezzo del ritiro"}, "pickup_time": {"label": "Orario del ritiro"}, "address": {"label": "Dettagli del negozio"}, "image": {"label": "Immagine del negozio"}, "closing_times": {"label": "Orari di chiusura (opzionale)", "info": "Aggiungi 7 righe, una per ogni giorno della settimana, a partire da domenica."}, "timezone": {"label": "<PERSON><PERSON> orario", "info": "Utiliz<PERSON><PERSON> per mostrare correttamente gli orari di chiusura"}, "map": {"name": "<PERSON><PERSON><PERSON> sulla mappa", "info": "Se la mappa è abilitata, devi definire uno spillo personalizzato per questo indirizzo. [Scopri come ottenere le coordinate del tuo indirizzo](https://support.google.com/maps/answer/18539?hl=en)"}, "map_latitude": {"label": "Latitudine", "info": "Coordinata della latitudine per l'indicatore. Esempio: 46.7834818"}, "map_longitude": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Coordinata della longitudine per l'indicatore. Esempio: 23.5464733"}, "get_directions_button": {"label": "Mostra il pulsante \"ottieni indicazioni\"", "info": "Apre una mappa più grande in una nuova scheda del browser."}, "map_pin": {"label": "<PERSON><PERSON><PERSON>", "info": "90 x 90px trasparente .png"}}}}}, "header": {"settings": {"layout": {"label": "Disposizione dell'intestazione", "info": "Interessa la posizione del blocchi personalizzati e le azioni predefinite", "option__1": "<PERSON><PERSON> in alto, azioni predefinite in basso", "option__2": "Azioni predefinite in alto, bloc<PERSON> in basso"}, "sticky": {"label": "Intestazione adesiva", "info": "Mostra la navigazione quando l'utente scorre verso l'alto"}}, "blocks": {"info": {"name": "Informazioni", "style": {"label": "Stile", "option__1": "Informazione testuale", "option__2": "Pulsante", "info": "Sui pulsanti è visibile solo la didascalia, cos<PERSON> come l'etichetta del pulsante."}, "custom-icon": {"label": "Icona personalizzata", "info": "Carica un'immagine di 76 x 76px .png"}, "icon": {"label": "Icona"}, "link_type": {"label": "Apri il link", "option__1": "All'interno di una finestra modale", "option__2": "<PERSON>la stessa pagina", "option__3": "In una nuova pagina", "info": "Le finestre modali funzionano solo con i link interni alle pagine"}}, "store-selector": {"name": "Selettore del negozio", "content": "Il selettore del negozio può essere configurato nella sezione Selettore del negozio. [Scopri di più](https://shopify-support.krownthemes.com/article/799-store-selector)", "info": "Questo tema consente di collegare le posizioni dei tuoi negozi reali ad un selettore interattivo di negozi. [Scopri di più](https://shopify-support.krownthemes.com/article/799-store-selector"}, "mega-menu": {"name": "Menu mega", "settings": {"menu_handle": {"label": "Riferimento menu", "info": "Questo tema utilizza i menu mega. [<PERSON><PERSON><PERSON> di più](https://shopify-support.krownthemes.com/article/798-header-section#mega-menus)"}}}}}, "marquee": {"name": "<PERSON>o scorrevole", "settings": {"scroll_direction": "Direzione di scorrimento", "scroll_speed": "Velocità di scorrimento", "scroll_speed_info": "Maggiore è il valore, più lento è lo scorrimento", "pause_on_mouseover": "Pausa al passaggio del mouse", "scroll_item": "Elemento di scorrimento", "scroll_item_text": "Testo di scorrimento"}}, "image-section": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"image_size": {"label": "Larghezza del <PERSON>", "info": "Su dispositivi mobili l'immagine sarà a larghezza intera."}}}, "media-with-text-overlay": {"name": "File multimediale con sovrapposizione del testo", "blocks": {"media": "File multimediale", "image": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "link": {"info": "Il titolo si trasformerà in un collegamento a meno che non sia presente un'etichetta per il pulsante"}, "video": {"name": "Video", "label": "Video", "info": "<PERSON><PERSON><PERSON><PERSON> mostrata l'immagine in alto se questo video non può essere riprodotto."}}, "settings": {"height": "Altezza della scheda", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "Grande", "option__3": "Extra large", "option__4": "Schermo intero", "option__5": "Regolare"}}, "blog-posts": {"settings": {"emphasize": {"label": "Enfatizza il primo articolo", "info": "Solo su desktop"}}, "blocks": {"summary": {"name": "<PERSON><PERSON><PERSON>", "settings": {"excerpt_limit": "Numero di parole", "excerpt_limit_info": "Viene applicato se l'articolo non ha un estratto manuale aggiunto nell'amministratore."}}}}, "testimonials": {"name": "<PERSON><PERSON><PERSON><PERSON>", "blocks": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "slideshow": {"name": "Presentazione", "block": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"caption_size": "Dimensione della didascalia"}}, "rich-text": {"settings": {"image_position": {"label": "Posizione dell'immagine", "option__1": "<PERSON><PERSON> sinistra", "option__2": "Sopra il testo", "option__3": "Sulla destra"}, "fullwidth": {"label": "A larghezza intera", "info": "Estendi lo sfondo di questa sezione per riempire lo schermo."}, "height": {"label": "Altezza della scheda", "info": "Altezza minima della scheda su desktop. Su dispositivi mobili, l'altezza dipenderà dal contenuto."}, "crop": {"label": "Riempi l'area dell'immagine", "info": "L'immagine verrà ritagliata per riempire l'intera altezza della scheda su desktop. Su dispositivi mobili, l'immagine verrà sempre mostrata per intero."}, "remove_margin": {"label": "Rimuovi il margine superiore della sezione"}}}, "main-header": {"settings": {"mobile": {"name": "Navigazione mobile", "info": "Questi interessano solamente la visibilità all'interno del drawer di navigazione mobile.", "header_actions": "Mostra il selettore di negozi e i blocchi informativi", "header_info_blocks": {"header": "Blocchi informativi dell'intestazione", "label_1": "Mostra il selettore di negozi e i blocchi informativi nell'intestazione sui dispositivi mobili", "label_2": "Posiziona i blocchi informativi in cima alla prima sezione sulla pagina principale", "label_2_info": "Si integra bene quando la prima sezione è una presentazione a larghezza intera"}}, "promotion_block": {"title": {"label": "<PERSON><PERSON>", "size": "Dimensione del titolo"}, "subtitle": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "size": "Dimensione del sottotitolo"}, "button": {"label": "Etichetta del pulsante", "size": "Dimensione del pulsante", "link": "Link del pulsante", "style": "Stile del pulsante"}}, "header_actions": {"header": "Blocchi informativi dell'intestazione su dispositivi mobili", "show_in_drawer": "Mostra all'interno del drawer di navigazione"}}}, "main-cart": {"blocks": {"shipping-calculator": {"name": "Calcolatrice di spedizione"}, "related-products": {"info": "The product recommendations are based on products that are commonly purchased together or products in related collections. If there are no recommendations, the block will not appear."}}}, "main-search": {"settings": {"blogs": {"name": "Risultati dell'articolo"}, "products": {"name": "Risultati del prodotto", "info": "Il contenuto della scheda del prodotto deve essere impostato usando i blocchi di sezione."}}}, "main-product": {"name": "Pagina del prodotto", "settings": {"gallery_pagination": "Impaginazione del dispositivo di scorrimento della galleria", "show_border": "Mostra il bordo intorno alla galleria", "gallery_border": "Gallery border", "gallery_border_color": "Gallery border color"}, "blocks": {"pickup_availability": {"name": "Disponibilità per il ritiro", "info": "Questo tema mostra la disponibilità per il ritiro in base al negozio selezionato. Scopri di più", "settings": {"style": "Stile", "option__1": "Compatto", "option__2": "<PERSON><PERSON><PERSON>"}}, "buy_buttons": {"settings": {"show_price": "Mostra il prezzo"}}, "related": {"name": "Prodotti collegati", "settings": {"products": "<PERSON><PERSON>tti"}}, "tax_info": {"name": "Informazioni fiscali"}, "icons": {"name": "Elenco delle icone", "info": "Per visualizzare e scaricare le icone incluse nel tema, visita [questo link](https://resources.krownthemes.com/icons/).", "help": "Questo tema ti permette di aggiungere icone personalizzate dei prodotti tramite contenuto dinamico. [Scop<PERSON> di più](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "headers": {"icon_1": "Icona 1", "icon_2": "Icona 2", "icon_3": "Icona 3", "icon_4": "Icona 4", "icon_5": "Icona 5", "icon_6": "Icona 6"}, "settings": {"icon": "Icona", "icon_info": "96 x 96px", "label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "main-blog": {"name": "Blog principale"}, "main-article": {"name": "Articolo", "settings": {"show_tags": "Mostra le etichette", "enhance_product_links": {"label": "Migliora i link ai prodotti", "info": "Tutti i link dei prodotti apriranno la finestra modale di acquisto rapido del prodotto."}}}, "main-article-comments": {"name": "Commenti all'articolo", "info": "Per abilitare i commenti vai alle [impostazioni del blog].(/amministratore/blog)"}, "main-article-navigation": {"name": "Navigazione dell'articolo", "settings": {"header": {"content": "Post del blog", "info": "<PERSON><PERSON> vuoto se vuoi caricare il post del blog predefinito precedente o successivo."}, "posts": {"next": "Post successivo", "previous": "Post precedente"}}}, "main-page": {"settings": {"center": {"label": "Centra il contenuto su desktop"}}}, "main-footer": {"blocks": {"payment": {"name": "Icone di pagamento", "info": "Le icone mostrate sono determinate dalle [impostazioni di pagamento](/amministratore/impostazioni/pagamenti) del tuo negozio e dall'area geografica e valuta del cliente.", "custom_icons": "Custom icons", "custom_icons_info": "Upload 114 x 72px .png images", "only_show_custom_icons": "Only show the custom icons"}, "text": {"name": "Text & social icons"}}}, "customers": {"reset-password": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "order": {"name": "<PERSON><PERSON><PERSON> d'or<PERSON>"}, "register": {"name": "Pagina di registrazione"}, "activate-account": {"name": "Pagina di attivazione dell'account"}, "login": {"name": "Pagina di accesso", "shop_login_button": {"enable": "Abilita Accedi con Shop"}}, "account": {"name": "Pagina dell'account"}, "addresses": {"name": "<PERSON>diriz<PERSON>"}}, "headings": {"heading": "Intestazione", "subheading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caption": "Didascalia", "text_content": "Contenuto del testo", "custom_colors": "Colori personalizzati", "text_style": "Stile del testo"}, "columns": {"name": "Disposizione desktop", "info": "La disposizione si adatta da sé sui dispositivi mobili.", "option__0": "1 colonna", "option__1": "2 colonne", "option__2": "3 colonne", "option__3": "4 colonne", "option__4": "5 colonne", "option__5": "6 colonne", "name_mobile": "Mobile layout"}, "promotion-cards": {"name": "Carte promozionali", "blocks": {"name": "Carta"}}, "faq": {"headings": {"header": "Intestazione", "content": "<PERSON><PERSON><PERSON>"}, "settings": {"form": {"header": "Modulo di contatto", "show": "Mostra il modulo", "title": "<PERSON><PERSON> del modulo"}}}, "product-quick-view": {"name": "Vista rapida", "info": "Questo modello controlla come viene realizzata la vista rapida. Solo questa sezione apparirà nella finestra modale."}, "product-card": {"blocks": {"price": "Prezzo", "title": "<PERSON><PERSON>", "vendor": "Venditore", "text": {"name": "Testo dinamico", "info": "Usa una fonte dinamica per evidenziare un attributo unico creando un metafield del prodotto. [Scop<PERSON> di più](https://shopify-support.krownthemes.com/article/805-collection-page)", "settings": {"text": {"label": "Metafield etichetta"}, "size": {"label": "Dimensione del testo", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "Regolare", "option__3": "Grande"}, "color": {"label": "Colore del testo", "option__1": "Primario", "option__2": "Secondario"}, "transform": {"label": "Trasformazione del testo (maiuscolo)"}}}, "icons": {"info": "Usa delle fonti dinamiche per evidenziare attributi unici creando metafield del prodotto per l'elenco delle icone. [Scopri di più](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "settings": {"icon": "Metafield icona", "label": "Metafield etichetta"}}, "quick_buy": "Acquisto veloce", "rating": "Valutazione"}}, "buttons": {"style": {"label": "Stile del pulsante", "option__1": "Contorno", "option__2": "Pieno"}}}}, "split-extra-words": {"settings_schema": {"colors": {"headings": {"header_sidebars": "Titolo e barre laterali", "main": "Corpo", "footer": "Piè di pagina", "custom_colors": "Colori personalizzati"}, "settings": {"background": "Sfondo", "text": "<PERSON><PERSON>", "links": "Collegamenti attivi", "borders": "<PERSON><PERSON> bordi"}}, "typography": {"headings": {"headings": "Intestazioni", "body": "Corpo", "logo_menus": "Logo e menu", "buttons": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"font_family": "Famiglia di caratteri", "base_size": "Dimensione di base", "line_height": "Altezza della riga", "hr": {"label": "<PERSON>ra barre or<PERSON>i", "info": "Mostra una piccola barra orizzontale sotto alcuni titoli"}, "border_radius": "Raggio del bordo"}}, "embellishments": {"preloader": {"label": "Indicatore di caricamento", "info": "Mostra un piccolo indicatore circolare mentre i contenuti multimediali nel tuo negozio sono in caricamento."}, "breadcrumb": {"label": "Mostra percorso di navigazione", "info": "La navigazione Breadcrumb aiuta gli utenti a navigare nel negozio e viene visualizzata solo nelle pagine collezione, prodotto, ricerca e account."}}, "cart": {"page": "Articoli nel carrello", "show_recommendations": "Mostra i suggerimenti in base al carrello"}, "headings": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product-grid": {"animation_style": {"label": "Riquadro didascalia (desktop)", "options__1": "Visibile", "options__2": "In sovrimpressione", "info": "Su dispositivi mobili, la didascalia sarà sempre visibile per una migliore esperienza degli utenti."}, "overlay_colors": {"background": "Sfondo della didascalia in sovrimpressione", "text": "Testo della didascalia in sovrimpressione"}, "aspect_ratio": {"label": "Aspetto dei media del prodotto", "options__1": "Ritagliate", "options__2": "Naturali"}, "show_secondary_image": {"info": "Solo sul desktop"}, "quick_buy": {"name": "Acquisto rapido", "info": "Aggiunge un pulsante \"aggiungi al carrello\" istantaneo. Se il prodotto ha delle varianti, verrà visualizzato un pop-up di \"acquisto rapido\".", "label": "Abilita acquisto rapido"}, "rating": {"label": "Riquadro valutazioni (desktop)", "options__1": "Non mostrare", "options__2": "Mostra al passaggio", "options__3": "Sempre visibile", "show_on_mobile": "Mostra su dispositivi mobili"}}}, "sections": {"header": {"name": "<PERSON><PERSON>", "settings": {"logo_height": "Altezza massima immagine logo", "menu": "<PERSON><PERSON>", "menu_style": {"label": "Stile del menu desktop", "options__1": "Classico", "options__2": "Drawer"}, "collections_menu": {"header": "Menu collezioni", "info": "Ha un design appariscente, soprattutto nel menu stile classico, dove si trasforma in un \"mega menu\" con la possibilità di aggiungere immagini e una promozione.", "settings": {"show_images": {"label": "Mostra immagini della collezione", "info": "Si applica solo se gli articoli principali fanno parte di una collezione."}}}, "promotional_block": {"name": "Blocco promozione", "settings": {"show": {"label": "Mostra blocco promozione", "info": "<PERSON><PERSON> stile minimale, viene visualizzato in fondo al menu. <PERSON>o stile classico, viene visualizzato nel menu collezioni, se presente."}, "title": {"label": "Titolo della promozione"}, "content": {"label": "Contenuto della promozione"}, "button": {"label": "Etichetta del pulsante promozione"}, "link": {"label": "Collegamento del pulsante promozione"}, "txt_color": {"label": "Colore del testo della promozione"}, "bg_color": {"label": "Colore di sfondo della promozione"}, "image": {"label": "Immagine della promozione"}}}, "announcement_bar": {"content": {"info": "Massimo 50 caratteri"}}}}, "footer": {"blocks": {"menu": {"name": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}}}, "main-product": {"name": "Pagina del prodotto", "settings": {"header": {"label": "<PERSON><PERSON>", "info": "Su dispositivi mobili, il titolo del prodotto verrà mostrato sempre in alto, sopra la galleria del prodotto.", "show_tax_info": "Mostra informazioni sulle imposte", "show_reviews": "Mostra valutazione prodotto", "show_sku": "Mostra SKU", "show_barcode": "Mostra CODICE A BARRE", "show_vendor": "<PERSON>ra fornitore", "show_badge": "Mostra badge del prodotto"}, "variants": {"label": "Tipo di selezione delle varianti", "options__1": "<PERSON><PERSON>", "options__2": "A discesa"}, "gallery_aspect": {"label": "Scala le immagini a scorrimento adattandole all'area di visualizzazione", "info": "Su dispositivi mobili, le immagini saranno sempre adattate all'area di visualizzazione del dispositivo."}, "color_swatches": {"label": "Mostra campioni di colore (solo per lo stile a blocchi)", "info": "<PERSON>o tema può mostrare immagini personalizzate per i campioni di colore. [Learn more](https://shopify-support.krownthemes.com/article/554-using-color-swatches)"}}}, "countdown-banner": {"name": "Banner del conto alla rovescia", "settings": {"header": "Orologio del conto alla rovescia", "show_countdown": "Mostra orologio del conto alla rovescia", "countdown_year": "<PERSON><PERSON> <PERSON>", "countdown_month": "Mese di fine", "countdown_day": "<PERSON><PERSON><PERSON>", "countdown_hour": "Orario di fine", "countdown_timezone": "<PERSON><PERSON> orario", "size": "Altezza del banner"}}, "map": {"settings": {"map": {"api": {"label": "Chiave API Google Maps", "info": "Dovrai registrare una [Google Maps API Key](http://shopify-support.krownthemes.com/article/385-generate-an-api-key-for-google-maps) per visualizzare la mappa."}}}}}}, "complementary_products": {"name": "Prodotti complementari", "settings": {"paragraph": {"content": "Aggiungi l'app Search & Discovery per selezionare i prodotti complementari. [Maggiori informazioni](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}}}, "main-cart-footer": {"name": "Subtotale carrello", "blocks": {"subtotal_button": {"name": "Subtotale e cassa"}}}, "main-cart-items": {"name": "Articolo del carrello"}, "main-list-collections": {"name": "Pagina elenco collezioni", "blocks": {"collection": {"name": "Collezione", "settings": {"collection": {"label": "Collezione"}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Se vuoi aggiungere un'immagine personalizzata per la collezione."}}}}, "settings": {"header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "layout": {"label": "Layout", "options__1": {"label": "Una colonna"}, "options__2": {"label": "Due colonne"}}, "paragraph": {"content": "Tutte le tue collezioni sono elencate per impostazione predefinita. Per personalizzar<PERSON> l'elenco, scegli \"Selezionate\" e aggiungi le collezioni."}, "display_type": {"label": "Seleziona le collezioni da mostrare", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Selezionate"}}, "sort": {"label": "Ordina le collezioni per:", "info": "L'ordinamento si applica solo quando è selezionato \"Tutte\"", "options__1": {"label": "In ordine alfabetico, A-Z"}, "options__2": {"label": "In ordine alfabetico, Z-A"}, "options__3": {"label": "Data, dal più recente al più vecchio"}, "options__4": {"label": "Data, dal più vecchio al più recente"}, "options__5": {"label": "<PERSON><PERSON><PERSON> prodotti, da alto a basso"}, "options__6": {"label": "<PERSON><PERSON><PERSON> prodotti, da basso ad alto"}}, "items_per_row": "Numero di articoli per riga"}}, "sidebar": {"name": "Barra laterale", "settings": {"image": {"label": "Immagine logo"}, "image_width": {"label": "Larghezza immagine logo"}, "primary_navigation": {"label": "Navigazione principale"}, "secondary_navigation": {"label": "Navigazione secondaria"}, "search": {"content": "Cerca", "label": "Visualizza ricerca"}}}, "text-columns-with-icons": {"name": "Colonne di testo con icone", "settings": {"content": {"label": "Mostra solo nelle pagine selezionate:"}, "show_on_homepage": {"label": "Homepage"}, "show_on_product": {"label": "Pagine dei prodotti"}, "show_on_collection": {"label": "Pagine delle collezioni"}, "show_on_blog": {"label": "Pagine del blog e degli articoli"}, "show_on_regular": {"label": "Pagine normali"}, "icons": {"label": "Icone", "info": "Per visualizzare tutte le icone incluse nel tema, visita [questo link](https://krownthemes.com/krown-icons/)"}}, "blocks": {"icon": {"name": "<PERSON>o con icona", "settings": {"title": {"label": "Intestazione"}, "text": {"label": "<PERSON><PERSON>"}, "icon": {"label": "Seleziona un'icona"}}}}}, "footer": {"name": "Piè di pagina", "settings": {"show_payment_icons": {"label": "Mostra icone di pagamento"}, "language_selector": {"content": "Selettore della lingua", "info": "Per aggiungere una lingua, vai alle [impostazioni lingua.](/admin/settings/languages)"}, "language_selector_show": {"label": "Mostra selettore della lingua"}, "country_selector": {"content": "Selettore paese/area geografica", "info": "Per aggiungere un paese/un'area geografica, vai alle [impostazioni di pagamento.](/admin/settings/payments)"}, "country_selector_show": {"label": "Abilita selettore paese/area geografica"}}, "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"title": {"label": "Intestazione"}, "content": {"label": "<PERSON><PERSON><PERSON>"}, "text_size": {"label": "Dimensione del testo", "options__1": {"label": "Normale"}, "options__2": {"label": "Grande"}}}}, "menus": {"name": "<PERSON><PERSON>", "settings": {"title_1": {"label": "Prima intestazione del menu"}, "title_2": {"label": "Seconda intestazione del menu"}, "menu_1": {"label": "Primo menu", "info": "Questo menu non mostrerà elementi a discesa"}, "menu_2": {"label": "Secondo menu"}}}, "newsletter": {"name": "Registrazione tramite e-mail"}, "social": {"name": "Collegamenti social"}, "image": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Seleziona immagine"}}}}}, "contact-form": {"name": "Modulo di contatto", "settings": {"title": {"label": "Intestazione"}}, "blocks": {"field": {"name": "Campo modulo", "settings": {"type": {"label": "Tipo", "options__1": {"label": "Linea singola"}, "options__2": {"label": "Linea multipla"}}, "required_field": {"label": "Obbligatorio"}, "labels": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Assicurati che tutti i campi siano associati a etichette univoche!"}}}, "email": {"name": "Nome ed e-mail"}, "button": {"name": "Pulsante Invio", "settings": {"label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Modulo di contatto"}}, "image-with-text": {"name": "Immagini con testo", "blocks": {"image": {"name": "Immagine con testo", "settings": {"title": {"label": "Intestazione"}, "body": {"label": "<PERSON><PERSON>"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON> pulsante"}, "url": {"label": "Link", "info": "L'intero blocco si trasformerà in un collegamento a meno che non sia presente un'etichetta per il pulsante"}, "image": {"label": "Immagine di sfondo"}}}}, "settings": {"image_height": {"label": "Altezza immagine", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Completa"}}, "text_width": {"label": "<PERSON><PERSON><PERSON><PERSON> contenitore testo", "options__1": {"label": "Media"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Completa"}}, "text_size": {"label": "Dimensione intestazione", "options__1": {"label": "Normale"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Extra large"}}, "text_alignment": {"label": "Allineamento testo", "options__1": {"label": "In alto a sinistra"}, "options__2": {"label": "In alto centrato"}, "options__3": {"label": "In alto a destra"}, "options__4": {"label": "Centrale a sinistra"}, "options__5": {"label": "Centrale centrato"}, "options__6": {"label": "Centrale a destra"}, "options__7": {"label": "In basso a sinistra"}, "options__8": {"label": "In basso centrato"}, "options__9": {"label": "In basso a destra"}}, "options__5": {"label": "Extra large"}}, "presets": {"name": "Immagini con testo"}}, "featured-product": {"name": "Prodotto in primo piano", "settings": {"product": {"label": "Seleziona prodotto"}}, "blocks": {"product_link": {"name": "<PERSON> prodotto"}}}, "featured-collection": {"name": "Collezione in primo piano", "settings": {"title": {"label": "Intestazione"}, "show_view_all": {"label": "Mostra il collegamento alla pagina della collezione"}, "layout": {"label": "Layout", "options__1": {"label": "Slide<PERSON>"}, "options__2": {"label": "Griglia"}}, "products_number": {"label": "Numero massimo di prodotti visualizzati"}, "collection": {"label": "Collezione"}}, "presets": {"name": "Raccolta di funzionalità"}}, "gallery": {"name": "Galleria", "blocks": {"image": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"label": "Didascalia"}, "featured": {"label": "Ingrandisci l'immagine nella griglia"}}}}, "settings": {"aspect_ratio": {"label": "Proporzioni delle immagini", "options__1": {"label": "Corte (4:3)", "group": "Ritagliate"}, "options__2": {"label": "Quadrate (1:1)"}, "options__3": {"label": "<PERSON><PERSON> (5:6)"}, "options__4": {"label": "<PERSON><PERSON> (2:3)"}, "options__5": {"label": "Naturali", "group": "Senza ritagli"}, "info": "Per un design a griglia pulito, quando utilizzi le proporzioni naturali assicurati di ridimensionare le miniature alla stessa dimensione. Utilizzando una delle impostazioni con ritaglio, tutte le miniature verranno ridimensionate alla stessa dimensione."}, "style_mobile": {"label": "Trasforma la galleria in uno slider su dispositivi mobili"}, "slider_height": {"label": "Altezza slider su dispositivo mobile", "options__1": {"label": "Media"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Completa"}}, "lightbox": {"label": "Abilita Lightbox", "info": "Mostra l'immagine più grande al clic"}}, "presets": {"name": "Galleria"}}, "heading": {"name": "Intestazione", "settings": {"title": {"label": "<PERSON><PERSON>"}}, "presets": {"name": "Intestazione"}}, "image": {"name": "<PERSON><PERSON><PERSON><PERSON>", "mobile_image": "Immagine per dispositivi mobili (opzionale)", "fullwidth": "Larghezza intera"}, "apps": {"name": "App", "settings": {"include_margins": {"label": "Rendi i margini della sezione uguali al tema"}}, "presets": {"name": "App"}}, "rich-text": {"name": "Rich text", "blocks": {"heading": {"name": "Intestazione", "settings": {"heading": {"label": "Intestazione"}, "heading_size": {"label": "Dimensione intestazione", "options__1": {"label": "Normale"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Extra large"}}}}, "icon": {"name": "Icona"}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}}}, "button": {"name": "Pulsante", "settings": {"button_label": {"label": "<PERSON><PERSON><PERSON><PERSON> pulsante"}, "button_link": {"label": "Link del pulsante"}, "button_size": {"label": "Dimensione del pulsante", "options__1": {"label": "Normale"}, "options__2": {"label": "Grande"}}}}}, "settings": {"text_alignment": {"label": "Allineamento testo", "options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_position": {"label": "Posizione immagine", "options__1": {"label": "A sinistra"}, "options__2": {"label": "A destra"}}, "image_height": {"label": "Altezza immagine", "options__1": {"label": "Normale"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Completa"}}}, "presets": {"name": "Rich text"}}, "shop-the-look": {"name": "Acquista il tuo stile", "settings": {"heading": {"label": "Intestazione"}, "subheading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "image": {"label": "Immagine di sfondo"}}, "blocks": {"product": {"name": "<PERSON><PERSON><PERSON>", "settings": {"select_product": {"label": "Seleziona prodotto"}}}}, "presets": {"name": "Acquista il tuo stile"}}, "testimonials": {"name": "<PERSON><PERSON><PERSON><PERSON>", "blocks": {"testimonial": {"name": "Testimonial", "settings": {"quote": {"label": "Citazione"}, "author_name": {"label": "Nome dell'autore"}, "author_title": {"label": "Titolo dell'autore"}, "author_avatar": {"label": "Avatar dell'autore"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "announcement-bar": {"name": "Barra degli annunci", "settings": {"bar_show": {"label": "Mostra la barra degli annunci"}, "bar_show_on_homepage": {"label": "Mostra solo sulla homepage"}, "bar_show_dismiss": {"label": "Mostra il pulsante Ignora"}, "bar_message": {"label": "<PERSON><PERSON><PERSON>"}, "bar_link": {"label": "Link"}, "bar_bgcolor": {"label": "Colore di sfondo"}, "bar_txtcolor": {"label": "Colore del testo"}}}, "text-columns-with-images": {"name": "Colonne di testo con immagini", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"title": {"label": "Intestazione"}, "text": {"label": "<PERSON><PERSON>"}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Colonne di testo con immagini"}}, "slider": {"slider_horizontal": {"name": "Presentazione: orizzontale"}, "slider_vertical": {"name": "Presentazione: verticale"}, "settings": {"desktop_height": {"label": "Altezza slider desktop", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Completa"}}, "mobile_height": {"label": "Altezza slider su dispositivo mobile"}, "text_style": {"header": "Stile testo"}, "mobile_design": {"header": "Design per dispositivi mobili", "label": "Trasforma lo slider da verticale in orizzontale sul cellulare"}}, "blocks": {"image": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "heading": {"label": "Intestazione"}, "subheading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "caption": {"label": "Didascalia"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON> pulsante"}, "link": {"label": "Link", "info": "A meno che non sia presente un'etichetta per il pulsante, il collegamento sarà associato al testo."}}}}}, "video-popup": {"name": "Video: popup", "settings": {"video": {"label": "URL del video"}, "image": {"label": "Immagine di sfondo"}}}, "video-background": {"name": "Video: sfondo", "settings": {"video": {"label": "URL del video", "info": "Percorso di un file .mp4"}, "image": {"label": "Immagine di riserva", "info": "Sui dispositivi mobili in cui la riproduzione automatica potrebbe essere disabilitata sarà utilizzata un'immagine di riserva."}, "size_alignment": {"content": "Dimensioni e allineamento"}, "video_height": {"label": "Altezza del video", "options__1": {"label": "<PERSON><PERSON> (16:9)", "group": "Senza ritagli"}, "options__2": {"label": "Grande", "group": "Ritagliate"}, "options__3": {"label": "Completa"}}}}, "main-password-header": {"name": "Intestazione password"}, "main-password-content": {"name": "Contenuto password"}, "main-password-footer": {"name": "<PERSON><PERSON> di pagina password", "settings": {"show_social": {"label": "Mostra icone social"}}}, "main-article": {"name": "Articolo del blog", "blocks": {"featured_image": {"name": "Immagine in primo piano", "settings": {"image_height": {"label": "Altezza dell'immagine in primo piano", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}}}}, "title": {"name": "<PERSON><PERSON>", "settings": {"blog_show_date": {"label": "Mostra data"}, "blog_show_author": {"label": "Mostra autore"}, "blog_show_comments": {"label": "Mostra numero di commenti"}}}, "content": {"name": "<PERSON><PERSON><PERSON>"}, "social_sharing": {"name": "Pulsanti di condivisione social"}, "blog_navigation": {"name": "Mostra i link agli articoli adiacenti"}}}, "main-blog": {"settings": {"header": {"content": "Cartolina del blog"}, "enable_tags": {"label": "Abilita il filtro per tag"}, "post_limit": {"label": "Numero di articoli per pagina"}}}, "blog-posts": {"name": "Articoli del blog", "blocks": {"title": {"name": "<PERSON><PERSON>"}, "info": {"name": "Informazioni", "settings": {"show_date": {"label": "Mostra data"}, "show_author": {"label": "Mostra autore"}}}, "summary": {"name": "<PERSON><PERSON><PERSON>"}, "link": {"name": "Link"}}, "settings": {"title": {"label": "Intestazione"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Articoli"}, "show_image": {"label": "<PERSON>ra immagine in primo piano"}, "show_view_all": {"label": "Mostra il collegamento alla pagina del blog"}, "layout": {"label": "Layout"}, "option_1": {"label": "Una colonna", "group": "Griglia"}, "option_2": {"label": "Due colonne"}, "option_3": {"label": "Fless<PERSON>le (2 - 5 colonne)", "group": "Slide<PERSON>"}}, "presets": {"name": "Articoli del blog"}}, "custom-colors": {"heading": {"label": "Intestazione"}, "text": {"label": "Colore del testo personalizzato"}, "overlay": {"label": "Colore della sovrimpressione"}, "background": {"label": "Personalizza il colore dello sfondo"}}, "custom-gutter": {"heading": {"content": "Spaziatura"}, "gutter_enabled": {"label": "Abilita la spaziatura interna del contenuto"}}, "newsletter": {"name": "Registrazione tramite e-mail", "blocks": {"heading": {"name": "Intestazione", "settings": {"heading": {"label": "Intestazione"}}}, "paragraph": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"label": "Descrizione"}}}, "email_form": {"name": "Modulo e-mail"}}, "presets": {"name": "Registrazione tramite e-mail"}}, "product-recommendations": {"name": "Consigli sui prodotti", "settings": {"heading": {"label": "Intestazione"}, "header__1": {"content": "Consigli sui prodotti", "info": "Le raccomandazioni dinamiche utilizzano le informazioni sugli ordini e sui prodotti per cambiare e migliorare nel tempo. [Ulteriori informazioni](https://help.shopify.com/en/themes/development/recommended-products)"}}}, "custom-liquid": {"name": "Liquido <PERSON>", "settings": {"custom_liquid": {"label": "Liquido <PERSON>"}}, "presets": {"name": "Liquido <PERSON>"}}, "collection-list": {"name": "Elenco delle collezioni", "presets": {"name": "Elenco delle collezioni"}}, "faq": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "Intestazione"}, "open_first": {"label": "Mantieni il primo selettore aperto per impostazione predefinita"}}, "blocks": {"text": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "<PERSON><PERSON>"}, "text": {"label": "<PERSON><PERSON>"}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "popup": {"name": "Popup", "settings": {"title": {"label": "Intestazione"}, "content": {"label": "<PERSON><PERSON><PERSON>"}, "show_newsletter": {"label": "Mostra il modulo di registrazione tramite e-mail"}, "functionality": {"content": "Funzionalità"}, "enable": {"label": "<PERSON><PERSON><PERSON> popup"}, "show_after": {"label": "Mostra il popup dopo", "info": "secondi"}, "frequency": {"label": "Frequenza del popup", "options__1": {"label": "Mostra ogni giorno"}, "options__2": {"label": "<PERSON><PERSON> ogni set<PERSON>"}, "options__3": {"label": "<PERSON>ra ogni mese"}}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "1.240 x 400 pixel .jpg consigliati. Appare solo sul desktop"}}}, "main-search": {"name": "Risultati della ricerca", "settings": {"products_per_page": {"label": "Risultati per pagina"}}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON>", "settings": {"products_per_page": {"label": "Prodotti per pagina"}, "enable_filtering": {"label": "Abilita filtro", "info": "[Personalizza filtri](/admin/menus)"}, "enable_sorting": {"label": "Abilita ordinamento"}, "image_filter_layout": {"label": "Layout del filtro immagine"}, "header__1": {"content": "Filtri e ordinamento"}}}, "main-collection-banner": {"name": "Banner della collezione", "settings": {"paragraph": {"content": "Per modificare le descrizioni o le immagini delle collezioni, [modifica le collezioni.](/admin/collections)"}, "show_collection_description": {"label": "Mostra descrizione collezione"}, "show_collection_image": {"label": "Mostra immagine collezione", "info": "Per ottenere i migliori risultati, utilizza un'immagine con proporzioni 16:9."}}}, "main-product": {"name": "Informazioni sul prodotto", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "Stile testo", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Lettere maiuscole"}}}}, "title": {"name": "<PERSON><PERSON>"}, "price": {"name": "Prezzo"}, "tax_info": {"name": "Informazioni fiscali"}, "sku_barcode": {"name": "SKU / codice a barre"}, "quantity_selector": {"name": "Selettore di quantità"}, "variant_picker": {"name": "Selettore di varianti", "settings": {"show_variant_labels": {"label": "Mostra etichette varianti"}, "hide_out_of_stock_variants": {"label": "Nascondi varianti esaurite"}, "low_inventory_notification": {"label": "Notifica della disponibilità di magazzino", "info": "Affinché questa funzionalità operi correttamente, per le varianti deve essere abilitato il monitoraggio della disponibilità di magazzino. [Ulteriori informazioni](https://help.shopify.com/en/manual/products/inventory/track_inventory)", "options__1": {"label": "Non mostrare le informazioni di disponibilità di magazzino"}, "options__2": {"label": "Mostra un avviso se la disponibilità di magazzino scende al di sotto di 5"}, "options__3": {"label": "Mostra sempre la disponibilità di magazzino"}}}}, "buy_buttons": {"name": "Pulsanti di acquisto", "settings": {"show_dynamic_checkout": {"label": "Mostra pulsanti di pagamento dinamico", "info": "Utilizzando i metodi di pagamento disponibili nel tuo negozio, i clienti visualizzano la loro opzione preferita, come PayPal o Apple Pay. [Ulteriori informazioni](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Mostra il modulo del destinatario per i buoni regalo", "info": "Quando sono attivi, i buoni regalo possono essere inviati al destinatario con un messaggio personale."}, "show_quantity_selector": {"label": "Mostra selettore quantità"}}}, "pickup_availability": {"name": "Disponibilità per il ritiro"}, "description": {"name": "Descrizione", "settings": {"product_description_truncated": {"label": "Tronca la descrizione", "info": "Tronca", "options__1": {"label": "Non troncare"}, "options__2": {"label": "Mostra un estratto di piccole dimensioni"}, "options__3": {"label": "Mostra un estratto di medie dimensioni"}, "options__4": {"label": "Mostra un estratto di grandi dimensioni"}}}}, "share": {"name": "Condi<PERSON><PERSON>", "settings": {"featured_image_info": {"content": "Se includi un link nei post sui social media, l'immagine in primo piano della pagina verrà mostrata come immagine di anteprima. [Ulteriori informazioni](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "All'immagine di anteprima sono associati un titolo e una descrizione del negozio. [Ulteriori informazioni](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "Scheda comprimibile", "settings": {"heading": {"info": "Includi un'intestazione che descriva il contenuto.", "label": "Intestazione"}, "content": {"label": "<PERSON>tenuto scheda"}, "page": {"label": "Contenuto scheda dalla pagina"}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON> scheda"}}}}, "settings": {"header": {"content": "Media", "info": "Ulteriori informazioni sui [tipi di media](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Abilita informazioni permanenti sui prodotti sugli schermi di grandi dimensioni"}, "enable_video_looping": {"label": "Abilita il loop del video"}, "enable_zoom": {"label": "Abilita lo zoom delle immagini"}, "gallery_gutter": {"label": "Aggiungi spazio tra i media"}, "gallery_slider_style": {"label": "Ridimensiona le immagini del dispositivo di scorrimento per adattarle alla finestra"}, "gallery_style": {"label": "Stile della galleria", "info": "L'impostazione predefinita è lo slider per i dispositivi mobili", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Slide<PERSON>"}}, "gallery_pagination": {"label": "Impaginazione galleria", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Anteprime"}}}}, "words_210": {"text_columns": {"name": "Text columns", "block": {"name": "Text"}}}, "images": {"label_1": "Immagine 1", "label_2": "Immagine 2", "label_3": "Immagine 3"}}, "color-schemes": {"name_plural": "Color schemes", "name_singular": "Color scheme", "default": "Default color scheme", "default_info": "Used to set colors for account pages, 404 pages, and other pages or sections without color scheme settings.", "drawers": "Modals/drawers", "other": "Other colors"}, "local-250": {"labels": {"show_filters_as": "<PERSON>ra filtri come", "expand_filters_by_default": "Espandi filtri per impostazione predefinita", "stick_filters_sidebar_to_top": "Fissa la barra laterale dei filtri in alto"}, "options": {"sidebar": "Barra laterale", "list": "Elenco"}}, "local-230": {"background_gradient": "Gradiente di sfondo", "variant_default": {"label": "Selezionare la prima variante disponibile per valore predefinito", "info": "Se deselezionata, l'utente dovrà selezionare una variante disponibile prima dell'acquisto."}, "slider_info": "Il link sarà applicato al bottone, o al titolo (se il bottone non c'è), o all'intera slide (se sia il titolo sia il bottone sono vuoti).", "buy_button_labels": {"label": "Etichette pulsanti di acquisto", "option_1": "Acquista ora", "option_2": "Scegli opzioni"}, "hide_on_mobile": "Nascondi su dispositivi mobili"}, "local-223": {"heading_text_color": "Colore del testo dell'intestazione", "slider_navigation_color": "Colore degli elementi di navigazione"}, "late_edits": {"badge": {"custom_badge": {"text_color": "Colore del testo"}, "sold_out": {"name": "Badge esaurito", "text_color": "Colore del testo 'esaurito'", "sale_text": "Colore di testo 'sconto'"}}, "rich-text": {"image_position": {"no_image": {"group": "<PERSON><PERSON><PERSON> immagine", "label": "Non mostrare l'immagine"}}}}}