{"variant_metafields": {"name": "Metacampo de variante", "label": "Chave do metacampo de variante", "info": "Este tema pode mostrar um metacampo de variante na página do produto. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/884-using-variant-metafields-in-product-pages)."}, "new_color_swatches": "O tipo de seletor de variante \"blocos\" oferece suporte para amostras de cores criadas com metacampos de categoria. [<PERSON><PERSON> mais](https://help.shopify.com/en/manual/custom-data/metafields/category-metafields/using-category-metafields).", "video_controls": "Mostrar controles de vídeo", "sticky_cart_actions": "Ativar ações de carrinho adesivo", "currency_codes": {"header": "Formato de moeda", "label": "Mostrar códigos de moeda", "info": "Exemplo: $1.00 USD."}, "a11": {"label": "Acessibilidade", "show_sidebars_scrollbar": "Mostrar barra de rolagem das barras laterais", "disable_all_image_animations": "<PERSON><PERSON><PERSON> todas as animações de imagens"}, "divider": {"label": "Divisor", "divider_design": "Design do divisor", "divider_style_solid": "<PERSON><PERSON><PERSON><PERSON>", "divider_style_dotted": "Pontil<PERSON><PERSON>", "divider_style_dashed": "<PERSON><PERSON><PERSON>", "divider_style_double": "<PERSON><PERSON><PERSON>", "divider_color": "Cor", "divider_image": "Imagem do divisor", "divider_image_info": "Uma imagem repetitiva horizontalmente. Substitui o estilo e a cor acima."}, "cart_actions": {"label": "Ações do carrinho de gaveta", "option_1": "<PERSON>rar bot<PERSON> \"ver carrinho\"", "option_2": "Mostrar botão \"terminar sessão\"", "option_3": "Mostrar ambos"}, "sticky_atc": {"label": "Adicionar ao carrinho fixo", "enable_sticky_atc": "Habilitar adicionar ao carrinho fixo", "sticky_atc_style_wide": "Wide", "sticky_atc_style_floating": "Floating"}, "performance": {"header": "SEO & desempenho", "name": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON>ré<PERSON><PERSON><PERSON><PERSON> links ao passar o mouse", "info": "Aumenta a velocidade de carregamento percebida das páginas."}, "recently_viewed": {"enable_recently_viewed_products": "Ativar produtos visualizados recentemente", "enable_recently_viewed_products_info": "<PERSON>uando ativado, o tema registrará os produtos visualizados, mas você precisa adicionar a seção na sua loja para mostrar esses produtos.", "recently_viewed_products": "Produtos visualizados recentemente", "recently_viewed_products_info": "Esta seção precisa ter a funcionalidade ativada nas Configurações de Tema. Ela só aparecerá depois que os usuários visitarem pelo menos uma página de produto.", "recently_viewed_products_limit": "Limite de produtos visualizados recentemente"}, "rating_apps_update": {"label": "App de avaliação", "info": "Aplicativos de terceiros podem exigir etapas adicionais para uma integração adequada."}, "local-220": {"preorder": "Mostrar etiqueta do botão \"pré-encomenda\"", "autorotate": {"heading": "Autorrotação", "info": "Rotacionar automaticamente através dos slides.", "enable": "Ativar autorrotação", "interval": "Intervalo", "pause_on_mouseover": "Pausar ao passar o mouse"}}, "custom-social-icons": {"header": "Link personalizado", "info": "Carregue um ícone personalizado para a sua rede social favorita", "icon": {"label": "Ícone", "info": "72 x 72px transparente .png"}, "link": {"label": "Link"}}, "local-march-update": {"labels": {"dynamic_content": {"name": "<PERSON><PERSON><PERSON><PERSON>", "hide_block": "Ocultar bloco se o conteúdo dinâmico não estiver presente", "hide_section": "Ocultar secção se o conteúdo dinâmico não estiver presente"}, "buttons": "<PERSON><PERSON><PERSON><PERSON>", "cards": "<PERSON><PERSON><PERSON><PERSON>", "heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buttons_custom": "Cores personalizadas dos botões", "center_heading": "Centrar o título", "section_design": "Desenho da secção", "bottom_margin": "Remover margem inferior", "text_spacing": "Espaçamento do texto", "inherit_card_design": "<PERSON><PERSON> propried<PERSON> de desenho de cartões", "align_button": "Alinhar o botão de compra na parte inferior do cartão", "custom_colors": "<PERSON>s personalizadas"}, "shadows": {"label": "Sombra", "label_plural": "Sombra", "offset_x": "Deslocamento horizontal", "offset_y": "Deslocamento vertical", "blur": "Desfoque", "hide": "Esconder sombra", "hide_button_shadows": "Ocultar sombras do botão"}, "blocks": {"countdown_timer": {"name": "Temporizador de contagem decrescente", "label": "Fonte dinâmica", "info": "Definir uma fonte de tempo dinâmica para o temporizador de contagem decrescente. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/642-product-pages#countdown-timer)"}, "progress_slider": {"name": "Gráfico de barras de progresso", "value": "Valor", "height": "Slider height", "width": "Slider width", "dynamic_content": {"info": "Use dynamic sources to define unique values by creating product metafields for the progress chart. [Learn more](https://shopify-support.krownthemes.com/article/804-product-page#Progress-chart-S8c6R)", "slider_label": "Value metafield", "dots_label": "Highlighted dots metafield"}}, "progress_dots": {"name": "Gráfico de pontos de progresso", "highlight": "Pontos em destaque", "total": "Pontos totais", "icon": "Dot icon", "size": "Dot size", "inactive_color": "Inactive color", "active_color": "Highlighted color"}, "store_selector": {"default": "Padrão para primeira loja"}, "rating": {"app": "Revisões de app", "default_option": "Por defeito"}, "space": {"name": "Espaço vazio"}, "badges": {"name": "Distintivos do produto"}, "nutritional": {"name": "Informações nutricionais", "label_left": "Etiqueta da coluna esquerda", "label_right": "Etiqueta da coluna direita", "information": {"label": "Informação", "info": "Etiqueta e valor separados com vírgula. Utilizar quebras de linha para adicionar uma nova linha. Utilizar um hífen para traçar linhas. [Sai<PERSON> mais](https://shopify-support.krownthemes.com/article/804-product-page#Nutritional-information-ypnLL)"}, "extra_information": "Informação extra"}}, "sections": {"progress_sliders": {"name": "Gráficos de barras de progresso", "block_name": "Barr<PERSON>"}, "header": {"settings": {"promotion": {"header_1": "Promoção 1", "header_2": "Promoção 2", "header_3": "Esquema do menu", "show": "Mostrar promoção", "image": "Imagem da promoção", "text": "Texto da promoção", "width": "<PERSON><PERSON><PERSON> da coluna"}}}}}, "settings_schema": {"exit_intent": {"exit_intent_popup": "Popup de intenção de saída", "exit_intent_popup_info": "Esta seção só funciona em desktop"}, "colors": {"name": "Cores", "settings": {"header__1": {"content": "Barra lateral"}, "header__2": {"content": "Corpo"}, "header__3": {"content": "Rodapé"}, "bg_color": {"label": "Fundo"}, "txt_color": {"label": "Texto"}, "link_color": {"label": "Hiperligações"}}}, "typography": {"name": "Tipografia", "settings": {"headings_font": {"label": "Cabeçalhos"}, "base_size": {"label": "Taman<PERSON>"}, "large_size": {"label": "Cabeçalhos grandes", "info": "Afeta os títulos grandes do \"slider\", o texto formatado e as imagens com secções de texto."}, "body_font": {"label": "Corpo"}, "nav_size": {"label": "Navegação primária"}}}, "product-grid": {"name": "<PERSON>rel<PERSON> de produtos", "settings": {"aspect_ratio": {"label": "Proporção do elemento multimédia"}, "show_secondary_image": {"label": "Mostrar o segundo elemento multimédia do produto ao passar o rato"}, "enhance_featured_products": {"label": "En<PERSON><PERSON>zar os produtos em destaque", "info": "[Saber mais](https://shopify-support.krownthemes.com/article/832-product-card)"}, "show_discount": {"label": "Mostrar desconto como...", "options__1": {"label": "Texto"}, "options__2": {"label": "Percentagem"}}, "caption_placement": {"label": "Posicionamento da legenda", "options__1": {"label": "Sobreposição", "group": "Visível ao passar com o cursor do rato"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>", "group": "Sempre visível"}}, "grid_color_bg": {"label": "Fundo da legenda sobreposta"}, "grid_color_text": {"label": "Cor do texto da legenda sobreposta"}, "header__1": {"content": "Avaliação do produto", "info": "Para exibir avaliações, adicione um aplicativo de avaliação de produto. [Saber mais](https://apps.shopify.com/search?q=product%20reviews)"}, "header__2": {"content": "Avaliaçãoes de produtos"}, "show_reviews": {"label": "Mostrar avaliação"}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Imagem do favicon", "info": "É necessário um ficheiro .png com 48 x 48 píxeis"}}}, "cart-page": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "<PERSON><PERSON><PERSON> <PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Gaveta"}}, "cart_notes": {"label": "Ativar as notas do carrinho"}, "cart_buttons": {"label": "Mostrar botões de finalização de compra adicionais"}}}, "embellishments": {"name": "<PERSON><PERSON><PERSON>", "settings": {"show_preloader": {"label": "Pré-carregador de imagens", "info": "Mostra um pequeno pré-carregador circular enquanto as imagens da sua loja ainda estão a carregar."}, "show_breadcrumb": {"label": "<PERSON>rar trilho", "info": "O trilho de navegação ajuda os utilizadores a navegarem pela loja e só é exibido nas páginas das coleções, dos produtos e da pesquisa."}, "show_go_top": {"label": "Mostrar botão 'ir para o topo'"}}}, "search": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"predictive_search": {"label": "Ativar a pesquisa preditiva"}, "show_vendor": {"label": "<PERSON><PERSON> vendedor"}, "show_price": {"label": "Mostrar preço"}, "include_articles": {"label": "Incluir artigos nos resultados de pesquisa"}, "include_pages": {"label": "Incluir páginas nos resultados de pesquisa"}}}, "social": {"name": "Redes Sociais"}, "follow_on_shop": {"content": "Se<PERSON>ir no <PERSON>", "info": "Para permitir que os clientes sigam a loja no app do Shop a partir de sua vitrine, o Shop Pay precisa estar habilitado. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "label": "Ativar Seguir no Shop"}, "labels": {"hide_block_if_no_content_info": "Ocultar bloco se o conteúdo não estiver definido", "popup_page_info": "Substitui o conteúdo do texto se uma página for selecionada", "page": "<PERSON><PERSON><PERSON><PERSON>", "popup": "Popup", "open_popup": "<PERSON><PERSON><PERSON> popup"}}, "sections": {"main-404": {"name": "404 Principal"}, "main-gift-card": {"name": "<PERSON><PERSON><PERSON>"}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON> principal"}, "refactor_words": {"seo": {"name": "SEO", "label": "Etiqueta do cabeçalho", "info": "Especifique o nível do cabeçalho para ajudar os motores de pesquisa na indexação da estrutura da sua página.", "microdata": {"label": "Desativar esquema de microdados", "info": "Está opção removerá da página a marcação schema.org. Desative apenas se estiver a usar uma aplicação de terceiros para SEO!"}}, "late_edits": {"image_with_hotspots": {"image_on_mobile": "Imagem para dispositivos móveis", "position_on_mobile": "Posição em dispositivos móveis", "hotspot": {"mobile_info": "Apenas se estiver definida uma imagem para dispositivos móveis"}}, "product-card": {"thumbnails": {"border": "Cor do limite dos elementos multimédia"}}, "labels": {"optional": "Opcional"}, "before-after": {"layout": {"invert": "Inverter esquema nos dispositivos móveis"}}}, "labels": {"footer_group": "Grupo de rodapés", "header_group": "Grupo de cabeçalho", "overlay_group": "Grupo de sobreposição", "embellishments": "<PERSON><PERSON><PERSON>", "show_button": "Show button", "optional": "Optional"}, "text": {"icons_info": "Para visualizar e descarregar mais <PERSON>, por favor visite [este link](https://resources.krownthemes.com/icons/)."}, "borders": {"top_border": "Borda superior", "bottom_border": "Borda inferior", "show_border": "Show border"}, "colors": {"heading_background": "Contexto do título", "shadow": "Mostrar sombra"}, "social": {"phone": "Telefone", "discord": "Discord"}, "settings": {"image_with_hotspots": {"label": "Imagem com hotspots", "hotspot": {"label": "Hotspot", "label_desktop_offset": "Hotspot de escritório", "label_mobile_offset": "Hotspot móvel", "offset_horizontal": "Deslocamento horizontal", "offset_vertical": "Deslocamento vertical", "tooltip": {"label": "Dica de ferramenta", "position": {"label": "Posição", "option_1": "Parte superior", "option_2": "Parte inferior", "option_3": "E<PERSON>rda", "option_4": "<PERSON><PERSON><PERSON>"}, "show_tooltip": "Show tooltip"}, "mobile_info": "Only if a mobile image is set"}, "image_on_mobile": "Mobile image", "position_on_mobile": "Position on mobile"}, "scrolling_images": {"label": "Imagens rolantes", "image_size": "<PERSON><PERSON><PERSON>", "columns": "Columns"}, "video": {"label": "Vídeo", "info": "Formato MP4 necess<PERSON>rio, sem som"}, "variants_functionality": {"label": "Lidar com variantes indisponíveis", "option_1": "Ocultar", "option_2": "Desativar", "option_3": "Mostrar"}, "auto_height": {"label": "Altura automática", "info_slider": "A verificação desta opção irá sobrescrever as definições de altura acima e tornar a altura do diapositivo em resposta à imagem dentro de cada diapositivo."}}, "header": {"promotion_block": {"image_link": "Ligação imagem promocional"}, "sticky": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"sticky\"", "option_1": "Desativado", "option_2": "Sempre", "option_3": "Apenas no scroll para cima"}}, "inventory": {"name": "Nível do estoque", "settings": {"show_progress_bar": "Mostrar barra de progresso", "low_inventory_threshold": "Limiar de estoque baixo", "show_block": {"always": "<PERSON><PERSON><PERSON> sempre", "low": "<PERSON>ó exibir quando o estoque for inferior ao limiar predefinido"}}}, "breadcrumb": {"name": "Navegação em categoria", "info": "A navegação em migalhas não aparece na página inicial"}, "announcement-bar": {"visibility": {"label": "Visibilidade", "option_1": "<PERSON><PERSON> as p<PERSON><PERSON><PERSON>", "option_2": "Página inicial apenas", "option_3": "<PERSON><PERSON> as páginas excepto a página inicial", "option_4": "Apenas páginas de produtos", "option_5": "Apenas a página do carrinho"}, "color": {"border": "<PERSON><PERSON> <PERSON> b<PERSON>a"}}, "promotional_banner": {"name": "Banner promocional", "enable": "Mostrar faixa"}, "cookies_banner": {"name": "Cookies", "enable": "Mostrar aviso de cookies"}, "before_after": {"name": "Comparação de imagens", "layout": {"label": "Esquema", "option__1": "Horizontal", "option__2": "Vertical"}, "style": {"label": "<PERSON><PERSON><PERSON>", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "Escuro"}, "image": {"label__1": "Imagem", "label__2": "Imagem para dispositivos móveis", "label__3": "<PERSON><PERSON><PERSON><PERSON>"}}, "cart_upsell": {"name": "Recomendação de produto individual", "product": "Produ<PERSON>", "info": "As recomendações dinâmicas são baseadas nos artigos do seu carrinho. Eles mudam e melhoram com o tempo. [<PERSON><PERSON> mais](https://help.shopify.com/en/themes/development/recommended-products)"}, "gift_wrapping": {"name": "Embalagem para presente", "info": "O embrulho de presentes precisa de ser criado como um produto. [<PERSON><PERSON> mais](https://shopify-support.krownthemes.com/article/696-cart-features#gift-wrap)", "settings": {"label": "Texto", "button": "Rótulo do botão"}}, "custom_code": {"name": "HTML personalizado / Líquido"}, "rating": {"name": "Revisões de app", "default": "Por defeito"}, "product-page": {"size_guide": {"label": "<PERSON><PERSON><PERSON>", "page": "Página do guia de ta<PERSON>", "options": {"label": "<PERSON><PERSON><PERSON> as opções", "option_1": "Pop-up", "option_2": "<PERSON><PERSON> janela", "option_3": "Nova janela"}}, "gallery_resize": {"label": "Proporção das imagens", "info": "Os vídeos e outros tipos de meios de comunicação serão exibidos na sua relação de aspecto original.", "option_1": "Colocar imagens dentro do contentor"}, "gallery_padding": {"label": "Espaçamento interior da galeria"}, "gallery_background": {"label": "Fundo da galeria", "info": "Apenas visível se as imagens estiverem configuradas para caber dentro do contentor."}}, "product-card": {"name": "Cartão de produto", "labels": {"thumbnail": "Miniatura do produto", "caption": "Legenda do produto", "color_swatches": "Amostras de cores"}, "thumbnails": {"fit": "Colocar meios dentro do contentor", "padding": {"label": "Espaçamento interior do contentor", "info": "Só funciona se os meios de comunicação estiverem preparados para caber no interior do contentor."}, "background": {"label": "Fundo do contentor", "info": "Apenas visível se os meios de comunicação estiverem configurados para caber dentro do contentor."}, "border": "<PERSON><PERSON> <PERSON> b<PERSON>a", "color_swatches": "Mostrar amostras de cor no cartão de produto", "color_swatches_on_hover": "Mostrar amostras de cor no cartão de produto (em hover)"}, "color_swatches_label": {"label": "Etiquetas das amostras de cores", "info": "Escreva os títulos para as diversas variantes (separados por vírgulas) que pretende transformar em amostras de cores."}, "badges": {"name": "Distintivos do produto", "show_badges": "Mostrar distintivos", "settings": {"colors": {"text": "Cor de texto dos emblemas", "sold_out": "Cor de fundo de 'Esgotado'", "sale": "Cor de fundo de 'Desconto'"}}, "badge_sale": {"name": "Emblema de desconto", "amount_saved": "<PERSON>or poupado"}, "regular_badges": {"info": "Saiba mais sobre os crachás dos produtos [aqui](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)."}, "sold_out": {"name": "Crach<PERSON> de 'Esgotado'", "text_color": "Cor do texto de 'Esgotado'", "sale_text": "Cor do texto de 'Desconto'"}, "custom_badges": {"name": "Emblemas personalizados de produtos", "info": "Este tema utiliza crachás de produtos personalizados que pode definir aqui. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/564-product-pages#product-badges)", "split_info": "Este tema utiliza crachás de produtos personalizados que pode definir aqui. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)", "name__1": "Emblema personalizado 1", "name__2": "Emblema personalizado 2", "name__3": "Emblema personalizado 3", "text": "Texto", "tags": "Identificador", "color": "Cor do fundo", "text_color": "Cor do texto", "border_color": "<PERSON><PERSON> <PERSON> b<PERSON>a"}}}, "seo_late": {"breadcrumbs": {"label": "Improve product breadcrumb navigation", "info": "Adds collection names to the breadcrumb navigation, by duplicating product url's. [Learn more](https://shopify-support.krownthemes.com/article/697-seo#breadcrumbs)"}}}, "local-extra-words": {"settings_schema": {"colors": {"headings": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"borders": "Fronteira", "hide_border": "<PERSON><PERSON><PERSON><PERSON>", "accent": "Realse", "overlay": "Overlay"}}, "typography": {"buttons": {"label": "Peso da fonte dos botões", "option__1": "Normal", "option__2": "<PERSON><PERSON>"}, "menus": {"header": "Menus", "size": "Tamanho base", "weight": "<PERSON><PERSON><PERSON> da fonte", "weight_bold": "Negrito"}}, "borders": {"name": "Fronteiras", "main": {"name": "Secções", "info": "Esta configuração controla o estilo das fronteiras em todas as secções do tema."}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "forms": {"name": "Formulários"}, "settings": {"width": "<PERSON><PERSON><PERSON>", "radius": "Raio"}}, "layout": {"name": "Layout", "sections": {"vertical_space": "Espaço vertical entre secções", "remove_vertical_space": "Remover a margem do topo", "remove_bottom_margin": "Remover a margem inferior"}, "grid": {"name": "Grelha", "info": "Afeta áreas com um layout multi-colunas.", "horizontal_space": "Espaço horizontal", "vertical_space": "Espaço vertical"}}, "cart": {"shipping": {"name": "<PERSON><PERSON>", "show": {"label": "Mostrar montante mínimo para envio gratuito", "info": "Para configurar as taxas de envio, vá às suas [definições de envio](/administrador/definições/envio)."}, "amount": {"label": "Montante mínimo para envio gratuito", "info": "Escreva um número, sem letras ou caracteres especiais."}, "info": "This feature only works with a single currency and shipping method. If you're using multiple shipping methods or currencies, it might show false values to the customer."}, "settings": {"media": {"header": "Media aspect ratio", "label": "Aspect ratio", "info_cart": "Changes the aspect ratio for small product thumbnails used in the cart, search popup and some other areas that require small product thumbnails."}}}, "aspect_ratio": {"landscape": {"label": "<PERSON><PERSON> curto (3:2)"}}, "maps": {"name": "Mapas"}, "search": {"predictive_search": {"name": "Pesquisa preditiva", "info": "A pesquisa preditiva suporta sugestões de produtos, coleções, páginas e artigos."}}, "product-card": {"name": "Cartão de produto", "title-size": {"name": "Tamanho do título", "options__1": "Pequeno", "options__2": "Grande"}, "local-pickup": {"name": "Disponibilidade local", "info": "Este tema mostra a disponibilidade local dos produtos. [Sai<PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/805-collection-page)"}, "badges": {"name": "Emblemas de produtos predefinidos", "settings": {"colors": {"text": "Cor de texto dos emblemas", "sold_out": "Cor de fundo de \"Esgotado\"", "sale": "Cor de fundo de \"Desconto\""}}, "badge_sale": {"name": "Emblema de desconto"}, "custom_badges": {"name": "Emblemas personalizados de produtos", "info": "Este tema usa símbolos de produtos personalizados que pode definir aqui. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/795-product-card#Product-badges-_KIdK)", "name__1": "Emblema personalizado 1", "name__2": "Emblema personalizado 2", "name__3": "Emblema personalizado 3", "text": "Texto", "tags": "Identificador", "color": "Cor de fundo", "text_color": "Text color"}}, "icons_list": "Lista de ícones dinâmica", "products-list": {"name": "Products list"}}, "embellishments": {"settings": {"show_gotop": {"label": "Show go to top button"}}}}, "sections": {"video": {"name": "Vídeo", "settings": {"video": {"label": "URL de Vídeo"}, "image": {"label": "Imagem de fundo"}}}, "contact-form": {"settings": {"form-fields": {"name": "Campos do formulário", "show-phone": "Mostrar telefone", "show-subject": "Mostrar assunto"}}, "blocks": {"contact-info": {"name": "Informações de contacto", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}}, "text-columns-with-icons": {"blocks": {"icon": {"name": "Ícone personalizado", "info": "256 x 256px"}, "select_icon": {"info": "Para visualizar e descarregar mais <PERSON>, visite [este link](https://resources.krownthemes.com/icons/)"}, "icon_color": {"label": "Ícone", "info": "Apenas funciona em ícones incluídos"}}}, "content-toggles": {"name": "Seletores de conteúdo", "block": "<PERSON><PERSON><PERSON><PERSON>"}, "text-columns-with-images": {"blocks": {"image": {"info": "640 x 640px"}}}, "announcement-bar": {"settings": {"social": {"header": "Ícones sociais", "info": "Para configurar perfis sociais, vá a Definições de Tema > Social.", "label": "Mostrar ícones sociais"}}, "blocks": {"content": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"text": "Texto", "link": "Link", "target": "Abrir link numa nova janela"}}}}, "newsletter": {"show_icon": "<PERSON><PERSON>"}, "cookies": {"name": "Popup de cookies", "cookies_info": "Este site usa cookies para garantir a melhor experiência do usuário. [<PERSON><PERSON> mais](https://shopify-support.krownthemes.com/article/790-cookies-popup)."}, "popups": {"name": "Popups", "blocks": {"model": {"model-1": "Cookies", "model-2": "Newsletter", "model-3": "Personalizado"}, "settings": {"size": {"label": "Tamanho do popup", "option_1": "Pequeno", "option_2": "Grande"}}}}, "age-verification": {"name": "Verificação de idade", "settings": {"button-text": "Texto do botão"}}, "stores-map": {"name": "Mapa de lojas", "settings": {"map": {"title": "Mapa"}, "gallery": {"title": "Galeria da loja"}}}, "store-selector": {"name": "<PERSON><PERSON><PERSON> de <PERSON>", "settings": {"map": {"label": "Ativar mapa dinâmico", "info": "Certifique-se de que tem a Chave API do Google Maps devidamente configurada nas Definições de Tema"}, "zoom": {"label": "Zoom do mapa", "info": "Escolha um valor adequado para ver todas as lojas desejadas de uma vez."}}, "blocks": {"map": {"name": "Localização do mapa", "settings": {"address": {"label": "<PERSON><PERSON>", "info": "<PERSON><PERSON> mais"}, "image": {"label": "Imagem", "info": "Carregue uma imagem estática se não quiser usar um mapa dinâmico."}, "style": {"label": "Estilo do mapa", "option__1": "Padrão", "option__2": "Prata", "option__3": "Retro", "option__4": "Escuro", "option__5": "Noite", "option__6": "<PERSON><PERSON><PERSON>"}, "pin": {"label": "Pino personalizado do mapa", "info": "240 x 240px transparente .png"}}}, "store": {"name": "<PERSON><PERSON>", "settings": {"name": {"label": "Nome", "info": "O nome da loja tem de coincidir com o nome da sua loja definido nas suas [definições de localização](/administrador/definições/localizações)"}, "pickup_price": {"label": "Preço de levantamento"}, "pickup_time": {"label": "Hora de levantamento"}, "address": {"label": "<PERSON><PERSON><PERSON> da loja"}, "image": {"label": "<PERSON><PERSON> da loja"}, "closing_times": {"label": "Horários de encerramento (opcional)", "info": "Adicione 7 linhas, uma para cada dia da semana, a começar com domingo."}, "timezone": {"label": "<PERSON><PERSON>", "info": "Usado para mostrar adequadamente os horários de encerramento."}, "map": {"name": "Pino do mapa", "info": "Se o mapa estiver ativado, tem de definir um pino personalizado para esta morada  [Saiba como obter as coordenadas da sua morada](https://support.google.com/maps/answer/18539?hl=en)"}, "map_latitude": {"label": "Latitude", "info": "Coordenada latitude para o marcador. Exemplo: 46.7834818"}, "map_longitude": {"label": "Longitude", "info": "Coordenada longitude para o marcador. Exemplo: 23.5464733"}, "get_directions_button": {"label": "Mostrar botão \"obter direções\"", "info": "Abre um mapa maior num novo separador de browser."}, "map_pin": {"label": "<PERSON><PERSON> personaliza<PERSON>", "info": "90 x 90px transparente .png"}}}}}, "header": {"settings": {"layout": {"label": "Layout do cabeçalho", "info": "Afeta a posição dos blocos personalizados e ações predefinidas", "option__1": "Blocos personalizados em cima, ações predefinidas em no fundo", "option__2": "Ações predefinidas em cima, blocos personalizados no fundo"}, "sticky": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"sticky\"", "info": "Mostra a navegação quando o utilizador se descola para cima"}}, "blocks": {"info": {"name": "Informações", "style": {"label": "<PERSON><PERSON><PERSON>", "option__1": "Informações de texto", "option__2": "Botão", "info": "Nos botões apenas a legenda é visível, assim como a etiqueta do botão."}, "custom-icon": {"label": "Ícone personalizado", "info": "Carregue uma imagem 76 x 76px .png"}, "icon": {"label": "Ícone"}, "link_type": {"label": "Abrir link", "option__1": "<PERSON>tro de uma janela modal", "option__2": "Na mesma página", "option__3": "Numa nova página", "info": "As janelas modais apenas funcional com links de páginas internas"}}, "store-selector": {"name": "<PERSON><PERSON><PERSON>", "content": "O seletor de lojas pode ser configurado na secção Seletor de Lojas. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/799-store-selector)", "info": "Este tema permite-lhe conectar as localizações das suas lojas reais a um seletor de lojas interativo. [<PERSON><PERSON> mais](https://shopify-support.krownthemes.com/article/799-store-selector)"}, "mega-menu": {"name": "Mega menu", "settings": {"menu_handle": {"label": "Nome do menu", "info": "Este tema usa mega menus. [<PERSON><PERSON> mais](https://shopify-support.krownthemes.com/article/798-header-section#mega-menus)"}}}}}, "marquee": {"name": "Texto de deslocação", "settings": {"scroll_direction": "Direção de deslocação", "scroll_speed": "Velocidade de deslocação", "scroll_speed_info": "Quanto maior o valor, mais lentamente se desloca", "pause_on_mouseover": "Parar ao passar o cursor", "scroll_item": "Item de <PERSON>loc<PERSON>", "scroll_item_text": "Texto de deslocação"}}, "image-section": {"name": "Imagem", "settings": {"image_size": {"label": "Largura de <PERSON>", "info": "Em mobile a imagem terá largura total."}}}, "media-with-text-overlay": {"name": "Média com sobreposição de texto", "blocks": {"media": "Média", "image": {"name": "Imagem"}, "link": {"info": "O título se transformará em um link, a menos que haja um rótulo para o botão"}, "video": {"name": "Vídeo", "label": "Vídeo", "info": "A imagem acima mostra se este vídeo não pode ser reproduzido."}}, "settings": {"height": "Altura do cartão", "option__1": "Pequeno", "option__2": "Grande", "option__3": "Extra grande", "option__4": "Ecrã total", "option__5": "Normal"}}, "blog-posts": {"settings": {"emphasize": {"label": "Destacar o primeiro artigo", "info": "Apenas em desktop"}}, "blocks": {"summary": {"name": "Excerto", "settings": {"excerpt_limit": "Número de palavras", "excerpt_limit_info": "Aplica-se se o artigo não tiver um excerto manual adicionado no administrador."}}}}, "testimonials": {"name": "<PERSON><PERSON>un<PERSON>", "blocks": {"name": "Imagem"}}, "slideshow": {"name": "Slideshow", "block": {"name": "Imagem"}, "settings": {"caption_size": "<PERSON><PERSON><PERSON>a"}}, "rich-text": {"settings": {"image_position": {"label": "Posição da imagem", "option__1": "À esquerda", "option__2": "Acima do texto", "option__3": "À direita"}, "fullwidth": {"label": "Largura total", "info": "Estender o fundo desta secção para preencher o ecrã."}, "height": {"label": "Altura do cartão", "info": "Altura mínima do cartão em desktop. Em mobile, a altura irá depender do conteúdo."}, "crop": {"label": "<PERSON><PERSON><PERSON> á<PERSON> da <PERSON>m", "info": "A imagem será cortada para preencher a altura total do cartão em desktop. Em mobile, a imagem será sempre mostrada na totalidade."}, "remove_margin": {"label": "Remover margem superior da secção"}}}, "main-header": {"settings": {"mobile": {"name": "Navegação em mobile", "info": "Estes apenas afetam a visibilidade dentro do navigation drawer em mobile.", "header_actions": "Mostrar seletor de lojas e blocos de informação", "header_info_blocks": {"header": "Blocos de informação do cabeçalho", "label_1": "Mostrar seletor de lojas e blocos de informação no cabeçalho em dispositivos móveis", "label_2": "Posicionar blocos de informação no topo da primeira secção na página inicial", "label_2_info": "Integra bem quando a primeira secção é um slideshow de largura total"}}, "promotion_block": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "size": "Tamanho do título"}, "subtitle": {"label": "Subtítulo", "size": "Tamanho do subtítulo"}, "button": {"label": "Etiqueta do botão", "size": "Tamanho do botão", "link": "Link do botão", "style": "Estilo do botão"}}, "header_actions": {"header": "Blocos de informação no cabeçalho em mobile", "show_in_drawer": "Mostrar dentro do navigation drawer"}}}, "main-cart": {"blocks": {"shipping-calculator": {"name": "Calculador de envio"}, "related-products": {"info": "The product recommendations are based on products that are commonly purchased together or products in related collections. If there are no recommendations, the block will not appear."}}}, "main-search": {"settings": {"blogs": {"name": "Resultados de artigos"}, "products": {"name": "Resultados de produtos", "info": "O conteúdo do cartão de produto tem de ser configurado usando os blocos de secção."}}}, "main-product": {"name": "Página de produto", "settings": {"gallery_pagination": "Paginação do slider de galeria", "show_border": "Mostrar fronteira em torno da galeria", "gallery_border": "Gallery border", "gallery_border_color": "Gallery border color"}, "blocks": {"pickup_availability": {"name": "Disponibilidade de levantamento", "info": "Este tema mostra a disponibilidade de levantamento com base na loja selecionada. Saiba mais", "settings": {"style": "<PERSON><PERSON><PERSON>", "option__1": "Compacto", "option__2": "Extenso"}}, "buy_buttons": {"settings": {"show_price": "Mostrar preço"}}, "related": {"name": "Produtos relacionados", "settings": {"products": "<PERSON><PERSON><PERSON>"}}, "tax_info": {"name": "Informações fiscais"}, "icons": {"name": "Lista de ícones", "info": "Para visualizar e descarregar ícones incluídos no tema, visite [este link](https://resources.krownthemes.com/icons/).", "help": "Este tema permite-lhe adicionar ícones de produto personalizados através de conteúdo dinâmico. [<PERSON><PERSON> mais](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "headers": {"icon_1": "Ícone 1", "icon_2": "Ícone 2", "icon_3": "Ícone 3", "icon_4": "Ícone 4", "icon_5": "Ícone 5", "icon_6": "Ícone 6"}, "settings": {"icon": "Ícone", "icon_info": "96 x 96px", "label": "Etiqueta"}}}}, "main-blog": {"name": "Blog principal"}, "main-article": {"name": "Artigo", "settings": {"show_tags": "Mostrar identificadores", "enhance_product_links": {"label": "Melhorar links de produtos", "info": "Todos os links para produtos irão abrir a janela modal de compra rápida do produto."}}}, "main-article-comments": {"name": "Comentários de artigos", "info": "Para ativar os comentários vá a [definições de blog](/administrador/blogs)"}, "main-article-navigation": {"name": "Navegação de artigos", "settings": {"header": {"content": "Publicações de blog", "info": "Deixe em branco se quiser carregar a publicação de blog anterior ou seguinte predefinida."}, "posts": {"next": "Próxima publicação", "previous": "Publicação anterior"}}}, "main-page": {"settings": {"center": {"label": "Centrar o conteúdo em <PERSON>"}}}, "main-footer": {"blocks": {"payment": {"name": "Ícones de pagamento", "info": "Os ícones mostrados são determinados pelas [definições de pagamento](/administrador/definições/pagamentos) da sua loja e a região e moeda do cliente.", "custom_icons": "Custom icons", "custom_icons_info": "Upload 114 x 72px .png images", "only_show_custom_icons": "Only show the custom icons"}, "text": {"name": "Text & social icons"}}}, "customers": {"reset-password": {"name": "<PERSON><PERSON> se<PERSON>a"}, "order": {"name": "<PERSON><PERSON><PERSON><PERSON> de encomenda"}, "register": {"name": "Página de registo"}, "activate-account": {"name": "Página de ativação de conta"}, "login": {"name": "Página de autenticação", "shop_login_button": {"enable": "Ativar a opção Fazer login com o Shop"}}, "account": {"name": "Página de conta"}, "addresses": {"name": "<PERSON><PERSON><PERSON>"}}, "headings": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subheading": "Sub-cabeçalho", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Subtítulo", "caption": "Legent<PERSON>", "text_content": "Conte<PERSON><PERSON> de texto", "custom_colors": "<PERSON>s personalizadas", "text_style": "Estilo de texto"}, "columns": {"name": "Layout de desktop", "info": "O layout adapta-se a dispositivos móveis.", "option__0": "1 coluna", "option__1": "2 colunas", "option__2": "3 colunas", "option__3": "4 colunas", "option__4": "5 colunas", "option__5": "6 colunas", "name_mobile": "Mobile layout"}, "promotion-cards": {"name": "Cartões de promoção", "blocks": {"name": "Cartão"}}, "faq": {"headings": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"form": {"header": "Formulário de contacto", "show": "<PERSON><PERSON>", "title": "Título do formulário"}}}, "product-quick-view": {"name": "Vista rápida", "info": "Este modelo controla como é criada a vista rápida de produto. Apenas esta secção irá aparecer na janela modal."}, "product-card": {"blocks": {"price": "Preço", "title": "<PERSON><PERSON><PERSON><PERSON>", "vendor": "Fornecedor", "text": {"name": "Texto dinâmico", "info": "Use uma fonte dinâmica para destacar um atributo único ao criar um meta campo de produto. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/805-collection-page)", "settings": {"text": {"label": "Etiqueta de meta campo"}, "size": {"label": "Tamanho do texto", "option__1": "Pequeno", "option__2": "Normal", "option__3": "Grande"}, "color": {"label": "Cor do texto", "option__1": "<PERSON><PERSON><PERSON><PERSON>", "option__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "transform": {"label": "Transformação de texto (maiúsculas)"}}}, "icons": {"info": "Use fontes dinâmicas para destacar atributos únicos ao criar meta campos de produtos para a lista de ícones. [<PERSON><PERSON> ma<PERSON>](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "settings": {"icon": "Meta campo de produto", "label": "Meta campo de etiqueta"}}, "quick_buy": "Compra r<PERSON>a", "rating": "Classificação"}}, "buttons": {"style": {"label": "Estilo do botão", "option__1": "Contorno", "option__2": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "complementary_products": {"name": "<PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Para selecionar produtos complementares, adicione o app Search & Discovery. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}}}, "split-extra-words": {"settings_schema": {"colors": {"headings": {"header_sidebars": "Cabeçalho e barras laterais", "main": "Corpo", "footer": "Rodapé", "custom_colors": "<PERSON>s personalizadas"}, "settings": {"background": "Fundo", "text": "Texto", "links": "Hiperligações ativas", "borders": "Mostrar limites"}}, "typography": {"headings": {"headings": "Cabeçalhos", "body": "Corpo", "logo_menus": "Logótipo e menus", "buttons": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"font_family": "Família de tipos de letra", "base_size": "Taman<PERSON>", "line_height": "<PERSON>ura da linha", "hr": {"label": "<PERSON><PERSON> linhas <PERSON>s", "info": "Exibe uma pequena linha horizontal ou separador visual em alguns títulos"}, "border_radius": "Raio do limite"}}, "embellishments": {"preloader": {"label": "Pré-carregador de elementos multimédia", "info": "Exibe um pequeno pré-carregador circular enquanto os elementos multimédia da sua loja estão a ser carregados."}, "breadcrumb": {"label": "<PERSON>rar trilho", "info": "O trilho de navegação orienta os utilizadores quando estes navegam pela loja e apenas é exibido nas páginas das coleções, dos produtos, da pesquisa e da conta."}}, "cart": {"page": "Itens do carrinho", "show_recommendations": "Mostrar recomendações do carrinho"}, "headings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Subtítulo"}, "product-grid": {"animation_style": {"label": "Exibição de legendas (computador)", "options__1": "Visível", "options__2": "Sobreposição", "info": "Nos dispositivos móveis, a legenda estará sempre visível para uma melhor experiência de utilização"}, "overlay_colors": {"background": "Fundo da legenda sobreposta", "text": "Sobrepor o texto da legenda"}, "aspect_ratio": {"label": "Proporção dos elementos multimédia do produto", "options__1": "Recortada", "options__2": "Natural"}, "show_secondary_image": {"info": "Só em computadores"}, "quick_buy": {"name": "Compra r<PERSON>a", "info": "Adiciona um botão para \"adicionar ao carrinho\" de forma instantânea. Se o produto tiver variantes, esta opção exibirá um pop-up de \"compra rápida\".", "label": "Ativar a compra rápida"}, "rating": {"label": "Exibição da avaliação (computador)", "options__1": "<PERSON><PERSON>", "options__2": "Mostrar ao passar com o rato", "options__3": "Sempre visível", "show_on_mobile": "Mostrar nos dispositivos móveis"}}}, "sections": {"header": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"logo_height": "Altura máxima da imagem do logótipo", "menu": "<PERSON><PERSON>", "menu_style": {"label": "Estilo de menu do ambiente de trabalho", "options__1": "Clássico", "options__2": "Gaveta"}, "collections_menu": {"header": "Menu de coleções", "info": "Isto tem um design negrito, especialmente no estilo de menu clássico, onde se transforma num megamenu com a possibilidade de adicionar imagens e uma promoção.", "settings": {"show_images": {"label": "Mostrar imagens da coleção", "info": "Aplica-se apenas se os itens principais pertencerem a uma coleção."}}}, "promotional_block": {"name": "Bloco de promoção", "settings": {"show": {"label": "Mostrar bloco de promoção", "info": "Em estilo minimalista, aparece na parte inferior da gaveta do menu. Em estilo clássico, aparece no menu de coleções, se presente."}, "title": {"label": "Título da promoção"}, "content": {"label": "Conteúdo da promoção"}, "button": {"label": "Etiqueta do botão da promoção"}, "link": {"label": "Ligação do botão da promoção"}, "txt_color": {"label": "Cor de texto da promoção"}, "bg_color": {"label": "Cor de fundo da promoção"}, "image": {"label": "Imagem da promoção"}}}, "announcement_bar": {"content": {"info": "No máximo, 50 caracteres"}}}}, "footer": {"blocks": {"menu": {"name": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}}}, "main-product": {"name": "Página do produto", "settings": {"header": {"label": "Cabeçalho do produto", "info": "Em dispositivos móveis, o cabeçalho do produto aparecerá sempre no topo, por cima da galeria do produto.", "show_tax_info": "Mostrar informações fiscais", "show_reviews": "Mostrar avaliação do produto", "show_sku": "Mostrar SKU", "show_barcode": "Mostrar CÓDIGO DE BARRAS", "show_vendor": "<PERSON><PERSON> vendedor", "show_badge": "Mostrar o emblema do produto"}, "variants": {"label": "Tipo de seletor de variantes", "options__1": "Blocos", "options__2": "Lista pendente"}, "gallery_aspect": {"label": "Di<PERSON><PERSON>e as imagens do cursor deslizante para caberem na janela de visualização", "info": "Nos dispositivos móveis, as imagens caberão sempre na janela de visualização do dispositivo."}, "color_swatches": {"label": "Mostrar coleções de cores (apenas para o estilo bloco)", "info": "Este tema pode apresentar imagens personalizadas para coleções de cores. [Learn more](https://shopify-support.krownthemes.com/article/554-using-color-swatches)"}}}, "countdown-banner": {"name": "Faixa de contagem decrescente", "settings": {"header": "Relógio de contagem decrescente", "show_countdown": "Mostrar relógio de contagem decrescente", "countdown_year": "Ano de fim", "countdown_month": "<PERSON><PERSON><PERSON> <PERSON>", "countdown_day": "Dia de fim", "countdown_hour": "Hora de fim", "countdown_timezone": "<PERSON><PERSON>", "size": "Altura da faixa"}}, "map": {"settings": {"map": {"api": {"label": "Chave da <PERSON> do Google Maps", "info": "Tem de registar um [Google Maps API Key](http://shopify-support.krownthemes.com/article/385-generate-an-api-key-for-google-maps) to display the map."}}}}}}, "main-cart-footer": {"name": "Subtotal do carrinho", "blocks": {"subtotal_button": {"name": "Subtotal e finalização da compra"}}}, "main-cart-items": {"name": "Artigos de <PERSON>ho"}, "main-list-collections": {"name": "Página da lista de coleções", "blocks": {"collection": {"name": "Coleção", "settings": {"collection": {"label": "Coleção"}, "image": {"label": "Imagem", "info": "Se quiser adicionar uma imagem personalizada que identifique a coleção."}}}}, "settings": {"header": {"content": "Coleções"}, "layout": {"label": "Esquema", "options__1": {"label": "<PERSON>a coluna"}, "options__2": {"label": "<PERSON><PERSON> colu<PERSON>"}}, "paragraph": {"content": "<PERSON><PERSON> as suas coleções são listadas por predefinição. Para personalizar sua lista, escolha \"Selecionadas\" e adicione as coleções pretendidas."}, "display_type": {"label": "<PERSON><PERSON><PERSON><PERSON> as coleções a mostrar", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Selecionadas"}}, "sort": {"label": "Ordenar coleções por:", "info": "A ordenação só se aplica quando seleciona \"Todas\"", "options__1": {"label": "Alfabeticamente, A-Z"}, "options__2": {"label": "Alfabeticamente, Z-A"}, "options__3": {"label": "<PERSON>, da mais recente para a mais antiga"}, "options__4": {"label": "<PERSON>, da mais antiga para a mais recente"}, "options__5": {"label": "Contage<PERSON> de produtos, da mais alta para a mais baixa"}, "options__6": {"label": "Contage<PERSON> de produtos, da mais baixa para a mais alta"}}, "items_per_row": "Número de artigos por fila"}}, "sidebar": {"name": "Barra lateral", "settings": {"image": {"label": "Imagem do logótipo"}, "image_width": {"label": "Largura da imagem do logótipo"}, "primary_navigation": {"label": "Navegação primária"}, "secondary_navigation": {"label": "Navegação secundária"}, "search": {"content": "<PERSON><PERSON><PERSON><PERSON>", "label": "Mostrar pesquisa"}}}, "text-columns-with-icons": {"name": "Colunas de texto com ícones", "settings": {"content": {"label": "Mostrar apenas nas páginas selecionadas:"}, "show_on_homepage": {"label": "Página inicial"}, "show_on_product": {"label": "Páginas de produtos"}, "show_on_collection": {"label": "Páginas de coleções"}, "show_on_blog": {"label": "Páginas do blogue e dos artigos"}, "show_on_regular": {"label": "<PERSON><PERSON><PERSON><PERSON> normais"}, "icons": {"label": "Ícones", "info": "Para visualizar todos os ícones incluídos no tema, visite [esta hiperligação](https://krownthemes.com/krown-icons/)"}}, "blocks": {"icon": {"name": "Texto com ícone", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Texto"}, "icon": {"label": "Selecione um ícone"}}}}}, "footer": {"name": "Rodapé", "settings": {"show_payment_icons": {"label": "Mostrar ícones de pagamento"}, "language_selector": {"content": "Se<PERSON>or de idioma", "info": "Para adicionar um idioma, vá até às suas [definições de idioma.](/admin/settings/languages)"}, "language_selector_show": {"label": "Mostrar seletor de idioma"}, "country_selector": {"content": "Se<PERSON>or de país/região", "info": "Adicione um país/uma região em [configurações de pagamento.](/admin/settings/payments)"}, "country_selector_show": {"label": "Mostrar seletor de país/região"}}, "blocks": {"text": {"name": "Texto", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text_size": {"label": "Tamanho do texto", "options__1": {"label": "Normal"}, "options__2": {"label": "Grande"}}}}, "menus": {"name": "Menus", "settings": {"title_1": {"label": "Cabeçalho do primeiro menu"}, "title_2": {"label": "Cabeçalho do segundo menu"}, "menu_1": {"label": "Primeiro menu", "info": "Este menu não exibirá itens de lista pendente"}, "menu_2": {"label": "Segundo menu"}}}, "newsletter": {"name": "Registo de email"}, "social": {"name": "Hiperligações das redes sociais"}, "image": {"name": "Imagem", "settings": {"image": {"label": "Selecionar imagem"}}}}}, "contact-form": {"name": "Formulário de <PERSON>o", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"field": {"name": "Campo de formulário", "settings": {"type": {"label": "Tipo", "options__1": {"label": "Linha ú<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}}, "required_field": {"label": "Obrigatório"}, "labels": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Certifique-se de que todos os seus campos têm rótulos únic<PERSON>!"}}}, "email": {"name": "Nome e email"}, "button": {"name": "Botão \"Enviar\"", "settings": {"label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Formulário de contacto"}}, "image-with-text": {"name": "Imagens com texto", "blocks": {"image": {"name": "Imagem com texto", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "body": {"label": "Texto"}, "button_label": {"label": "Rótulo do botão"}, "url": {"label": "Hiperligação", "info": "Caso não exista um rótulo para o botão, todo o bloco será transformado numa hiperligação"}, "image": {"label": "Imagem de fundo"}}}}, "settings": {"image_height": {"label": "<PERSON><PERSON> da imagem", "options__1": {"label": "Pequena"}, "options__2": {"label": "Média"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Completa"}}, "text_width": {"label": "Largura do contentor de texto", "options__1": {"label": "Média"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Completa"}}, "text_size": {"label": "Tamanho do cabeçalho", "options__1": {"label": "Normal"}, "options__2": {"label": "Grande"}, "options__3": {"label": "<PERSON>ito grande"}}, "text_alignment": {"label": "Alinhamento do texto", "options__1": {"label": "Parte superior esquerda"}, "options__2": {"label": "Parte superior, ao meio"}, "options__3": {"label": "Parte superior direita"}, "options__4": {"label": "Parte central esquerda"}, "options__5": {"label": "Parte central, ao meio"}, "options__6": {"label": "Parte central direita"}, "options__7": {"label": "Parte inferior esquerda"}, "options__8": {"label": "Parte inferior, ao meio"}, "options__9": {"label": "Parte inferior direita"}}, "options__5": {"label": "Extra grande"}}, "presets": {"name": "Imagens com texto"}}, "featured-product": {"name": "Produto em destaque", "settings": {"product": {"label": "Selecionar o produto"}}, "blocks": {"product_link": {"name": "Hiperligação do produto"}}}, "featured-collection": {"name": "Coleção em destaque", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "Mostrar hiperligação para a página da coleção"}, "layout": {"label": "Esquema", "options__1": {"label": "\"Slider\""}, "options__2": {"label": "Grelha"}}, "products_number": {"label": "Número máximo de produtos exibidos"}, "collection": {"label": "Coleção"}}, "presets": {"name": "Destacar co<PERSON>ção"}}, "gallery": {"name": "Galeria", "blocks": {"image": {"name": "Imagem", "settings": {"image": {"label": "Imagem"}, "caption": {"label": "<PERSON>a"}, "featured": {"label": "Aumentar a imagem na grelha"}}}}, "settings": {"aspect_ratio": {"label": "Proporção das imagens", "options__1": {"label": "Baixa (4:3)", "group": "Recortada"}, "options__2": {"label": "Quadrada (1:1)"}, "options__3": {"label": "<PERSON><PERSON> (5:6)"}, "options__4": {"label": "<PERSON><PERSON> (2:3)"}, "options__5": {"label": "Natural", "group": "<PERSON><PERSON> recortada"}, "info": "Ao usar a proporção natural, certifique-se de que redimensiona as suas miniaturas com o mesmo tamanho para harmonizar e simplificar o design da grelha. Ao usar uma das definições de recorte das imagens, todas as miniaturas serão redimensionadas com o mesmo tamanho."}, "style_mobile": {"label": "Nos dispositivos móveis, transformar a galeria num \"slider\""}, "slider_height": {"label": "Altura do \"slider\" nos dispositivos móveis", "options__1": {"label": "Média"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Completa"}}, "lightbox": {"label": "Ativar \"lightbox\"", "info": "Mostra uma imagem maior ao clicar"}}, "presets": {"name": "Galeria"}}, "heading": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "image": {"name": "Imagem", "mobile_image": "Imagem mobile (opcional)", "fullwidth": "Largura total"}, "apps": {"name": "Aplicações", "settings": {"include_margins": {"label": "<PERSON><PERSON> as margens das secções iguais às margens do tema"}}, "presets": {"name": "Aplicações"}}, "rich-text": {"name": "Texto formatado", "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "heading_size": {"label": "Tamanho do cabeçalho", "options__1": {"label": "Normal"}, "options__2": {"label": "Grande"}, "options__3": {"label": "<PERSON>ito grande"}}}}, "icon": {"name": "Ícone"}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto"}}}, "button": {"name": "Botão", "settings": {"button_label": {"label": "Rótulo do botão"}, "button_link": {"label": "Hiperligação do botão"}, "button_size": {"label": "Tamanho do botão", "options__1": {"label": "Normal"}, "options__2": {"label": "Grande"}}}}}, "settings": {"text_alignment": {"label": "Alinhamento do texto", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "image": {"label": "Imagem"}, "image_position": {"label": "Posição da imagem", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_height": {"label": "<PERSON><PERSON> da imagem", "options__1": {"label": "Normal"}, "options__2": {"label": "Grande"}, "options__3": {"label": "Completa"}}}, "presets": {"name": "Texto formatado"}}, "shop-the-look": {"name": "Comprar o visual", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "subheading": {"label": "Subcabeçalho"}, "image": {"label": "Imagem de fundo"}}, "blocks": {"product": {"name": "Produ<PERSON>", "settings": {"select_product": {"label": "Selecionar o produto"}}}}, "presets": {"name": "Comprar o visual"}}, "testimonials": {"name": "<PERSON><PERSON>un<PERSON>", "blocks": {"testimonial": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"quote": {"label": "Citação"}, "author_name": {"label": "Nome do autor"}, "author_title": {"label": "Título do autor"}, "author_avatar": {"label": "Avatar do autor"}}}}, "presets": {"name": "<PERSON><PERSON>un<PERSON>"}}, "announcement-bar": {"name": "Barra de anúncio", "settings": {"bar_show": {"label": "Mostrar bar<PERSON> de <PERSON>"}, "bar_show_on_homepage": {"label": "Mostrar apenas na página inicial"}, "bar_show_dismiss": {"label": "Mostrar botão \"Ignorar\""}, "bar_message": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "bar_link": {"label": "Hiperligação"}, "bar_bgcolor": {"label": "Cor do fundo"}, "bar_txtcolor": {"label": "Cor do texto"}}}, "text-columns-with-images": {"name": "Colunas de texto com imagens", "blocks": {"text": {"name": "Texto", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Texto"}, "image": {"label": "Imagem"}}}}, "presets": {"name": "Colunas de texto com imagens"}}, "slider": {"slider_horizontal": {"name": "Apresentação de diapositivos: horizontal"}, "slider_vertical": {"name": "Apresentação de diapositivos: vertical"}, "settings": {"desktop_height": {"label": "Altura do \"slider\" nos computadores de secretária", "options__1": {"label": "Pequena"}, "options__2": {"label": "Média"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Completa"}}, "mobile_height": {"label": "Altura do \"slider\" nos dispositivos móveis"}, "text_style": {"header": "Estilo do texto"}, "mobile_design": {"header": "Design para dispositivos móveis", "label": "Transformar o \"slider\" vertical em horizontal nos dispositivos móveis"}}, "blocks": {"image": {"name": "Imagem", "settings": {"image": {"label": "Imagem"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "subheading": {"label": "Subcabeçalho"}, "caption": {"label": "<PERSON>a"}, "button_label": {"label": "Rótulo do botão"}, "link": {"label": "Hiperligação", "info": "Caso não exista um rótulo para o botão, a hiperligação será ativada no texto."}}}}}, "video-popup": {"name": "Ví<PERSON><PERSON>: pop-up", "settings": {"video": {"label": "URL do vídeo"}, "image": {"label": "Imagem de fundo"}}}, "video-background": {"name": "Vídeo: segundo plano", "settings": {"video": {"label": "URL do vídeo", "info": "<PERSON><PERSON><PERSON> para um ficheiro .mp4"}, "image": {"label": "Imagem de recurso", "info": "Será usada uma imagem de recurso nos dispositivos móveis onde a reprodução automática possa estar desativada."}, "size_alignment": {"content": "Tamanho e alinhamento"}, "video_height": {"label": "Altura do vídeo", "options__1": {"label": "Natural (16:9)", "group": "<PERSON><PERSON> recortada"}, "options__2": {"label": "Grande", "group": "Recortada"}, "options__3": {"label": "Completa"}}}}, "main-password-header": {"name": "Cabeçalho da palavra-passe"}, "main-password-content": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "<PERSON><PERSON><PERSON> da palavra-passe", "settings": {"show_social": {"label": "Mostrar ícones das redes sociais"}}}, "main-article": {"name": "Postagem no blogue", "blocks": {"featured_image": {"name": "Imagem em destaque", "settings": {"image_height": {"label": "Altura da imagem em destaque", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Média"}, "options__3": {"label": "Grande"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "Mostrar data"}, "blog_show_author": {"label": "Mostrar autor"}, "blog_show_comments": {"label": "Mostrar número de comentários"}}}, "content": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "social_sharing": {"name": "Botões de partilha nas redes sociais"}, "blog_navigation": {"name": "Mostrar hiperligações para postagens adjacentes"}}}, "main-blog": {"settings": {"header": {"content": "Cartão de postagem no blogue"}, "enable_tags": {"label": "Ativar filtragem por etiquetas"}, "post_limit": {"label": "Número de postagens por página"}}}, "blog-posts": {"name": "Postagens no blogue", "blocks": {"title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "info": {"name": "Informações", "settings": {"show_date": {"label": "Mostrar Data"}, "show_author": {"label": "Mostrar autor"}}}, "summary": {"name": "Excerto"}, "link": {"name": "Hiperligação"}}, "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "blog": {"label": "Blogue"}, "post_limit": {"label": "Postagens"}, "show_image": {"label": "Mostrar imagem em destaque"}, "show_view_all": {"label": "Mostrar hiperligação para a página do blogue"}, "layout": {"label": "Esquema"}, "option_1": {"label": "<PERSON>a coluna", "group": "Grelha"}, "option_2": {"label": "<PERSON><PERSON> colu<PERSON>"}, "option_3": {"label": "Flexível (2 a 5 colunas)", "group": "\"Slider\""}}, "presets": {"name": "Postagens no blogue"}}, "custom-colors": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Cor de texto personalizada"}, "overlay": {"label": "Cor de sobreposição"}, "background": {"label": "Cor de fundo personalizada"}}, "custom-gutter": {"heading": {"content": "Medianiz"}, "gutter_enabled": {"label": "Ativar o espaçamento do conteúdo interno"}}, "newsletter": {"name": "Registo de email", "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "Subcabeçalho", "settings": {"paragraph": {"label": "Descrição"}}}, "email_form": {"name": "Formulário de email"}}, "presets": {"name": "Registo de email"}}, "product-recommendations": {"name": "Recomendações de produtos", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"content": "Recomendações de produtos", "info": "As recomendações dinâmicas usam as informações dos pedidos e dos produtos para se ajustarem e melhorarem ao longo do tempo. [Saber mais](https://help.shopify.com/en/themes/development/recommended-products)"}}}, "custom-liquid": {"name": "Código Liquid Personalizado", "settings": {"custom_liquid": {"label": "Código Liquid Personalizado"}}, "presets": {"name": "Código Liquid Personalizado"}}, "collection-list": {"name": "Lista de coleções", "presets": {"name": "Lista de coleções"}}, "faq": {"name": "Perguntas Frequentes", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "open_first": {"label": "Ter o primeiro comutador aberto por predefinição"}}, "blocks": {"text": {"name": "Perguntas Frequentes", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Texto"}, "image": {"label": "Imagem"}}}}, "presets": {"name": "Perguntas Frequentes"}}, "popup": {"name": "Pop-up", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_newsletter": {"label": "Mostrar formulário de registo de email"}, "functionality": {"content": "Funcionalidade"}, "enable": {"label": "Ativar pop-up"}, "show_after": {"label": "Mostrar pop-up após", "info": "segundos"}, "frequency": {"label": "Frequência do pop-up", "options__1": {"label": "Mostrar diariamente"}, "options__2": {"label": "<PERSON><PERSON> se<PERSON>"}, "options__3": {"label": "Mostrar mensalmente"}}, "image": {"label": "Imagem", "info": "É recomendado um ficheiro .jpg com 1240 x 400 píxeis. Só aparece nos computadores de secretária"}}}, "main-search": {"name": "Resultados da pesquisa", "settings": {"products_per_page": {"label": "Resultados por página"}}}, "main-collection-product-grid": {"name": "<PERSON>rel<PERSON> de produtos", "settings": {"products_per_page": {"label": "Produtos por página"}, "enable_filtering": {"label": "Ativar filtragem", "info": "[Personalizar filtros](/admin/menus)"}, "enable_sorting": {"label": "Ativar ordena<PERSON>"}, "image_filter_layout": {"label": "Layout do filtro de imagem"}, "header__1": {"content": "Filtragem e ordenação"}}}, "main-collection-banner": {"name": "Faixa da coleção", "settings": {"paragraph": {"content": "Para alterar as descrições das coleções ou as imagens das coleções, [edite as suas coleções.](/admin/collections)"}, "show_collection_description": {"label": "Mostrar descrição da coleção"}, "show_collection_image": {"label": "Mostrar imagem da coleção", "info": "Para obter os melhores resultados, use uma imagem com uma proporção de 16:9."}}}, "main-product": {"name": "Informações do produto", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto"}, "text_style": {"label": "Estilo do texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Mai<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "tax_info": {"name": "Informações fiscais"}, "sku_barcode": {"name": "SKU / código de barras"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"show_variant_labels": {"label": "Mostrar rótulos das variantes"}, "hide_out_of_stock_variants": {"label": "Ocultar variantes esgotadas"}, "low_inventory_notification": {"label": "Notificação de estoque", "info": "É necessário ativar o rastreio de estoque das variantes para poder usar esta funcionalidade. [<PERSON>ber mais](https://help.shopify.com/en/manual/products/inventory/track_inventory)", "options__1": {"label": "Não mostrar informações de estoque"}, "options__2": {"label": "Mostrar um aviso se o estoque for inferior a 5 unidades"}, "options__3": {"label": "Mostrar sempre o estoque"}}}}, "buy_buttons": {"name": "Botõ<PERSON> de compra", "settings": {"show_dynamic_checkout": {"label": "Mostrar botões de finalização de compra dinâmicos", "info": "Usando os métodos de pagamento disponíveis na sua loja, os clientes veem a sua opção preferencial, como o PayPal ou o Apple Pay. [Saber mais](https://help.shopify.com/en/manual/products/inventory/track_inventory)"}, "show_gift_card_recipient": {"label": "Mostrar formulário para destinatários de produtos \"cartão-presente\"", "info": "Quando ativos, os produtos \"cartão-presente\" podem ser enviados a um destinatário com uma mensagem pessoal."}, "show_quantity_selector": {"label": "Mostrar seletor de quantidade"}}}, "pickup_availability": {"name": "Disponibilidade para recolha"}, "description": {"name": "Descrição", "settings": {"product_description_truncated": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON> truncar"}, "options__2": {"label": "Mostrar um excerto pequeno"}, "options__3": {"label": "Mostrar um excerto médio"}, "options__4": {"label": "Mostrar um excerto grande"}}}}, "share": {"name": "Partilhar", "settings": {"featured_image_info": {"content": "Se incluir uma hiperligação nas postagens das redes sociais, a imagem em destaque da página será exibida como imagem de pré-visualização. [<PERSON><PERSON> mais](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Com a imagem de pré-visualização são incluídos um título e uma descrição da loja. [<PERSON><PERSON> mais](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "Separador mini<PERSON>", "settings": {"heading": {"info": "Inclua um cabeçalho que explique o conteúdo.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "Conteúdo do separador"}, "page": {"label": "Conteúdo do separador a partir da página"}, "image": {"label": "Imagem do separador"}}}}, "settings": {"header": {"content": "Elementos multimédia", "info": "Saber mais sobre os [tipos de elementos multimédia](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Ativar a afixação de informações do produto em ecrãs grandes"}, "enable_video_looping": {"label": "Ativar a repetição contínua do vídeo"}, "enable_zoom": {"label": "Ativar o zoom das imagens"}, "gallery_gutter": {"label": "Adicionar o espaçamento entre os elementos multimédia"}, "gallery_slider_style": {"label": "Dimensione as imagens do controle deslizante para caber na janela de visualização"}, "gallery_style": {"label": "<PERSON><PERSON><PERSON>", "info": "O \"slider\" é a predefinição para dispositivos móveis", "options__1": {"label": "Deslocamento"}, "options__2": {"label": "\"Slider\""}}, "gallery_pagination": {"label": "Paginação da galeria", "options__1": {"label": "Pontos"}, "options__2": {"label": "Miniaturas"}}}}, "words_210": {"text_columns": {"name": "Text columns", "block": {"name": "Text"}}}, "images": {"label_1": "Imagem 1", "label_2": "Imagem 2", "label_3": "Imagem 3"}}, "color-schemes": {"name_plural": "Color schemes", "name_singular": "Color scheme", "default": "Default color scheme", "default_info": "Used to set colors for account pages, 404 pages, and other pages or sections without color scheme settings.", "drawers": "Modals/drawers", "other": "Other colors"}, "local-250": {"labels": {"show_filters_as": "Mostrar filtros como", "expand_filters_by_default": "Expandir filtros por padrão", "stick_filters_sidebar_to_top": "Fixar barra lateral de filtros no topo"}, "options": {"sidebar": "Barra lateral", "list": "Lista"}}, "local-230": {"background_gradient": "Gradiente de fundo", "variant_default": {"label": "Selecione a primeira variante disponível por padrão", "info": "Se não for assinalado, o utilizador terá que selecionar uma variante disponível antes de poder comprar."}, "slider_info": "O link será aplicado ao botão, ou ao título (se não houver botão), ou ao slide inteiro (se tanto o título quanto o botão estiverem em branco).", "buy_button_labels": {"label": "Etiquetas do botão de compra", "option_1": "Compre agora", "option_2": "Escolha opções"}, "hide_on_mobile": "Ocultar em dispositivos móveis"}, "local-223": {"heading_text_color": "Cor do texto do cabeçalho", "slider_navigation_color": "Cor dos elementos de navegação"}, "late_edits": {"badge": {"custom_badge": {"text_color": "Cor do texto"}, "sold_out": {"name": "Crachá esgotado", "text_color": "Cor de texto 'esgotada'", "sale_text": "Cor de texto 'desconto'"}}, "rich-text": {"image_position": {"no_image": {"group": "<PERSON><PERSON><PERSON><PERSON> imagem", "label": "Não mostre imagem"}}}}}