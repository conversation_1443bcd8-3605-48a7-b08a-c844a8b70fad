{"variant_metafields": {"name": "Variantmetaveld", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sleutel", "info": "Dit thema kan een variantmetaveld weergeven op de productpagina. [Meer informatie](https://shopify-support.krownthemes.com/article/884-using-variant-metafields-in-product-pages)."}, "new_color_swatches": "Het type variantselector \"blokken\" biedt ondersteuning voor kleurstalen die zijn gemaakt met categorie-meta velden. [Meer informatie](https://help.shopify.com/en/manual/custom-data/metafields/category-metafields/using-category-metafields).", "video_controls": "Videobediening tonen", "sticky_cart_actions": "Plakkerige lade-winkelwagenacties inschakelen", "currency_codes": {"header": "Valutaformaat", "label": "Valutacodes tonen", "info": "Voorbeeld: $1.00 USD."}, "a11": {"label": "Toegankelijkheid", "show_sidebars_scrollbar": "Toon schuifbalken in zijbalken", "disable_all_image_animations": "<PERSON><PERSON><PERSON> alle afbeeldingsanimaties uit"}, "divider": {"label": "<PERSON><PERSON>", "divider_design": "<PERSON>ler ontwerp", "divider_style_solid": "Solide", "divider_style_dotted": "Gestippeld", "divider_style_dashed": "Gestreept", "divider_style_double": "<PERSON><PERSON>", "divider_color": "<PERSON><PERSON><PERSON>", "divider_image": "<PERSON><PERSON> afbeelding", "divider_image_info": "<PERSON>en horizontaal herhalend beeld. Vervan<PERSON> de stijl en kleur hierboven."}, "cart_actions": {"label": "Ladeacties voor de winkelwagen", "option_1": "Knop \"Winkelwagen bekijken\" weergeven", "option_2": "<PERSON><PERSON><PERSON> \"Checkout\" weergeven", "option_3": "<PERSON><PERSON> weergeven"}, "sticky_atc": {"label": "Sticky toevoegen aan winkelwagen", "enable_sticky_atc": "Sticky toevoegen aan winkelwagen inschakelen", "sticky_atc_style_wide": "Wide", "sticky_atc_style_floating": "Floating"}, "performance": {"header": "SEO & prestatie", "name": "Prestatie", "label": "<PERSON>s voorladen bij hover", "info": "Verhoogt de waargenomen laadsnelhe<PERSON> van p<PERSON>'s."}, "recently_viewed": {"enable_recently_viewed_products": "Recent bekeken producten inschakelen", "enable_recently_viewed_products_info": "<PERSON><PERSON> ing<PERSON>, zal het thema bekeken producten registreren, maar je moet de sectie in je winkel toevoegen om deze producten te tonen.", "recently_viewed_products": "Recent bekeken producten", "recently_viewed_products_info": "Deze sectie moet de functionaliteit ingeschakeld hebben in Thema Instellingen. Het zal alleen verschijnen nadat gebruikers ten minste één productpagina hebben bezocht.", "recently_viewed_products_limit": "Limiet voor recent bekeken producten"}, "rating_apps_update": {"label": "Beoordelingsapp", "info": "<PERSON><PERSON> van derden kunnen extra stappen vereisen voor een juiste integratie."}, "local-220": {"preorder": "Toon \"pre-order\" knoplabel", "autorotate": {"heading": "Auto-rotatie", "info": "Roteer automatisch door de dia's.", "enable": "Auto-rotatie inschakelen", "interval": "Interval", "pause_on_mouseover": "<PERSON><PERSON><PERSON> bij mui<PERSON>"}}, "custom-social-icons": {"header": "Aangepaste link", "info": "Upload een aangepast pictogram voor uw favoriete sociale netwerk", "icon": {"label": "Pictogram", "info": "72 x 72px transparant .png"}, "link": {"label": "Link"}}, "local-march-update": {"labels": {"dynamic_content": {"name": "Dynamische inhoud", "hide_block": "Blok verbergen als dynamische inhoud niet aanwezig is", "hide_section": "Sectie verbergen als dynamische inhoud niet aanwezig is"}, "buttons": "Knoppen", "cards": "Ka<PERSON><PERSON>", "heading": "<PERSON><PERSON><PERSON><PERSON>", "buttons_custom": "Knop aangepaste k<PERSON>uren", "center_heading": "Koptekst midden", "section_design": "Sectieontwerp", "bottom_margin": "Onderste marge verwijderen", "text_spacing": "Tekstafstand", "inherit_card_design": "Kaartontwerp-eigenschappen overnemen", "align_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> met de <PERSON><PERSON><PERSON><PERSON> de <PERSON>", "custom_colors": "Aangepast<PERSON> k<PERSON>uren"}, "shadows": {"label": "<PERSON><PERSON><PERSON><PERSON>", "label_plural": "<PERSON><PERSON><PERSON><PERSON>", "offset_x": "Horizontale offset", "offset_y": "Verticale offset", "blur": "Waas", "hide": "Sc<PERSON><PERSON>w verbergen", "hide_button_shadows": "Knopschaduwen verbergen"}, "blocks": {"countdown_timer": {"name": "Afteltimer", "label": "Dynamische bron", "info": "Stel een dynamische tijdbron in voor de afteltimer. [Meer informatie](https://shopify-support.krownthemes.com/article/642-product-pages#countdown-timer)"}, "progress_slider": {"name": "Voortgangstaafdiagram", "value": "<PERSON><PERSON><PERSON>", "height": "Slider height", "width": "Slider width", "dynamic_content": {"info": "Use dynamic sources to define unique values by creating product metafields for the progress chart. [Learn more](https://shopify-support.krownthemes.com/article/804-product-page#Progress-chart-S8c6R)", "slider_label": "Value metafield", "dots_label": "Highlighted dots metafield"}}, "progress_dots": {"name": "Voortgangstippendiagram", "highlight": "Gemarkeerde stippen", "total": "Totale stippen", "icon": "Dot icon", "size": "Dot size", "inactive_color": "Inactive color", "active_color": "Highlighted color"}, "store_selector": {"default": "Standaard naar eerste winkel"}, "rating": {"app": "Beoordelingsapp", "default_option": "Standaard"}, "space": {"name": "<PERSON><PERSON> rui<PERSON>"}, "badges": {"name": "Productbadges"}, "nutritional": {"name": "Voedingsinformatie", "label_first": "Etiket eerste kolom", "label_second": "Etiket tweede kolom", "label_third": "Etiket derde kolom", "information": {"label": "Informatie", "info": "Scheid label en waarde met een komma. Gebruik regeleinden om een ​​nieuwe rij toe te voegen. Gebruik een koppelteken om rijen te laten inspringen. [Meer informatie](https://shopify-support.krownthemes.com/article/804-product-page#Nutritional-information-ypnLL)"}, "extra_information": "Extra informatie"}}, "sections": {"progress_sliders": {"name": "Voortgangstaafdiagrammen", "block_name": "St<PERSON><PERSON>"}, "header": {"settings": {"promotion": {"header_1": "Promotie 1", "header_2": "Promotie 2", "header_3": "Menu-indeling", "show": "Promotie weergeven", "image": "Promotie-afbeelding", "text": "Promotietekst", "width": "Kolombreedte"}}}}}, "settings_schema": {"exit_intent": {"exit_intent_popup": "Popup voor uitgangsintentie", "exit_intent_popup_info": "Deze sectie werkt alleen op desktop"}, "colors": {"name": "<PERSON><PERSON><PERSON>", "settings": {"header__1": {"content": "Zijbalk"}, "header__2": {"content": "Hoofdtekst"}, "header__3": {"content": "Voetnoot"}, "bg_color": {"label": "Achtergrond"}, "txt_color": {"label": "Tekst"}, "link_color": {"label": "Links"}}}, "typography": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"headings_font": {"label": "<PERSON><PERSON><PERSON>"}, "base_size": {"label": "Basisgrootte"}, "large_size": {"label": "<PERSON><PERSON> koppen", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t grote titels van de schuifregelaar, rijke text en afbeeldingen met tekstsecties."}, "body_font": {"label": "Hoofdtekst"}, "nav_size": {"label": "Primaire navigatie"}}}, "product-grid": {"name": "Productraster", "settings": {"aspect_ratio": {"label": "Beeldverhouding media"}, "show_secondary_image": {"label": "Toon tweede productmedia wanneer de muis over het item zweeft"}, "enhance_featured_products": {"label": "Uitgelichte producten benadrukken", "info": "[Learn more](https://shopify-support.krownthemes.com/article/832-product-card)"}, "show_discount": {"label": "Toon korting als...", "options__1": {"label": "Tekst"}, "options__2": {"label": "Percentage"}}, "caption_placement": {"label": "Pla<PERSON><PERSON> van ondertiteling", "options__1": {"label": "Overlay", "group": "<PERSON><PERSON><PERSON>baar bij roll-over"}, "options__2": {"label": "<PERSON>der a<PERSON>beelding", "group": "<PERSON><PERSON><PERSON><PERSON>"}}, "grid_color_bg": {"label": "Overlay bijschrift achtergrond"}, "grid_color_text": {"label": "Overlay tekstkleur van bijschrift"}, "header__1": {"content": "Productbeoordeling", "info": "Voeg een app voor productbeoordeling toe om beoordelingen weer te geven. [Learn more](https://apps.shopify.com/search?q=product%20reviews)"}, "header__2": {"content": "Productrecensies"}, "show_reviews": {"label": "Beoordeling weergeven"}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Favicon-afbeelding", "info": "48 x 48px .png vereist"}}}, "cart-page": {"name": "Winkelwagen", "settings": {"cart_type": {"label": "Type winkelwagen", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Lade"}}, "cart_notes": {"label": "Winkelwagennotities inschakelen"}, "cart_buttons": {"label": "Toon extra betaalknoppen"}}}, "embellishments": {"name": "Versieringen", "settings": {"show_preloader": {"label": "Afbeelding voorlader", "info": "Toont een kleine ronde preloader terwijl afbeeldingen in je winkel nog steeds worden geladen."}, "show_breadcrumb": {"label": "Toon broodk<PERSON>imel", "info": "Breadcrumb-navigatie helpt gebruikers bij het navigeren door de winkel en wordt alleen weergegeven op collectie-, product- en zoekpagina's."}, "show_go_top": {"label": "Toon 'ga naar boven'-knop"}}}, "search": {"name": "<PERSON><PERSON>", "settings": {"predictive_search": {"label": "Voorspellend zoeken inschakelen"}, "show_vendor": {"label": "Leverancier weergeven"}, "show_price": {"label": "<PERSON>n prijs"}, "include_articles": {"label": "Artikelen opnemen in zoekresultaten"}, "include_pages": {"label": "<PERSON><PERSON><PERSON>'s opnemen in zoekresultaten"}}}, "social": {"name": "Sociaal"}, "follow_on_shop": {"content": "Volgen op Shop", "info": "Klanten kunnen de winkel alleen vanuit de webshop volgen in de Shop-app als Shop Pay is ingeschakeld. [Meer informatie](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "label": "'Volgen op Shop' inschakelen"}, "labels": {"hide_block_if_no_content_info": "Verberg blok als er geen inhoud is gedefinieerd", "popup_page_info": "<PERSON><PERSON><PERSON><PERSON> de <PERSON><PERSON> als een pagina is geselecteerd", "page": "<PERSON><PERSON><PERSON>", "popup": "Popup", "open_popup": "Popup openen"}}, "sections": {"main-404": {"name": "Hoofd 404"}, "main-gift-card": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-page": {"name": "Hoofdpagina"}, "refactor_words": {"seo": {"name": "SEO", "label": "Kop-tag", "info": "Specificeer kopniveau om zoekmachines te helpen de structuur van je pagina te indexeren.", "microdata": {"label": "Microdata van schema uitschakelen", "info": "Dit zal de opmaak van schema.org verwijderen van de pagina. Schakel dit alleen uit als je een externe app gebruikt voor SEO!"}}, "late_edits": {"image_with_hotspots": {"image_on_mobile": "Mo<PERSON>le afbeelding", "position_on_mobile": "Positie op mobiel", "hotspot": {"mobile_info": "Alleen als er een mobiele afbeelding is ingesteld"}}, "product-card": {"thumbnails": {"border": "Randkleur van media"}}, "labels": {"optional": "Optioneel"}, "before-after": {"layout": {"invert": "Lay-out omdraaien op mobiele apparaten"}}}, "labels": {"footer_group": "Voettekstgroep", "header_group": "Koptekstgroep", "overlay_group": "Overlaygroep", "embellishments": "Versieringen", "show_button": "Show button", "optional": "Optional"}, "text": {"icons_info": "Bezoek [deze link] om meer iconen te visualiseren en downloaden: (https://resources.krownthemes.com/icons/)."}, "borders": {"top_border": "<PERSON><PERSON><PERSON>", "bottom_border": "<PERSON><PERSON><PERSON>", "show_border": "Show border"}, "colors": {"heading_background": "Kopachtergrond", "shadow": "<PERSON><PERSON><PERSON><PERSON> tonen"}, "social": {"phone": "Telefoonnummer", "discord": "Discord"}, "settings": {"image_with_hotspots": {"label": "Afbeelding met hotspots", "hotspot": {"label": "Hotspot", "label_desktop_offset": "Destkophotspot", "label_mobile_offset": "Mobiele hotspot", "offset_horizontal": "Horizontale offset", "offset_vertical": "Verticale offset", "tooltip": {"label": "<PERSON><PERSON><PERSON>", "position": {"label": "<PERSON><PERSON><PERSON>", "option_1": "Bovenkant", "option_2": "Onderkant", "option_3": "Links", "option_4": "<PERSON><PERSON><PERSON>"}, "show_tooltip": "Show tooltip"}, "mobile_info": "Only if a mobile image is set"}, "image_on_mobile": "Mobile image", "position_on_mobile": "Position on mobile"}, "scrolling_images": {"label": "Scrollende afbeeldingen", "image_size": "Afbeeldingsgrootte", "columns": "Columns"}, "video": {"label": "Video", "info": "MP4-indeling vereist, geen geluid"}, "variants_functionality": {"label": "Behandel niet-beschikbare varianten", "option_1": "Verbergen", "option_2": "Uitschakelen", "option_3": "Weergeven"}, "auto_height": {"label": "Auto-hoogte", "info_slider": "Als u deze optie aanvinkt, worden de bovenstaande hoogte-instellingen overschreven en wordt de hoogte van de diavoorstelling aangepast aan de afbeelding in elke dia."}}, "header": {"promotion_block": {"image_link": "Promotie-afbeeldingslink"}, "sticky": {"label": "Kleverige kopstekst", "option_1": "Uitgeschakeld", "option_2": "Altijd", "option_3": "Alleen bij omhoog scrollen"}}, "inventory": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"show_progress_bar": "Toon voortgangsbalk", "low_inventory_threshold": "Lage voorraaddrempel", "show_block": {"always": "<PERSON><PERSON><PERSON><PERSON> we<PERSON>geven", "low": "<PERSON><PERSON> weergeven wanneer de voorraad onder de drempel daalt"}}}, "breadcrumb": {"name": "Breadcrumb", "info": "De breadcrumb-navigatie verschijnt niet op de startpagina"}, "announcement-bar": {"visibility": {"label": "<PERSON><PERSON><PERSON>ba<PERSON><PERSON><PERSON>", "option_1": "Alle pagina's", "option_2": "<PERSON><PERSON> startpagina", "option_3": "Alle pagina's behalve startpagina", "option_4": "Alleen productpagina's", "option_5": "<PERSON><PERSON>'s"}, "color": {"border": "<PERSON><PERSON><PERSON>"}}, "promotional_banner": {"name": "Promotie-banner", "enable": "Toon banner"}, "cookies_banner": {"name": "Cookies", "enable": "Cookiemelding tonen"}, "before_after": {"name": "Afbeeldingsvergelijking", "layout": {"label": "Lay-out", "option__1": "Horizontaal", "option__2": "Verticaal"}, "style": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "option__1": "Licht", "option__2": "<PERSON><PERSON>"}, "image": {"label__1": "Afbeelding", "label__2": "Mo<PERSON>le afbeelding", "label__3": "Label"}}, "cart_upsell": {"name": "Individuele productaanbeveling", "product": "Product", "info": "Dynamische aanbevelingen zijn gebaseerd op de artikelen in uw winkelwagen. Ze veranderen en verbeteren door de tijd heen. [Meer informatie](https://help.shopify.com/en/themes/development/recommended-products)"}, "gift_wrapping": {"name": "Cadeauverpakking", "info": "Cadeauverpakking dient als product ingesteld te worden. [Meer informatie](https://shopify-support.krownthemes.com/article/696-cart-features#gift-wrap)", "settings": {"label": "Tekst", "button": "Knoplabel"}}, "custom_code": {"name": "Aangepaste html/liquid"}, "rating": {"name": "Beoordelingsapp", "default": "Standaard"}, "product-page": {"size_guide": {"label": "Maattabel", "page": "Ma<PERSON><PERSON>l<PERSON>gin<PERSON>", "options": {"label": "Opties openen", "option_1": "Pop-up", "option_2": "<PERSON><PERSON><PERSON> venster", "option_3": "<PERSON><PERSON><PERSON>"}}, "gallery_resize": {"label": "Beeldverhouding voor afbeeldingen", "info": "Video's en andere soorten media worden weergegeven in hun oorspronkelijke beeldverhouding.", "option_1": "Afbeeldingen passend maken voor container"}, "gallery_padding": {"label": "Binnenafstand van galerij"}, "gallery_background": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Alleen z<PERSON> als de afbeeldingen zo zijn ingesteld dat ze in de container passen."}}, "product-card": {"name": "<PERSON><PERSON><PERSON>", "labels": {"thumbnail": "Productminiatuur", "caption": "Productbijschrift", "color_swatches": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "thumbnails": {"fit": "Media passend maken voor container", "padding": {"label": "Binnenafstand van container", "info": "Werkt alleen als de media is ingesteld om in de container te passen."}, "background": {"label": "Achtergrond van container", "info": "Alleen z<PERSON>ar als de media is ingesteld om in de container te passen."}, "border": "<PERSON><PERSON><PERSON>", "color_swatches": "Toon kleurstalen in productkaart", "color_swatches_on_hover": "Toon kleurstalen in productkaart (wanneer de cursor erboven staat)"}, "color_swatches_label": {"label": "<PERSON><PERSON><PERSON><PERSON> met k<PERSON><PERSON><PERSON>n", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON> me<PERSON>ere varianttitels (gescheiden door komma's) waarvan u kleurstalen wilt maken."}, "badges": {"name": "Productbadges", "show_badges": "Badges tonen", "settings": {"colors": {"text": "Tekstkleur badges", "sold_out": "'Uitverkocht' achtergrondkleur", "sale": "'Korting' acht<PERSON><PERSON><PERSON><PERSON>ur"}}, "badge_sale": {"name": "Kortingsbadge", "amount_saved": "<PERSON><PERSON> ges<PERSON>"}, "regular_badges": {"info": "<PERSON>s [hier] meer informatie over productbadges (https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)."}, "sold_out": {"name": "Uitverkochtbadge", "text_color": "'Uitverkocht'-tekstkleur", "sale_text": "'Korting'-<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "custom_badges": {"name": "Aangepaste productbadges", "info": "Dit thema maakt gebruik van aangepaste productbadges die u hier kunt definiëren. [Meer informatie](https://shopify-support.krownthemes.com/article/564-product-pages#product-badges)", "split_info": "Dit thema maakt gebruik van aangepaste productbadges die u hier kunt definiëren. [Meer informatie](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)", "name__1": "Aangepaste badge 1", "name__2": "Aangepaste badge 2", "name__3": "Aangepaste badge 3", "text": "Tekst", "tags": "Label", "color": "<PERSON>chtergrondkleur", "text_color": "Tekstkleur", "border_color": "<PERSON><PERSON><PERSON>"}}}, "seo_late": {"breadcrumbs": {"label": "Improve product breadcrumb navigation", "info": "Adds collection names to the breadcrumb navigation, by duplicating product url's. [Learn more](https://shopify-support.krownthemes.com/article/697-seo#breadcrumbs)"}}}, "local-extra-words": {"settings_schema": {"colors": {"headings": {"header": "<PERSON><PERSON>", "cards": "Ka<PERSON><PERSON>"}, "settings": {"borders": "Rand", "hide_border": "Rand verbergen", "accent": "Accent", "overlay": "Overlay"}}, "typography": {"buttons": {"label": "Lettertype gew<PERSON><PERSON> van knoppen", "option__1": "Normaal", "option__2": "Vetter"}, "menus": {"header": "<PERSON><PERSON>'s", "size": "<PERSON>sis maat", "weight": "Lettertype dikte", "weight_bold": "Vetgedrukt"}}, "borders": {"name": "<PERSON><PERSON>", "main": {"name": "Secties", "info": "<PERSON><PERSON> instelling bepa<PERSON><PERSON> de stij<PERSON> van de randen van alle secties in het thema."}, "buttons": {"name": "<PERSON><PERSON><PERSON>"}, "forms": {"name": "Formulieren"}, "settings": {"width": "<PERSON><PERSON><PERSON>", "radius": "<PERSON><PERSON><PERSON>"}}, "layout": {"name": "Lay-out", "sections": {"vertical_space": "Verticale ruimte tussen secties", "remove_vertical_space": "Bovenmarge verwijderen", "remove_bottom_margin": "Ondermarge verwijderen"}, "grid": {"name": "Rooster", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON> met een lay-out met me<PERSON><PERSON> kolomme<PERSON>.", "horizontal_space": "Horizontale ruimte", "vertical_space": "Verticale ruimte"}}, "cart": {"shipping": {"name": "Verzenden", "show": {"label": "Minimumbedrag gratis verzending weergeven", "info": "Om verzendtarieven te configureren, ga je naar je [verzendinstellingen](/beheerder/instellingen/verzenden)."}, "amount": {"label": "Minimumbed<PERSON> gratis verzending", "info": "<PERSON><PERSON>i<PERSON><PERSON> een cijfer, geen letters of speciale tekens."}, "info": "This feature only works with a single currency and shipping method. If you're using multiple shipping methods or currencies, it might show false values to the customer."}, "settings": {"media": {"header": "Media aspect ratio", "label": "Aspect ratio", "info_cart": "Changes the aspect ratio for small product thumbnails used in the cart, search popup and some other areas that require small product thumbnails."}}}, "aspect_ratio": {"landscape": {"label": "Korter (3:2)"}}, "maps": {"name": "Ka<PERSON><PERSON>"}, "search": {"predictive_search": {"name": "Voorspellend zoeken", "info": "Voorspellend zoeken ondersteunt suggesties voor producten, collecties, pagina's en artikelen."}}, "product-card": {"name": "<PERSON><PERSON><PERSON>", "title-size": {"name": "Titel grootte", "options__1": "<PERSON>", "options__2": "Groot"}, "local-pickup": {"name": "Lokale beschikbaarheid", "info": "Dit thema toont de lokale beschikbaarheid van producten. [<PERSON><PERSON> <PERSON>](https://shopify-support.krownthemes.com/article/805-collection-page)"}, "badges": {"name": "Standaard productbadges", "settings": {"colors": {"text": "Tekstkleur badges", "sold_out": "'Uitverkocht' achtergrondkleur", "sale": "'Korting' acht<PERSON><PERSON><PERSON><PERSON>ur"}}, "badge_sale": {"name": "Kortingsbadge"}, "custom_badges": {"name": "Aangepaste productbadges", "info": "Dit thema maakt gebruik van aangepaste productbadges die je hier kunt definiëren. [<PERSON><PERSON> meer](https://shopify-support.krownthemes.com/article/795-product-card#Product-badges-_KIdK)", "name__1": "Aangepaste badge 1", "name__2": "Aangepaste badge 2", "name__3": "Aangepaste badge 3", "text": "Tekst", "tags": "Label", "color": "<PERSON>chtergrondkleur", "text_color": "Text color"}}, "icons_list": "<PERSON><PERSON><PERSON> met dynamische pictogrammen", "products-list": {"name": "Products list"}}, "embellishments": {"settings": {"show_gotop": {"label": "Show go to top button"}}}}, "sections": {"video": {"name": "Video", "settings": {"video": {"label": "Video-URL"}, "image": {"label": "Achtergrondafbeelding"}}}, "contact-form": {"settings": {"form-fields": {"name": "Formuliervelden", "show-phone": "Toon telefoon", "show-subject": "Toon onderwerp"}}, "blocks": {"contact-info": {"name": "Contactinformatie", "settings": {"title": {"label": "Titel"}, "content": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "text-columns-with-icons": {"blocks": {"icon": {"name": "Aangepast pictogram", "info": "256 x 256 px"}, "select_icon": {"info": "Ga naar [deze link] om meer pictogrammen te visualiseren en te downloaden (https://resources.krownthemes.com/icons/)"}, "icon_color": {"label": "Pictogram", "info": "Werkt alleen voor meegeleverde pictogrammen"}}}, "content-toggles": {"name": "<PERSON><PERSON><PERSON> s<PERSON>", "block": "<PERSON><PERSON><PERSON>"}, "text-columns-with-images": {"blocks": {"image": {"info": "640 x 640 pixels"}}}, "announcement-bar": {"settings": {"social": {"header": "Sociale pictogrammen", "info": "<PERSON>a naar Thema-instellingen > Sociaal om sociale profielen in te stellen.", "label": "Sociale pictogrammen weergeven"}}, "blocks": {"content": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": "Tekst", "link": "Link", "target": "Open link in een nieuw venster"}}}}, "newsletter": {"show_icon": "Toon pictogram"}, "cookies": {"name": "Pop-up Cookies", "cookies_info": "Deze website maakt gebruik van cookies om de beste gebruikerservaring te garanderen. [Meer informatie](https://shopify-support.krownthemes.com/article/790-cookies-popup)."}, "popups": {"name": "Pop-ups", "blocks": {"model": {"model-1": "Cookies", "model-2": "Nieuwsbrief", "model-3": "Aangepast"}, "settings": {"size": {"label": "Pop-upgrootte", "option_1": "<PERSON>", "option_2": "Groot"}}}}, "age-verification": {"name": "Leeftijdsverificatie", "settings": {"button-text": "Knoptekst"}}, "stores-map": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"map": {"title": "<PERSON><PERSON>"}, "gallery": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "store-selector": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"map": {"label": "Dynamische kaart inschakelen", "info": "Zorg ervoor dat de Google Maps API-sleutel correct is ingesteld in Thema-instellingen"}, "zoom": {"label": "Kaartzoom", "info": "Kies een juiste waarde om alle gewenste winkels tegelijk te zien."}}, "blocks": {"map": {"name": "Locatie op de kaart", "settings": {"address": {"label": "<PERSON><PERSON>", "info": "<PERSON><PERSON> meer"}, "image": {"label": "Afbeelding", "info": "Upload een statische afbeelding als je de dynamische kaart niet wilt gebruiken."}, "style": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "option__1": "Standaard", "option__2": "Zilver", "option__3": "Retro", "option__4": "<PERSON><PERSON>", "option__5": "<PERSON><PERSON>", "option__6": "Aubergine"}, "pin": {"label": "Aangepaste pin op kaart", "info": "240 x 240 px transparant.png"}}}, "store": {"name": "<PERSON><PERSON>", "settings": {"name": {"label": "<PERSON><PERSON>", "info": "De winkelnaam moet overeen<PERSON><PERSON> met de naam van je winkel die is gedefinieerd in je [locatie-instellingen](/beheerder/instellingen/locaties)"}, "pickup_price": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pickup_time": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "address": {"label": "Winkelgegevens"}, "image": {"label": "Winkelafbeelding"}, "closing_times": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (optioneel)", "info": "Voeg 7 regels toe, <PERSON><PERSON> voor elke dag van de week, begin<PERSON><PERSON> met z<PERSON><PERSON>."}, "timezone": {"label": "Tijdzone", "info": "Gebruikt voor het correct weergeven van sluitingstijden"}, "map": {"name": "<PERSON><PERSON>sp<PERSON>", "info": "<PERSON><PERSON> de ka<PERSON> is ingeschakeld, moet je een aangepaste pincode voor dit adres definiëren. [Meer informatie over het verkrijgen van je adrescoördinaten](https://support.google.com/maps/answer/18539?hl=nl)"}, "map_latitude": {"label": "Breedtegraad", "info": "Breedtegraadcoördinaat voor de markering. Voorbeeld: 46.7834818"}, "map_longitude": {"label": "Lengtegraad", "info": "Lengtegraadcoördinaat voor de markering. Voorbeeld: 23.5464733"}, "get_directions_button": {"label": "Toon \"routebeschrijving\"-knop", "info": "Opent een grotere kaart in een nieuw browsertabblad."}, "map_pin": {"label": "Aangepaste speld", "info": "90 x 90 px transparant.png"}}}}}, "header": {"settings": {"layout": {"label": "Koptekstindeling", "info": "Beïnvloedt de positie van de aangepaste blokken en standaardacties", "option__1": "Aangepaste blokken bovenaan, standaardacties onderaan", "option__2": "Standaardacties bovenaan, aangepaste blokken onderaan"}, "sticky": {"label": "Kleverige kopstekst", "info": "Toont de navigatie wanneer de gebruiker omhoog scrolt"}}, "blocks": {"info": {"name": "Info", "style": {"label": "<PERSON><PERSON><PERSON><PERSON>", "option__1": "Tekstinformatie", "option__2": "Knop", "info": "Op knoppen is alleen het bijschrift zichtbaar, zoals het label van de knop."}, "custom-icon": {"label": "Aangepast pictogram", "info": "Upload een 76 x 76px.png-afbeelding"}, "icon": {"label": "Pictogram"}, "link_type": {"label": "Open link", "option__1": "Binnen een modaal venster", "option__2": "Op dezelfde pagina", "option__3": "Op een nieuwe pagina", "info": "Modale vensters werken alleen met interne paginalinks"}}, "store-selector": {"name": "<PERSON><PERSON><PERSON><PERSON>", "content": "De wink<PERSON> kan worden geconfigureerd in het gede<PERSON><PERSON>. [Meer informatie](https://shopify-support.kroownthemes.com/article/640-header-the-store-selector#store-selector)", "info": "Met dit thema kun je je echte winkellocaties verbinden met een interactieve winkelkiezer. [Meer informatie](https://shopify-support.kroownthemes.com/article/640-header-the-store-selector#store-selector)"}, "mega-menu": {"name": "Mega-menu", "settings": {"menu_handle": {"label": "<PERSON>u hand<PERSON>", "info": "Dit thema maakt gebruik van <PERSON>'s. [Meer informatie](https://shopify-support.kroownthemes.com/article/640-header-the-store-selector#mega-menus)"}}}}}, "marquee": {"name": "Scrollende tekst", "settings": {"scroll_direction": "Scrollrichting", "scroll_speed": "Scrollsnelheid", "scroll_speed_info": "Hoe groter de waarde, hoe langzamer het scrollt", "pause_on_mouseover": "<PERSON><PERSON>ren bij mui<PERSON>", "scroll_item": "Scrollend item", "scroll_item_text": "Scrollende tekst"}}, "image-section": {"name": "Afbeelding", "settings": {"image_size": {"label": "Desktopbreedte", "info": "Op mobiel is de afbeelding over de volledige breedte."}}}, "media-with-text-overlay": {"name": "Media met teks<PERSON><PERSON><PERSON>", "blocks": {"media": "Media", "image": {"name": "Afbeelding"}, "link": {"info": "De titel verandert in een link, tenzij er een label voor de knop is"}, "video": {"name": "Video", "label": "Video", "info": "De afbeelding hierboven laat zien of deze video niet kan worden afgespeeld."}}, "settings": {"height": "Kaarthoogte", "option__1": "<PERSON>", "option__2": "Groot", "option__3": "Extra groot", "option__4": "Volledig scherm", "option__5": "Normaal"}}, "blog-posts": {"settings": {"emphasize": {"label": "Benadruk eerste artikel", "info": "Alleen op desktop"}}, "blocks": {"summary": {"name": "Uittreksel", "settings": {"excerpt_limit": "Aantal woorden", "excerpt_limit_info": "<PERSON> van toepassing als aan het artikel geen handmatig uittreksel is toegevoegd in de beheerdersinstellingen."}}}}, "testimonials": {"name": "Getuigenissen", "blocks": {"name": "Afbeelding"}}, "slideshow": {"name": "Diavoorstelling", "block": {"name": "Afbeelding"}, "settings": {"caption_size": "Onderschriftgrootte"}}, "rich-text": {"settings": {"image_position": {"label": "Afbeeldingspositie", "option__1": "<PERSON><PERSON>", "option__2": "<PERSON><PERSON>", "option__3": "<PERSON><PERSON>"}, "fullwidth": {"label": "Volledige breedte", "info": "<PERSON><PERSON><PERSON> de <PERSON> van dit gedeelte uit om het scherm te vullen."}, "height": {"label": "Kaarthoogte", "info": "Minimale hoogte van de kaart op desktop. Op mobiel is de hoogte a<PERSON><PERSON><PERSON><PERSON><PERSON> van <PERSON>."}, "crop": {"label": "Afbeeldingsgebied vullen", "info": "Afbeelding wordt bijgesneden om de volledige hoogte van de kaart op te vullen op desktop. Op mobiel wordt de afbeelding altijd volledig weergegeven."}, "remove_margin": {"label": "Bovenmarge van sectie verwijderen"}}}, "main-header": {"settings": {"mobile": {"name": "Mobiele navigatie", "info": "Deze hebben alleen invloed op de zichtbaarheid in de mobiele navigatielade.", "header_actions": "Toon winkelkiezer &amp; infoblokken", "header_info_blocks": {"header": "Koptekstinfo blokken", "label_1": "Toon winkelkiezer &amp; infoblokken in de kop op mobiele apparaten", "label_2": "Plaats infoblokken bovenaan het eerste gedeelte van de startpagina", "label_2_info": "Integreert goed wanneer het eerste gedeelte een diavoorstelling over de volledige breedte is"}}, "promotion_block": {"title": {"label": "Titel", "size": "Tite<PERSON>gro<PERSON>"}, "subtitle": {"label": "Ondertitel", "size": "Ondertitelgrootte"}, "button": {"label": "Knoplabel", "size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "Knoplink", "style": "Knopstijl"}}, "header_actions": {"header": "Koptekstinfoblokken op mobiel", "show_in_drawer": "Weergeven in de navigatielade"}}}, "main-cart": {"blocks": {"shipping-calculator": {"name": "Verzendcalculator"}, "related-products": {"info": "The product recommendations are based on products that are commonly purchased together or products in related collections. If there are no recommendations, the block will not appear."}}}, "main-search": {"settings": {"blogs": {"name": "Artikelresultaten"}, "products": {"name": "Productresultaten", "info": "<PERSON> <PERSON><PERSON><PERSON> de <PERSON> moet worden ingesteld met be<PERSON><PERSON> van de sectieblokken."}}}, "main-product": {"name": "Productpagina", "settings": {"gallery_pagination": "<PERSON><PERSON><PERSON> van de galerijschuifregelaar", "show_border": "<PERSON> rond galerij weergeven", "gallery_border": "Gallery border", "gallery_border_color": "Gallery border color"}, "blocks": {"pickup_availability": {"name": "Afhaalbeschikbaarheid", "info": "Dit thema toont de afhaalbeschikbaarheid op basis van de geselecteerde winkel. <PERSON><PERSON> meer", "settings": {"style": "<PERSON><PERSON><PERSON><PERSON>", "option__1": "Compact", "option__2": "Verlengd"}}, "buy_buttons": {"settings": {"show_price": "<PERSON>n prijs"}}, "related": {"name": "Gerelateerde producten", "settings": {"products": "<PERSON><PERSON>"}}, "tax_info": {"name": "Belastinginformatie"}, "icons": {"name": "<PERSON><PERSON><PERSON> met pictogrammen", "info": "Ga naar [deze link](https://www.krownthemes.com/crown-icons/) om pictogrammen die deel uitmaken van het thema te visualiseren en te downloaden.", "help": "Met dit thema kun je aangepaste productpictogrammen toevoegen via dynamische inhoud. [Meer informatie](https://shopify-support.kroownthemes.com/article/639-theme-settings#dynamic-icons)", "headers": {"icon_1": "Pictogram 1", "icon_2": "Pictogram 2", "icon_3": "Pictogram 3", "icon_4": "Pictogram 4", "icon_5": "Pictogram 5", "icon_6": "Pictogram 6"}, "settings": {"icon": "Pictogram", "icon_info": "96 x 96 px", "label": "Label"}}}}, "main-blog": {"name": "Hoofdblog"}, "main-article": {"name": "Artikel", "settings": {"show_tags": "Toon labels", "enhance_product_links": {"label": "Productlinks verbeteren", "info": "Alle links naar producten openen het modale venster voor snel kopen van producten."}}}, "main-article-comments": {"name": "Artikel opmerkingen", "info": "Om reacties in te schakelen, ga naar je [bloginstellingen].(/beheerder/blogs)"}, "main-article-navigation": {"name": "Artikelnavigatie", "settings": {"header": {"content": "Blogberichten", "info": "Laat leeg als je de standaard vorige of volgende blogpost wilt laden."}, "posts": {"next": "Volgende bericht", "previous": "Vorige post"}}}, "main-page": {"settings": {"center": {"label": "Inhoud centreren op bureaukop"}}}, "main-footer": {"blocks": {"payment": {"name": "Betalingspictogrammen", "info": "De pictogrammen die worden weergegeven, worden bepaald door de [betalingsinstellingen](/admin/settings/payments) van je winkel en de regio en valuta van de <PERSON>.", "custom_icons": "Custom icons", "custom_icons_info": "Upload 114 x 72px .png images", "only_show_custom_icons": "Only show the custom icons"}, "text": {"name": "Text & social icons"}}}, "customers": {"reset-password": {"name": "Wachtwoord opnieuw instellen"}, "order": {"name": "Bestelpagina"}, "register": {"name": "Registratiepagina"}, "activate-account": {"name": "Accountpagina activeren"}, "login": {"name": "Loginpagina", "shop_login_button": {"enable": "Inloggen met Shop inschakelen"}}, "account": {"name": "Account<PERSON><PERSON><PERSON>"}, "addresses": {"name": "<PERSON><PERSON><PERSON>"}}, "headings": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "subheading": "Ondertitel", "title": "Titel", "subtitle": "Ondertitel", "caption": "Onderschrift", "text_content": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom_colors": "Aangepast<PERSON> k<PERSON>uren", "text_style": "Tekststijl"}, "columns": {"name": "Desktopindeling", "info": "De indeling past zich aan voor mobiele apparaten.", "option__0": "1 kolom", "option__1": "2 kolommen", "option__2": "3 kolommen", "option__3": "4 kolommen", "option__4": "5 kolommen", "option__5": "6 kolommen", "name_mobile": "Mobile layout"}, "promotion-cards": {"name": "Promotiekaarten", "blocks": {"name": "<PERSON><PERSON>"}}, "faq": {"headings": {"header": "<PERSON><PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON>"}, "settings": {"form": {"header": "Contactformulier", "show": "<PERSON>n formulier", "title": "Formuliertitel"}}}, "product-quick-view": {"name": "Snelle weergave", "info": "Deze sjabloon bepaalt hoe de snelle productweergave wordt opgebouwd. Alleen deze sectie verschijnt in het modale venster."}, "product-card": {"blocks": {"price": "<PERSON><PERSON><PERSON><PERSON>", "title": "Titel", "vendor": "Leverancier", "text": {"name": "Dynamische tekst", "info": "Gebruik een dynamische bron om een uniek kenmerk te markeren door een productmetaveld te maken. [Learn more](https://shopify-support.krownthemes.com/article/805-collection-page)", "settings": {"text": {"label": "Label metaveld"}, "size": {"label": "<PERSON><PERSON><PERSON>", "option__1": "<PERSON>", "option__2": "Normaal", "option__3": "Groot"}, "color": {"label": "Tekstkleur", "option__1": "Primair", "option__2": "Secundair"}, "transform": {"label": "Teksttransformatie (hoofdletters)"}}}, "icons": {"info": "Gebruik dynamische bronnen om unieke kenmerken te benadrukken door productmetavelden te maken voor de pictogrammenlijst. [Meer informatie](https://shopify-support.kroownthemes.com/article/639-theme-settings#dynamic-icons)", "settings": {"icon": "Pictogram metaveld", "label": "Label metaveld"}}, "quick_buy": "Snel kopen", "rating": "Beoordeling"}}, "buttons": {"style": {"label": "Knopstijl", "option__1": "Overzicht", "option__2": "<PERSON><PERSON><PERSON>"}}}}, "complementary_products": {"name": "Aanvullende producten", "settings": {"paragraph": {"content": "Als je aanvullende producten wilt selecteren, voeg je de Search en Zichtbaarheid-app toe. [Meer informatie](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}}}, "split-extra-words": {"settings_schema": {"colors": {"headings": {"header_sidebars": "Koptekst en zijbalken", "main": "Hoofdtekst", "footer": "Voetnoot", "custom_colors": "Aangepast<PERSON> k<PERSON>uren"}, "settings": {"background": "Achtergrond", "text": "Tekst", "links": "Actieve links", "borders": "<PERSON>n randen"}}, "typography": {"headings": {"headings": "<PERSON><PERSON><PERSON>", "body": "Hoofdtekst", "logo_menus": "Logo en menu's", "buttons": "Knoppen"}, "settings": {"font_family": "Lettertypefamilie", "base_size": "Basisgrootte", "line_height": "Regelhoogte", "hr": {"label": "Toon horizontale regels", "info": "Toont een kleine visuele horizontale regel op sommige titels"}, "border_radius": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "embellishments": {"preloader": {"label": "Media-voorlader", "info": "Toont een kleine cirkelvormige voorlader terwijl de media in uw winkel nog aan het laden zijn."}, "breadcrumb": {"label": "Toon broodk<PERSON>imel", "info": "Broodkruimelnavigatie helpt gebruikers door de winkel te navigeren en is alleen zichtbaar op collectie-, product-, zoek- en accountpagina's."}}, "cart": {"page": "Artikelen in winkelwagen", "show_recommendations": "Toon aanbevelingen voor winkelwagen"}, "headings": {"title": "Titel", "subtitle": "Ondertitel"}, "product-grid": {"animation_style": {"label": "Weergave bijschriften (desktop)", "options__1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__2": "Overlay", "info": "Op mobiel zal het bijschrift altijd zichtbaar zijn voor een betere UX"}, "overlay_colors": {"background": "Overlay bijschrift achtergrond", "text": "Overlay bijschrift tekst"}, "aspect_ratio": {"label": "Product media aspect", "options__1": "Bijgesneden", "options__2": "Natuurlijk"}, "show_secondary_image": {"info": "Alleen op desktop"}, "quick_buy": {"name": "Snel kopen", "info": "Voegt een directe 'toevoegen aan winkelwagen'-knop toe. Als het product varianten heeft, zal er een 'snel kopen'-popup verschijnen.", "label": "Snel kopen inschakelen"}, "rating": {"label": "Rating-weergave (desktop)", "options__1": "<PERSON>et tonen", "options__2": "<PERSON>n bij zweven", "options__3": "<PERSON><PERSON><PERSON><PERSON>", "show_on_mobile": "Toon op mobiel"}}}, "sections": {"header": {"name": "<PERSON><PERSON>", "settings": {"logo_height": "Maximumhoogte logo-afbeelding", "menu": "<PERSON><PERSON>", "menu_style": {"label": "Desktop menustijl", "options__1": "Klassiek", "options__2": "Lade"}, "collections_menu": {"header": "Collectiesmenu", "info": "Dit heeft een gedurfd ontwerp, vooral in de klassieke menustijl, waar het verandert in een megamenu met de mogelijkheid om afbeeldingen en een promotie toe te voegen.", "settings": {"show_images": {"label": "Toon collectieafbeeldingen", "info": "<PERSON><PERSON> van toepassing als de bovenliggende artikelen een collectie is."}}}, "promotional_block": {"name": "Promotieblok", "settings": {"show": {"label": "<PERSON>n promotieb<PERSON><PERSON>", "info": "In minimale stijl staat het onderaan de menulade. In de klassieke stijl wordt het getoond in het collectiemenu, indien aanwezig."}, "title": {"label": "Promotietitel"}, "content": {"label": "Promotie-inhoud"}, "button": {"label": "Promotieknop label"}, "link": {"label": "Promotieknop link"}, "txt_color": {"label": "Promotie-tekstkleur"}, "bg_color": {"label": "Promotie-achtergrondkleur"}, "image": {"label": "Promotie-afbeelding"}}}, "announcement_bar": {"content": {"info": "Max. 50 tekens"}}}}, "footer": {"blocks": {"menu": {"name": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}}}, "main-product": {"name": "Productpagina", "settings": {"header": {"label": "Productkoptekst", "info": "Op mobiele apparaten verschijnt de productkoptekst altijd bovenaan, boven de productgalerij.", "show_tax_info": "Belastinginformatie weergeven", "show_reviews": "Productbeoordeling weergeven", "show_sku": "SKU Weergeven", "show_barcode": "BARCODE Weergeven", "show_vendor": "Leverancier weergeven", "show_badge": "Productbadge weergeven"}, "variants": {"label": "Varianten-selector type", "options__1": "Blokken", "options__2": "Dropdown"}, "gallery_aspect": {"label": "<PERSON><PERSON> het formaa<PERSON> van a<PERSON>bee<PERSON>en met <PERSON><PERSON><PERSON> van de schuifregelaar zodat deze in de kijkvenster passen", "info": "Op mobiele apparaten passen afbeeldingen altijd in de kijkvenster van het apparaat."}, "color_swatches": {"label": "<PERSON><PERSON> kle<PERSON>talen (alleen voor blokstijl)", "info": "Dit thema kan aangepaste afbeeldingen voor kleurstalen tonen. [Learn more](https://shopify-support.krownthemes.com/article/554-using-color-swatches)"}}}, "countdown-banner": {"name": "Aftelbanner", "settings": {"header": "Aftelklok", "show_countdown": "Aftelklok weergeven", "countdown_year": "<PERSON><PERSON><PERSON><PERSON>", "countdown_month": "Eindmaand", "countdown_day": "Einddag", "countdown_hour": "Ein<PERSON>ur", "countdown_timezone": "Tijdzone", "size": "Bannerhoogte"}}, "map": {"settings": {"map": {"api": {"label": "Google Maps API-sleutel", "info": "Je moet je aanmelden voor een [Google Maps API Key](http://shopify-support.krownthemes.com/article/385-generate-an-api-key-for-google-maps) to display the map."}}}}}}, "main-cart-footer": {"name": "Subtotaal winkelwagen", "blocks": {"subtotal_button": {"name": "Subtotaal &amp; afrekenen"}}}, "main-cart-items": {"name": "Winkelwagen items"}, "main-list-collections": {"name": "Lijstpagina met collecties", "blocks": {"collection": {"name": "Col<PERSON><PERSON>", "settings": {"collection": {"label": "Col<PERSON><PERSON>"}, "image": {"label": "Afbeelding", "info": "Als je een aangepaste afbeelding voor de collectie wilt toevoegen."}}}}, "settings": {"header": {"content": "Collecties"}, "layout": {"label": "Lay-out", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON>wee kolommen"}}, "paragraph": {"content": "Al je collecties worden standaard weergegeven. Om je lijst aan te passen, kies je 'Geselecteerd' en voeg je collecties toe."}, "display_type": {"label": "Selecteer collecties om weer te geven", "options__1": {"label": "Alle"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "sort": {"label": "Sorteer collecties op:", "info": "<PERSON><PERSON><PERSON> is alleen van toepassing als 'Alle' is geselecteerd", "options__1": {"label": "Alfabetisch, A-Z"}, "options__2": {"label": "Alfabetisch, Z-A"}, "options__3": {"label": "<PERSON><PERSON>, nieuw naar oud"}, "options__4": {"label": "<PERSON><PERSON>, oud naar nieuw"}, "options__5": {"label": "Productaantal, hoog naar laag"}, "options__6": {"label": "Product<PERSON>tal, laag naar hoog"}}, "items_per_row": "Aantal items per rij"}}, "sidebar": {"name": "Zijbalk", "settings": {"image": {"label": "Logo-afbeelding"}, "image_width": {"label": "Breedte logo afbeelding"}, "primary_navigation": {"label": "Primaire navigatie"}, "secondary_navigation": {"label": "Secundaire navigatie"}, "search": {"content": "<PERSON><PERSON>", "label": "Zoekopdracht weergeven"}}}, "text-columns-with-icons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met pictogrammen", "settings": {"content": {"label": "<PERSON><PERSON> weergeven op geselecteerde pagina's:"}, "show_on_homepage": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "show_on_product": {"label": "Productpagina's"}, "show_on_collection": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s"}, "show_on_blog": {"label": "Blog- en artikelpagina's"}, "show_on_regular": {"label": "Reguliere pagina's"}, "icons": {"label": "Iconen", "info": "Om alle iconen in het thema te visualiseren, gaat u naar [this link](https://krownthemes.com/kroown-icons/)"}}, "blocks": {"icon": {"name": "<PERSON><PERSON><PERSON> met icoon", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Tekst"}, "icon": {"label": "Selecteer een icoon"}}}}}, "footer": {"name": "Voetnoot", "settings": {"show_payment_icons": {"label": "<PERSON>n <PERSON>"}, "language_selector": {"content": "<PERSON><PERSON><PERSON><PERSON>", "info": "Om een taal toe te voegen, gaat u naar uw [language settings.](/admin/settings/languages)"}, "language_selector_show": {"label": "<PERSON><PERSON>"}, "country_selector": {"content": "Kiezer voor land/regio", "info": "Ga naar je [payment settings.](/admin/settings/payments) om een land/regio toe te voegen"}, "country_selector_show": {"label": "Kiezer voor land/regio inschakelen"}}, "blocks": {"text": {"name": "Tekst", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON>"}, "text_size": {"label": "Tekstgrootte", "options__1": {"label": "Normaal"}, "options__2": {"label": "Groot"}}}}, "menus": {"name": "<PERSON><PERSON>'s", "settings": {"title_1": {"label": "Kop eerste menu"}, "title_2": {"label": "Kop tweede menu"}, "menu_1": {"label": "Eerste menu", "info": "Dit menu geeft geen dropdown-items weer"}, "menu_2": {"label": "Tweede menu"}}}, "newsletter": {"name": "E-mailinschrijving"}, "social": {"name": "Sociale links"}, "image": {"name": "Afbeelding", "settings": {"image": {"label": "Selecteer af<PERSON><PERSON>"}}}}}, "contact-form": {"name": "Contactformulier", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"field": {"name": "Formulierveld", "settings": {"type": {"label": "Type", "options__1": {"label": "<PERSON><PERSON><PERSON> regel"}, "options__2": {"label": "<PERSON><PERSON><PERSON> regel<PERSON>"}}, "required_field": {"label": "Vere<PERSON>"}, "labels": {"label": "Label", "info": "Zorg ervoor dat al uw velden unieke labels hebben!"}}}, "email": {"name": "Naam en e-mailadres"}, "button": {"name": "Verzendknop", "settings": {"label": {"label": "Label"}}}}, "presets": {"name": "Contactformulier"}}, "image-with-text": {"name": "Afbeelding met tekst", "blocks": {"image": {"name": "Afbeelding met tekst", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "body": {"label": "Tekst"}, "button_label": {"label": "Knoplabel"}, "url": {"label": "Link", "info": "Het hele blok wordt omgezet in een link, tenzij er een label voor de knop is"}, "image": {"label": "Achtergrondafbeelding"}}}}, "settings": {"image_height": {"label": "Afbeeldingshoogte", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Groot"}, "options__4": {"label": "Vol"}}, "text_width": {"label": "Breedte tekstvak", "options__1": {"label": "Medium"}, "options__2": {"label": "Groot"}, "options__3": {"label": "Vol"}}, "text_size": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Normaal"}, "options__2": {"label": "Groot"}, "options__3": {"label": "Extra groot"}}, "text_alignment": {"label": "Tekstuitlijning", "options__1": {"label": "Linksboven"}, "options__2": {"label": "Middenboven"}, "options__3": {"label": "Rechtsboven"}, "options__4": {"label": "Middenlinks"}, "options__5": {"label": "Midden midden"}, "options__6": {"label": "Middenrechts"}, "options__7": {"label": "Linksonder"}, "options__8": {"label": "Middenonder"}, "options__9": {"label": "Rechtsonder"}}, "options__5": {"label": "Extra groot"}}, "presets": {"name": "A<PERSON><PERSON>ldingen met tekst"}}, "featured-product": {"name": "Uitgelicht product", "settings": {"product": {"label": "Selecteer product"}}, "blocks": {"product_link": {"name": "Productlink"}}}, "featured-collection": {"name": "Uitgelichte collectie", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "Toon link naar collectiepagina"}, "layout": {"label": "Lay-out", "options__1": {"label": "Slide<PERSON>"}, "options__2": {"label": "Rooster"}}, "products_number": {"label": "Maximum aantal weergegeven producten"}, "collection": {"label": "Col<PERSON><PERSON>"}}, "presets": {"name": "Uitgelichte collectie"}}, "gallery": {"name": "<PERSON><PERSON><PERSON>", "blocks": {"image": {"name": "Afbeelding", "settings": {"image": {"label": "Afbeelding"}, "caption": {"label": "Ondertiteling"}, "featured": {"label": "V<PERSON><PERSON>ot afbeelding in rooster"}}}}, "settings": {"aspect_ratio": {"label": "Beeldverhouding voor afbeeldingen", "options__1": {"label": "Kort (4:3)", "group": "Bijgesneden"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> (1:1)"}, "options__3": {"label": "<PERSON><PERSON> (5:6)"}, "options__4": {"label": "<PERSON><PERSON> (2:3)"}, "options__5": {"label": "Natuurlijk", "group": "<PERSON><PERSON> bi<PERSON>"}, "info": "Wanneer u de oorspronkelijke beeldverhouding gebruikt, moet u ervoor zorgen dat u uw miniaturen dezelfde afmetingen geeft, voor een strakke roosterweergave. Als u een van de instellingen om bij te snijden gebruikt, worden de afmetingen van alle miniaturen hetzelfde bijgesneden."}, "style_mobile": {"label": "<PERSON><PERSON> van de galerij een slider op mobiele apparaten"}, "slider_height": {"label": "<PERSON><PERSON><PERSON> mobiele slider", "options__1": {"label": "Medium"}, "options__2": {"label": "Groot"}, "options__3": {"label": "Vol"}}, "lightbox": {"label": "Lightbox inschakelen", "info": "Toon grotere afbeeldingen na klikken"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"title": {"label": "Titel"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "image": {"name": "Afbeelding", "mobile_image": "Mo<PERSON>le afbeelding (optioneel)", "fullwidth": "Volledige breedte"}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "Maak sectiemarges hetzelfde als het thema"}}, "presets": {"name": "Apps"}}, "rich-text": {"name": "Rich text", "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "heading_size": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Normaal"}, "options__2": {"label": "Groot"}, "options__3": {"label": "Extra groot"}}}}, "icon": {"name": "Icoon"}, "text": {"name": "Tekst", "settings": {"text": {"label": "Tekst"}}}, "button": {"name": "Knop", "settings": {"button_label": {"label": "Knoplabel"}, "button_link": {"label": "Knoplink"}, "button_size": {"label": "Knopgrootte", "options__1": {"label": "Normaal"}, "options__2": {"label": "Groot"}}}}}, "settings": {"text_alignment": {"label": "Tekstuitlijning", "options__1": {"label": "Links"}, "options__2": {"label": "Midden"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "image": {"label": "Afbeelding"}, "image_position": {"label": "Positie a<PERSON>", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_height": {"label": "Afbeeldingshoogte", "options__1": {"label": "Normaal"}, "options__2": {"label": "Groot"}, "options__3": {"label": "Vol"}}}, "presets": {"name": "Rich text"}}, "shop-the-look": {"name": "Shop de look", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "subheading": {"label": "Ondertitel"}, "image": {"label": "Achtergrondafbeelding"}}, "blocks": {"product": {"name": "Product", "settings": {"select_product": {"label": "Selecteer product"}}}}, "presets": {"name": "Shop de look"}}, "testimonials": {"name": "Getuigenissen", "blocks": {"testimonial": {"name": "Testimonial", "settings": {"quote": {"label": "Citaat"}, "author_name": {"label": "Auteursnaam"}, "author_title": {"label": "Auteurstitel"}, "author_avatar": {"label": "Afbeelding van auteur"}}}}, "presets": {"name": "Getuigenissen"}}, "announcement-bar": {"name": "Aankondigingenbalk", "settings": {"bar_show": {"label": "Toon aankondigingenbalk"}, "bar_show_on_homepage": {"label": "Alleen op homepage weergeven"}, "bar_show_dismiss": {"label": "Negeerknop weergeven"}, "bar_message": {"label": "<PERSON><PERSON><PERSON>"}, "bar_link": {"label": "Link"}, "bar_bgcolor": {"label": "<PERSON>chtergrondkleur"}, "bar_txtcolor": {"label": "Tekstkleur"}}}, "text-columns-with-images": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON>", "blocks": {"text": {"name": "Tekst", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Tekst"}, "image": {"label": "Afbeelding"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "slider": {"slider_horizontal": {"name": "Diavoorstelling: horizontaal"}, "slider_vertical": {"name": "Diavoorstelling: verticaal"}, "settings": {"desktop_height": {"label": "Hoogte desktopslider", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Groot"}, "options__4": {"label": "Vol"}}, "mobile_height": {"label": "Horizontale hoogte mobiele slider"}, "text_style": {"header": "Tekststijl"}, "mobile_design": {"header": "Mobiel ontwerp", "label": "Maak de verticale slider horizontaal op mobiele apparaten"}}, "blocks": {"image": {"name": "Afbeelding", "settings": {"image": {"label": "Afbeelding"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "subheading": {"label": "Ondertitel"}, "caption": {"label": "Ondertiteling"}, "button_label": {"label": "Knoplabel"}, "link": {"label": "Link", "info": "Tenzij er een label voor de knop is, staat de link in de tekst."}}}}}, "video-popup": {"name": "Video: pop-up", "settings": {"video": {"label": "Video-URL"}, "image": {"label": "Achtergrondafbeelding"}}}, "video-background": {"name": "Video: <PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"video": {"label": "Video-URL", "info": "Pad naar een .mp4-bestand"}, "image": {"label": "Noodafbeelding", "info": "<PERSON>en noodafbeelding wordt gebruikt op mobiele apparaten waarop automatisch afspelen mogelijk is uitgeschakeld."}, "size_alignment": {"content": "Formaat en uitlijning"}, "video_height": {"label": "Videohoogte", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (16:9)", "group": "<PERSON><PERSON> bi<PERSON>"}, "options__2": {"label": "Groot", "group": "Bijgesneden"}, "options__3": {"label": "Vol"}}}}, "main-password-header": {"name": "Wachtwoordheader"}, "main-password-content": {"name": "Wachtwoordcontent"}, "main-password-footer": {"name": "Wachtwoordfooter", "settings": {"show_social": {"label": "Sociale pictogrammen weergeven"}}}, "main-article": {"name": "Blogpost", "blocks": {"featured_image": {"name": "Uitgelichte afbeelding", "settings": {"image_height": {"label": "Hoogte uitgelichte afbeelding", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON> aan a<PERSON>"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Groot"}}}}, "title": {"name": "Titel", "settings": {"blog_show_date": {"label": "Toon datum"}, "blog_show_author": {"label": "Toon auteur"}, "blog_show_comments": {"label": "Toon aantal reacties"}}}, "content": {"name": "<PERSON><PERSON><PERSON>"}, "social_sharing": {"name": "Knoppen om op sociale media te delen"}, "blog_navigation": {"name": "<PERSON>s naar verwante be<PERSON>ten"}}}, "main-blog": {"settings": {"header": {"content": "Blogpostkaart"}, "enable_tags": {"label": "Maak filteren op labels mogelijk"}, "post_limit": {"label": "Aantal berichten per pagina"}}}, "blog-posts": {"name": "Blogposts", "blocks": {"title": {"name": "Titel"}, "info": {"name": "Info", "settings": {"show_date": {"label": "Toon datum"}, "show_author": {"label": "Toon auteur"}}}, "summary": {"name": "Fragment"}, "link": {"name": "Link"}}, "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Berichten"}, "show_image": {"label": "Toon uitgelichte afbeelding"}, "show_view_all": {"label": "Toon link naar blogpagina"}, "layout": {"label": "Lay-out"}, "option_1": {"label": "<PERSON><PERSON> kolom", "group": "Rooster"}, "option_2": {"label": "<PERSON>wee kolommen"}, "option_3": {"label": "Flexibel (2 - 5 kolommen)", "group": "Slide<PERSON>"}}, "presets": {"name": "Blogposts"}}, "custom-colors": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"label": "Aangepaste tekstkleur"}, "overlay": {"label": "Achtergrond-overlay"}, "background": {"label": "Aangepaste achtergrondkleur"}}, "custom-gutter": {"heading": {"content": "Goot"}, "gutter_enabled": {"label": "Afstand voor interne content inschakelen"}}, "newsletter": {"name": "E-mailinschrijving", "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "paragraph": {"name": "Ondertitel", "settings": {"paragraph": {"label": "Beschrijving"}}}, "email_form": {"name": "E-mailformulier"}}, "presets": {"name": "E-mailinschrijving"}}, "product-recommendations": {"name": "Productaanbevelingen", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"content": "Productaanbevelingen", "info": "Dynamische aanbevelingen gebruiken bestel- en productinformatie om in de loop van de tijd te veranderen en te verbeteren. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)"}}}, "custom-liquid": {"name": "Gepersonaliseerde Liquid", "settings": {"custom_liquid": {"label": "Gepersonaliseerde Liquid"}}, "presets": {"name": "Gepersonaliseerde Liquid"}}, "collection-list": {"name": "Collectielijst", "presets": {"name": "Collectielijst"}}, "faq": {"name": "FAQ", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "open_first": {"label": "Zet de eerste schakelaar standaard open"}}, "blocks": {"text": {"name": "FAQ", "settings": {"title": {"label": "Titel"}, "text": {"label": "Tekst"}, "image": {"label": "Afbeelding"}}}}, "presets": {"name": "FAQ"}}, "popup": {"name": "Pop-up", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON>"}, "show_newsletter": {"label": "Aanmeldformulier voor e-mail weergeven"}, "functionality": {"content": "Functionaliteit"}, "enable": {"label": "Pop-up inschakelen"}, "show_after": {"label": "Toon pop-up na", "info": "sec"}, "frequency": {"label": "Pop-upfrequentie", "options__1": {"label": "Toon elke dag"}, "options__2": {"label": "Toon elke week"}, "options__3": {"label": "Toon elke maand"}}, "image": {"label": "Afbeelding", "info": "1240 x 400px .jpg aanbevolen. Het wordt alleen op desktop weergegeven"}}}, "main-search": {"name": "Zoekresultaten", "settings": {"products_per_page": {"label": "Resultaten per pagina"}}}, "main-collection-product-grid": {"name": "Productraster", "settings": {"products_per_page": {"label": "Producten per pagina"}, "enable_filtering": {"label": "Filteren inschakelen", "info": "[Customize filters](/admin/menus)"}, "enable_sorting": {"label": "Sorteren inschakelen"}, "image_filter_layout": {"label": "In<PERSON>ing van beeld<PERSON>lter"}, "header__1": {"content": "Filteren en sortering"}}}, "main-collection-banner": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Ga naar [edit your collections.](/admin/collections) om collectiebeschrijvingen of collectie-afbeeldingen te wijzigen"}, "show_collection_description": {"label": "Toon collectiebeschrijving"}, "show_collection_image": {"label": "Collectieafbeelding weergeven", "info": "Gebruik voor de beste resultaten een afbeelding met een beeld<PERSON>hou<PERSON> van 16:9."}}}, "main-product": {"name": "Productinformatie", "blocks": {"text": {"name": "Tekst", "settings": {"text": {"label": "Tekst"}, "text_style": {"label": "Tekststijl", "options__1": {"label": "Hoofdtekst"}, "options__2": {"label": "Ondertitel"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titel"}, "price": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "tax_info": {"name": "Belastinginformatie weergeven"}, "sku_barcode": {"name": "Artikelnummer / streepjescode"}, "quantity_selector": {"name": "Hoeveel<PERSON><PERSON><PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"show_variant_labels": {"label": "Toon variantlabels"}, "hide_out_of_stock_variants": {"label": "Verberg varianten die niet op voorra<PERSON> zijn"}, "low_inventory_notification": {"label": "Voorraadmelding", "info": "Voorraadtracering  van varianten moet zijn ingeschakeld om deze functie te laten werken. [Learn more](https://help.shopify.com/en/manual/products/inventory/track_inventory)", "options__1": {"label": "Geen voorraadgegevens weergeven"}, "options__2": {"label": "Toon een melding als de voorraad minder is dan 5"}, "options__3": {"label": "<PERSON> v<PERSON> altijd weergeven"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON>knop<PERSON>", "settings": {"show_dynamic_checkout": {"label": "Toon dynamische kassaknoppen", "info": "Met behul<PERSON> van de op<PERSON> 'beschikbare betaalmethodes' in uw winkel, zien klanten hun voorkeursoptie, bijvoorbeeld PayPal of Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Formulier voor ontvanger weergeven voor cadeaubonnen", "info": "<PERSON><PERSON>, krijgen klanten de optie om cadeaubonnen met een persoonlijk bericht naar de ontvanger te sturen."}, "show_quantity_selector": {"label": "<PERSON><PERSON> ho<PERSON>"}}}, "pickup_availability": {"name": "Afhaalmogelijkheid"}, "description": {"name": "Beschrijving", "settings": {"product_description_truncated": {"label": "Ingekorte beschrijving", "info": "Inkorten", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Toon een kort fragment"}, "options__3": {"label": "Toon een medium fragment"}, "options__4": {"label": "Toon een groot fragment"}}}}, "share": {"name": "<PERSON><PERSON>", "settings": {"featured_image_info": {"content": "Als u een link toevoegt aan een bericht op sociale media, wordt de uitgelichte afbeelding van de pagina weergegeven als de voorbeeldafbeelding. [Learn more](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "<PERSON>en winkel<PERSON>l en beschrijving zijn bij de voorbeeldafbeelding inbegrepen. [Learn more](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "Opvouwbaar tabblad", "settings": {"heading": {"info": "Voeg een kop toe die de content uitlegt.", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON> tabblad"}, "page": {"label": "<PERSON><PERSON><PERSON> tabb<PERSON> van pagina"}, "image": {"label": "Afbeelding tabblad"}}}}, "settings": {"header": {"content": "Media", "info": "<PERSON><PERSON><PERSON> meer over [media types](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Vastgezette productinformatie op grote schermen activeren"}, "enable_video_looping": {"label": "Videolus activeren"}, "enable_zoom": {"label": "Inzoomen op afbeelding activeren"}, "gallery_gutter": {"label": "Afstand tussen media invoeren"}, "gallery_slider_style": {"label": "<PERSON><PERSON> het formaa<PERSON> van a<PERSON>bee<PERSON>en met <PERSON><PERSON><PERSON> van de schuifregelaar zodat deze in de kijkvenster passen"}, "gallery_style": {"label": "Galerijst<PERSON><PERSON><PERSON>", "info": "Standaard ingesteld op slider voor mobiele apparaten", "options__1": {"label": "Scrollen"}, "options__2": {"label": "Slide<PERSON>"}}, "gallery_pagination": {"label": "Pagina-indeling galerij", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Miniaturen"}}}}, "words_210": {"text_columns": {"name": "Text columns", "block": {"name": "Text"}}}, "images": {"label_1": "Afbeelding 1", "label_2": "Afbeelding 2", "label_3": "Afbeelding 3"}}, "color-schemes": {"name_plural": "Color schemes", "name_singular": "Color scheme", "default": "Default color scheme", "default_info": "Used to set colors for account pages, 404 pages, and other pages or sections without color scheme settings.", "drawers": "Modals/drawers", "other": "Other colors"}, "local-250": {"labels": {"show_filters_as": "Filters weergeven als", "expand_filters_by_default": "Filters standaard u<PERSON><PERSON><PERSON><PERSON>", "stick_filters_sidebar_to_top": "Filters zi<PERSON><PERSON><PERSON> bovenaan vast<PERSON>"}, "options": {"sidebar": "Zijbalk", "list": "Lijst"}}, "local-230": {"background_gradient": "Achtergrondverloop", "variant_default": {"label": "Standaard de eerste beschikbare variant selecteren", "info": "Als deze optie niet is aang<PERSON><PERSON><PERSON>, moet de gebruiker een beschikbare variant selecteren voordat hij een aankoop kan doen."}, "slider_info": "De link zal worden toegepast op de knop, of op de titel (als er geen knop is), of op de gehele slide (als zowel de titel als de knop leeg zijn).", "buy_button_labels": {"label": "Koopknoplabels", "option_1": "Koop nu", "option_2": "<PERSON><PERSON> opties"}, "hide_on_mobile": "Verberg op mobiele apparaten"}, "local-223": {"heading_text_color": "<PERSON><PERSON><PERSON> van de <PERSON>op tekst", "slider_navigation_color": "<PERSON><PERSON><PERSON> van navigatie-elementen"}, "late_edits": {"badge": {"custom_badge": {"text_color": "<PERSON><PERSON><PERSON> kleur"}, "sold_out": {"name": "Uitverkochte badge", "text_color": "'Uitverkocht' tekstkleur", "sale_text": "'Discount' te<PERSON><PERSON><PERSON>ur"}}, "rich-text": {"image_position": {"no_image": {"group": "<PERSON><PERSON>", "label": "Toon geen afbeelding"}}}}}