{"variant_metafields": {"name": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON> metapola war<PERSON>u", "info": "Ten szablon może wyświetlić metapole wariantu na stronie produktu. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/884-using-variant-metafields-in-product-pages)."}, "new_color_swatches": "Typ selektora wariantów „bloki” oferuje wsparcie dla próbek kolorów tworzonych za pomocą pól metadanych kategorii. [<PERSON><PERSON><PERSON> się więcej](https://help.shopify.com/en/manual/custom-data/metafields/category-metafields/using-category-metafields).", "video_controls": "Pokaż kontrolki wideo", "sticky_cart_actions": "Włącz działania lepkiego koszyka", "currency_codes": {"header": "Format waluty", "label": "Pokaż kody walut", "info": "Przykład: $1.00 USD."}, "a11": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "show_sidebars_scrollbar": "Pokaż pasek przewijania bocznych pasków", "disable_all_image_animations": "Wyłącz wszystkie animacje obrazów"}, "divider": {"label": "<PERSON><PERSON><PERSON><PERSON>", "divider_design": "Projekt podziału", "divider_style_solid": "Ciągły", "divider_style_dotted": "Kropkowany", "divider_style_dashed": "Przerywany", "divider_style_double": "Podwójny", "divider_color": "<PERSON><PERSON>", "divider_image": "<PERSON><PERSON><PERSON>", "divider_image_info": "Obraz powtarzający się poziomo. Zastępuje styl i kolor powyżej."}, "cart_actions": {"label": "Działania wózka z szufladami", "option_1": "Pokaż przycisk \"Wyświetl koszyk\"", "option_2": "Pokaż przycisk \"Do kasy\"", "option_3": "Pokaż oba"}, "sticky_atc": {"label": "Lepkie dodanie do koszyka", "enable_sticky_atc": "Włącz lepkie dodawanie do koszyka", "sticky_atc_style_wide": "Wide", "sticky_atc_style_floating": "Floating"}, "performance": {"header": "SEO i wydajność", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Wstępne ładowanie linków po najechaniu", "info": "Zwiększa postrzeganą szybkość ładowania stron."}, "recently_viewed": {"enable_recently_viewed_products": "Włącz niedawno przeglądane produkty", "enable_recently_viewed_products_info": "<PERSON> włą<PERSON>, motyw będzie rejestrował przeglądane produkty, ale musisz dodać sekcję w swoim sklepie, aby wy<PERSON>ć te produkty.", "recently_viewed_products": "Niedawno przeglądane produkty", "recently_viewed_products_info": "Ta sekcja musi mieć włączoną funkcjonalność w Ustawieniach Motywu. Zostanie wyświetlona tylko po tym, jak użytkownicy odwiedzą przynajmniej jedną stronę produktu.", "recently_viewed_products_limit": "Limit niedawno przeglądanych produktów"}, "rating_apps_update": {"label": "Aplikacja do oceniania", "info": "Aplikacje od osó<PERSON> trzecich mogą wymagać dodatkowych kroków do właściwej integracji."}, "local-220": {"preorder": "Pokaż etykietę przycisku \"Przedpremierowe zamówienie\"", "autorotate": {"heading": "Auto-rotacja", "info": "Automatyczne przewijanie slajdów.", "enable": "Włącz auto-rotację", "interval": "Interwał", "pause_on_mouseover": "Pauza przy najechaniu my<PERSON>ą"}}, "custom-social-icons": {"header": "Niestandardowy link", "info": "Prześlij niestandardową ikonę dla swojej ulubionej sieci społecznościowej", "icon": {"label": "<PERSON><PERSON><PERSON>", "info": "72 x 72px przezroczysty .png"}, "link": {"label": "Link"}}, "local-march-update": {"labels": {"dynamic_content": {"name": "Z<PERSON><PERSON><PERSON><PERSON><PERSON>", "hide_block": "<PERSON><PERSON><PERSON><PERSON>, je<PERSON><PERSON> nie jest obecna", "hide_section": "<PERSON><PERSON><PERSON><PERSON>, je<PERSON><PERSON> z<PERSON> nie jest obecna"}, "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards": "<PERSON><PERSON><PERSON>", "heading": "Nagłówek", "buttons_custom": "Niestandardowe kolory przycisków", "center_heading": "Wyśrodkuj nagłówek", "section_design": "Projekt sek<PERSON>ji", "bottom_margin": "Usuń margines dolny", "text_spacing": "Odstępy w tekście", "inherit_card_design": "Dziedzicz właściwości projektu karty", "align_button": "Wyrównaj przycisk zakupu do dolnej części karty", "custom_colors": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>"}, "shadows": {"label": "Cień", "label_plural": "<PERSON><PERSON><PERSON>", "offset_x": "Przesunię<PERSON> poziome", "offset_y": "Przesunięcie pionowe", "blur": "Roz<PERSON><PERSON>", "hide": "<PERSON>k<PERSON>j <PERSON>", "hide_button_shadows": "Ukryj cienie przycisków"}, "blocks": {"countdown_timer": {"name": "<PERSON><PERSON><PERSON>", "label": "Źródło <PERSON><PERSON>ne", "info": "Ustaw dynamiczne źródło czasu dla zegara odliczającego. [Dow<PERSON>z się więcej](https://shopify-support.krownthemes.com/article/642-product-pages#countdown-timer)"}, "progress_slider": {"name": "Wykres słupkowy postępu", "value": "<PERSON><PERSON><PERSON><PERSON>", "height": "Slider height", "width": "Slider width", "dynamic_content": {"info": "Use dynamic sources to define unique values by creating product metafields for the progress chart. [Learn more](https://shopify-support.krownthemes.com/article/804-product-page#Progress-chart-S8c6R)", "slider_label": "Value metafield", "dots_label": "Highlighted dots metafield"}}, "progress_dots": {"name": "Wykres punktowy postępu", "highlight": "Wyróżnione punkty", "total": "Łączna liczba punktów", "icon": "Dot icon", "size": "Dot size", "inactive_color": "Inactive color", "active_color": "Highlighted color"}, "store_selector": {"default": "Domyślnie do pierwszego sklepu"}, "rating": {"app": "Aplikacja recenzji", "default_option": "Domy<PERSON><PERSON><PERSON>"}, "space": {"name": "Pusta przestrzeń"}, "badges": {"name": "Plakietki produktów"}, "nutritional": {"name": "Informacje żywieniowe", "label_first": "Etykietka pierwszej kolumny", "label_second": "Etykietka drugiej kolumny", "label_third": "Etykietka trzeciej kolumny", "information": {"label": "Informacje", "info": "Oddziel etykietkę i wartość przecinkiem. Używaj podziałów wiersza, aby dodać nowy wiersz. Używaj myślnika do wcięcia wierszy. [Dowiedz się więcej](https://shopify-support.krownthemes.com/article/804-product-page#Nutritional-information-ypnLL)"}, "extra_information": "Dodatkowe informacje"}}, "sections": {"progress_sliders": {"name": "Wykresy słupkowe postępu", "block_name": "Słupek"}, "header": {"settings": {"promotion": {"header_1": "Promocja 1", "header_2": "Promocja 2", "header_3": "<PERSON><PERSON><PERSON><PERSON>", "show": "Po<PERSON><PERSON> promocję", "image": "<PERSON><PERSON>z promocji", "text": "Tekst promocji", "width": "Szerokość kolumny"}}}}}, "settings_schema": {"exit_intent": {"exit_intent_popup": "Popup zamiaru wyjścia", "exit_intent_popup_info": "Ta sekcja działa tylko na komputerach stacjonarnych"}, "colors": {"name": "<PERSON><PERSON><PERSON>", "settings": {"header__1": {"content": "Panel boczny"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__3": {"content": "Stopka"}, "bg_color": {"label": "Tło"}, "txt_color": {"label": "Tekst"}, "link_color": {"label": "<PERSON><PERSON>"}}}, "typography": {"name": "Typografia", "settings": {"headings_font": {"label": "Nagłówki"}, "base_size": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> podstawy"}, "large_size": {"label": "Duże nagłówki", "info": "Ma wpływ na duże tytuły z suwaka, tekst sformatowany i obraz z sekcjami tekstowymi."}, "body_font": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "nav_size": {"label": "Nawigacja główna"}}}, "product-grid": {"name": "Siatka produktu", "settings": {"aspect_ratio": {"label": "Proporcje multimediów"}, "show_secondary_image": {"label": "Pokaż dodatkowe multimedia produktu po najechaniu"}, "enhance_featured_products": {"label": "Wyeksponuj wyróżnione produkty", "info": "[Learn more](https://shopify-support.krownthemes.com/article/832-product-card)"}, "show_discount": {"label": "Pokaż zniżkę jako...", "options__1": {"label": "Tekst"}, "options__2": {"label": "Procent"}}, "caption_placement": {"label": "Umiejscowienie podpisu", "options__1": {"label": "Nakładka", "group": "Widoczny po najechaniu"}, "options__2": {"label": "Pod obrazem", "group": "<PERSON><PERSON><PERSON> widoczne"}}, "grid_color_bg": {"label": "<PERSON>ło podpisu nakładki"}, "grid_color_text": {"label": "<PERSON><PERSON> tekstu podpisu nakładki"}, "header__1": {"content": "<PERSON>cena produktu", "info": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> o<PERSON>, dodaj aplikację do oceniania produktu. [Learn more](https://apps.shopify.com/search?q=product%20reviews)"}, "header__2": {"content": "Recenzje produktu"}, "show_reviews": {"label": "Pokaż ocenę"}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "<PERSON><PERSON><PERSON>", "info": "Wymagany plik PNG o wymiarach 48 x 48 px"}}}, "cart-page": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "Typ k<PERSON>zy<PERSON>", "options__1": {"label": "Strona"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "cart_notes": {"label": "Włącz uwagi w koszyku"}, "cart_buttons": {"label": "Pokaż dodatkowe przyciski kasy"}}}, "embellishments": {"name": "Ozdobniki", "settings": {"show_preloader": {"label": "Wskaźnik wczytywania obrazu", "info": "Pokazuje niewielki okrągły wskaźnik, gdy obrazy w sklepie są wczytywane."}, "show_breadcrumb": {"label": "Pokaż menu okruszkowe", "info": "Nawigacja okruszkowa pomaga użytkownikom poruszać się po sklepie i pokazuje się tylko na stronach kolekcji, produktu oraz wyszukiwania."}, "show_go_top": {"label": "Pokaż przycisk „idź do góry”"}}}, "search": {"name": "Wyszukaj", "settings": {"predictive_search": {"label": "Włącz wyszukiwanie predykcyjne"}, "show_vendor": {"label": "Pokaż sprzedawcę"}, "show_price": {"label": "Pokaż cenę"}, "include_articles": {"label": "Uwzględnij artykuły w wynikach wyszukiwania"}, "include_pages": {"label": "Uwzględnij strony w wynikach wyszukiwania"}}}, "social": {"name": "Media społecznościowe"}, "follow_on_shop": {"content": "<PERSON><PERSON>er<PERSON><PERSON> w Shop", "info": "Aby um<PERSON><PERSON><PERSON><PERSON><PERSON> klientom śledzenie Twojego sklepu w aplikacji Shop z poziomu Twojej witryny sklepu, na<PERSON><PERSON><PERSON> włączyć funkcję Shop Pay. [Dowiedz się więcej](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "label": "Włącz opcję Obserwuj w <PERSON>"}, "labels": {"hide_block_if_no_content_info": "<PERSON><PERSON><PERSON><PERSON>, je<PERSON><PERSON> tre<PERSON> nie jest zdefiniowana", "popup_page_info": "<PERSON>ast<PERSON><PERSON><PERSON> t<PERSON> te<PERSON>, gdy strona jest wybrana", "page": "Strona", "popup": "Popup", "open_popup": "<PERSON><PERSON><PERSON><PERSON><PERSON> popup"}}, "sections": {"main-404": {"name": "Strona główna 404"}, "main-gift-card": {"name": "<PERSON><PERSON> podaru<PERSON>"}, "main-page": {"name": "Strona główna"}, "refactor_words": {"seo": {"name": "SEO", "label": "Znacznik nagłówka", "info": "Określ poziom nagłówka, aby pomóc wyszukiwarkom w indeksowaniu struktury strony.", "microdata": {"label": "Wyłącz mikrodane (schema)", "info": "Spowoduje to usunięcie znaczników schema.org ze strony. Wyłącz tylko wtedy, gdy korzystasz z aplikacji innej firmy do SEO!"}}, "late_edits": {"image_with_hotspots": {"image_on_mobile": "Obraz na urządzeniu mobilnym", "position_on_mobile": "Pozycja w wersji mobilnej", "hotspot": {"mobile_info": "Tylko w przypadku ustawienia obrazu dla wersji mobilnej"}}, "product-card": {"thumbnails": {"border": "<PERSON><PERSON> obramowania multimediów"}}, "labels": {"optional": "Opcjonalnie"}, "before-after": {"layout": {"invert": "Odwróć układ na urządzeniach mobilnych"}}}, "labels": {"footer_group": "Grupa stopki", "header_group": "Grupa nagłówka", "overlay_group": "Grupa <PERSON>", "embellishments": "Ozdobniki", "show_button": "Show button", "optional": "Optional"}, "text": {"icons_info": "<PERSON><PERSON> zob<PERSON>zy<PERSON> i pobrać więcej ikon, użyj [tego linku](https://resources.krownthemes.com/icons/)."}, "borders": {"top_border": "<PERSON><PERSON><PERSON>", "bottom_border": "<PERSON><PERSON><PERSON> obramowan<PERSON>", "show_border": "Show border"}, "colors": {"heading_background": "Tło nagłówka", "shadow": "Pokazuj cień"}, "social": {"phone": "Nr telefonu", "discord": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"image_with_hotspots": {"label": "Obraz z aktywnymi punktami", "hotspot": {"label": "Aktywny punkt", "label_desktop_offset": "Aktywny punkt na desktopie", "label_mobile_offset": "Aktywny punkt na urządzeniu przenośnym", "offset_horizontal": "Przesunię<PERSON> poziome", "offset_vertical": "Przesunięcie pionowe", "tooltip": {"label": "Etykieta", "position": {"label": "Położenie", "option_1": "Góra", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "<PERSON> lewe<PERSON>", "option_4": "Po prawej"}, "show_tooltip": "Show tooltip"}, "mobile_info": "Only if a mobile image is set"}, "image_on_mobile": "Mobile image", "position_on_mobile": "Position on mobile"}, "scrolling_images": {"label": "Przewijanie obrazów", "image_size": "<PERSON><PERSON><PERSON><PERSON>", "columns": "Columns"}, "video": {"label": "Wideo", "info": "Wymagany format MP4, bez dźwięku"}, "variants_functionality": {"label": "Zarządzanie niedostępnymi wariantami", "option_1": "<PERSON><PERSON><PERSON><PERSON>", "option_2": "Wyłącz", "option_3": "Po<PERSON><PERSON>"}, "auto_height": {"label": "Automatyczna wysokość", "info_slider": "Zaznaczenie tej opcji spowoduje zastąpienie ustawień wysokości powyżej i sprawi, że wysokość pokazu slajdów będzie odpowiadać obrazowi w każdym slajdzie."}}, "header": {"promotion_block": {"image_link": "<PERSON> o<PERSON><PERSON> promo<PERSON>"}, "sticky": {"label": "Przyklejony nagłówek", "option_1": "Wyłączone", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Tylko przy przewijaniu w górę"}}, "inventory": {"name": "Poziom zapasów", "settings": {"show_progress_bar": "Pokaż pasek postępu", "low_inventory_threshold": "Próg niskiego poziomu z<PERSON>sów", "show_block": {"always": "Zawsze wyświetlaj", "low": "Wyświet<PERSON><PERSON> tyl<PERSON> w<PERSON>y, gdy poziom zapasów spada poniżej progu"}}}, "breadcrumb": {"name": "<PERSON><PERSON><PERSON><PERSON>", "info": "Nawigacja okruszkowa nie pojawia się na stronie głównej"}, "announcement-bar": {"visibility": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "option_1": "Wszystkie strony", "option_2": "<PERSON><PERSON><PERSON> strona główna", "option_3": "Wszystkie strony oprócz strony głównej", "option_4": "Tylko strony produktów", "option_5": "<PERSON><PERSON><PERSON> strona koszyka"}, "color": {"border": "<PERSON><PERSON>"}}, "promotional_banner": {"name": "<PERSON><PERSON>", "enable": "<PERSON><PERSON><PERSON> baner"}, "cookies_banner": {"name": "Pliki cookie", "enable": "Pokazuj informacje o plikach cookie"}, "before_after": {"name": "Porównanie obrazów", "layout": {"label": "Layout", "option__1": "Poziomo", "option__2": "<PERSON><PERSON><PERSON>"}, "style": {"label": "Styl kolorystyczny", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "Ciemny"}, "image": {"label__1": "<PERSON><PERSON><PERSON>", "label__2": "Obraz na urządzeniu mobilnym", "label__3": "Etykieta"}}, "cart_upsell": {"name": "Indywidualne rekomendacje produktów", "product": "Produkt", "info": "Dynamiczne rekomendacje opierają się na produktach w koszyku. Zmieniają się i poprawiają z czasem. [Dowiedz się więcej](https://help.shopify.com/en/themes/development/recommended-products)"}, "gift_wrapping": {"name": "Pak<PERSON><PERSON> pre<PERSON>", "info": "Pakowanie prezentu należy skonfigurować jako produkt. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/696-cart-features#gift-wrap)", "settings": {"label": "Tekst", "button": "Etykietka przycisku"}}, "custom_code": {"name": "Niestandardowy kod HTML / Liquid"}, "rating": {"name": "Aplikacja recenzji", "default": "Domy<PERSON><PERSON><PERSON>"}, "product-page": {"size_guide": {"label": "Tabela rozmiarów", "page": "Strona tabeli rozmiarów", "options": {"label": "Ot<PERSON><PERSON><PERSON> op<PERSON>", "option_1": "Wyskakują<PERSON> okienko", "option_2": "To samo okno", "option_3": "Nowe okno"}}, "gallery_resize": {"label": "Proporcje obrazów", "info": "Filmy i inne rodzaje multimediów będą wyświetlane w oryginalnych proporcjach.", "option_1": "Dopas<PERSON>j obrazy wewną<PERSON> konte<PERSON>a"}, "gallery_padding": {"label": "Odstęp wewnętrzny galerii"}, "gallery_background": {"label": "<PERSON><PERSON><PERSON>i", "info": "<PERSON><PERSON><PERSON><PERSON> tylko wtedy, gdy obrazy mają być dopasowane wewnątrz kontenera."}}, "product-card": {"name": "Karta produktu", "labels": {"thumbnail": "Miniaturka produktu", "caption": "Podpis produktu", "color_swatches": "Próbki kolorów"}, "thumbnails": {"fit": "Dopasuj multimedia wewnątrz kontenera", "padding": {"label": "Odstęp wewnętrzny kontenera", "info": "Dzia<PERSON> tylko w<PERSON>y, gdy multimedia mają być dopasowane wewnątrz kontenera."}, "background": {"label": "<PERSON><PERSON><PERSON> konte<PERSON>", "info": "<PERSON>ido<PERSON>ne tylko wtedy, gdy <PERSON> mają być dopasowane wewnątrz kontenera."}, "border": "<PERSON><PERSON>", "color_swatches": "Pokazuj próbki kolorów na karcie produktu", "color_swatches_on_hover": "Pokazuj próbki kolorów na karcie produktu (po najechaniu)"}, "color_swatches_label": {"label": "Etykietki próbek kolorów", "info": "<PERSON><PERSON>z tytuły wielu wariantów (oddzielone przecinkami), które mają stać się próbkami kolorów."}, "badges": {"name": "Plakietki produktów", "show_badges": "Pokaż plakietki", "settings": {"colors": {"text": "<PERSON><PERSON> te<PERSON> p<PERSON>", "sold_out": "<PERSON><PERSON> tła „Wyprzedane”", "sale": "<PERSON><PERSON> tła „Zniżka”"}}, "badge_sale": {"name": "Plakietka zniżki", "amount_saved": "Zaoszczędzona kwota"}, "regular_badges": {"info": "Dowiedz się więcej o oznaczeniach produktów [tutaj](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)."}, "sold_out": {"name": "Oznaczenie „Wyprzedane”", "text_color": "Kolor tekstu „Wyprzedane”", "sale_text": "Kolor tekstu „Zniżka”"}, "custom_badges": {"name": "Niestandardowe plakietki produktów", "info": "Ten motyw wykorzystuje niestandardowe oznaczenia produktów, które można zdefiniować tutaj. [Dow<PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/564-product-pages#product-badges)", "split_info": "Ten motyw wykorzystuje niestandardowe oznaczenia produktów, które można zdefiniować tutaj. [Dow<PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)", "name__1": "Niestandardowa plakietka 1", "name__2": "Niestandardowa plakietka 2", "name__3": "Niestandardowa plakietka 3", "text": "Tekst", "tags": "Tag", "color": "<PERSON><PERSON>", "text_color": "<PERSON><PERSON>", "border_color": "<PERSON><PERSON>"}}}, "seo_late": {"breadcrumbs": {"label": "Improve product breadcrumb navigation", "info": "Adds collection names to the breadcrumb navigation, by duplicating product url's. [Learn more](https://shopify-support.krownthemes.com/article/697-seo#breadcrumbs)"}}}, "local-extra-words": {"settings_schema": {"colors": {"headings": {"header": "Nagłówek", "cards": "<PERSON><PERSON><PERSON>"}, "settings": {"borders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hide_border": "<PERSON><PERSON><PERSON><PERSON>", "accent": "<PERSON><PERSON><PERSON>", "overlay": "Overlay"}}, "typography": {"buttons": {"label": "Grubość czcionki przycisków", "option__1": "Normalna", "option__2": "<PERSON><PERSON><PERSON><PERSON>"}, "menus": {"header": "<PERSON><PERSON>", "size": "Rozmiar podstawowy", "weight": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "weight_bold": "Pogrubiona"}}, "borders": {"name": "Obramowania", "main": {"name": "<PERSON><PERSON><PERSON><PERSON>", "info": "To ustawienie kontroluje styl obramowań wszystkich sekcji w całym motywie."}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "forms": {"name": "Formularze"}, "settings": {"width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "radius": "Promień"}}, "layout": {"name": "<PERSON><PERSON><PERSON><PERSON>", "sections": {"vertical_space": "Przestrzeń pionowa między sekcjami", "remove_vertical_space": "Usuń górny margines", "remove_bottom_margin": "Usuń dolny margines"}, "grid": {"name": "Siatka", "info": "Wpływa na obszary z układem wielokolumnowym.", "horizontal_space": "Przestrzeń pozioma", "vertical_space": "Przestrzeń pionowa"}}, "cart": {"shipping": {"name": "Wysyłka", "show": {"label": "Pokaż minimalną kwotę bezpłatnej wysyłki", "info": "<PERSON><PERSON> skonfigurow<PERSON>ć koszty wysyłki, przej<PERSON><PERSON> do [ustawień wysyłki](/admin/settings/shipping)."}, "amount": {"label": "Minimalna kwota bezpłatnej wysyłki", "info": "Wpisz liczbę, bez liter i znaków specjalnych."}, "info": "This feature only works with a single currency and shipping method. If you're using multiple shipping methods or currencies, it might show false values to the customer."}, "settings": {"media": {"header": "Media aspect ratio", "label": "Aspect ratio", "info_cart": "Changes the aspect ratio for small product thumbnails used in the cart, search popup and some other areas that require small product thumbnails."}}}, "aspect_ratio": {"landscape": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (3:2)"}}, "maps": {"name": "Mapy"}, "search": {"predictive_search": {"name": "Wyszukiwanie predykcyjne", "info": "Wyszukiwanie predykcyjne obsługuje sugestie dotyczące produktów, kolekcji, stron i artykułów."}}, "product-card": {"name": "Karta produktu", "title-size": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> tytułu", "options__1": "Ma<PERSON><PERSON>", "options__2": "<PERSON><PERSON><PERSON>"}, "local-pickup": {"name": "<PERSON><PERSON><PERSON>", "info": "Ten motyw pokazuje lokalną dostępność produktów. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/805-collection-page)"}, "badges": {"name": "Domyślne plakietki produktów", "settings": {"colors": {"text": "<PERSON><PERSON> te<PERSON> p<PERSON>", "sold_out": "<PERSON><PERSON> tła „Wyprzedane”", "sale": "<PERSON><PERSON> tła „Zniżka”"}}, "badge_sale": {"name": "Plakietka zniżki"}, "custom_badges": {"name": "Niestandardowe plakietki produktów", "info": "Ten motyw używa niestandardowych plakietek produktów, które możesz zdefiniować tutaj. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/795-product-card#Product-badges-_KIdK)", "name__1": "Niestandardowa plakietka 1", "name__2": "Niestandardowa plakietka 2", "name__3": "Niestandardowa plakietka 3", "text": "Tekst", "tags": "Tag", "color": "<PERSON><PERSON>", "text_color": "Text color"}}, "icons_list": "Lista ikon dynamicznych", "products-list": {"name": "Products list"}}, "embellishments": {"settings": {"show_gotop": {"label": "Show go to top button"}}}}, "sections": {"video": {"name": "Wideo", "settings": {"video": {"label": "Adres URL wideo"}, "image": {"label": "<PERSON><PERSON>z tła"}}}, "contact-form": {"settings": {"form-fields": {"name": "Pola formularza", "show-phone": "Pokaż telefon", "show-subject": "<PERSON><PERSON><PERSON> temat"}}, "blocks": {"contact-info": {"name": "<PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}}}, "text-columns-with-icons": {"blocks": {"icon": {"name": "Niestandardowa ikona", "info": "256 x 256 px"}, "select_icon": {"info": "A<PERSON> wyświetlić i pobrać więcej ikon, wejdź pod [ten link](https://resources.krownthemes.com/icons/)"}, "icon_color": {"label": "<PERSON><PERSON><PERSON>", "info": "Działa tylko dla uwzględnionych ikon"}}}, "content-toggles": {"name": "Przełączniki zawartości", "block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text-columns-with-images": {"blocks": {"image": {"info": "640 x 640 px"}}}, "announcement-bar": {"settings": {"social": {"header": "<PERSON><PERSON><PERSON> społecznościowe", "info": "<PERSON><PERSON> s<PERSON> profile s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ciowe, wejdź w Ustawienia motywu > Społecznościowe.", "label": "Pokaż ikony społecznościowe"}}, "blocks": {"content": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"text": "Tekst", "link": "Link", "target": "Otwórz link w nowym oknie"}}}}, "newsletter": {"show_icon": "Po<PERSON><PERSON> ikon<PERSON>"}, "cookies": {"name": "Wyskakujące okienko plików cookie", "cookies_info": "Ta strona wykorzystuje pliki cookie, aby z<PERSON><PERSON><PERSON> najlepsze wrażenia użytkownika. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/790-cookies-popup)."}, "popups": {"name": "Wyskakujące okienka", "blocks": {"model": {"model-1": "Pliki cookie", "model-2": "Newsletter", "model-3": "Niestandardowe"}, "settings": {"size": {"label": "Wielkość okienka", "option_1": "Małe", "option_2": "<PERSON><PERSON><PERSON>"}}}}, "age-verification": {"name": "Weryfika<PERSON>ja wieku", "settings": {"button-text": "Tekst przycisku"}}, "stores-map": {"name": "Mapa sklepów", "settings": {"map": {"title": "Mapa"}, "gallery": {"title": "<PERSON><PERSON> sklepu"}}}, "store-selector": {"name": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "settings": {"map": {"label": "Włącz mapę dynamic<PERSON>ą", "info": "Upewnij się, że klucz API Map Google jest prawidłowo skonfigurowany w ustawieniach motywu"}, "zoom": {"label": "Powiększenie mapy", "info": "<PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON>, aby z<PERSON><PERSON><PERSON><PERSON> jednocześnie wszystkie pożądane sklepy."}}, "blocks": {"map": {"name": "Lokalizacja mapy", "settings": {"address": {"label": "<PERSON><PERSON>", "info": "Dowiedz się więcej"}, "image": {"label": "<PERSON><PERSON><PERSON>", "info": "Prześ<PERSON><PERSON> obraz statyczny, jeśli nie chcesz używać mapy dynamicznej."}, "style": {"label": "Styl mapy", "option__1": "Standardowa", "option__2": "Srebrna", "option__3": "Retro", "option__4": "Ciemna", "option__5": "Nocna", "option__6": "Bakłażanowa"}, "pin": {"label": "Niestandardowa pinezka mapy", "info": "240 x 240 px, prz<PERSON><PERSON><PERSON><PERSON><PERSON>, .png"}}}, "store": {"name": "<PERSON>kle<PERSON>", "settings": {"name": {"label": "Nazwa", "info": "Nazwa sklepu musi odpowiadać nazwie sklepu zdefiniowanej w [ustawieniach lokalizacji](/admin/settings/locations)"}, "pickup_price": {"label": "<PERSON><PERSON>"}, "pickup_time": {"label": "Czas odbioru"}, "address": {"label": "Szczegóły sklepu"}, "image": {"label": "<PERSON><PERSON><PERSON> skle<PERSON>"}, "closing_times": {"label": "<PERSON><PERSON><PERSON>ę<PERSON> (opcjonalne)", "info": "Dodaj 7 w<PERSON><PERSON>, jeden na każdy dzień tygodnia, począwszy od niedzieli."}, "timezone": {"label": "<PERSON><PERSON><PERSON>", "info": "Używana do prawidłowego pokazywania godzin zamknięcia"}, "map": {"name": "Pinezka mapy", "info": "<PERSON><PERSON><PERSON> mapa jest wł<PERSON><PERSON><PERSON>, musisz zdefiniować niestandardową pinezkę dla tego adresu. [<PERSON><PERSON><PERSON> się, jak uzyskać współrzędne swojego adresu](https://support.google.com/maps/answer/18539?hl=en)"}, "map_latitude": {"label": "Szerokość geograficzna", "info": "Współrzędna szerokości geograficznej dla znacznika. Przykład: 46.7834818"}, "map_longitude": {"label": "Długość geograficzna", "info": "Współrzędna długości geograficznej dla znacznika. Przykład: 23.5464733"}, "get_directions_button": {"label": "Pokaż przycisk „Wskazówki dojazdu”", "info": "Otwiera większą mapę w nowej karcie przeglądarki."}, "map_pin": {"label": "Niestandardowa pinezka", "info": "90 x 90 px, prz<PERSON><PERSON><PERSON><PERSON><PERSON>, .png"}}}}}, "header": {"settings": {"layout": {"label": "Układ nagłówka", "info": "Wpływa na położenie niestandardowych bloków i domyślnych akcji", "option__1": "Niestandardowe bloki na górze, domyślne akcje na dole", "option__2": "Domyślne akcje na górze, niestandardowe bloki na dole"}, "sticky": {"label": "Przyklejony nagłówek", "info": "Pokazuje menu nawigacji, kiedy użytkownik przewija w górę"}}, "blocks": {"info": {"name": "Informacje", "style": {"label": "<PERSON><PERSON>", "option__1": "Informacje tekstowe", "option__2": "Przycisk", "info": "Na przyciskach widoczny jest tylko nap<PERSON>, jako etykietka przycisku."}, "custom-icon": {"label": "Niestandardowa ikona", "info": "Prześlij obraz 76 x 76 px w formacie .png"}, "icon": {"label": "<PERSON><PERSON><PERSON>"}, "link_type": {"label": "Otwórz link", "option__1": "W oknie modalnym", "option__2": "Na tej samej stronie", "option__3": "<PERSON> <PERSON><PERSON> stronie", "info": "Okna modalne działają tylko z linkami wewnętrznymi stron"}}, "store-selector": {"name": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "content": "Selektor sklepu można skonfigurować w sekcji Selektor sklepu. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/799-store-selector)", "info": "Ten motyw pozwala połączyć rzeczywiste lokalizacje sklepowe z interaktywnym selektorem sklepu. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/799-store-selector)"}, "mega-menu": {"name": "Rozbudowane menu", "settings": {"menu_handle": {"label": "Nazwa menu", "info": "Ten motyw używa rozbudowanych menu. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/798-header-section#mega-menus)"}}}}}, "marquee": {"name": "Tekst przewijania", "settings": {"scroll_direction": "Kierunek przewijania", "scroll_speed": "Szybkość przewijania", "scroll_speed_info": "<PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON>, tym wolniej<PERSON>e przewijanie", "pause_on_mouseover": "Zatrzymaj po najechaniu my<PERSON>", "scroll_item": "Element przewijania", "scroll_item_text": "Tekst przewijania"}}, "image-section": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image_size": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> desktopa", "info": "Na urządzeniu mobilnym obraz będzie w pełnej szerokości."}}}, "media-with-text-overlay": {"name": "Multimedia z nakładką tekstową", "blocks": {"media": "Multimedia", "image": {"name": "<PERSON><PERSON><PERSON>"}, "link": {"info": "Tytuł zmieni się w link, chyba że przycisk będzie miał etyk<PERSON>ę"}, "video": {"name": "Wideo", "label": "Adres URL pliku wideo MP4", "info": "Obraz powyżej pokaże się, jeśli nie można odtworzyć pliku wideo."}}, "settings": {"height": "<PERSON><PERSON><PERSON><PERSON>ć karty", "option__1": "<PERSON><PERSON>", "option__2": "<PERSON>ż<PERSON>", "option__3": "<PERSON><PERSON><PERSON>", "option__4": "Pełny ekran", "option__5": "Normalna"}}, "blog-posts": {"settings": {"emphasize": {"label": "Wyróż<PERSON><PERSON>ws<PERSON>", "info": "Tylko na desktopie"}}, "blocks": {"summary": {"name": "Fragment", "settings": {"excerpt_limit": "Liczba słów", "excerpt_limit_info": "<PERSON>, je<PERSON>li nie dodano fragmentu ręcznie w panelu administracyjnym."}}}}, "testimonials": {"name": "Opinie", "blocks": {"name": "<PERSON><PERSON><PERSON>"}}, "slideshow": {"name": "Pokaz slajdów", "block": {"name": "<PERSON><PERSON><PERSON>"}, "settings": {"caption_size": "<PERSON><PERSON><PERSON><PERSON><PERSON> podpisu"}}, "rich-text": {"settings": {"image_position": {"label": "Położenie obrazu", "option__1": "<PERSON> lewe<PERSON>", "option__2": "Nad tekstem", "option__3": "Po prawej"}, "fullwidth": {"label": "<PERSON><PERSON>ł<PERSON>", "info": "Rozciągnij tło tej sekcji, aby wypełnić ekran."}, "height": {"label": "<PERSON><PERSON><PERSON><PERSON>ć karty", "info": "Minimalna wysokość karty na desktopie. Na urządzeniu mobilnym wysokość będzie zależała od zawartości."}, "crop": {"label": "<PERSON><PERSON>zar wypełnienia obrazu", "info": "Obraz zostanie przycięty, aby wypełnić całą wysokość karty na desktopie. Na urządzeniu mobilnym obraz będzie zawsze pokazywany w całości."}, "remove_margin": {"label": "Usuń górny margines sekcji"}}}, "main-header": {"settings": {"mobile": {"name": "<PERSON><PERSON><PERSON><PERSON> mobilna", "info": "Wpływa tylko na widoczność w mobilnym menu nawigacyjnym.", "header_actions": "Pokaż selektor sklepu i bloki informacji", "header_info_blocks": {"header": "Bloki informacji nagłówka", "label_1": "Wyświetl selektor sklepu i bloki informacji w nagłówku na urządzeniach mobilnych", "label_2": "Umieść bloki informacji na górze pierwszej sekcji na stronie głównej", "label_2_info": "Integruje się dobrze, kiedy pierwsza sekcja jest pokazem slajdów o pełnej szerokości"}}, "promotion_block": {"title": {"label": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON><PERSON><PERSON> tytułu"}, "subtitle": {"label": "Podtytuł", "size": "<PERSON><PERSON><PERSON><PERSON><PERSON> pod<PERSON>tuł<PERSON>"}, "button": {"label": "Etykietka przycisku", "size": "Wielkość przycisku", "link": "<PERSON>", "style": "<PERSON><PERSON> przycis<PERSON>"}}, "header_actions": {"header": "Bloki informacji nagłówka na urządzeniu mobilnym", "show_in_drawer": "Pokaż w menu nawigacyjnym"}}}, "main-cart": {"blocks": {"shipping-calculator": {"name": "Ka<PERSON>ulator wysyłki"}, "related-products": {"info": "The product recommendations are based on products that are commonly purchased together or products in related collections. If there are no recommendations, the block will not appear."}}}, "main-search": {"settings": {"blogs": {"name": "Wyniki artykułu"}, "products": {"name": "Wyniki produktu", "info": "Zawartość karty produktu musi być ustawiona za pomocą bloków sekcji."}}}, "main-product": {"name": "Strona produktu", "settings": {"gallery_pagination": "<PERSON><PERSON><PERSON><PERSON> galerii", "show_border": "Pokaż obramowanie wokół galerii", "gallery_border": "Gallery border", "gallery_border_color": "Gallery border color"}, "blocks": {"pickup_availability": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odbioru", "info": "Ten motyw pokazuje dostępność odbioru na podstawie wybranego sklepu. Dowiedz się więcej", "settings": {"style": "<PERSON><PERSON>", "option__1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "option__2": "Rozszerzony"}}, "buy_buttons": {"settings": {"show_price": "Pokaż cenę"}}, "related": {"name": "Powiązane produkty", "settings": {"products": "Produkty"}}, "tax_info": {"name": "Informacje podatkowe"}, "icons": {"name": "Lista ikon", "info": "A<PERSON> wyświetlić i pobrać ikony uwzględnione w tym motywie, wejdź pod [ten link](https://resources.krownthemes.com/icons/).", "help": "Ten motyw pozwala dodawać niestandardowe ikony produktu poprzez zawartość dynamiczną. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "headers": {"icon_1": "Ikona 1", "icon_2": "Ikona 2", "icon_3": "Ikona 3", "icon_4": "Ikona 4", "icon_5": "Ikona 5", "icon_6": "Ikona 6"}, "settings": {"icon": "<PERSON><PERSON><PERSON>", "icon_info": "96 x 96 px", "label": "Etykietka"}}}}, "main-blog": {"name": "Główny blog"}, "main-article": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_tags": "Pokaż tagi", "enhance_product_links": {"label": "Ulepsz linki produktów", "info": "Wszystkie linki do produktów będą otwierać okno modalne szybkiego zakupu produktu."}}}, "main-article-comments": {"name": "<PERSON><PERSON><PERSON><PERSON>ł<PERSON>", "info": "<PERSON><PERSON> wł<PERSON><PERSON><PERSON>ć komentarze, przej<PERSON><PERSON> do [ustawień bloga](/admin/blogs)."}, "main-article-navigation": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"header": {"content": "Posty na blogu", "info": "Po<PERSON><PERSON><PERSON> puste, je<PERSON><PERSON> chcesz wczytać domyślny poprzedni lub kolejny post na blogu. "}, "posts": {"next": "Kolejny post", "previous": "Poprzedni post"}}}, "main-page": {"settings": {"center": {"label": "Wyśrodkuj zawartość na desktopie"}}}, "main-footer": {"blocks": {"payment": {"name": "Ikon<PERSON>", "info": "<PERSON><PERSON><PERSON>, kt<PERSON><PERSON> s<PERSON>, zale<PERSON>ą od [ustawie<PERSON> płat<PERSON>](/admin/settings/payments) sklepu oraz regionu i waluty klienta.", "custom_icons": "Custom icons", "custom_icons_info": "Upload 114 x 72px .png images", "only_show_custom_icons": "Only show the custom icons"}, "text": {"name": "Text & social icons"}}}, "customers": {"reset-password": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "order": {"name": "Strona zamówienia"}, "register": {"name": "Strona rejestracji"}, "activate-account": {"name": "Strona aktywacji konta"}, "login": {"name": "Strona logowania", "shop_login_button": {"enable": "Włącz logowanie przez Shop"}}, "account": {"name": "Strona konta"}, "addresses": {"name": "Ad<PERSON>y"}}, "headings": {"heading": "Nagłówek", "subheading": "Podpis pod nagłówkiem", "title": "<PERSON><PERSON><PERSON>", "subtitle": "Podtytuł", "caption": "Podpis", "text_content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "custom_colors": "Niestandardowe kolory", "text_style": "<PERSON><PERSON>"}, "columns": {"name": "Układ na desktopie", "info": "Uk<PERSON>d dostosowuje się do urządzeń mobilnych.", "option__0": "1 kolumna", "option__1": "2 kolumny", "option__2": "3 kolumny", "option__3": "4 kolumny", "option__4": "5 kolumn", "option__5": "6 kolumn", "name_mobile": "Mobile layout"}, "promotion-cards": {"name": "<PERSON><PERSON><PERSON> promo<PERSON>", "blocks": {"name": "Karta"}}, "faq": {"headings": {"header": "Nagłówek", "content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"form": {"header": "<PERSON><PERSON>", "show": "Pokaż formularz", "title": "<PERSON><PERSON><PERSON>"}}}, "product-quick-view": {"name": "Szybki podgląd", "info": "Ten szablon kontroluje sposób budowy szybkiego podglądu produktu. Tylko ta sekcja pojawi się w oknie modalnym."}, "product-card": {"blocks": {"price": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "vendor": "Sprzedawca", "text": {"name": "Tekst dynamiczny", "info": "Użyj <PERSON><PERSON><PERSON><PERSON><PERSON>z<PERSON>, aby w<PERSON><PERSON><PERSON><PERSON>ć unikalny atrybut, tworząc metapole produktu. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/805-collection-page)", "settings": {"text": {"label": "Metapole etykiety"}, "size": {"label": "Rozmiar <PERSON>", "option__1": "Ma<PERSON><PERSON>", "option__2": "Normalny", "option__3": "<PERSON><PERSON><PERSON>"}, "color": {"label": "<PERSON><PERSON>", "option__1": "Główny", "option__2": "Dodatkowy"}, "transform": {"label": "Przekształcenie tekstu (wielkie litery)"}}}, "icons": {"info": "Uży<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, aby w<PERSON><PERSON><PERSON><PERSON>ć unikalne atrybuty, tworząc metapola produktu dla listy ikon. [<PERSON><PERSON><PERSON> się więcej](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "settings": {"icon": "Metapole ikony", "label": "Metapole etykiety"}}, "quick_buy": "Szybki zakup", "rating": "Ocena"}}, "buttons": {"style": {"label": "<PERSON><PERSON> przycis<PERSON>", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "Pełny"}}}}, "complementary_products": {"name": "Produkty uzupełniające", "settings": {"paragraph": {"content": "<PERSON><PERSON> w<PERSON><PERSON> produkty uzupełniające, dodaj aplikację Search & Discovery. [<PERSON><PERSON><PERSON> się więcej](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}}}, "split-extra-words": {"settings_schema": {"colors": {"headings": {"header_sidebars": "Nagłówek i panele boczne", "main": "<PERSON><PERSON><PERSON><PERSON>", "footer": "Stopka", "custom_colors": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>"}, "settings": {"background": "Tło", "text": "Tekst", "links": "Aktywne linki", "borders": "Pokaż obramowania"}}, "typography": {"headings": {"headings": "Nagłówki", "body": "<PERSON><PERSON><PERSON><PERSON>", "logo_menus": "Logo i menu", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"font_family": "<PERSON><PERSON><PERSON>", "base_size": "<PERSON><PERSON><PERSON><PERSON><PERSON> podstawy", "line_height": "<PERSON><PERSON><PERSON><PERSON><PERSON> linii", "hr": {"label": "Pokaż linie poziome", "info": "Wyświetla niewielką wizualną linię poziomą na niektórych tytułach"}, "border_radius": "Zaokrąglenie rogów"}}, "embellishments": {"preloader": {"label": "Wskaźnik wczytywania multimediów", "info": "Pokazuje niewielki okrągły wskaźnik, gdy multimedia w sklepie są wczytywane."}, "breadcrumb": {"label": "Pokaż menu okruszkowe", "info": "Nawigacja okruszkowa pomaga użytkownikom poruszać się po sklepie i pokazuje się tylko na stronach kolekcji, produktu, wyszukiwania oraz kont."}}, "cart": {"page": "Produkty w koszyku", "show_recommendations": "Pokaż rekomendacje na podstawie koszyka"}, "headings": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Podtytuł"}, "product-grid": {"animation_style": {"label": "Wyświetlanie podpisu (desktop)", "options__1": "Widoczny", "options__2": "Nakładka", "info": "Na urządzeniach mobilnych podpis będzie zawsze widoczny dla większego komfortu użytkowania"}, "overlay_colors": {"background": "<PERSON>ło podpisu nakładki", "text": "Tekst podpisu nakładki"}, "aspect_ratio": {"label": "Proporcje multimediów produktu", "options__1": "Przycięte", "options__2": "Naturalne"}, "show_secondary_image": {"info": "Tylko na desktopie"}, "quick_buy": {"name": "Szybki zakup", "info": "Dodaje błyskawiczny przycisk „dodaj do koszyka”. Jeśli produkt ma odmiany, wyskoczy okienko „szybki zakup”.", "label": "Włącz szybki zakup"}, "rating": {"label": "Wyświetlanie o<PERSON> (desktop)", "options__1": "<PERSON><PERSON>", "options__2": "Pokaż po najechaniu", "options__3": "<PERSON><PERSON><PERSON> widoczne", "show_on_mobile": "Pokaż na urządzeniu mobilnym"}}}, "sections": {"header": {"name": "Nagłówek", "settings": {"logo_height": "Maksymalna wysokość obrazu logo", "menu": "<PERSON><PERSON>", "menu_style": {"label": "Styl menu na desktopie", "options__1": "Klasyczny", "options__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "collections_menu": {"header": "<PERSON><PERSON> k<PERSON>", "info": "Ma wyrazisty wygląd, zwłaszcza w przypadku klasycznego stylu menu, gdzie przekształca się w wielkie menu z możliwością dodania obrazów i promocji.", "settings": {"show_images": {"label": "Pokaż obrazy kole<PERSON>ji", "info": "Ma zastosowanie wyłącznie wtedy, gdy produkty nadrzędne stanowią kolekcję."}}}, "promotional_block": {"name": "Blok promocji", "settings": {"show": {"label": "Pokaż blok promocji", "info": "W stylu minimalistycznym pokazuje się na dole wysuwanego menu. W stylu klasycznym pokazuje się w menu kolekcji, jeśli są obecne."}, "title": {"label": "<PERSON><PERSON><PERSON> pro<PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> promocji"}, "button": {"label": "Etykietka przycisku promocji"}, "link": {"label": "Link przycisku promocji"}, "txt_color": {"label": "<PERSON><PERSON> te<PERSON> promoc<PERSON>"}, "bg_color": {"label": "<PERSON><PERSON> tła promocji"}, "image": {"label": "<PERSON><PERSON>z promocji"}}}, "announcement_bar": {"content": {"info": "Maks. 50 znaków"}}}}, "footer": {"blocks": {"menu": {"name": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}}}, "main-product": {"name": "Strona produktu", "settings": {"header": {"label": "Nagłówek produktu", "info": "Na urządzeniach mobilnych nagłówek produktu będzie zawsze pojawiał się u góry, nad galerią produktu.", "show_tax_info": "Pokaż informacje o podatku", "show_reviews": "Pokaż ocenę produktu", "show_sku": "Pokaż SKU", "show_barcode": "Pokaż KOD KRESKOWY", "show_vendor": "Pokaż sprzedawcę", "show_badge": "Pokaż plakietkę produktu"}, "variants": {"label": "Typ selektora odmian", "options__1": "Bloki", "options__2": "Rozwijany"}, "gallery_aspect": {"label": "<PERSON><PERSON><PERSON><PERSON> obra<PERSON>, aby pasowały do okna podglądu", "info": "Na urządzeniu mobilnym obrazy będą zawsze pasowały do okna podglądu urządzenia."}, "color_swatches": {"label": "Pokaż próbki kolorów (tylko dla stylu blokowego)", "info": "Ten motyw może pokazywać własne obrazy dla próbek kolorów. [Learn more](https://shopify-support.krownthemes.com/article/554-using-color-swatches)"}}}, "countdown-banner": {"name": "Baner z licznikiem", "settings": {"header": "<PERSON><PERSON><PERSON>", "show_countdown": "Pokaż zegar odliczający", "countdown_year": "Rok zakończenia", "countdown_month": "Miesiąc zakończenia", "countdown_day": "Dzień zakończenia", "countdown_hour": "Godzina zakończenia", "countdown_timezone": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON><PERSON><PERSON> banera"}}, "map": {"settings": {"map": {"api": {"label": "Klucz API Map Google", "info": "<PERSON><PERSON><PERSON> [Google Maps API Key](http://shopify-support.krownthemes.com/article/385-generate-an-api-key-for-google-maps) to display the map."}}}}}}, "main-cart-footer": {"name": "Suma częściowa koszyka", "blocks": {"subtotal_button": {"name": "Suma częściowa i kasa"}}}, "main-cart-items": {"name": "Elementy koszyka"}, "main-list-collections": {"name": "Strona listy kolekcji", "blocks": {"collection": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "image": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> ch<PERSON>z dodać własny obraz dla kolekcji."}}}}, "settings": {"header": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "layout": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON> k<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON> kol<PERSON>"}}, "paragraph": {"content": "Wszystkie kolekcje są domyślnie umieszczone na liście. Aby dostosować swoją listę, w<PERSON><PERSON><PERSON> „Zaznaczone” i dodaj kolekcje."}, "display_type": {"label": "Zaznacz kolekcje do pokazania", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Zaznaczone"}}, "sort": {"label": "So<PERSON><PERSON><PERSON> kole<PERSON>:", "info": "Sortowanie ma zastosowanie tylko wtedy, gdy zaz<PERSON>zono „Wszystko”", "options__1": {"label": "Alfabetycznie, A–Z"}, "options__2": {"label": "Alfabetycznie, Z–A"}, "options__3": {"label": "Data, od nowych do starych"}, "options__4": {"label": "Data, od starych do nowych"}, "options__5": {"label": "Liczba produktów, od wysokiej do niskiej"}, "options__6": {"label": "Liczba produktów, od niskiej do wysokiej"}}, "items_per_row": "Liczba elementów w rzędzie"}}, "sidebar": {"name": "Panel boczny", "settings": {"image": {"label": "Obraz logo"}, "image_width": {"label": "Szerokość obrazu logo"}, "primary_navigation": {"label": "Nawigacja główna"}, "secondary_navigation": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "search": {"content": "Wyszukaj", "label": "Pokaż wyszukiwanie"}}}, "text-columns-with-icons": {"name": "Kolumny tekstu z ikonkami", "settings": {"content": {"label": "Pokaż tylko na zaznaczonych stronach:"}, "show_on_homepage": {"label": "Strona główna"}, "show_on_product": {"label": "Strony produktu"}, "show_on_collection": {"label": "<PERSON><PERSON><PERSON> k<PERSON>"}, "show_on_blog": {"label": "Strony bloga i artykułów"}, "show_on_regular": {"label": "Zwykłe strony"}, "icons": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> wszystkie ikony zawarte w motywie, przej<PERSON><PERSON> na [this link](https://krownthemes.com/krown-icons/)"}}, "blocks": {"icon": {"name": "Tekst z ikoną", "settings": {"title": {"label": "Nagłówek"}, "text": {"label": "Tekst"}, "icon": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}}, "footer": {"name": "Stopka", "settings": {"show_payment_icons": {"label": "Pokaż ikony płatności"}, "language_selector": {"content": "Selektor <PERSON>zy<PERSON>", "info": "<PERSON><PERSON> do<PERSON>, prz<PERSON><PERSON><PERSON> do [language settings.](/admin/settings/languages)"}, "language_selector_show": {"label": "Pokaż selektor języka"}, "country_selector": {"content": "Selektor kraju/regionu", "info": "Aby dodać kraj/region, przejdź do swoich [ustawień płatności.](/admin/settings/payments)"}, "country_selector_show": {"label": "Pokaż selektor kraju/regionu"}}, "blocks": {"text": {"name": "Tekst", "settings": {"title": {"label": "Nagłówek"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "text_size": {"label": "Rozmiar <PERSON>", "options__1": {"label": "Zwykły"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}}, "menus": {"name": "<PERSON><PERSON>", "settings": {"title_1": {"label": "Nagłówek pierwszego menu"}, "title_2": {"label": "Nagłówek drugiego menu"}, "menu_1": {"label": "Pierwsze menu", "info": "To menu nie wyświetla listy rozwijanej"}, "menu_2": {"label": "Drugie menu"}}}, "newsletter": {"name": "Tworzenie konta za pomocą adresu e-mail"}, "social": {"name": "Linki do portali społecznościowych"}, "image": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON> obraz"}}}}}, "contact-form": {"name": "<PERSON><PERSON>", "settings": {"title": {"label": "Nagłówek"}}, "blocks": {"field": {"name": "Pole formularza", "settings": {"type": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON> w<PERSON>z"}, "options__2": {"label": "<PERSON>iele wierszy"}}, "required_field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "labels": {"label": "Etykieta", "info": "<PERSON><PERSON><PERSON><PERSON>, że wszystkie pola mają niepowtarzalne etykiety."}}}, "email": {"name": "Nazwa i e-mail"}, "button": {"name": "Przycisk przesyłania", "settings": {"label": {"label": "Etykieta"}}}}, "presets": {"name": "<PERSON><PERSON>"}}, "image-with-text": {"name": "Obraz z tekstem", "blocks": {"image": {"name": "Obraz z tekstem", "settings": {"title": {"label": "Nagłówek"}, "body": {"label": "Tekst"}, "button_label": {"label": "Etykietka przycisku"}, "url": {"label": "Link", "info": "Cały blok zostanie zmieniony w link, chyba że przycisk będzie mieć etykietę"}, "image": {"label": "<PERSON><PERSON>z tła"}}}}, "settings": {"image_height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "options__1": {"label": "Ma<PERSON><PERSON>"}, "options__2": {"label": "Nośnik"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Pełna"}}, "text_width": {"label": "<PERSON><PERSON><PERSON><PERSON>ć kontenera tekstu", "options__1": {"label": "Nośnik"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Pełna"}}, "text_size": {"label": "Wielkość nagłówka", "options__1": {"label": "Zwykły"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "text_alignment": {"label": "Wyrównanie tekstu", "options__1": {"label": "U góry do lewej"}, "options__2": {"label": "U góry do centrum"}, "options__3": {"label": "U góry do prawej"}, "options__4": {"label": "W środku do lewej"}, "options__5": {"label": "W środku do centrum"}, "options__6": {"label": "W środku do prawej"}, "options__7": {"label": "Na dole do lewej"}, "options__8": {"label": "Na dole do centrum"}, "options__9": {"label": "Na dole do prawej"}}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}}, "presets": {"name": "Obrazy z tekstem"}}, "featured-product": {"name": "Wyróżnione produkty", "settings": {"product": {"label": "<PERSON><PERSON><PERSON>rz produkt"}}, "blocks": {"product_link": {"name": "Link do produktu"}}}, "featured-collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "Nagłówek"}, "show_view_all": {"label": "Pokaz link do strony kolekcji"}, "layout": {"label": "Layout", "options__1": {"label": "Suwak"}, "options__2": {"label": "Siatka"}}, "products_number": {"label": "Maksymalna liczba widocznych produktów"}, "collection": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "gallery": {"name": "Galeria", "blocks": {"image": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON>"}, "caption": {"label": "Podpis"}, "featured": {"label": "Powiększ obraz w siatce"}}}}, "settings": {"aspect_ratio": {"label": "Proporcje obrazów", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (4:3)", "group": "Przycięte"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1:1)"}, "options__3": {"label": "<PERSON><PERSON><PERSON> (5:6)"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (2:3)"}, "options__5": {"label": "Naturalne", "group": "Nieprzycięty"}, "info": "Korzys<PERSON>j<PERSON><PERSON> z naturalnych proporcji, <PERSON><PERSON>, <PERSON><PERSON> zachowasz je podczas zmiany wielkości miniatury. Dzięki temu uzyskasz czysty projekt siatki. Gdy skorzystasz z jednego z ustawień przycinania, wiel<PERSON>ść wszystkich miniatur zostanie dopasowana do tego samego wymiaru."}, "style_mobile": {"label": "Zmień galerię w slajdy na urządzeniu mobilnym"}, "slider_height": {"label": "Wysokość przewijania na urządzeniu mobilnym", "options__1": {"label": "Nośnik"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Pełna"}}, "lightbox": {"label": "Włącz lightbox", "info": "Po kliknięciu wyświetla większy obraz"}}, "presets": {"name": "Galeria"}}, "heading": {"name": "Nagłówek", "settings": {"title": {"label": "<PERSON><PERSON><PERSON>"}}, "presets": {"name": "Nagłówek"}}, "image": {"name": "<PERSON><PERSON><PERSON>", "mobile_image": "Obraz mobilny (opcjonalny)", "fullwidth": "<PERSON><PERSON>ł<PERSON>"}, "apps": {"name": "Aplikacje", "settings": {"include_margins": {"label": "Zrównaj marginesy sekcji z marginesami motywu"}}, "presets": {"name": "Aplikacje"}}, "rich-text": {"name": "Tekst sformatowany", "blocks": {"heading": {"name": "Nagłówek", "settings": {"heading": {"label": "Nagłówek"}, "heading_size": {"label": "Wielkość nagłówka", "options__1": {"label": "Zwykły"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "icon": {"name": "Ikonka"}, "text": {"name": "Tekst", "settings": {"text": {"label": "Tekst"}}}, "button": {"name": "Przycisk", "settings": {"button_label": {"label": "Etykietka przycisku"}, "button_link": {"label": "<PERSON>"}, "button_size": {"label": "Wielkość przycisku", "options__1": {"label": "Zwykły"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "settings": {"text_alignment": {"label": "Wyrównanie tekstu", "options__1": {"label": "<PERSON> lewe<PERSON>"}, "options__2": {"label": "Środek"}, "options__3": {"label": "Po prawej"}}, "image": {"label": "<PERSON><PERSON><PERSON>"}, "image_position": {"label": "Położenie obrazu", "options__1": {"label": "<PERSON> lewe<PERSON>"}, "options__2": {"label": "Po prawej"}}, "image_height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "options__1": {"label": "Zwykły"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Pełna"}}}, "presets": {"name": "Tekst sformatowany"}}, "shop-the-look": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Nagłówek"}, "subheading": {"label": "Podnagłówek"}, "image": {"label": "<PERSON><PERSON>z tła"}}, "blocks": {"product": {"name": "Produkt", "settings": {"select_product": {"label": "<PERSON><PERSON><PERSON>rz produkt"}}}}, "presets": {"name": "<PERSON><PERSON>"}}, "testimonials": {"name": "Rekomendacje", "blocks": {"testimonial": {"name": "Opinia", "settings": {"quote": {"label": "Cytat"}, "author_name": {"label": "Nazwisko autora"}, "author_title": {"label": "Zwrot grzecznościowy autora"}, "author_avatar": {"label": "Awatar autora"}}}}, "presets": {"name": "Rekomendacje"}}, "announcement-bar": {"name": "Pasek <PERSON>ł<PERSON>zeń", "settings": {"bar_show": {"label": "Pokaż pasek ogłoszeń"}, "bar_show_on_homepage": {"label": "Pokaż tylko na stronie głównej"}, "bar_show_dismiss": {"label": "Pokaż przycisk odrzucania"}, "bar_message": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "bar_link": {"label": "Link"}, "bar_bgcolor": {"label": "<PERSON><PERSON>"}, "bar_txtcolor": {"label": "<PERSON><PERSON>"}}}, "text-columns-with-images": {"name": "Kolumny tekstu z obrazami", "blocks": {"text": {"name": "Tekst", "settings": {"title": {"label": "Nagłówek"}, "text": {"label": "Tekst"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Kolumny tekstu z obrazami"}}, "slider": {"slider_horizontal": {"name": "Pokaz slajdów: poziomo"}, "slider_vertical": {"name": "Pokaz slajdów: pionowo"}, "settings": {"desktop_height": {"label": "Wysokość przewijania na urządzeniu stacjonarnym", "options__1": {"label": "Ma<PERSON><PERSON>"}, "options__2": {"label": "Nośnik"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Pełna"}}, "mobile_height": {"label": "Wysokość przewijania poziomo na urządzeniu mobilnym"}, "text_style": {"header": "<PERSON><PERSON>"}, "mobile_design": {"header": "Wersja mobilna", "label": "Zmień przewijanie pionowe na poziome w wersji mobilnej"}}, "blocks": {"image": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON>"}, "heading": {"label": "Nagłówek"}, "subheading": {"label": "Podnagłówek"}, "caption": {"label": "Podpis"}, "button_label": {"label": "Etykietka przycisku"}, "link": {"label": "Link", "info": "Je<PERSON><PERSON> przycisk nie będzie mieć etykiety, podlinkowany zostanie tekst."}}}}}, "video-popup": {"name": "Wideo: wys<PERSON><PERSON><PERSON><PERSON><PERSON> okienko", "settings": {"video": {"label": "Adres URL wideo"}, "image": {"label": "<PERSON><PERSON>z tła"}}}, "video-background": {"name": "Wideo: tło", "settings": {"video": {"label": "Adres URL wideo", "info": "Ścieżka do pliku .mp4"}, "image": {"label": "Obraz bazowy", "info": "Obraz bazowy będzie wykorzystywany na urządzeniach mobilnych, gdy wyłączone będzie autoodtwarzanie."}, "size_alignment": {"content": "Rozmiar i wyrównanie"}, "video_height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>o", "options__1": {"label": "<PERSON><PERSON> (16:9)", "group": "Nieprzycięty"}, "options__2": {"label": "<PERSON><PERSON><PERSON>", "group": "Przycięte"}, "options__3": {"label": "Pełna"}}}}, "main-password-header": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasła"}, "main-password-content": {"name": "<PERSON><PERSON><PERSON><PERSON> hasła"}, "main-password-footer": {"name": "<PERSON><PERSON> hasła", "settings": {"show_social": {"label": "Pokaż ikonki mediów społecznościowych"}}}, "main-article": {"name": "Post na blogu", "blocks": {"featured_image": {"name": "Polecany obraz", "settings": {"image_height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> polecanego obrazu", "options__1": {"label": "Dostosuj do obrazu"}, "options__2": {"label": "Nośnik"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "Po<PERSON><PERSON> datę"}, "blog_show_author": {"label": "Pokaż autora"}, "blog_show_comments": {"label": "Pokaż liczbę komentarzy"}}}, "content": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "social_sharing": {"name": "Przyciski udostępniania w mediach społecznościowych"}, "blog_navigation": {"name": "Linki do sąsiadujących postów"}}}, "main-blog": {"settings": {"header": {"content": "Karta posta na blogu"}, "enable_tags": {"label": "Włącz filtrowanie wg tagów"}, "post_limit": {"label": "Liczba postów na stronie"}}}, "blog-posts": {"name": "Posty na blogu", "blocks": {"title": {"name": "<PERSON><PERSON><PERSON>"}, "info": {"name": "Informacja", "settings": {"show_date": {"label": "Po<PERSON><PERSON> datę"}, "show_author": {"label": "Pokaż autora"}}}, "summary": {"name": "Fragment"}, "link": {"name": "Link"}}, "settings": {"title": {"label": "Nagłówek"}, "blog": {"label": "Blog"}, "post_limit": {"label": "<PERSON>y"}, "show_image": {"label": "Pokaż polecany obraz"}, "show_view_all": {"label": "Pokaż link do strony bloga"}, "layout": {"label": "Layout"}, "option_1": {"label": "<PERSON><PERSON> k<PERSON>", "group": "Siatka"}, "option_2": {"label": "<PERSON><PERSON><PERSON> kol<PERSON>"}, "option_3": {"label": "Elastyczne (2–5 kolumn)", "group": "Suwak"}}, "presets": {"name": "Posty na blogu"}}, "custom-colors": {"heading": {"label": "Nagłówek"}, "text": {"label": "Własny kolor tekstu"}, "overlay": {"label": "Nakładka tła"}, "background": {"label": "Własny kolor tła"}}, "custom-gutter": {"heading": {"content": "Odstęp"}, "gutter_enabled": {"label": "Włącz odstępy między zawartością wewnętrzną"}}, "newsletter": {"name": "Tworzenie konta za pomocą adresu e-mail", "blocks": {"heading": {"name": "Nagłówek", "settings": {"heading": {"label": "Nagłówek"}}}, "paragraph": {"name": "Podnagłówek", "settings": {"paragraph": {"label": "Opis"}}}, "email_form": {"name": "Formularz wiadomości e-mail"}}, "presets": {"name": "Tworzenie konta za pomocą adresu e-mail"}}, "product-recommendations": {"name": "Rekomendacje produktu", "settings": {"heading": {"label": "Nagłówek"}, "header__1": {"content": "Rekomendacje produktu", "info": "W dynamicznych rekomendacjach kolejność produktów i informacja o nich wykorzystywane są do ciągłego wprowadzania zmian i ulepszeń. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)"}}}, "custom-liquid": {"name": "Niestandardowy język Liquid", "settings": {"custom_liquid": {"label": "Niestandardowy język Liquid"}}, "presets": {"name": "Niestandardowy język Liquid"}}, "collection-list": {"name": "Lista kolekcji", "presets": {"name": "Lista kolekcji"}}, "faq": {"name": "Pytania i odpowiedzi", "settings": {"title": {"label": "Nagłówek"}, "open_first": {"label": "Domyślnie otwórz pierwszy przełącznik"}}, "blocks": {"text": {"name": "Pytania i odpowiedzi", "settings": {"title": {"label": "<PERSON><PERSON><PERSON>"}, "text": {"label": "Tekst"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Pytania i odpowiedzi"}}, "popup": {"name": "Wyskakują<PERSON> okienko", "settings": {"title": {"label": "Nagłówek"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "show_newsletter": {"label": "Pokaż formularz tworzenia konta za pomocą adresu e-mail"}, "functionality": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "enable": {"label": "Włącz wyskakujące okienko"}, "show_after": {"label": "Pokaż wyskakujące okienko po", "info": "sek."}, "frequency": {"label": "Czę<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wyskakującego okienka", "options__1": {"label": "Pokaż codziennie"}, "options__2": {"label": "Pokaż co tydzień"}, "options__3": {"label": "Pokaż co miesiąc"}}, "image": {"label": "<PERSON><PERSON><PERSON>", "info": "Rekomendowany wymiar pliku .jpg 1240x400 piks. Pojawia się tylko na pulpicie"}}}, "main-search": {"name": "Wyniki wyszukiwania", "settings": {"products_per_page": {"label": "Wyniki na stronie"}}}, "main-collection-product-grid": {"name": "Siatka produktu", "settings": {"products_per_page": {"label": "Produkty na stronie"}, "enable_filtering": {"label": "Włącz filtrowanie", "info": "[Customize filters](/admin/menus)"}, "enable_sorting": {"label": "Włącz sortowanie"}, "image_filter_layout": {"label": "Układ filtrów obrazu"}, "header__1": {"content": "Filtrowanie i sortowanie"}}}, "main-collection-banner": {"name": "<PERSON><PERSON>", "settings": {"paragraph": {"content": "<PERSON><PERSON> <PERSON><PERSON> opisy lub obra<PERSON> k<PERSON>, [edit your collections.](/admin/collections)"}, "show_collection_description": {"label": "Pokaż opis kolekcji"}, "show_collection_image": {"label": "Pokaż obraz kolekcji", "info": "<PERSON><PERSON> <PERSON><PERSON><PERSON> najlepsze rezultaty, uż<PERSON>j obrazu o proporcjach 16:9."}}}, "main-product": {"name": "Informacje o produkcie", "blocks": {"text": {"name": "Tekst", "settings": {"text": {"label": "Tekst"}, "text_style": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Podtytuł"}, "options__3": {"label": "Wielkie litery"}}}}, "title": {"name": "<PERSON><PERSON><PERSON>"}, "price": {"name": "<PERSON><PERSON>"}, "tax_info": {"name": "Pokaż informacje o podatku"}, "sku_barcode": {"name": "SKU / kod kreskowy"}, "quantity_selector": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "variant_picker": {"name": "Narzędzie wyboru wariantu", "settings": {"show_variant_labels": {"label": "Pokaż warianty etykiet"}, "hide_out_of_stock_variants": {"label": "<PERSON>k<PERSON><PERSON> niedostępne warianty"}, "low_inventory_notification": {"label": "Powiadomienia o zapasach", "info": "Aby ta funkcja d<PERSON>łała, w przypadku wariantów należy włączyć śledzenie zapasów. [Learn more](https://help.shopify.com/en/manual/products/inventory/track_inventory)", "options__1": {"label": "Nie pokazuj informacji o zapasach."}, "options__2": {"label": "Wyświ<PERSON>l informację, gdy zapasy spadną poniżej 5"}, "options__3": {"label": "Zawsze pokazuj zapasy"}}}}, "buy_buttons": {"name": "Prz<PERSON>cis<PERSON>", "settings": {"show_dynamic_checkout": {"label": "Pokaż dynamiczne przyciski finalizacji zakupu", "info": "Korzystając z metod płatności dostępnych w sklepie, klienci widzą swoją preferowaną opcję, np. PayPal lub Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Pokaż formularz odbiorcy dla produktów typu karta prezentowa", "info": "Po włączeniu, produkty w postaci kart prezentowych mogą być opcjonalnie wysyłane do odbiorcy z osobistą wiadomością."}, "show_quantity_selector": {"label": "Po<PERSON><PERSON> selektor <PERSON>"}}}, "pickup_availability": {"name": "<PERSON><PERSON><PERSON> odbioru"}, "description": {"name": "Opis", "settings": {"product_description_truncated": {"label": "Opis obci<PERSON>", "info": "Obetnij", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Pokaż mały fragment"}, "options__3": {"label": "Pokaż średni fragment"}, "options__4": {"label": "Pokaż duży fragment"}}}}, "share": {"name": "Udostępnij", "settings": {"featured_image_info": {"content": "Jeśli do postów w mediach społecznościowych dołączysz link, polecany obraz strony zostanie pokazany jako obraz podglądu. [Learn more](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Tytuł i opis strony są dołączone do obrazu podglądu. [Learn more](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "<PERSON><PERSON><PERSON>", "settings": {"heading": {"info": "Dołącz nagłówek wyjaśniający zawartość.", "label": "Nagłówek"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zak<PERSON>d<PERSON>"}, "page": {"label": "Zawartość zakładki ze strony"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}}, "settings": {"header": {"content": "Multimedia", "info": "<PERSON><PERSON><PERSON> się więcej o [media types](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Na dużych ekranach włącz przyklejoną informację o produkcie"}, "enable_video_looping": {"label": "Włącz zapętlenie wideo"}, "enable_zoom": {"label": "Włącz powiększanie obrazu"}, "gallery_gutter": {"label": "Dodaj odstęp między mediami"}, "gallery_slider_style": {"label": "<PERSON><PERSON><PERSON><PERSON> obra<PERSON>, aby pasowały do okna podglądu"}, "gallery_style": {"label": "<PERSON><PERSON>i", "info": "Domyślne do przewijania na urządzeniach mobilnych", "options__1": {"label": "Przewiń"}, "options__2": {"label": "Suwak"}}, "gallery_pagination": {"label": "<PERSON><PERSON><PERSON><PERSON>i", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Miniaturki"}}}}, "words_210": {"text_columns": {"name": "Text columns", "block": {"name": "Text"}}}, "images": {"label_1": "Obraz 1", "label_2": "Obraz 2", "label_3": "Obraz 3"}}, "color-schemes": {"name_plural": "Color schemes", "name_singular": "Color scheme", "default": "Default color scheme", "default_info": "Used to set colors for account pages, 404 pages, and other pages or sections without color scheme settings.", "drawers": "Modals/drawers", "other": "Other colors"}, "local-250": {"labels": {"show_filters_as": "Pokaż filtry jako", "expand_filters_by_default": "Rozwiń filtry domyślnie", "stick_filters_sidebar_to_top": "Przyklej pasek boczny filtrów na górze"}, "options": {"sidebar": "Pasek boczny", "list": "Lista"}}, "local-230": {"background_gradient": "Gradient tła", "variant_default": {"label": "Domyślnie wybierz pierwszy dostępny wariant", "info": "<PERSON><PERSON><PERSON> ta opcja nie jest z<PERSON>a, użytkownik będzie musiał wybrać dostępny wariant, zanim będzie mógł dokonać zakupu."}, "slider_info": "Link zostanie zastosowany do przycisku, lub do tytułu (jeśli przycisk nie istnieje), lub do całego slajdu (jeśli zarówno tytuł, jak i przycisk są puste).", "buy_button_labels": {"label": "Etykiety przycisków zakupu", "option_1": "<PERSON><PERSON>", "option_2": "<PERSON><PERSON><PERSON><PERSON>"}, "hide_on_mobile": "Ukryj na urządzeniach mobilnych"}, "local-223": {"heading_text_color": "<PERSON>lor tekstu nagłówka", "slider_navigation_color": "Kolor elementów nawigacji"}, "late_edits": {"badge": {"custom_badge": {"text_color": "<PERSON><PERSON>"}, "sold_out": {"name": "Wyprzedany odznaka", "text_color": "<PERSON><PERSON> tekstu „wyprzedany”", "sale_text": "Kolor tekstu „zniżki”"}}, "rich-text": {"image_position": {"no_image": {"group": "Brak obrazka", "label": "<PERSON><PERSON> p<PERSON><PERSON> o<PERSON>"}}}}}