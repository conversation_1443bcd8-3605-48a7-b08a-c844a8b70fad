{"variant_metafields": {"name": "Varyant metafield", "label": "Varyant metafield anahtarı", "info": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> say<PERSON>ında bir varyant metafield gösterebilir. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/884-using-variant-metafields-in-product-pages)."}, "new_color_swatches": "\"Bloklar\" varyant se<PERSON><PERSON> t<PERSON>, kategori meta alanlarıyla oluşturulan renk örneklerini destekler. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/custom-data/metafields/category-metafields/using-category-metafields).", "video_controls": "Video kontrollerini göster", "sticky_cart_actions": "Sabit çekmece sepeti işlemlerini etkinleştir", "currency_codes": {"header": "Para birimi formatı", "label": "Para birimi kodlarını göster", "info": "Örnek: $1.00 USD."}, "a11": {"label": "Erişilebilirlik", "show_sidebars_scrollbar": "<PERSON><PERSON> kaydırma ç<PERSON> g<PERSON>", "disable_all_image_animations": "Tüm resim animasyonlarını devre dışı bırak"}, "divider": {"label": "B<PERSON>lücü", "divider_design": "Bölücü tasarımı", "divider_style_solid": "Kat<PERSON>", "divider_style_dotted": "Noktalı", "divider_style_dashed": "Çizgili", "divider_style_double": "Çift", "divider_color": "Renk", "divider_image": "Bölücü resim", "divider_image_info": "<PERSON><PERSON><PERSON> o<PERSON> tekrar eden bir resim. Üstteki stili ve rengi değiştirir."}, "cart_actions": {"label": "Çekmece sepeti e<PERSON>mleri", "option_1": "\"Sepeti görüntüle\" düğmesini göster", "option_2": "\"Ödeme\" düğ<PERSON>ini göster", "option_3": "<PERSON> i<PERSON><PERSON>"}, "sticky_atc": {"label": "Yapışkan sepete ekle", "enable_sticky_atc": "Yapışkan sepete ekle'yi <PERSON>", "sticky_atc_style_wide": "Wide", "sticky_atc_style_floating": "Floating"}, "performance": {"header": "SEO & performans", "name": "Performans", "label": "Üzerine gelindiğinde bağlantıları önceden yükle", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON> algılanan yükleme hızını artırır."}, "recently_viewed": {"enable_recently_viewed_products": "Son gö<PERSON><PERSON><PERSON><PERSON><PERSON>n ürünleri etkinleştir", "enable_recently_viewed_products_info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tema görüntülenen ürünleri kaydedecek, ancak bu ürünleri göstermek için mağazanıza bölüm eklemeniz gerekmektedir.", "recently_viewed_products": "<PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ürü<PERSON>ler", "recently_viewed_products_info": "<PERSON><PERSON> b<PERSON><PERSON>ümün Tema Ayarları'nda etkinleştirilmiş olması gerekmektedir. Kullanıcılar en az bir ürün sayfasını ziyaret ettikten sonra görünecektir.", "recently_viewed_products_limit": "Son gö<PERSON><PERSON><PERSON><PERSON><PERSON>n ürünler limiti"}, "rating_apps_update": {"label": "Değerlendirme uygulaması", "info": "Üçüncü ta<PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON> bir entegrasyon için ek adımlar gerekebilir."}, "local-220": {"preorder": "\"Ön sipariş\" düğ<PERSON> etiketini gö<PERSON>", "autorotate": {"heading": "Otomatik döndür", "info": "Slaytlar arasında otomatik olarak geçiş yap.", "enable": "Otomatik döndürmeyi etkinleştir", "interval": "Aralık", "pause_on_mouseover": "Fare üzerindeyken duraklat"}}, "custom-social-icons": {"header": "Özel link", "info": "<PERSON><PERSON>ori sosyal ağınız için özel bir simge yükleyin", "icon": {"label": "<PERSON>m<PERSON>", "info": "72 x 72px şeffaf .png"}, "link": {"label": "Link"}}, "local-march-update": {"labels": {"dynamic_content": {"name": "Dinamik içerik", "hide_block": "Dinamik içerik yoksa bloğu gizle", "hide_section": "Dinamik içerik yoksa bölümü gizle"}, "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cards": "<PERSON><PERSON><PERSON>", "heading": "Başlık", "buttons_custom": "<PERSON><PERSON><PERSON><PERSON> ö<PERSON>leri", "center_heading": "<PERSON><PERSON> b<PERSON>şl<PERSON>", "section_design": "Bölüm ta<PERSON>ımı", "bottom_margin": "Alt kenar boşluğunu kaldır", "text_spacing": "<PERSON><PERSON>", "inherit_card_design": "<PERSON>rt tasarım özelliklerini devral", "align_button": "Satın alma düğmesini kartın altına hizala", "custom_colors": "<PERSON><PERSON>"}, "shadows": {"label": "<PERSON><PERSON><PERSON>", "label_plural": "<PERSON><PERSON><PERSON><PERSON>", "offset_x": "<PERSON><PERSON><PERSON> <PERSON>", "offset_y": "Dikey ofset", "blur": "Bulanıklık", "hide": "<PERSON><PERSON><PERSON><PERSON> gizle", "hide_button_shadows": "<PERSON><PERSON>ğme gölgelerini gizle"}, "blocks": {"countdown_timer": {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>ı<PERSON>ı", "label": "Dinamik kaynak", "info": "<PERSON><PERSON> <PERSON>ı<PERSON> zamanlayıcısı için dinamik bir zaman kaynağı ayarlayın.[Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/642-product-pages#countdown-timer)"}, "progress_slider": {"name": "İlerleme <PERSON>ğu gra<PERSON>ği", "value": "<PERSON><PERSON><PERSON>", "height": "Slider height", "width": "Slider width", "dynamic_content": {"info": "Use dynamic sources to define unique values by creating product metafields for the progress chart. [Learn more](https://shopify-support.krownthemes.com/article/804-product-page#Progress-chart-S8c6R)", "slider_label": "Value metafield", "dots_label": "Highlighted dots metafield"}}, "progress_dots": {"name": "İlerleme noktaları tablosu", "highlight": "<PERSON><PERSON><PERSON><PERSON> no<PERSON>", "total": "<PERSON>lam noktalar", "icon": "Dot icon", "size": "Dot size", "inactive_color": "Inactive color", "active_color": "Highlighted color"}, "store_selector": {"default": "İlk mağazayı varsayılan yap"}, "rating": {"app": "<PERSON>rum uygulaması", "default_option": "Varsayılan"}, "space": {"name": "<PERSON><PERSON>"}, "badges": {"name": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>"}, "nutritional": {"name": "Beslenme bil<PERSON>i", "label_first": "<PERSON><PERSON><PERSON><PERSON>", "label_second": "<PERSON><PERSON><PERSON> s<PERSON>", "label_third": "Üçüncü sütun etiketi", "information": {"label": "<PERSON><PERSON><PERSON>", "info": "Etiket ve değeri virgülle ayırın. Yeni bir satır eklemek için satır sonlarını kullanın. Satırları girintilemek için kısa çizgi kullanın. [Daha fazla bilgi edin](https://shopify-support.krownthemes.com/article/804-product-page#Nutritional-information-ypnLL)"}, "extra_information": "Ekstra bilgi"}}, "sections": {"progress_sliders": {"name": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block_name": "Ç<PERSON>uk"}, "header": {"settings": {"promotion": {"header_1": "Promosyon 1", "header_2": "Promosyon 2", "header_3": "<PERSON><PERSON>", "show": "Promosyonu <PERSON>", "image": "Promosyon görseli", "text": "Promos<PERSON>ni", "width": "<PERSON><PERSON><PERSON> g<PERSON>liği"}}}}}, "settings_schema": {"exit_intent": {"exit_intent_popup": "Çıkış niyeti popup", "exit_intent_popup_info": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> sadece masaüstünde çalışır"}, "colors": {"name": "Ren<PERSON>r", "settings": {"header__1": {"content": "<PERSON><PERSON>"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__3": {"content": "Alt bilgi"}, "bg_color": {"label": "Arka plan"}, "txt_color": {"label": "<PERSON><PERSON>"}, "link_color": {"label": "Bağlantılar"}}}, "typography": {"name": "Tipografi", "settings": {"headings_font": {"label": "Başlıklar"}, "base_size": {"label": "<PERSON><PERSON>u"}, "large_size": {"label": "Büyük başlıklar", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zengin metindeki ve metin kısımları olan görseldeki büyük başlıkları etkiler"}, "body_font": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "nav_size": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "product-grid": {"name": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "settings": {"aspect_ratio": {"label": "Medya en boy oranı"}, "show_secondary_image": {"label": "İkinci ürün medyasını üzerine gelindiğinde göster"}, "enhance_featured_products": {"label": "Öne çıkan ürünleri vurgula", "info": "[Learn more](https://shopify-support.krownthemes.com/article/832-product-card)"}, "show_discount": {"label": "İndirimi şö<PERSON>:", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>z<PERSON>"}}, "caption_placement": {"label": "Açıklama yazısı yerleşimi", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON> g<PERSON>"}, "options__2": {"label": "Alt görsel", "group": "Her zaman gö<PERSON>ü<PERSON>ür"}}, "grid_color_bg": {"label": "Kaplama açıklama yazısı arka planı"}, "grid_color_text": {"label": "<PERSON><PERSON><PERSON>a açıklama yazısı metin rengi"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>", "info": "Değerlendirmeleri göstermek için bir ürün değerlendirme uygulaması ekleyin. [Learn more](https://apps.shopify.com/search?q=product%20reviews)"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "show_reviews": {"label": "Değerlendirmeyi göster"}}}, "favicon": {"name": "Site simgesi", "settings": {"favicon": {"label": "Site simgesi görseli", "info": "48 x 48px .png gerekir"}}}, "cart-page": {"name": "Alışveriş Sepeti", "settings": {"cart_type": {"label": "<PERSON><PERSON> tipi", "options__1": {"label": "Say<PERSON>"}, "options__2": {"label": "Çekmece"}}, "cart_notes": {"label": "Sepet notlarını etkinleştir"}, "cart_buttons": {"label": "Ek ödeme düğmelerini göster"}}}, "embellishments": {"name": "Sü<PERSON>meler", "settings": {"show_preloader": {"label": "Görsel ö<PERSON>kley<PERSON>", "info": "Mağazandaki görseller yüklenmekteyken küçük dairesel bir önyükleyici gösterir."}, "show_breadcrumb": {"label": "İçerik haritasını göster", "info": "İçerik haritası gezinmesi mağazada gezinmelerinde kullanıcılara yardımcı olur ve yalnızca koleksiyon, ürün ve arama sayfalarında gözükür."}, "show_go_top": {"label": "'En üste git' d<PERSON><PERSON><PERSON><PERSON> gö<PERSON>"}}}, "search": {"name": "Ara", "settings": {"predictive_search": {"label": "<PERSON><PERSON><PERSON> a<PERSON> etkinleştir"}, "show_vendor": {"label": "Satıcıyı göster"}, "show_price": {"label": "Fiyatı göster"}, "include_articles": {"label": "<PERSON><PERSON> ma<PERSON> da<PERSON> et"}, "include_pages": {"label": "<PERSON><PERSON> dahil et"}}}, "social": {"name": "<PERSON><PERSON><PERSON>"}, "follow_on_shop": {"content": "Follow on Shop", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Shop uygulamasındaki mağazanızı vitrininizden takip edebilmesi için Shop Pay'i etkinleştirmeniz gerekir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "label": "Follow on Shop'u etkinleştir"}, "labels": {"hide_block_if_no_content_info": "İçerik tanımlanmamışsa bloğu gizle", "popup_page_info": "Bir sayfa seçildiğinde metin içeriğini değiştirir", "page": "Say<PERSON>", "popup": "Popup", "open_popup": "Popup aç"}}, "sections": {"main-404": {"name": "Ana 404"}, "main-gift-card": {"name": "Hediye kartı"}, "main-page": {"name": "<PERSON>"}, "refactor_words": {"seo": {"name": "SEO", "label": "Başlık etiketi", "info": "Arama motorlarının sayfanızın yapısını indekslemesine yardımcı olmak için başlık seviyesini belirtin.", "microdata": {"label": "Mikroveri şemasını devre dışı bırak", "info": "Bu sayfadan schema.org biçimlendirmesini kaldırır. Sadece SEO için üçüncü parti bir uygulama kullanıyorsanız devre dışı bırakın."}}, "late_edits": {"image_with_hotspots": {"image_on_mobile": "<PERSON><PERSON>", "position_on_mobile": "Mobilde konumlandır", "hotspot": {"mobile_info": "Sadece bir mobil g<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>"}}, "product-card": {"thumbnails": {"border": "<PERSON><PERSON><PERSON> sı<PERSON><PERSON>i"}}, "labels": {"optional": "İsteğe bağlı"}, "before-after": {"layout": {"invert": "<PERSON><PERSON>ş<PERSON>i g<PERSON>ev<PERSON>"}}}, "labels": {"footer_group": "Alt bilgi grubu", "header_group": "Üst bilgi grubu", "overlay_group": "<PERSON><PERSON> grubu", "embellishments": "Sü<PERSON>meler", "show_button": "Show button", "optional": "Optional"}, "text": {"icons_info": "Daha fazla simgeyi görselleştirmek ve indirmek için lütfen [bu bağlantıyı](https://resources.krownthemes.com/icons/) ziyaret edin."}, "borders": {"top_border": "Üst kenarlık", "bottom_border": "Alt kenarlık", "show_border": "Show border"}, "colors": {"heading_background": "Başlık arka planı", "shadow": "G<PERSON><PERSON>yi gö<PERSON>"}, "social": {"phone": "Telefon", "discord": "Discord"}, "settings": {"image_with_hotspots": {"label": "Etkin noktalar içeren görsel", "hotspot": {"label": "Etkin nokta", "label_desktop_offset": "Masaüstü etkin nokta", "label_mobile_offset": "Mobil etkin nokta", "offset_horizontal": "<PERSON><PERSON><PERSON> <PERSON>", "offset_vertical": "Dikey ofset", "tooltip": {"label": "<PERSON><PERSON><PERSON>", "position": {"label": "Pozisyon", "option_1": "Üst", "option_2": "Alt", "option_3": "Sol ", "option_4": "Sağ"}, "show_tooltip": "Show tooltip"}, "mobile_info": "Only if a mobile image is set"}, "image_on_mobile": "Mobile image", "position_on_mobile": "Position on mobile"}, "scrolling_images": {"label": "Görüntüleri kaydırma", "image_size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columns": "Columns"}, "video": {"label": "Video", "info": "MP4 formatı gerekli, ses yok"}, "variants_functionality": {"label": "Kullanılamayan varyantları işle", "option_1": "<PERSON><PERSON><PERSON>", "option_2": "<PERSON><PERSON> dışı bırak", "option_3": "<PERSON><PERSON><PERSON>"}, "auto_height": {"label": "Otomatik yükseklik", "info_slider": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>aretlemek, yuka<PERSON><PERSON><PERSON><PERSON> yükseklik ayarlarının üzerine yazacak ve slayt gösterisinin yüksekliğini her slaydın içindeki görüntüye duyarlı hale getirecektir."}}, "header": {"promotion_block": {"image_link": "Promosyon görseli bağlantısı"}, "sticky": {"label": "Yapışkan üst bilgi", "option_1": "<PERSON><PERSON> dı<PERSON>ı", "option_2": "Her zaman", "option_3": "Yalnızca yukarı kaydırıldığında"}}, "inventory": {"name": "<PERSON><PERSON>", "settings": {"show_progress_bar": "<PERSON><PERSON><PERSON><PERSON>", "low_inventory_threshold": "Düşük envanter eşiği", "show_block": {"always": "Her zaman göster", "low": "Yalnızca envanter eşiğin altına düştüğünde göster"}}}, "breadcrumb": {"name": "İçerik haritası", "info": "İçerik haritası gezintisi ana sayfada görünmez"}, "announcement-bar": {"visibility": {"label": "Görünürlük", "option_1": "<PERSON><PERSON><PERSON>", "option_2": "Yalnızca ana sayfa", "option_3": "<PERSON> sayfa hariç tüm say<PERSON>", "option_4": "Yalnızca ürün <PERSON>ı", "option_5": "Yalnızca sepet sayfası"}, "color": {"border": "Kenarlık rengi"}}, "promotional_banner": {"name": "Promosyon afişi", "enable": "<PERSON><PERSON><PERSON>"}, "cookies_banner": {"name": "<PERSON><PERSON><PERSON><PERSON>", "enable": "Çerez bildirimini g<PERSON>"}, "before_after": {"name": "G<PERSON>r<PERSON><PERSON><PERSON> ka<PERSON>şılaştırma", "layout": {"label": "D<PERSON>zen", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "<PERSON><PERSON>"}, "style": {"label": "<PERSON><PERSON> stili", "option__1": "Açık", "option__2": "<PERSON><PERSON>"}, "image": {"label__1": "G<PERSON><PERSON><PERSON>", "label__2": "<PERSON><PERSON>", "label__3": "Etiket"}}, "cart_upsell": {"name": "Tek ürün önerisi", "product": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>, se<PERSON>inizdeki ürünlere göredir. Zamanla değişir ve iyileşirler. [Daha fazla bilgi edinin](https://help.shopify.com/en/themes/development/recommended-products)"}, "gift_wrapping": {"name": "<PERSON><PERSON><PERSON> p<PERSON>", "info": "<PERSON><PERSON><PERSON>, bir <PERSON><PERSON><PERSON><PERSON> o<PERSON>lanmalıdır. [<PERSON>ha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/696-cart-features#gift-wrap)", "settings": {"label": "<PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>"}}, "custom_code": {"name": "Özel HTML / Liquid"}, "rating": {"name": "<PERSON>rum uygulaması", "default": "Varsayılan"}, "product-page": {"size_guide": {"label": "<PERSON><PERSON>", "page": "<PERSON><PERSON> re<PERSON>beri say<PERSON>ı", "options": {"label": "Seçenekleri aç", "option_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere", "option_2": "<PERSON><PERSON>ı pencere", "option_3": "<PERSON><PERSON> pencere"}}, "gallery_resize": {"label": "G<PERSON>rsellerin en boy oranı", "info": "<PERSON>lar ve diğer medya <PERSON>, orijinal en boy or<PERSON><PERSON><PERSON><PERSON> görüntülenecektir.", "option_1": "Görselleri konteynerin içine sığdır"}, "gallery_padding": {"label": "Galeri iç aralığı"}, "gallery_background": {"label": "Galeri arka planı", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, konteynerin içine sığacak şekilde ayarlanmışsa görünür."}}, "product-card": {"name": "Ürün kartı", "labels": {"thumbnail": "<PERSON>rün küçük resmi", "caption": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>ığı", "color_swatches": "Renk kartelaları"}, "thumbnails": {"fit": "Medyayı konteynerin içine sığdır", "padding": {"label": "Konteyner iç aralığı", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON> medy<PERSON>, konteynerin içine sığacak şekilde ayarlanmışsa çalışır."}, "background": {"label": "Konteyner arka planı", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON> medy<PERSON>, konteynerin içine sığacak şekilde ayarlanmışsa görünür."}, "border": "Kenarlık rengi", "color_swatches": "Ürün kartında renk kartelalarını göster", "color_swatches_on_hover": "Ürün kartında renk kartelalarını göster (üzerine gelindiğinde)"}, "color_swatches_label": {"label": "Renk örnekleri etiketleri", "info": "Renk örnekleri haline gelmesini istediğiniz birden fazla varyant başlığı (virgülle ayırarak) yazın."}, "badges": {"name": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "show_badges": "Rozetleri göster", "settings": {"colors": {"text": "<PERSON>ozet metin rengi", "sold_out": "'Tükendi' arka planı rengi", "sale": "'İndirim' arka planı rengi"}}, "badge_sale": {"name": "İndirim rozeti", "amount_saved": "<PERSON><PERSON><PERSON><PERSON> miktar"}, "regular_badges": {"info": "<PERSON><PERSON><PERSON>n rozetleri hakkında [buradan](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges) daha fazla bilgi edinin."}, "sold_out": {"name": "Tükendi rozeti", "text_color": "'<PERSON><PERSON><PERSON><PERSON>' met<PERSON> rengi", "sale_text": "'<PERSON><PERSON><PERSON>' metni rengi"}, "custom_badges": {"name": "<PERSON>zel ürün rozetleri", "info": "<PERSON><PERSON> tema, burada tanımlayabileceğiniz özel ürün rozetlerini kullanır. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/564-product-pages#product-badges)", "split_info": "<PERSON><PERSON> tema, burada tanımlayabileceğiniz özel ürün rozetlerini kullanır. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)", "name__1": "Özel rozet 1", "name__2": "Özel rozet 2", "name__3": "Özel rozet 3", "text": "<PERSON><PERSON>", "tags": "Etiket", "color": "Arka plan rengi", "text_color": "<PERSON><PERSON>", "border_color": "Kenarlık rengi"}}}, "seo_late": {"breadcrumbs": {"label": "Improve product breadcrumb navigation", "info": "Adds collection names to the breadcrumb navigation, by duplicating product url's. [Learn more](https://shopify-support.krownthemes.com/article/697-seo#breadcrumbs)"}}}, "local-extra-words": {"settings_schema": {"colors": {"headings": {"header": "Üst Bilgi", "cards": "<PERSON><PERSON><PERSON>"}, "settings": {"borders": "Kenarlık", "hide_border": "Kenarlığı gizle", "accent": "Vurgu", "overlay": "Overlay"}}, "typography": {"buttons": {"label": "Butonların yazı tipi kalınlığı", "option__1": "Normal", "option__2": "<PERSON><PERSON> kalın"}, "menus": {"header": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>u", "weight": "Yazı tipi kalınlığı", "weight_bold": "Kalı<PERSON>"}}, "borders": {"name": "Kenarlıklar", "main": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON> <PERSON><PERSON>, temada bulunan tüm b<PERSON>lümlerdeki kenarlık stilini kontrol eder."}, "buttons": {"name": "Butonlar"}, "forms": {"name": "Formlar"}, "settings": {"width": "Genişlik", "radius": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "layout": {"name": "D<PERSON>zen", "sections": {"vertical_space": "Bölümler arasındaki dikey boşluk", "remove_vertical_space": "Üst kenar bo<PERSON>luğunu kaldır", "remove_bottom_margin": "Alt kenar boşluğunu kaldır"}, "grid": {"name": "Izgara", "info": "Çok sütunlu bir düzene sahip alanları etkiler.", "horizontal_space": "<PERSON><PERSON><PERSON>", "vertical_space": "<PERSON><PERSON> b<PERSON>"}}, "cart": {"shipping": {"name": "<PERSON><PERSON>", "show": {"label": "Ücretsiz kargo için en düşük tutarı göster", "info": "Kargo ücretlerini yapılandırmak için [shipping settings](/admin/settings/shipping) bölümünüze gidin."}, "amount": {"label": "Ücretsiz kargo için en düşük tutar", "info": "<PERSON><PERSON> sayı ya<PERSON>ın, harf veya özel karakter kullanmayın."}, "info": "This feature only works with a single currency and shipping method. If you're using multiple shipping methods or currencies, it might show false values to the customer."}, "settings": {"media": {"header": "Media aspect ratio", "label": "Aspect ratio", "info_cart": "Changes the aspect ratio for small product thumbnails used in the cart, search popup and some other areas that require small product thumbnails."}}}, "aspect_ratio": {"landscape": {"label": "<PERSON><PERSON> (3:2)"}}, "maps": {"name": "<PERSON><PERSON><PERSON>"}, "search": {"predictive_search": {"name": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ve makaleler i<PERSON>in önerileri desteklemektedir."}}, "product-card": {"name": "Ürün kartı", "title-size": {"name": "Başlık boyutu", "options__1": "Küçük", "options__2": "Büyük"}, "local-pickup": {"name": "<PERSON><PERSON> stok", "info": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> yerel stoğ<PERSON>. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/805-collection-page)"}, "badges": {"name": "Varsayılan ürün rozetleri", "settings": {"colors": {"text": "<PERSON>ozet metin rengi", "sold_out": "'Tükendi' arka planı rengi", "sale": "'İndirim' arka planı rengi"}}, "badge_sale": {"name": "İndirim rozeti"}, "custom_badges": {"name": "<PERSON>zel ürün rozetleri", "info": "<PERSON><PERSON> tema, burada tanımlayabileceğiniz özel ürün rozetlerini kullanır. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/795-product-card#Product-badges-_KIdK)", "name__1": "Özel rozet 1", "name__2": "Özel rozet 2", "name__3": "Özel rozet 3", "text": "<PERSON><PERSON>", "tags": "Etiket", "color": "Arka plan rengi", "text_color": "Text color"}}, "icons_list": "Dinamik simge listesi", "products-list": {"name": "Products list"}}, "embellishments": {"settings": {"show_gotop": {"label": "Show go to top button"}}}}, "sections": {"video": {"name": "Video", "settings": {"video": {"label": "Video URL"}, "image": {"label": "Arka plan görseli"}}}, "contact-form": {"settings": {"form-fields": {"name": "Form alanları", "show-phone": "Telefonu göster", "show-subject": "<PERSON><PERSON><PERSON> göster"}}, "blocks": {"contact-info": {"name": "İletişim bilgileri", "settings": {"title": {"label": "Başlık"}, "content": {"label": "İçerik"}}}}}, "text-columns-with-icons": {"blocks": {"icon": {"name": "<PERSON><PERSON> simge", "info": "256 x 256 piksel"}, "select_icon": {"info": "Daha fazla simgeyi görselleştirmek ve indirmek için lütfen [bu bağlantıyı](https://resources.krownthemes.com/icons/) ziyaret edin\n"}, "icon_color": {"label": "<PERSON>m<PERSON>", "info": "Yalnızca ekli simgeler için çalışır"}}}, "content-toggles": {"name": "İçerik geçişleri", "block": "İçerik"}, "text-columns-with-images": {"blocks": {"image": {"info": "640 x 640 piksel"}}}, "announcement-bar": {"settings": {"social": {"header": "So<PERSON><PERSON> medya sim<PERSON>eri", "info": "Sosyal medya profillerini a<PERSON>lamak için Te<PERSON> Ayarları > Sosyal Medya'ya gidin.", "label": "Sosyal medya simgeleri<PERSON>"}}, "blocks": {"content": {"name": "İçerik", "settings": {"text": "<PERSON><PERSON>", "link": "Bağlantı", "target": "Bağlantıyı yeni pencerede aç"}}}}, "newsletter": {"show_icon": "Simgeyi göster"}, "cookies": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> pencer<PERSON>", "cookies_info": "Bu site, en iyi kullanıcı deneyimini sağlamak için çerezleri kullanır. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/790-cookies-popup)."}, "popups": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blocks": {"model": {"model-1": "<PERSON><PERSON><PERSON><PERSON>", "model-2": "<PERSON><PERSON><PERSON>", "model-3": "<PERSON><PERSON>"}, "settings": {"size": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere boyutu", "option_1": "Küçük", "option_2": "Büyük"}}}}, "age-verification": {"name": "Yaş doğrulaması", "settings": {"button-text": "<PERSON><PERSON> metni"}}, "stores-map": {"name": "Mağaza haritası", "settings": {"map": {"title": "Haritası"}, "gallery": {"title": "Mağaza gal<PERSON>"}}}, "store-selector": {"name": "Mağaza seçici", "settings": {"map": {"label": "Dinamik haritayı etkinleştir", "info": "Tema Ayarları'nda Google Haritalar API Anahtarı'nın doğru bir şekilde ayarlandığından emin olun."}, "zoom": {"label": "<PERSON><PERSON>", "info": "İstediğiniz tüm mağazaları tek seferde görmek için uygun bir değer seçin."}}, "blocks": {"map": {"name": "<PERSON><PERSON> k<PERSON>u", "settings": {"address": {"label": "<PERSON><PERSON>", "info": "Daha fazla bilgi edinin"}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Dinamik haritayı kullanmak istemiyorsanız statik bir görüntü yü<PERSON>in."}, "style": {"label": "<PERSON><PERSON> stili", "option__1": "<PERSON><PERSON>", "option__2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "option__3": "Retro", "option__4": "Karanlık", "option__5": "<PERSON><PERSON><PERSON>", "option__6": "<PERSON><PERSON>"}, "pin": {"label": "<PERSON><PERSON>", "info": "240 x 240 piksel şeffaf .png"}}}, "store": {"name": "Mağaza", "settings": {"name": {"label": "Ad", "info": "Mağaza adının [konum ayarlarınızda](/admin/settings/locations) tanımlanan mağaza adınızla aynı olması gerekmektedir. "}, "pickup_price": {"label": "Teslim alma fiyatı"}, "pickup_time": {"label": "<PERSON><PERSON><PERSON> alma zamanı"}, "address": {"label": "Mağaza bilgileri"}, "image": {"label": "Mağaza görüntüsü"}, "closing_times": {"label": "Kapanış saatleri (isteğe bağlı)", "info": "Pazar gününden başlayarak haftanın her günü için tek sefer olmak üzere 7 satır ekleyin."}, "timezone": {"label": "Zaman <PERSON>", "info": "Kapanış zamanlarını düzgün göstermek için kullanılır"}, "map": {"name": "<PERSON><PERSON>", "info": "Harita etkinse bu adres için özel bir sabitleme tanımlamanız gerekir. [Adres koordinatlarınızı nasıl elde edeceğinizi öğrenin](https://support.google.com/maps/answer/18539?hl=en)"}, "map_latitude": {"label": "En<PERSON>", "info": "İşaretleyici için enlem koordinatı. Örnek: 46.7834818"}, "map_longitude": {"label": "<PERSON><PERSON>", "info": "İşaretleyici için boylam koordinatı. Örnek: 23.5464733"}, "get_directions_button": {"label": "\"Yol tarifi al\" but<PERSON><PERSON><PERSON>", "info": "Yeni bir tarayıcı sekmesinde daha büyük bir harita açar."}, "map_pin": {"label": "<PERSON><PERSON>", "info": "90 x 90 piksel şeffaf .png"}}}}}, "header": {"settings": {"layout": {"label": "Üst bil<PERSON>", "info": "<PERSON>zel blokların ve varsayılan işlemlerin konumunu etkiler", "option__1": "Özel bloklar üstte, varsayılan işlemler altta", "option__2": "Varsayılan işlemler üstte, özel bloklar altta"}, "sticky": {"label": "Yapışkan üst bilgi", "info": "Kullanıcı yukarı kaydırdığında gezinti çubuğunu gösterir"}}, "blocks": {"info": {"name": "<PERSON><PERSON><PERSON>", "style": {"label": "Stil", "option__1": "<PERSON><PERSON> bi<PERSON>", "option__2": "Buton", "info": "Butonlarda buton etiketi olarak yalnızca başlık görünür."}, "custom-icon": {"label": "<PERSON><PERSON> simge", "info": "76 x 76 piksel .png bir g<PERSON><PERSON><PERSON>"}, "icon": {"label": "<PERSON>m<PERSON>"}, "link_type": {"label": "Bağlantıyı aç", "option__1": "Kalıcı pencerenin içinde", "option__2": "Aynı sayfada", "option__3": "<PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, yalnızca dahili sayfa bağlantılarıyla çalışır"}}, "store-selector": {"name": "Mağaza seçici", "content": "<PERSON><PERSON><PERSON><PERSON>ç<PERSON>, <PERSON><PERSON><PERSON>a Seçici bölümünde yapılandırılabilir. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/799-store-selector)", "info": "<PERSON><PERSON> <PERSON><PERSON>, gerç<PERSON> mağaza konumlarınızı interaktif bir mağaza seçiciye bağlamanıza olanak tanır. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/799-store-selector)"}, "mega-menu": {"name": "Mega menü", "settings": {"menu_handle": {"label": "<PERSON><PERSON>", "info": "Bu tema mega menüleri kullanır. [<PERSON>ha fazla bilgi edin](https://shopify-support.krownthemes.com/article/798-header-section#mega-menus)"}}}}}, "marquee": {"name": "<PERSON><PERSON>", "settings": {"scroll_direction": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>", "scroll_speed": "<PERSON><PERSON><PERSON>rma hızı", "scroll_speed_info": "<PERSON><PERSON><PERSON> ne kadar büyükse o kadar yavaş kayar", "pause_on_mouseover": "Fare imleci üzerine geldiğinde duraklat", "scroll_item": "<PERSON><PERSON> ka<PERSON>ı<PERSON>", "scroll_item_text": "<PERSON><PERSON>"}}, "image-section": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"image_size": {"label": "Masaüstü genişliği", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mobil cihazlarda tam genişlikte olacaktır."}}}, "media-with-text-overlay": {"name": "<PERSON><PERSON> ka<PERSON>ı medya", "blocks": {"media": "<PERSON><PERSON><PERSON>", "image": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "link": {"info": "Düğme için bir etiket olmadığı sürece başlık bir bağlantıya dönüşecektir."}, "video": {"name": "Video", "label": "Video", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bu <PERSON>nun oynatılıp oynatılamayacağını gösterecektir."}}, "settings": {"height": "<PERSON><PERSON> yüksekliği", "option__1": "Küçük", "option__2": "Büyük", "option__3": "Çok büyük", "option__4": "<PERSON>", "option__5": "Normal"}}, "blog-posts": {"settings": {"emphasize": {"label": "<PERSON><PERSON> makaleyi vurgula", "info": "<PERSON><PERSON><PERSON>"}}, "blocks": {"summary": {"name": "Alıntı", "settings": {"excerpt_limit": "<PERSON><PERSON><PERSON> say<PERSON>", "excerpt_limit_info": "Makalenin yöneticiye manuel olarak eklenmiş bir alıntısı yoksa uygulanır."}}}}, "testimonials": {"name": "Referanslar", "blocks": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "slideshow": {"name": "<PERSON><PERSON><PERSON> gö<PERSON>", "block": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"caption_size": "Alt yazı boyutu"}}, "rich-text": {"settings": {"image_position": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "option__1": "Solda", "option__2": "Metnin üstünde", "option__3": "Sağda"}, "fullwidth": {"label": "<PERSON>", "info": "Ekranı doldurmak için bu bölümün arka planını genişletin."}, "height": {"label": "<PERSON><PERSON> yüksekliği", "info": "Masaüstünde kartın minimum yüksekliği. Mobilde yükseklik içeriğe bağlı olacaktır."}, "crop": {"label": "Görüntü alanını doldur", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, masaüstünde kartın tüm yüksekliğini dolduracak şekilde kırpılacaktır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mobilde her zaman tam olarak gösterilecektir."}, "remove_margin": {"label": "Bölüm üst kenar boşluğunu kaldır"}}}, "main-header": {"settings": {"mobile": {"name": "<PERSON><PERSON>", "info": "Bunlar yalnızca gezinti bölmesinin içindeki görünürlüğü etkiler.", "header_actions": "Mağaza seçici &amp; bilgi bloklarını göster", "header_info_blocks": {"header": "Üst bilgi blokları", "label_1": "Mobil cihazlarda mağaza seçici &amp; bilgi bloklarını üst bilgide görüntüle", "label_2": "Bilgi bloklarını ana sayfadaki ilk bölümün üstünde konumla", "label_2_info": "İ<PERSON> bölüm tam genişlikte bir slayt gösterisi olduğunda iyi bir şekilde entegre olur"}}, "promotion_block": {"title": {"label": "Başlık", "size": "Başlık boyutu"}, "subtitle": {"label": "Alt başlık", "size": "Alt başlık boyutu"}, "button": {"label": "<PERSON><PERSON> etiketi", "size": "<PERSON><PERSON> boyutu", "link": "Buton bağlantısı", "style": "Buton stili"}}, "header_actions": {"header": "Mobilde üst bilgi blokları", "show_in_drawer": "Gezinti bölmesin içerisinde göster"}}}, "main-cart": {"blocks": {"shipping-calculator": {"name": "<PERSON>rgo he<PERSON>"}, "related-products": {"info": "The product recommendations are based on products that are commonly purchased together or products in related collections. If there are no recommendations, the block will not appear."}}}, "main-search": {"settings": {"blogs": {"name": "Makale sonuçları"}, "products": {"name": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>ı<PERSON>, b<PERSON><PERSON><PERSON>m blokları kullanılarak ayarlanmalıdır."}}}, "main-product": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"gallery_pagination": "Galeri kaydırıcı sayfalandırması", "show_border": "Galeri etrafında kenarlığı göster", "gallery_border": "Gallery border", "gallery_border_color": "Gallery border color"}, "blocks": {"pickup_availability": {"name": "<PERSON><PERSON><PERSON> alma durumu", "info": "<PERSON><PERSON> <PERSON><PERSON>, se<PERSON><PERSON>n mağazaya göre teslim alma durumunu gösterir. Daha fazla bilgi edinin", "settings": {"style": "Stil", "option__1": "Kompakt", "option__2": "Genişletilmiş"}}, "buy_buttons": {"settings": {"show_price": "Fiyatı göster"}}, "related": {"name": "İlgili <PERSON>", "settings": {"products": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "tax_info": {"name": "<PERSON><PERSON>gi bil<PERSON>i"}, "icons": {"name": "<PERSON><PERSON><PERSON> list<PERSON>", "info": "Temada bulunan simgeleri görselleştirmek ve indirmek için lütfen [bu bağlantıyı](https://resources.krownthemes.com/icons/) ziyaret edin.", "help": "<PERSON><PERSON> <PERSON><PERSON>, dinamik içerik aracılığıyla özel ürün simgeleri eklemenize olanak tanır. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "headers": {"icon_1": "Simge 1", "icon_2": "Simge 2", "icon_3": "Simge 3", "icon_4": "Simge 4", "icon_5": "Simge 5", "icon_6": "Simge 6"}, "settings": {"icon": "<PERSON>m<PERSON>", "icon_info": "96 x 96 piksel", "label": "Etiket"}}}}, "main-blog": {"name": "Ana blog"}, "main-article": {"name": "Makale", "settings": {"show_tags": "Etiketleri göster", "enhance_product_links": {"label": "Ü<PERSON>ün bağlantılarını geliştir", "info": "<PERSON><PERSON>ü<PERSON>lere verilen tüm ba<PERSON>, ürün hızlı satın alım kalıcı penceresini açacaktır."}}}, "main-article-comments": {"name": "Makale yo<PERSON>ı", "info": "Yorumları etkinleştirmek için [blog ayarlarınıza](/admin/blogs) gidin."}, "main-article-navigation": {"name": "Makale gezintisi", "settings": {"header": {"content": "Blog gönderileri", "info": "Varsayılan önceki veya sonraki blog gönderisini yüklemek istiyorsanız boş bırakın."}, "posts": {"next": "<PERSON><PERSON><PERSON>", "previous": "Önceki g<PERSON>"}}}, "main-page": {"settings": {"center": {"label": "Masaüstünde içeriği ortala"}}}, "main-footer": {"blocks": {"payment": {"name": "<PERSON><PERSON><PERSON> si<PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [öde<PERSON> a<PERSON>](/admin/settings/payments) ve müşterinin bulunduğu bölge ve para birimine göre belirlenir.", "custom_icons": "Custom icons", "custom_icons_info": "Upload 114 x 72px .png images", "only_show_custom_icons": "Only show the custom icons"}, "text": {"name": "Text & social icons"}}}, "customers": {"reset-password": {"name": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "order": {"name": "Sipariş sayfası"}, "register": {"name": "<PERSON><PERSON>t say<PERSON>ı"}, "activate-account": {"name": "<PERSON><PERSON>p <PERSON>fa<PERSON>ını etkinleştir"}, "login": {"name": "<PERSON><PERSON><PERSON>", "shop_login_button": {"enable": "Shop ile giriş yapmayı etkinleştirin"}}, "account": {"name": "<PERSON><PERSON><PERSON>"}, "addresses": {"name": "<PERSON><PERSON><PERSON>"}}, "headings": {"heading": "Başlık", "subheading": "Alt başlık", "title": "Başlık", "subtitle": "Alt başlık", "caption": "Alt yazı", "text_content": "<PERSON><PERSON>", "custom_colors": "<PERSON><PERSON>", "text_style": "<PERSON><PERSON> stili"}, "columns": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>, kendisini mobil cihazlara göre u<PERSON>.", "option__0": "1 sütun", "option__1": "2 sütun", "option__2": "3 sütun", "option__3": "4 sütun", "option__4": "5 sütun", "option__5": "6 sütun", "name_mobile": "Mobile layout"}, "promotion-cards": {"name": "Promosyon kartları", "blocks": {"name": "Kart"}}, "faq": {"headings": {"header": "Üst bilgi", "content": "İçerik"}, "settings": {"form": {"header": "İletişim formu", "show": "<PERSON><PERSON>", "title": "Form başlığı"}}}, "product-quick-view": {"name": "Hızlı görünüm", "info": "<PERSON><PERSON>, ürün hızlı görünümünün nasıl oluşturulduğunu kontrol eder. Kalıcı pencerede sadece bu bölüm görünecektir."}, "product-card": {"blocks": {"price": "<PERSON><PERSON><PERSON>", "title": "Başlık", "vendor": "Satıcı", "text": {"name": "<PERSON><PERSON><PERSON> metin", "info": "Bir ürün meta alanı oluşturarak eşsiz bir özelliği vurgulamak için dinamik bir kaynak kullanın. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/805-collection-page)", "settings": {"text": {"label": "Etiket meta alanı"}, "size": {"label": "<PERSON><PERSON>", "option__1": "Küçük", "option__2": "Normal", "option__3": "Büyük"}, "color": {"label": "<PERSON><PERSON>", "option__1": "Birincil", "option__2": "İkincil"}, "transform": {"label": "<PERSON><PERSON> (büyük harf)"}}}, "icons": {"info": "Simge listesi için ürün meta alanları oluşturarak eşsiz özellikleri vurgulamak için dinamik kaynakları kullanın. [Daha fazla bilgi edinin](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "settings": {"icon": "Simge meta alanı", "label": "Etiket meta alanı"}}, "quick_buy": "Hızlı alım", "rating": "<PERSON><PERSON>"}}, "buttons": {"style": {"label": "Buton stili", "option__1": "Taslak", "option__2": "Do<PERSON>"}}}}, "complementary_products": {"name": "Tamamlayıcı ürünler", "settings": {"paragraph": {"content": "Tamamlayıcı ürünleri seçmek için Search & Discovery uygulamasını ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}}}, "split-extra-words": {"settings_schema": {"colors": {"headings": {"header_sidebars": "Başlık ve kenar çubukları", "main": "<PERSON><PERSON><PERSON><PERSON>", "footer": "Alt bilgi", "custom_colors": "<PERSON><PERSON>"}, "settings": {"background": "Arka plan", "text": "<PERSON><PERSON>", "links": "<PERSON><PERSON><PERSON> bağlantılar", "borders": "Kenarları göster"}}, "typography": {"headings": {"headings": "Başlıklar", "body": "<PERSON><PERSON><PERSON><PERSON>", "logo_menus": "Logo ve <PERSON>üler", "buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"font_family": "Yazı tipi ailesi", "base_size": "<PERSON><PERSON>u", "line_height": "Sa<PERSON>ır yü<PERSON>ği", "hr": {"label": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "info": "Bazı başlıklarda küçük bir görsel yatay cetvel gösterir"}, "border_radius": "Kenar yarıçapı"}}, "embellishments": {"preloader": {"label": "<PERSON><PERSON><PERSON>", "info": "Mağazandaki medya yüklenmekteyken küçük dairesel bir önyükleyici gösterir."}, "breadcrumb": {"label": "İçerik haritasını göster", "info": "İçerik haritası gezinmesi mağazada gezinmelerinde kullanıcılara yardımcı olur ve yalnızca koleksiyon, <PERSON><PERSON><PERSON><PERSON>, arama ve hesap sayfalarında gözükür."}}, "cart": {"page": "<PERSON><PERSON> mad<PERSON>", "show_recommendations": "Sepet önerilerini göster"}, "headings": {"title": "Başlık", "subtitle": "Alt başlık"}, "product-grid": {"animation_style": {"label": "Açıklama yazısı gösterimi (masaüstü)", "options__1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options__2": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON>, daha iyi UX için açıklama yazısı daima görünür o<PERSON>k"}, "overlay_colors": {"background": "Kaplama açıklama yazısı arka planı", "text": "Kaplama açıklama yazısı metni"}, "aspect_ratio": {"label": "<PERSON><PERSON><PERSON><PERSON> medya gö<PERSON>", "options__1": "Kesilmiş", "options__2": "<PERSON><PERSON><PERSON>"}, "show_secondary_image": {"info": "<PERSON><PERSON><PERSON>"}, "quick_buy": {"name": "Hı<PERSON>lı al", "info": "Anı<PERSON> bir \"sepete ekle\" d<PERSON><PERSON><PERSON><PERSON> ekler. Ürünün çeşitleri varsa, \"hızlı al\" açılan penceresi gösterecek.", "label": "Hızlı alı etkinleştir"}, "rating": {"label": "Derecelendirme <PERSON> (masaüstü)", "options__1": "G<PERSON><PERSON><PERSON>", "options__2": "Üzerine gelince g<PERSON>", "options__3": "Her zaman gö<PERSON>ü<PERSON>ür", "show_on_mobile": "Mobilde göster"}}}, "sections": {"header": {"name": "Başlık", "settings": {"logo_height": "Logo görseli azami yükseklik", "menu": "<PERSON><PERSON>", "menu_style": {"label": "Ma<PERSON>üst<PERSON> menü stili", "options__1": "Klasik", "options__2": "Çekmece"}, "collections_menu": {"header": "<PERSON>leks<PERSON><PERSON><PERSON>", "info": "Bunun cesur bir tasarı<PERSON> vardır,özellikle de görseller ve bir promosyon ekleme olasılığıyla bir mega menüye dönüştüğü klasik menü stilinde.", "settings": {"show_images": {"label": "Koleksiyon görsellerini göster", "info": "Sadece üst madde bir koleksiyonsa geçerlidir"}}}, "promotional_block": {"name": "Promosyon bloğu", "settings": {"show": {"label": "Promosyon bloğunu <PERSON>", "info": "Minimal stilde menü çerçevesinin altında gözükür. Klasik stilde eğer mevcutsa koleksiyonlar menüsünde gözükür."}, "title": {"label": "Promosyon başlığı"}, "content": {"label": "Promosyon içeriği"}, "button": {"label": "Promosyon düğmesi etiketi"}, "link": {"label": "Promosyon düğmesi bağlantısı"}, "txt_color": {"label": "Promosyon metni rengi"}, "bg_color": {"label": "Promosyon arka planı rengi"}, "image": {"label": "Promosyon görseli"}}}, "announcement_bar": {"content": {"info": "En fazla 50 karakter"}}}}, "footer": {"blocks": {"menu": {"name": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}}}, "main-product": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"header": {"label": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>ığı", "info": "Mobil cihazlarda ürün başlığı daima ürün galerisinin üzerinde, yukar<PERSON>da yer alacak.", "show_tax_info": "<PERSON><PERSON><PERSON> bi<PERSON><PERSON>", "show_reviews": "<PERSON><PERSON><PERSON>n derecelendirmesini göster", "show_sku": "SKU göster", "show_barcode": "BARKOD göster", "show_vendor": "Satıcıyı göster", "show_badge": "<PERSON><PERSON><PERSON><PERSON> am<PERSON> g<PERSON>"}, "variants": {"label": "<PERSON><PERSON><PERSON> seçici<PERSON> tipi", "options__1": "Bloklar", "options__2": "Açılır liste"}, "gallery_aspect": {"label": "Kaydırıcı görsellerini görünüm penceresine sığacak şekilde ölçekle", "info": "<PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> daima cihazın gör<PERSON><PERSON><PERSON><PERSON> penceresine sı<PERSON>acak."}, "color_swatches": {"label": "Renk örneklerini göster (yalnızca blok stili için)", "info": "Bu tema renk örnekleri için özel görseller gösterebilir. [Learn more](https://shopify-support.krownthemes.com/article/554-using-color-swatches)"}}}, "countdown-banner": {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "settings": {"header": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "show_countdown": "<PERSON><PERSON> <PERSON><PERSON><PERSON> saati<PERSON>", "countdown_year": "Bitiş yılı", "countdown_month": "Bitiş ayı", "countdown_day": "Bitiş günü", "countdown_hour": "Bitiş <PERSON>ati", "countdown_timezone": "Saat dilimi", "size": "Şerit yüksekliği"}}, "map": {"settings": {"map": {"api": {"label": "Google Haritalar API anahtarı", "info": "Haritayı görüntülemek için bir [Google Maps API Key](http://shopify-support.krownthemes.com/article/385-generate-an-api-key-for-google-maps) to display the map."}}}}}}, "main-cart-footer": {"name": "Sepet ara toplamı", "blocks": {"subtotal_button": {"name": "Ara <PERSON> ve ödeme"}}}, "main-cart-items": {"name": "<PERSON><PERSON> öğeleri"}, "main-list-collections": {"name": "Koleksiyonlar listesi sayfası", "blocks": {"collection": {"name": "Koleksiyon", "settings": {"collection": {"label": "Koleksiyon"}, "image": {"label": "G<PERSON><PERSON><PERSON>", "info": "Koleksiyon için özel bir görsel eklemek istiyorsanız."}}}}, "settings": {"header": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "layout": {"label": "D<PERSON>zen", "options__1": {"label": "<PERSON><PERSON>tun"}, "options__2": {"label": "<PERSON><PERSON>"}}, "paragraph": {"content": "Varsayılan olarak tüm koleksiyonlarınız listelenir. Listenizi kişiselleştirmek için 'Seçili'yi seçin ve koleksiyonlar ekleyin."}, "display_type": {"label": "Gösterilecek koleksiyonları seçin", "options__1": {"label": "Tümü"}, "options__2": {"label": "Seçili"}}, "sort": {"label": "Koleksiyonları sırala:", "info": "Sıralama yalnızca 'Tümü' seçili olduğunda uygulanır", "options__1": {"label": "Alfabetik, A-Z"}, "options__2": {"label": "Alfabetik, Z-A"}, "options__3": {"label": "<PERSON><PERSON><PERSON>, yeniden eskiye"}, "options__4": {"label": "<PERSON><PERSON><PERSON>, eskiden yeniye"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON>, azalan"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>, artan"}}, "items_per_row": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON>na öğe say<PERSON>ı"}}, "sidebar": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Lo<PERSON>"}, "image_width": {"label": "Logo görseli genişliği"}, "primary_navigation": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_navigation": {"label": "İkincil navigasyon"}, "search": {"content": "Ara", "label": "Aramayı göster"}}}, "text-columns-with-icons": {"name": "<PERSON><PERSON><PERSON><PERSON> metin <PERSON>ı", "settings": {"content": {"label": "<PERSON><PERSON><PERSON>:"}, "show_on_homepage": {"label": "<PERSON>"}, "show_on_product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_on_collection": {"label": "Koleksiyon <PERSON>"}, "show_on_blog": {"label": "Blog ve makale say<PERSON>ları"}, "show_on_regular": {"label": "Normal sayfalar"}, "icons": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Temadaki tüm simgeleri görselleştirmek için lütfen  [this link](https://krownthemes.com/krown-icons/) adresini ziyaret edin"}}, "blocks": {"icon": {"name": "<PERSON><PERSON><PERSON><PERSON> metin", "settings": {"title": {"label": "Başlık"}, "text": {"label": "<PERSON><PERSON>"}, "icon": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "footer": {"name": "Alt bilgi", "settings": {"show_payment_icons": {"label": "<PERSON>deme simgeleri<PERSON>"}, "language_selector": {"content": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON> eklemek i<PERSON> [language settings.](/yönetici/ayarlar/diller) b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gidin"}, "language_selector_show": {"label": "<PERSON><PERSON> <PERSON>ç<PERSON><PERSON> gö<PERSON>"}, "country_selector": {"content": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>", "info": "<PERSON><PERSON><PERSON>/bölge e<PERSON> [ödeme a<PERSON>larınıza ](/admin/settings/payments) gidin."}, "country_selector_show": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON>kinleştir"}}, "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"title": {"label": "Başlık"}, "content": {"label": "İçerik"}, "text_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Düz"}, "options__2": {"label": "Büyük"}}}}, "menus": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title_1": {"label": "İlk menü başlığı"}, "title_2": {"label": "İkinci menü başlığı"}, "menu_1": {"label": "İlk menü", "info": "Bu menü açılır liste ürünlerini göstermez"}, "menu_2": {"label": "<PERSON><PERSON><PERSON> menü"}}}, "newsletter": {"name": "E-posta kaydı"}, "social": {"name": "<PERSON><PERSON><PERSON> b<PERSON>ğ<PERSON>ılar"}, "image": {"name": "G<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON> seç"}}}}}, "contact-form": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"title": {"label": "Başlık"}}, "blocks": {"field": {"name": "Form alanı", "settings": {"type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Tek sıra"}, "options__2": {"label": "Çoklu sıra"}}, "required_field": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "labels": {"label": "Etiket", "info": "Tüm alanlarınızın farklı etiketlerinin olduğundan emin olun!"}}}, "email": {"name": "Ad ve e-posta"}, "button": {"name": "<PERSON><PERSON><PERSON>", "settings": {"label": {"label": "Etiket"}}}}, "presets": {"name": "İletişim formu"}}, "image-with-text": {"name": "<PERSON><PERSON><PERSON>", "blocks": {"image": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "Başlık"}, "body": {"label": "<PERSON><PERSON>"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "url": {"label": "Bağlantı", "info": "Düğme için bir etiket olmadığı takdirde tüm blok bir bağlantıya dönüşecek"}, "image": {"label": "Arka plan görseli"}}}}, "settings": {"image_height": {"label": "Görsel yüksekliği", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "options__4": {"label": "Tam"}}, "text_width": {"label": "<PERSON><PERSON> kut<PERSON> g<PERSON>", "options__1": {"label": "Orta"}, "options__2": {"label": "Büyük"}, "options__3": {"label": "Tam"}}, "text_size": {"label": "Başlık boyutu", "options__1": {"label": "Düz"}, "options__2": {"label": "Büyük"}, "options__3": {"label": "Çok büyük"}}, "text_alignment": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON> ü<PERSON>"}, "options__2": {"label": "Or<PERSON> ü<PERSON>"}, "options__3": {"label": "Sağ üst"}, "options__4": {"label": "Sol orta"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON>ğ orta"}, "options__7": {"label": "Sol alt"}, "options__8": {"label": "Orta alt"}, "options__9": {"label": "Sağ alt"}}, "options__5": {"label": "Ekstra büyük"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "featured-product": {"name": "<PERSON><PERSON>ü<PERSON>", "settings": {"product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"product_link": {"name": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}}}, "featured-collection": {"name": "Öne çıkan koleksiyon", "settings": {"title": {"label": "Başlık"}, "show_view_all": {"label": "Koleksiyon sayfasına bağlantı göster"}, "layout": {"label": "D<PERSON>zen", "options__1": {"label": "Kaydırıcı"}, "options__2": {"label": "<PERSON><PERSON>"}}, "products_number": {"label": "Gösterilen en fazla ürün sayısı"}, "collection": {"label": "Koleksiyon"}}, "presets": {"name": "Öne çıkan koleksiyon"}}, "gallery": {"name": "<PERSON><PERSON>", "blocks": {"image": {"name": "G<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "caption": {"label": "Alt başlık"}, "featured": {"label": "Karedeki gö<PERSON> b<PERSON>"}}}}, "settings": {"aspect_ratio": {"label": "G<PERSON>rsellerin en boy oranı", "options__1": {"label": "<PERSON><PERSON><PERSON> (4:3)", "group": "Kesilmiş"}, "options__2": {"label": "<PERSON><PERSON> (1:1)"}, "options__3": {"label": "<PERSON><PERSON><PERSON> (5:6)"}, "options__4": {"label": "<PERSON><PERSON> (2:3)"}, "options__5": {"label": "<PERSON><PERSON><PERSON>", "group": "Kırpılmamış"}, "info": "Doğal en boy oranın<PERSON> kullan<PERSON>rken, temiz bir karelaj tasarımı için küçük görsellerinizi aynı boyuta yeniden boyutlandırdığınızdan emin olun. Kırpma ayarlarından birini kullanarak tüm küçük görseller aynı boyuta boyutlandırılır."}, "style_mobile": {"label": "Mobilde galeriyi bir kaydırıcıya dönüştür"}, "slider_height": {"label": "<PERSON><PERSON> ka<PERSON>ırı<PERSON>ı boyu", "options__1": {"label": "Orta"}, "options__2": {"label": "Büyük"}, "options__3": {"label": "Tam"}}, "lightbox": {"label": "Işık kutusunu etkinleştir", "info": "Tıklama ile daha büyük görsel gösterir"}}, "presets": {"name": "<PERSON><PERSON>"}}, "heading": {"name": "Başlık", "settings": {"title": {"label": "Başlık"}}, "presets": {"name": "Başlık"}}, "image": {"name": "G<PERSON><PERSON><PERSON>", "mobile_image": "Mobil resim (isteğe bağlı)", "fullwidth": "<PERSON> g<PERSON>"}, "apps": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"include_margins": {"label": "B<PERSON>lüm kenar başlıklarını tema ile aynı yap"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "<PERSON><PERSON> metin", "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}, "heading_size": {"label": "Başlık boyutu", "options__1": {"label": "Düz"}, "options__2": {"label": "Büyük"}, "options__3": {"label": "Çok büyük"}}}}, "icon": {"name": "<PERSON>m<PERSON>"}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}}}, "button": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "button_size": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Düz"}, "options__2": {"label": "Büyük"}}}}}, "settings": {"text_alignment": {"label": "<PERSON><PERSON>", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}}, "image": {"label": "G<PERSON><PERSON><PERSON>"}, "image_position": {"label": "<PERSON><PERSON><PERSON><PERSON> konumu", "options__1": {"label": "Sol"}, "options__2": {"label": "Sağ"}}, "image_height": {"label": "Görsel yüksekliği", "options__1": {"label": "Düz"}, "options__2": {"label": "Büyük"}, "options__3": {"label": "Tam"}}}, "presets": {"name": "<PERSON><PERSON> metin"}}, "shop-the-look": {"name": "Shop the look", "settings": {"heading": {"label": "Başlık"}, "subheading": {"label": "Alt başlık"}, "image": {"label": "Arka plan görseli"}}, "blocks": {"product": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"select_product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Shop the look"}}, "testimonials": {"name": "Referanslar", "blocks": {"testimonial": {"name": "Müşteri görüşü", "settings": {"quote": {"label": "Alıntı"}, "author_name": {"label": "<PERSON><PERSON> adı"}, "author_title": {"label": "<PERSON><PERSON> b<PERSON>"}, "author_avatar": {"label": "<PERSON><PERSON> a<PERSON>"}}}}, "presets": {"name": "Referanslar"}}, "announcement-bar": {"name": "<PERSON><PERSON><PERSON>", "settings": {"bar_show": {"label": "<PERSON><PERSON><PERSON>"}, "bar_show_on_homepage": {"label": "Yalnızca ana sayfada göster"}, "bar_show_dismiss": {"label": "Kapatma düğmesini göster"}, "bar_message": {"label": "İçerik"}, "bar_link": {"label": "Bağlantı"}, "bar_bgcolor": {"label": "Arka plan rengi"}, "bar_txtcolor": {"label": "<PERSON><PERSON>"}}}, "text-columns-with-images": {"name": "<PERSON><PERSON><PERSON><PERSON> metin <PERSON>ı", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"title": {"label": "Başlık"}, "text": {"label": "<PERSON><PERSON>"}, "image": {"label": "G<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON> metin <PERSON>ı"}}, "slider": {"slider_horizontal": {"name": "<PERSON><PERSON><PERSON> gösterisi: yatay"}, "slider_vertical": {"name": "<PERSON><PERSON><PERSON> gösterisi: dikey"}, "settings": {"desktop_height": {"label": "Masaüstü kaydırıcı boyu", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "options__4": {"label": "Tam"}}, "mobile_height": {"label": "<PERSON><PERSON><PERSON> mobil kaydırıcı boyu"}, "text_style": {"header": "<PERSON><PERSON> stili"}, "mobile_design": {"header": "Mobil ta<PERSON>ım", "label": "Mobilde dikey kaydırıcıyı yataya dönüştür"}}, "blocks": {"image": {"name": "G<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "heading": {"label": "Başlık"}, "subheading": {"label": "Alt başlık"}, "caption": {"label": "Alt başlık"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "link": {"label": "Bağlantı", "info": "<PERSON><PERSON>ğ<PERSON> için bir etiket olmaması halinde bağlantı metnin üzerinde olur."}}}}}, "video-popup": {"name": "Video: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere", "settings": {"video": {"label": "Video URL"}, "image": {"label": "Arka plan görseli"}}}, "video-background": {"name": "Video: arka plan", "settings": {"video": {"label": "Video URL", "info": "Bir .mp4 dosyasının konumu"}, "image": {"label": "<PERSON><PERSON>", "info": "<PERSON><PERSON>, otomatik oynatmanın devre dışı bırakılmış olabilecek mobil cihazlarda kullanılır."}, "size_alignment": {"content": "<PERSON><PERSON> ve hi<PERSON>ama"}, "video_height": {"label": "Video yüksekliği", "options__1": {"label": "<PERSON><PERSON><PERSON> (16:9)", "group": "Kırpılmamış"}, "options__2": {"label": "Büyük", "group": "Kesilmiş"}, "options__3": {"label": "Tam"}}}}, "main-password-header": {"name": "<PERSON><PERSON><PERSON> b<PERSON>ı<PERSON>ı"}, "main-password-content": {"name": "<PERSON><PERSON><PERSON>i"}, "main-password-footer": {"name": "Şifre alt bilgisi", "settings": {"show_social": {"label": "<PERSON><PERSON> sim<PERSON> g<PERSON>"}}}, "main-article": {"name": "Blog gönderisi", "blocks": {"featured_image": {"name": "<PERSON>ne <PERSON>", "settings": {"image_height": {"label": "<PERSON><PERSON> g<PERSON>l boyu", "options__1": {"label": "Görsele adapte et"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}}}}, "title": {"name": "Başlık", "settings": {"blog_show_date": {"label": "<PERSON><PERSON><PERSON>"}, "blog_show_author": {"label": "Yazarı göster"}, "blog_show_comments": {"label": "<PERSON><PERSON> sayı<PERSON>ını göster"}}}, "content": {"name": "İçerik"}, "social_sharing": {"name": "<PERSON><PERSON><PERSON>"}, "blog_navigation": {"name": "Bitişik gönderiler bağlantıları"}}}, "main-blog": {"settings": {"header": {"content": "Blog gönderisi kartı"}, "enable_tags": {"label": "Etiketlere göre filtrelemeyi etkin<PERSON>ştir"}, "post_limit": {"label": "<PERSON><PERSON> ba<PERSON>ına gönder<PERSON> sayı<PERSON>ı"}}}, "blog-posts": {"name": "Blog gönderileri", "blocks": {"title": {"name": "Başlık"}, "info": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "Yazarı göster"}}}, "summary": {"name": "Alıntı"}, "link": {"name": "Bağlantı"}}, "settings": {"title": {"label": "Başlık"}, "blog": {"label": "Blog"}, "post_limit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_image": {"label": "<PERSON>ne çıkan görseli göster"}, "show_view_all": {"label": "Blog sayfasına bağlantıyı göster"}, "layout": {"label": "D<PERSON>zen"}, "option_1": {"label": "Tek <PERSON>", "group": "<PERSON><PERSON>"}, "option_2": {"label": "<PERSON><PERSON>"}, "option_3": {"label": "Esnek (2 - 5 sütun)", "group": "Kaydırıcı"}}, "presets": {"name": "Blog gönderileri"}}, "custom-colors": {"heading": {"label": "Başlık"}, "text": {"label": "<PERSON><PERSON> metin rengi"}, "overlay": {"label": "Arka plan düzeni"}, "background": {"label": "Özel arka plan rengi"}}, "custom-gutter": {"heading": {"content": "Cilt payı"}, "gutter_enabled": {"label": "İçerik içi aralığı etkinleştir"}}, "newsletter": {"name": "E-posta kaydı", "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "paragraph": {"name": "Alt başlık", "settings": {"paragraph": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}, "email_form": {"name": "E-postu formu"}}, "presets": {"name": "E-posta kaydı"}}, "product-recommendations": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "Başlık"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>", "info": "Zamanla değişmek ve gelişmek için ürün bilgisi ve dinamik öneri kullanım sırası. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)"}}}, "custom-liquid": {"name": "Özel Liquid", "settings": {"custom_liquid": {"label": "Özel Liquid"}}, "presets": {"name": "Özel Liquid"}}, "collection-list": {"name": "Koleks<PERSON><PERSON>esi", "presets": {"name": "Koleks<PERSON><PERSON>esi"}}, "faq": {"name": "SSS", "settings": {"title": {"label": "Başlık"}, "open_first": {"label": "İlk değiş<PERSON>i <PERSON> olarak yap"}}, "blocks": {"text": {"name": "SSS", "settings": {"title": {"label": "Başlık"}, "text": {"label": "<PERSON><PERSON>"}, "image": {"label": "G<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "SSS"}}, "popup": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere", "settings": {"title": {"label": "Başlık"}, "content": {"label": "İçerik"}, "show_newsletter": {"label": "E-posta kayıt formunu g<PERSON>"}, "functionality": {"content": "İşlevsellik"}, "enable": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r"}, "show_after": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pen<PERSON>ey<PERSON>", "info": "sn"}, "frequency": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere sıklığı", "options__1": {"label": "Her günü göster"}, "options__2": {"label": "Her haftayı göster"}, "options__3": {"label": "Her ayı göster"}}, "image": {"label": "G<PERSON><PERSON><PERSON>", "info": "1240 x 400px .jpg önerilir Yalnızca masaüstünde görünür"}}}, "main-search": {"name": "<PERSON><PERSON>", "settings": {"products_per_page": {"label": "<PERSON><PERSON> başına sonuç sayı<PERSON>ı"}}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "settings": {"products_per_page": {"label": "<PERSON><PERSON> başına ürün say<PERSON>ı"}, "enable_filtering": {"label": "<PERSON>lt<PERSON><PERSON><PERSON><PERSON>", "info": "[Customize filters](/admin/menus)"}, "enable_sorting": {"label": "Sıralamayı etkinleştir"}, "image_filter_layout": {"label": "Görüntü filtresi düzeni"}, "header__1": {"content": "Filtreleme ve sıralama"}}}, "main-collection-banner": {"name": "Koleksiyon şeridi", "settings": {"paragraph": {"content": "Koleksiyon açıklamalarını ve resimlerini değiştirmek için, [edit your collections.](/admin/collections)"}, "show_collection_description": {"label": "Koleksiyon açıklamasını göster"}, "show_collection_image": {"label": "Koleksiyon görselini göster", "info": "En iyi sonuçlar için 16:9 en boy oran<PERSON><PERSON> bir g<PERSON><PERSON><PERSON><PERSON><PERSON> kullan"}}}, "main-product": {"name": "<PERSON><PERSON><PERSON><PERSON> bil<PERSON>", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt başlık"}, "options__3": {"label": "Büyük harf"}}}}, "title": {"name": "Başlık"}, "price": {"name": "<PERSON><PERSON><PERSON>"}, "tax_info": {"name": "<PERSON><PERSON><PERSON> bi<PERSON><PERSON>"}, "sku_barcode": {"name": "SKU / barkod"}, "quantity_selector": {"name": "<PERSON><PERSON><PERSON>"}, "variant_picker": {"name": "Çeşit seçici", "settings": {"show_variant_labels": {"label": "Çeşit etiketlerini göster"}, "hide_out_of_stock_variants": {"label": "Stokta olmayan çeşitleri gizle"}, "low_inventory_notification": {"label": "<PERSON><PERSON><PERSON>", "info": "Bu özelliğin çalışması için envanter takibinin çeşitlerde etkinleştirilmesi gereklidir. [Learn more](https://help.shopify.com/en/manual/products/inventory/track_inventory)", "options__1": {"label": "<PERSON>vant<PERSON> bil<PERSON> g<PERSON>"}, "options__2": {"label": "Envanter 5'in altına düşerse bir bildirim göster"}, "options__3": {"label": "<PERSON><PERSON><PERSON> her zaman g<PERSON>ster"}}}}, "buy_buttons": {"name": "Satın alma but<PERSON>ları", "settings": {"show_dynamic_checkout": {"label": "Dinamik ödeme butonlarını göster", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mağazandaki mevcut ödeme yöntemlerini kullanarak PayPal veya Apple Pay gibi tercih ettiği seçenekleri görür. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Hediye kartı ürünleri için alıcı formunu göster", "info": "Etkinleş<PERSON>ril<PERSON><PERSON><PERSON>e, hediye kartı ürünleri isteğe bağlı olarak kişisel bir mesajla birlikte bir alıcıya gönderilebilir."}, "show_quantity_selector": {"label": "<PERSON><PERSON><PERSON>"}}}, "pickup_availability": {"name": "Toplama kullanılabilirliği"}, "description": {"name": "<PERSON><PERSON>ı<PERSON><PERSON>", "settings": {"product_description_truncated": {"label": "Açıklamayı kısalt", "info": "Kısalt", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Kısa bir alıntı göster"}, "options__3": {"label": "Orta uzunlukta bir alıntı göster"}, "options__4": {"label": "Uzun bir alıntı göster"}}}}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bir bağlantı eklersen sayfanın öne çıkan görüntüsü ön izleme görüntüsü olarak gösterilecektir. [Learn more](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Ma<PERSON>az<PERSON> ba<PERSON>l<PERSON>ğ<PERSON> ve açıklama önizleme görüntüsü ile birliktedir. [Learn more](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "Açılabilir sekme", "settings": {"heading": {"info": "İçeriği açıklayan bir başlık ekle", "label": "Başlık"}, "content": {"label": "<PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON> i<PERSON>"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}}, "settings": {"header": {"content": "<PERSON><PERSON><PERSON>", "info": "Hakkında daha fazla bilgi edin [media types](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Geniş ekranlardaki yapışkan ürün bilgisini etkinleştir"}, "enable_video_looping": {"label": "Video döngüs<PERSON><PERSON><PERSON>ş<PERSON>r"}, "enable_zoom": {"label": "Görüntü yakınlaştırmayı etkinleştir"}, "gallery_gutter": {"label": "<PERSON><PERSON><PERSON> bo<PERSON> ekle"}, "gallery_slider_style": {"label": "Kaydırıcı görsellerini görünüm penceresine sığacak şekilde ölçekle"}, "gallery_style": {"label": "<PERSON><PERSON> stili", "info": "Mobil cihazlar için kaydırıcı varsayılanları", "options__1": {"label": "Kaydır"}, "options__2": {"label": "Kaydırıcı"}}, "gallery_pagination": {"label": "<PERSON><PERSON>", "options__1": {"label": "Noktalar"}, "options__2": {"label": "Küçük resimler"}}}}, "words_210": {"text_columns": {"name": "Text columns", "block": {"name": "Text"}}}, "images": {"label_1": "Resim 1", "label_2": "Resim 2", "label_3": "Resim 3"}}, "color-schemes": {"name_plural": "Color schemes", "name_singular": "Color scheme", "default": "Default color scheme", "default_info": "Used to set colors for account pages, 404 pages, and other pages or sections without color scheme settings.", "drawers": "Modals/drawers", "other": "Other colors"}, "local-250": {"labels": {"show_filters_as": "Filtreleri <PERSON><PERSON> şek<PERSON>", "expand_filters_by_default": "Filtreleri varsayılan olarak genişlet", "stick_filters_sidebar_to_top": "Filtre kenar çubuğunu üste yapıştır"}, "options": {"sidebar": "<PERSON><PERSON>", "list": "Liste"}}, "local-230": {"background_gradient": "Arka plan gradyanı", "variant_default": {"label": "Varsayılan olarak mevcut ilk seçeneği kullan", "info": "Bu seçenek işaretlenmezse kullanıcının satın almadan önce mevcut seçeneklerden birini seçmesi gerekir."}, "slider_info": "<PERSON><PERSON><PERSON><PERSON>, but<PERSON>, ya da b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (eğer buton yoksa) uygulanacak, veya her ikisi de bo<PERSON>sa tüm slayda uygulanacaktır.", "buy_button_labels": {"label": "Satın alma düğmesi etiketleri", "option_1": "<PERSON><PERSON><PERSON> al", "option_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>"}, "hide_on_mobile": "<PERSON><PERSON> gizle"}, "local-223": {"heading_text_color": "Başlık metni re<PERSON>i", "slider_navigation_color": "Navigasyon elemanları rengi"}, "late_edits": {"badge": {"custom_badge": {"text_color": "<PERSON><PERSON>"}, "sold_out": {"name": "Satılan rozet", "text_color": "'<PERSON><PERSON><PERSON>' <PERSON><PERSON>", "sale_text": "'<PERSON><PERSON><PERSON>' metin rengi"}}, "rich-text": {"image_position": {"no_image": {"group": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}}}}}