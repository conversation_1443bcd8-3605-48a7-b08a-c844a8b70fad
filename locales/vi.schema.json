{"variant_metafields": {"name": "Tr<PERSON><PERSON><PERSON> siêu dữ liệu biến thể", "label": "<PERSON><PERSON><PERSON><PERSON> trường siêu dữ liệu biến thể", "info": "<PERSON><PERSON><PERSON> di<PERSON>n này có thể hiển thị trường siêu dữ liệu biến thể trên trang sản phẩm. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/884-using-variant-metafields-in-product-pages)."}, "new_color_swatches": "Loại trình chọn biến thể \"khối\" hỗ trợ các mẫu màu được tạo bằng các trường siêu dữ liệu danh mục. [Tìm hiểu thêm](https://help.shopify.com/en/manual/custom-data/metafields/category-metafields/using-category-metafields).", "video_controls": "<PERSON><PERSON>n thị điều khiển video", "sticky_cart_actions": "<PERSON><PERSON>t hành động giỏ hàng cố định", "currency_codes": {"header": "<PERSON><PERSON><PERSON> dạng tiền tệ", "label": "<PERSON><PERSON><PERSON> thị mã tiền tệ", "info": "Ví dụ: $1.00 USD."}, "a11": {"label": "<PERSON><PERSON><PERSON> n<PERSON>ng ti<PERSON> cận", "show_sidebars_scrollbar": "<PERSON><PERSON><PERSON> thị thanh cuộn của thanh bên", "disable_all_image_animations": "<PERSON><PERSON> hiệu hóa tất cả các hoạt <PERSON>nh hình <PERSON>nh"}, "divider": {"label": "<PERSON><PERSON><PERSON>", "divider_design": "<PERSON><PERSON><PERSON><PERSON> kế ngăn c<PERSON>ch", "divider_style_solid": "<PERSON><PERSON><PERSON>", "divider_style_dotted": "<PERSON><PERSON><PERSON>", "divider_style_dashed": "Gạch", "divider_style_double": "<PERSON><PERSON><PERSON>", "divider_color": "<PERSON><PERSON><PERSON>", "divider_image": "<PERSON><PERSON><PERSON> ng<PERSON>n c<PERSON>ch", "divider_image_info": "<PERSON><PERSON>nh ảnh lặp lại theo chiều ngang. Thay thế kiểu và màu sắc phía trên."}, "cart_actions": {"label": "<PERSON><PERSON>nh động với giỏ hàng dạng ngăn", "option_1": "<PERSON><PERSON><PERSON> thị nút \"xem giỏ hàng\"", "option_2": "<PERSON><PERSON><PERSON> thị nút \"thanh toán\"", "option_3": "<PERSON><PERSON><PERSON> thị cả hai"}, "sticky_atc": {"label": "<PERSON><PERSON>ê<PERSON> vào giỏ hàng cố định", "enable_sticky_atc": "<PERSON><PERSON><PERSON> ho<PERSON>t thêm vào giỏ hàng cố định", "sticky_atc_style_wide": "Wide", "sticky_atc_style_floating": "Floating"}, "performance": {"header": "SEO & hi<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> liên kết khi di chuột qua", "info": "<PERSON><PERSON><PERSON> tốc độ tải trang cảm nhận đư<PERSON>."}, "recently_viewed": {"enable_recently_viewed_products": "<PERSON><PERSON><PERSON> ho<PERSON>t sản phẩm đã xem gần đây", "enable_recently_viewed_products_info": "<PERSON><PERSON> đư<PERSON><PERSON>, chủ đề sẽ ghi lại các sản phẩm đã xem, nhưng bạn cần thêm phần này vào cửa hàng của bạn để hiển thị những sản phẩm này.", "recently_viewed_products": "<PERSON><PERSON>n phẩm đã xem gần đây", "recently_viewed_products_info": "Phần này cần đư<PERSON><PERSON> kích hoạt trong Cài đặt Chủ đề. Nó chỉ hiển thị sau khi người dùng truy cập ít nhất một trang sản phẩm.", "recently_viewed_products_limit": "<PERSON><PERSON><PERSON><PERSON> hạn sản phẩm đã xem gần đây"}, "rating_apps_update": {"label": "Ứng dụng đánh giá", "info": "Ứng dụng của bên thứ ba có thể yêu cầu các bước thêm để được tích hợp đúng cách."}, "local-220": {"preorder": "<PERSON><PERSON><PERSON> thị nhãn nút \"đặt hàng trước\"", "autorotate": {"heading": "Tự động xoay", "info": "Tự động chuyển qua các slide.", "enable": "<PERSON><PERSON>t chế độ tự động xoay", "interval": "<PERSON><PERSON><PERSON><PERSON> thời gian", "pause_on_mouseover": "<PERSON><PERSON><PERSON> dừng khi di chuột lên"}}, "custom-social-icons": {"header": "<PERSON><PERSON><PERSON> kết tùy chỉnh", "info": "<PERSON><PERSON><PERSON> lên biểu tượng tùy chỉnh cho mạng xã hội yêu thích của bạn", "icon": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "72 x 72px trong suốt .png"}, "link": {"label": "<PERSON><PERSON><PERSON>"}}, "local-march-update": {"labels": {"dynamic_content": {"name": "<PERSON><PERSON>i dung động", "hide_block": "Ẩn khối nếu không có nội dung động", "hide_section": "Ẩn mục nếu không có nội dung động"}, "buttons": "<PERSON><PERSON><PERSON>", "cards": "Thẻ", "heading": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "buttons_custom": "<PERSON><PERSON><PERSON> nút tùy chỉnh", "center_heading": "<PERSON>i<PERSON><PERSON> đ<PERSON> ch<PERSON>h gi<PERSON>a", "section_design": "<PERSON><PERSON><PERSON><PERSON> kế phần", "bottom_margin": "<PERSON>óa l<PERSON> dưới", "text_spacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch v<PERSON><PERSON> bản", "inherit_card_design": "<PERSON><PERSON> thừa các thuộc tính thiết kế thẻ", "align_button": "<PERSON><PERSON>n chỉnh nút mua ở dưới cùng của thẻ", "custom_colors": "<PERSON><PERSON><PERSON> s<PERSON>c tùy chỉnh"}, "shadows": {"label": "Bóng", "label_plural": "Bóng", "offset_x": "<PERSON><PERSON> d<PERSON> ngang", "offset_y": "<PERSON><PERSON> d<PERSON><PERSON> d<PERSON>", "blur": "<PERSON><PERSON><PERSON>", "hide": "Ẩn bóng", "hide_button_shadows": "Ẩn bóng của nút"}, "blocks": {"countdown_timer": {"name": "<PERSON><PERSON><PERSON> hồ đếm <PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON> linh động", "info": "Đặt nguồn thời gian linh động cho đồng hồ đếm ngược. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/642-product-pages#countdown-timer)"}, "progress_slider": {"name": "<PERSON><PERSON><PERSON><PERSON> đồ tiến trình dạng thanh", "value": "<PERSON><PERSON><PERSON> trị", "height": "<PERSON><PERSON><PERSON> cao thanh tr<PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON> rộ<PERSON>h tr<PERSON>", "dynamic_content": {"info": "Sử dụng các nguồn linh động để xác định các giá trị không trùng lặp bằng cách tạo trường thông tin bổ sung cho sản phẩm đối với biểu đồ tiến trình. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/804-product-page#Progress-chart-S8c6R)", "slider_label": "Tr<PERSON><PERSON><PERSON> thông tin bổ sung của giá trị", "dots_label": "Tr<PERSON><PERSON><PERSON> thông tin bổ sung dạng chấm đ<PERSON><PERSON><PERSON> đ<PERSON>h dấu"}}, "progress_dots": {"name": "<PERSON><PERSON><PERSON><PERSON> đồ tiến trình dạng chấm", "highlight": "<PERSON><PERSON><PERSON> chấm đ<PERSON><PERSON><PERSON> đ<PERSON>h dấu", "total": "Tổng số dấu chấm", "icon": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON>ng dấu chấm", "size": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> d<PERSON>u chấm", "inactive_color": "<PERSON><PERSON><PERSON>n", "active_color": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> đ<PERSON> dấu"}, "store_selector": {"default": "Mặc định là cửa hàng đầu tiên"}, "rating": {"app": "<PERSON><PERSON><PERSON> gi<PERSON>ng dụng", "default_option": "Mặc định"}, "space": {"name": "<PERSON><PERSON><PERSON><PERSON> trống"}, "badges": {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m"}, "nutritional": {"name": "Thông tin dinh dưỡng", "label_left": "<PERSON><PERSON><PERSON><PERSON> cột bên trái", "label_right": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>t bên ph<PERSON>i", "information": {"label": "Thông tin", "info": "Tách biệt nhãn và giá trị bằng dấu phẩy. Sử dụng ngắt dòng để thêm hàng mới. Sử dụng dấu gạch nối để thụt đầu hàng. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/804-product-page#Nutritional-information-ypnLL)"}, "extra_information": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung"}}, "sections": {"progress_sliders": {"name": "<PERSON><PERSON><PERSON><PERSON> đồ tiến trình dạng thanh", "block_name": "Thanh"}, "header": {"settings": {"promotion": {"header_1": "Khuyến mãi 1", "header_2": "Khuyến mãi 2", "header_3": "Bố cục menu", "show": "<PERSON><PERSON><PERSON> thị khu<PERSON>ến mãi", "image": "<PERSON><PERSON><PERSON> mãi", "text": "<PERSON><PERSON><PERSON> dung khu<PERSON>ến mãi", "width": "<PERSON><PERSON><PERSON> r<PERSON> c<PERSON>t"}}}}}, "settings_schema": {"exit_intent": {"exit_intent_popup": "<PERSON><PERSON> <PERSON> đ<PERSON> th<PERSON>", "exit_intent_popup_info": "Phần này chỉ hoạt động trên máy tính để bàn"}, "colors": {"name": "<PERSON><PERSON><PERSON>", "settings": {"header__1": {"content": "<PERSON><PERSON> b<PERSON>n"}, "header__2": {"content": "<PERSON><PERSON>i dung"}, "header__3": {"content": "<PERSON><PERSON> trang"}, "bg_color": {"label": "<PERSON><PERSON><PERSON>"}, "txt_color": {"label": "<PERSON><PERSON><PERSON>"}, "link_color": {"label": "<PERSON><PERSON><PERSON>"}}}, "typography": {"name": "<PERSON><PERSON><PERSON> chữ", "settings": {"headings_font": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "base_size": {"label": "<PERSON><PERSON><PERSON> c<PERSON> sở"}, "large_size": {"label": "Ti<PERSON>u đề lớn", "info": "Ảnh hưởng đến các tiêu đề lớn từ thanh trư<PERSON>, văn bản đa dạng thức và hình ảnh có phần văn bản."}, "body_font": {"label": "<PERSON><PERSON>i dung"}, "nav_size": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "product-grid": {"name": "Ô lư<PERSON>i sản phẩm", "settings": {"aspect_ratio": {"label": "Tỷ l<PERSON> khung hình media"}, "show_secondary_image": {"label": "Hiển thị media sản phẩm thứ hai khi di chuột"}, "enhance_featured_products": {"label": "<PERSON><PERSON><PERSON><PERSON> mạnh sản phẩm nổi bật", "info": "[Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/832-product-card)"}, "show_discount": {"label": "<PERSON><PERSON>n thị giảm giá dưới dạng...", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Tỷ lệ phần trăm"}}, "caption_placement": {"label": "<PERSON><PERSON> trí chú thích", "options__1": {"label": "<PERSON><PERSON><PERSON> phủ", "group": "<PERSON><PERSON><PERSON> thị khi cuộn qua"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>", "group": "<PERSON><PERSON><PERSON> hiển thị"}}, "grid_color_bg": {"label": "<PERSON><PERSON><PERSON> chú thích lớp phủ"}, "grid_color_text": {"label": "<PERSON><PERSON><PERSON> v<PERSON>n bản chú thích lớp phủ"}, "header__1": {"content": "<PERSON><PERSON><PERSON> hạng sản phẩm", "info": "<PERSON><PERSON> hiển thị điểm xếp hạng, h<PERSON><PERSON> thêm ứng dụng xếp hạng sản phẩm. [Tìm hiểu thêm](https://apps.shopify.com/search?q=product%20reviews)"}, "header__2": {"content": "<PERSON><PERSON><PERSON> gi<PERSON> sản phẩm"}, "show_reviews": {"label": "<PERSON><PERSON><PERSON> thị điểm xếp hạng"}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "<PERSON><PERSON><PERSON> favicon", "info": "Phải là .png 48 x 48px"}}}, "cart-page": {"name": "Giỏ hàng", "settings": {"cart_type": {"label": "Loại giỏ hàng", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON> k<PERSON>"}}, "cart_notes": {"label": "<PERSON><PERSON><PERSON> ghi chú giỏ hàng"}, "cart_buttons": {"label": "<PERSON><PERSON><PERSON> thị các nút <PERSON>h to<PERSON> bổ sung"}}}, "embellishments": {"name": "Trang trí", "settings": {"show_preloader": {"label": "<PERSON><PERSON><PERSON>nh chờ tải hình <PERSON>nh", "info": "Hiển thị hoạt ảnh chờ tải hình tròn nhỏ trong khi hình ảnh trên cửa hàng của bạn vẫn đang tải."}, "show_breadcrumb": {"label": "Hiển thị breadcrumb", "info": "Phần điều hướng breadcrumb giúp người dùng điều hướng qua cửa hàng và chỉ hiển thị trên các trang tìm kiếm, sản phẩm và bộ sưu tập."}, "show_go_top": {"label": "<PERSON><PERSON><PERSON> thị nút \"lên đầu trang\""}}}, "search": {"name": "<PERSON><PERSON><PERSON>", "settings": {"predictive_search": {"label": "<PERSON><PERSON><PERSON> tìm kiếm dự đoán"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> thị nhà cung cấp"}, "show_price": {"label": "<PERSON><PERSON><PERSON> thị giá"}, "include_articles": {"label": "<PERSON><PERSON> gồm các bài viết trong kết quả tìm kiếm"}, "include_pages": {"label": "<PERSON><PERSON> gồm các trang trong kết quả tìm kiếm"}}}, "social": {"name": "Mạng xã hội"}, "follow_on_shop": {"content": "<PERSON> trê<PERSON>", "info": "<PERSON><PERSON> cho phép khách hàng theo dõi cửa hàng của bạn trên ứng dụng Shop từ website cửa hàng, bạn phải bật Shop Pay. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "label": "<PERSON><PERSON><PERSON> t<PERSON> n<PERSON>ng <PERSON> dõi trên <PERSON>"}, "labels": {"hide_block_if_no_content_info": "Ẩn khối nếu nội dung không được xác định", "popup_page_info": "Thay thế nội dung văn bản nếu một trang đư<PERSON><PERSON> chọn", "page": "<PERSON><PERSON>", "popup": "Popup", "open_popup": "Mở popup"}}, "sections": {"main-404": {"name": "404 chính"}, "main-gift-card": {"name": "Thẻ quà tặng"}, "main-page": {"name": "<PERSON><PERSON>"}, "local-extra-words": {"settings_schema": {"embellishments": {"settings": {"show_gotop": {"label": "<PERSON><PERSON><PERSON> thị nút \"lên đầu trang\""}}}, "colors": {"headings": {"header": "<PERSON><PERSON><PERSON> trang", "cards": "Thẻ"}, "settings": {"borders": "<PERSON><PERSON><PERSON><PERSON> viền", "hide_border": "Ẩn đường viền", "accent": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>nh", "overlay": "<PERSON><PERSON><PERSON> phủ"}}, "typography": {"buttons": {"label": "<PERSON><PERSON> dày phông chữ của nút", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "<PERSON><PERSON><PERSON>"}, "menus": {"header": "<PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON> c<PERSON> sở", "weight": "<PERSON><PERSON> dày phông chữ", "weight_bold": "In đậm"}}, "borders": {"name": "<PERSON><PERSON><PERSON><PERSON> viền", "main": {"name": "<PERSON><PERSON><PERSON>", "info": "Cài đặt này kiểm soát kiểu đường viền trên tất cả các mục trong chủ đề."}, "buttons": {"name": "<PERSON><PERSON><PERSON>"}, "forms": {"name": "Bi<PERSON>u mẫu"}, "settings": {"width": "<PERSON><PERSON><PERSON> r<PERSON>", "radius": "<PERSON><PERSON>"}}, "layout": {"name": "Bố cục", "sections": {"vertical_space": "<PERSON><PERSON><PERSON><PERSON> cách dọc gi<PERSON> các ph<PERSON>n", "remove_vertical_space": "<PERSON><PERSON><PERSON> l<PERSON> trên", "remove_bottom_margin": "<PERSON>óa l<PERSON> dưới"}, "grid": {"name": "Ô lưới", "info": "Ảnh hưởng đến các khu vực có bố cục nhiều cột.", "horizontal_space": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch ngang", "vertical_space": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch d<PERSON>c"}}, "cart": {"settings": {"media": {"header": "Tỷ l<PERSON> khung hình media", "label": "Tỷ l<PERSON> khung hình", "info_cart": "Thay đổi tỷ lệ khung hình cho ảnh đại diện có kích thước nhỏ của sản phẩm được sử dụng trong giỏ hàng, cửa sổ bật lên tìm kiếm và một số khu vực khác yêu cầu ảnh đại diện có kích thước nhỏ."}}, "shipping": {"name": "<PERSON><PERSON><PERSON> ch<PERSON>", "info": "<PERSON><PERSON><PERSON> năng này chỉ hoạt động khi có một loại tiền tệ và phương thức vận chuyển. Nếu bạn đang sử dụng nhiều phương thức vận chuyển hoặc đơn vị tiền tệ thì tính năng này có thể hiển thị cho khách hàng giá trị không đúng.", "show": {"label": "<PERSON>ển thị số tiền tối thiểu để được vận chuyển miễn phí", "info": "<PERSON><PERSON> định cấu hình phí vận chuyển, h<PERSON><PERSON> và<PERSON> mụ<PERSON> [cài đặt vận chuyển](/admin/settings/shipping)."}, "amount": {"label": "<PERSON>ố tiền tối thiểu để được miễn phí vận chuyển", "info": "<PERSON><PERSON><PERSON><PERSON> một số, không có chữ cái hoặc ký tự đặc biệt."}}}, "aspect_ratio": {"landscape": {"label": "<PERSON><PERSON><PERSON> (3:2)"}}, "maps": {"name": "<PERSON><PERSON><PERSON>"}, "search": {"predictive_search": {"name": "<PERSON><PERSON><PERSON> kiếm dự đoán", "info": "<PERSON><PERSON><PERSON> năng tìm kiếm dự đoán hỗ trợ các đề xuất cho sản phẩm, bộ sưu tập, trang và bài viết."}}, "product-card": {"name": "Thẻ sản phẩm", "title-size": {"name": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tiêu đề", "options__1": "Nhỏ", "options__2": "Lớn"}, "local-pickup": {"name": "<PERSON><PERSON>nh trạng còn hàng tại địa phương", "info": "Chủ đề này hiển thị tình trạng còn hàng của sản phẩm tại địa phương. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/805-collection-page)"}, "products-list": {"name": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m"}, "badges": {"name": "<PERSON><PERSON> hi<PERSON><PERSON> sản phẩm mặc định", "settings": {"colors": {"text": "<PERSON><PERSON><PERSON> v<PERSON>n bản huy hiệu", "sold_out": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON> bán hết\"", "sale": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON> giá\""}}, "badge_sale": {"name": "<PERSON><PERSON> hi<PERSON> g<PERSON><PERSON> giá"}, "custom_badges": {"name": "<PERSON><PERSON> hi<PERSON>u sản phẩm tùy chỉnh", "info": "Chủ đề này sử dụng huy hiệu sản phẩm tùy chỉnh mà bạn có thể xác định tại đây. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/795-product-card#Product-badges-_KIdK)", "name__1": "<PERSON><PERSON> hi<PERSON>u tùy chỉnh 1", "name__2": "<PERSON><PERSON> hi<PERSON> tùy chỉnh 2", "name__3": "<PERSON><PERSON> <PERSON><PERSON> tùy chỉnh 3", "text": "<PERSON><PERSON><PERSON>", "tags": "Tag", "color": "<PERSON><PERSON><PERSON>", "text_color": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n"}}, "icons_list": "<PERSON><PERSON> s<PERSON>ch biểu tượng động"}}, "sections": {"video": {"name": "Video", "settings": {"video": {"label": "URL video"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}, "contact-form": {"settings": {"form-fields": {"name": "Trư<PERSON>ng thông tin biểu mẫu", "show-phone": "<PERSON><PERSON><PERSON> thị điện thoại", "show-subject": "<PERSON><PERSON><PERSON> thị chủ đề"}}, "blocks": {"contact-info": {"name": "Thông tin liên lạc", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "content": {"label": "<PERSON><PERSON>i dung"}}}}}, "text-columns-with-icons": {"blocks": {"icon": {"name": "<PERSON><PERSON><PERSON><PERSON> tượng tùy chỉnh", "info": "256 x 256px"}, "select_icon": {"info": "<PERSON><PERSON> trực quan hóa &amp; tả<PERSON> xuống nhiều biểu tượ<PERSON> hơn, xin vui lòng truy cập [liên kết này](https://resources.krownthemes.com/icons/)"}, "icon_color": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Chỉ hoạt động cho các biểu tượng đượ<PERSON> bao gồm"}}}, "content-toggles": {"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>/hiện nội dung", "block": "<PERSON><PERSON>i dung"}, "text-columns-with-images": {"blocks": {"image": {"info": "640 x 640px"}}}, "announcement-bar": {"settings": {"social": {"header": "<PERSON><PERSON><PERSON><PERSON> tượng mạng xã hội", "info": "<PERSON><PERSON> thiết lập hồ sơ mạng xã hội, h<PERSON><PERSON> và<PERSON> mụ<PERSON> đặt Chủ đề > <PERSON>ạng xã hội.", "label": "<PERSON><PERSON><PERSON> thị các biểu tượng xã hội"}}, "blocks": {"content": {"name": "<PERSON><PERSON>i dung", "settings": {"text": "<PERSON><PERSON><PERSON>", "link": "<PERSON><PERSON><PERSON>", "target": "Mở liên kết trong một cửa sổ mới"}}}}, "newsletter": {"show_icon": "<PERSON><PERSON><PERSON> thị biểu tư<PERSON>"}, "cookies": {"name": "<PERSON><PERSON><PERSON> sổ bật lên cookie", "cookies_info": "Trang web này sử dụng cookie để đảm bảo mang lại trải nghiệm tốt nhất cho người dùng. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/790-cookies-popup)."}, "popups": {"name": "<PERSON><PERSON><PERSON> sổ bật lên", "blocks": {"model": {"model-1": "<PERSON><PERSON>", "model-2": "Bản tin", "model-3": "<PERSON><PERSON><PERSON> chỉnh"}, "settings": {"size": {"label": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> c<PERSON>a sổ bật lên", "option_1": "Nhỏ", "option_2": "Lớn"}}}}, "age-verification": {"name": "<PERSON><PERSON><PERSON> <PERSON>h độ tuổi", "settings": {"button-text": "<PERSON><PERSON><PERSON> b<PERSON>"}}, "stores-map": {"name": "<PERSON><PERSON><PERSON> <PERSON> c<PERSON><PERSON> hàng", "settings": {"map": {"title": "<PERSON><PERSON><PERSON>"}, "gallery": {"title": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> c<PERSON><PERSON> hàng"}}}, "store-selector": {"name": "<PERSON><PERSON> chọn c<PERSON><PERSON> hàng", "settings": {"map": {"label": "<PERSON><PERSON><PERSON> bản đồ động", "info": "<PERSON><PERSON><PERSON> bảo rằng bạn đã thiết lập đúng <PERSON>a API Google Maps trong mục Cài đặt Chủ đề"}, "zoom": {"label": "<PERSON><PERSON> ph<PERSON>g bản đồ", "info": "<PERSON><PERSON>n một giá trị phù hợp để xem tất cả các cửa hàng mong muốn cùng một lúc."}}, "blocks": {"map": {"name": "<PERSON><PERSON> trí bản đồ", "settings": {"address": {"label": "Địa chỉ", "info": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm"}, "image": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> lên một hình ảnh tĩnh nếu bạn không muốn sử dụng bản đồ động."}, "style": {"label": "<PERSON><PERSON><PERSON> bản đồ", "option__1": "<PERSON><PERSON><PERSON><PERSON>", "option__2": "Bạc", "option__3": "<PERSON><PERSON><PERSON> cổ", "option__4": "<PERSON><PERSON><PERSON>", "option__5": "<PERSON><PERSON><PERSON>", "option__6": "Tím"}, "pin": {"label": "<PERSON><PERSON> tùy chỉnh trên bản đồ", "info": ".png trong suốt 240 x 240px"}}}, "store": {"name": "<PERSON><PERSON><PERSON> h<PERSON>", "settings": {"name": {"label": "<PERSON><PERSON><PERSON>", "info": "Tên cửa hàng cần trùng khớp với tên cửa hàng của bạn được xác định trong mục [cài đặt vị trí](/admin/settings/locations)"}, "pickup_price": {"label": "<PERSON><PERSON><PERSON> l<PERSON> h<PERSON>ng"}, "pickup_time": {"label": "<PERSON><PERSON><PERSON><PERSON> gian l<PERSON> hàng"}, "address": {"label": "<PERSON> tiết c<PERSON>a hàng"}, "image": {"label": "<PERSON><PERSON><PERSON> c<PERSON>a hàng"}, "closing_times": {"label": "<PERSON>h<PERSON><PERSON> gian đ<PERSON> c<PERSON> (kh<PERSON><PERSON> b<PERSON><PERSON> buộc)", "info": "Thêm 7 dòng, một dòng cho mỗi ngày trong tuần, bắt đầu từ <PERSON>."}, "timezone": {"label": "<PERSON><PERSON><PERSON> giờ", "info": "<PERSON><PERSON><PERSON><PERSON> sử dụng để hiển thị đúng thời gian đóng cửa"}, "map": {"name": "<PERSON><PERSON> trên bản đ<PERSON>", "info": "<PERSON><PERSON>u kích hoạt bản đồ, bạn cần xác định ghim tùy chỉnh cho địa chỉ này. [Tìm hiểu cách lấy tọa độ địa chỉ của bạn](https://support.google.com/maps/answer/18539?hl=en)"}, "map_latitude": {"label": "<PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> độ vĩ độ cho điểm đánh dấu. Ví dụ: 46,7834818"}, "map_longitude": {"label": "<PERSON>nh đ<PERSON>", "info": "T<PERSON><PERSON> độ kinh độ cho điểm đánh dấu. V<PERSON> dụ: 23,5464733"}, "get_directions_button": {"label": "Hiển thị nút \"chỉ đường\"", "info": "Mở bản đồ lớn hơn trong tab trình duyệt mới."}, "map_pin": {"label": "<PERSON><PERSON> tùy chỉnh", "info": ".png 90 x 90px trong suốt"}}}}}, "header": {"settings": {"layout": {"label": "<PERSON><PERSON> cục đầu trang", "info": "Ảnh hưởng đến vị trí của các khối tùy chỉnh &amp; hành động mặc định", "option__1": "<PERSON><PERSON><PERSON> khối tùy chỉnh ở trên cùng, các hành động mặc định ở dưới cùng", "option__2": "<PERSON><PERSON><PERSON> hành động mặc định ở trên cùng, các khối tùy chỉnh ở dưới cùng"}, "sticky": {"label": "<PERSON><PERSON><PERSON> trang d<PERSON>h", "info": "<PERSON><PERSON><PERSON> thị mục điều hướng khi người dùng cuộn lên"}}, "blocks": {"info": {"name": "Thông tin", "style": {"label": "<PERSON><PERSON><PERSON>", "option__1": "Th<PERSON>ng tin văn bản", "option__2": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> các nút chỉ hiển thị chú thích, d<PERSON><PERSON><PERSON> dạng nhãn của nút."}, "custom-icon": {"label": "<PERSON><PERSON><PERSON><PERSON> tượng tùy chỉnh", "info": "<PERSON><PERSON><PERSON> lê<PERSON> h<PERSON> .png 76 x 76px"}, "icon": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "link_type": {"label": "Mở liên kết", "option__1": "<PERSON><PERSON><PERSON> trong c<PERSON>a sổ hộp tho<PERSON>i", "option__2": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng một trang", "option__3": "Trong một trang mới", "info": "<PERSON><PERSON><PERSON> sổ hộp thoại chỉ hoạt động với các liên kết trang nội bộ"}}, "store-selector": {"name": "<PERSON><PERSON> chọn c<PERSON><PERSON> hàng", "content": "<PERSON><PERSON> thể và<PERSON> mục Bộ chọn Cửa hàng để định cấu hình bộ chọn cửa hàng. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/799-store-selector)", "info": "Chủ đề này cho phép bạn kết nối các vị trí cửa hàng thực của mình với bộ chọn cửa hàng tương tác. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/799-store-selector)"}, "mega-menu": {"name": "<PERSON><PERSON> lớn", "settings": {"menu_handle": {"label": "<PERSON><PERSON>n handle của menu", "info": "Chủ đề này sử dụng các menu lớn. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/798-header-section#mega-menus)"}}}}}, "marquee": {"name": "<PERSON><PERSON><PERSON> bản <PERSON>n", "settings": {"scroll_direction": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON>n", "scroll_speed": "<PERSON><PERSON><PERSON> độ cuộn", "scroll_speed_info": "<PERSON><PERSON><PERSON> trị càng lớn, tốc độ cuộn càng chậm", "pause_on_mouseover": "<PERSON><PERSON>m dừng khi di chuột qua", "scroll_item": "<PERSON><PERSON><PERSON>", "scroll_item_text": "<PERSON><PERSON><PERSON> bản <PERSON>n"}}, "image-section": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image_size": {"label": "<PERSON><PERSON><PERSON> rộng trên máy tính để bàn", "info": "<PERSON><PERSON><PERSON><PERSON> thiết bị di động, hình ảnh sẽ có chiều rộng đầy đủ."}}}, "media-with-text-overlay": {"name": "Media có lớp phủ văn bản", "blocks": {"media": "Media", "image": {"name": "<PERSON><PERSON><PERSON>"}, "link": {"info": "Tiêu đề sẽ chuyển thành liên kết trừ khi có nhãn cho nút."}, "video": {"name": "Video", "label": "Video", "info": "<PERSON><PERSON>nh ảnh trên sẽ hiển thị nếu không thể phát video này."}}, "settings": {"height": "<PERSON><PERSON><PERSON> cao thẻ", "option__1": "Nhỏ", "option__2": "Lớn", "option__3": "Cực lớn", "option__4": "<PERSON><PERSON><PERSON> màn hình", "option__5": "<PERSON><PERSON><PERSON>"}}, "blog-posts": {"settings": {"emphasize": {"label": "<PERSON><PERSON><PERSON><PERSON> mạnh bài viết đầu tiên", "info": "Chỉ trên máy tính để bàn"}}, "blocks": {"summary": {"name": "<PERSON><PERSON><PERSON><PERSON> trích", "settings": {"excerpt_limit": "Số từ", "excerpt_limit_info": "<PERSON><PERSON> dụng nếu bài viết không thêm đoạn trích thủ công trong phần quản trị."}}}}, "testimonials": {"name": "<PERSON><PERSON><PERSON> ch<PERSON>ng thực", "blocks": {"name": "<PERSON><PERSON><PERSON>"}}, "slideshow": {"name": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> ch<PERSON>", "block": {"name": "<PERSON><PERSON><PERSON>"}, "settings": {"caption_size": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> chú thích"}}, "rich-text": {"settings": {"image_position": {"label": "<PERSON><PERSON> trí h<PERSON>nh <PERSON>nh", "option__1": "<PERSON><PERSON><PERSON> t<PERSON>", "option__2": "<PERSON><PERSON> trên văn bản", "option__3": "<PERSON><PERSON><PERSON>"}, "fullwidth": {"label": "<PERSON><PERSON><PERSON> rộng đ<PERSON>y đủ", "info": "Mở rộng nền của phần này để lấp đầy màn hình."}, "height": {"label": "<PERSON><PERSON><PERSON> cao thẻ", "info": "Chiều cao tối thiểu của thẻ trên máy tính để bàn. Trên thiết bị di động, chiều cao sẽ phụ thuộc vào nội dung."}, "crop": {"label": "<PERSON><PERSON> v<PERSON><PERSON> lấp đ<PERSON><PERSON> h<PERSON>nh <PERSON>nh", "info": "Trên máy t<PERSON>h để bàn, hình ảnh sẽ cắt xén để lấp đầy toàn bộ chiều cao của thẻ. Trên thiết bị di động, hình ảnh sẽ luôn được hiển thị đầy đủ."}, "remove_margin": {"label": "<PERSON><PERSON><PERSON> l<PERSON> trên"}}}, "main-header": {"settings": {"mobile": {"name": "<PERSON><PERSON><PERSON><PERSON> hướng trên thiết bị di động", "info": "Những điều này chỉ ảnh hưởng đến chế độ hiển thị bên trong ngăn điều hướng trên thiết bị di động.", "header_actions": "<PERSON><PERSON><PERSON> thị bộ chọn cửa hàng &amp; kh<PERSON><PERSON> thông tin", "header_info_blocks": {"header": "<PERSON><PERSON><PERSON><PERSON> thông tin đầu trang", "label_1": "Hi<PERSON>n thị bộ chọn cửa hàng &amp; kh<PERSON><PERSON> thông tin trong phần đầu trang trên thiết bị di động", "label_2": "<PERSON><PERSON> trí khối thông tin trên đầu phần đầu tiên ở trang chủ", "label_2_info": "<PERSON><PERSON><PERSON> hợp tốt khi phần đầu tiên là bản trình chiếu có chiều rộng đầy đủ"}}, "promotion_block": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "size": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tiêu đề"}, "subtitle": {"label": "<PERSON><PERSON> đề", "size": "<PERSON><PERSON><PERSON> th<PERSON> phụ đề"}, "button": {"label": "<PERSON><PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "link": "<PERSON><PERSON><PERSON> k<PERSON>", "style": "<PERSON><PERSON><PERSON>"}}, "header_actions": {"header": "<PERSON><PERSON><PERSON><PERSON> thông tin đầu trang trên thiết bị di động", "show_in_drawer": "<PERSON><PERSON><PERSON> thị bên trong ngăn điều hướng"}}}, "main-cart": {"blocks": {"shipping-calculator": {"name": "<PERSON><PERSON><PERSON> t<PERSON> vận chuy<PERSON>n"}, "related-products": {"info": "Nội dung đề xuất sản phẩm dựa trên các sản phẩm thường được mua cùng nhau hoặc các sản phẩm trong bộ sưu tập có liên quan. Nếu không có đề xuất nào, khối này sẽ không xuất hiện."}}}, "main-search": {"settings": {"blogs": {"name": "<PERSON><PERSON><PERSON> qu<PERSON> bài viết"}, "products": {"name": "<PERSON><PERSON><PERSON> qu<PERSON> sản phẩm", "info": "Cần sử dụng các khối phần để thiết lập nội dung của thẻ sản phẩm."}}}, "main-product": {"name": "<PERSON><PERSON> sản ph<PERSON>m", "settings": {"gallery_pagination": "<PERSON><PERSON> trang thanh tr<PERSON><PERSON><PERSON> thư viện", "show_border": "<PERSON><PERSON><PERSON> thị đường viền xung quanh thư viện", "gallery_border": "<PERSON><PERSON><PERSON><PERSON> viền thư viện", "gallery_border_color": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> viền thư viện"}, "blocks": {"pickup_availability": {"name": "Tình trạng sẵn có của việc lấy hàng", "info": "Chủ đề này hiển thị tình trạng sẵn có của việc lấy hàng dựa trên cửa hàng đã chọn. Tìm hiểu thêm", "settings": {"style": "<PERSON><PERSON><PERSON>", "option__1": "Nhỏ gọn", "option__2": "Mở rộng"}}, "buy_buttons": {"settings": {"show_price": "<PERSON><PERSON><PERSON> thị giá"}}, "related": {"name": "<PERSON><PERSON><PERSON> phẩm có liên quan", "settings": {"products": "<PERSON><PERSON><PERSON> p<PERSON>m"}}, "tax_info": {"name": "<PERSON><PERSON><PERSON><PERSON> tin về thuế"}, "icons": {"name": "<PERSON><PERSON> s<PERSON>ch biểu t<PERSON>", "info": "<PERSON><PERSON> trực quan hóa &amp; tải xuống các biểu tượng có trong chủ đề này, xin vui lòng truy cập [liên kết này](https://resources.krownthemes.com/icons//).", "help": "Chủ đề này cho phép bạn thêm các biểu tượng sản phẩm tùy chỉnh thông qua nội dung động. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "headers": {"icon_1": "Biểu tượng 1", "icon_2": "Biểu t<PERSON> 2", "icon_3": "Biểu t<PERSON> 3", "icon_4": "Biểu <PERSON> 4", "icon_5": "<PERSON><PERSON><PERSON><PERSON> 5", "icon_6": "<PERSON><PERSON><PERSON><PERSON> 6"}, "settings": {"icon": "<PERSON><PERSON><PERSON><PERSON>", "icon_info": "96 x 96px", "label": "<PERSON><PERSON>ã<PERSON>"}}}}, "main-blog": {"name": "Blog chính"}, "main-article": {"name": "<PERSON><PERSON><PERSON> vi<PERSON>", "settings": {"show_tags": "<PERSON><PERSON><PERSON> thị tag", "enhance_product_links": {"label": "<PERSON><PERSON><PERSON> cư<PERSON><PERSON> liên kết sản phẩm", "info": "Tất cả các liên kết đến sản phẩm sẽ mở cửa sổ hộp thoại mua nhanh sản phẩm."}}}, "main-article-comments": {"name": "<PERSON><PERSON><PERSON> luận bài viết", "info": "<PERSON><PERSON> bật b<PERSON><PERSON>, h<PERSON><PERSON> v<PERSON><PERSON> m<PERSON> [cài đặt blog] củ<PERSON> bạn(/admin/blogs)."}, "main-article-navigation": {"name": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> bài viết", "settings": {"header": {"content": "<PERSON><PERSON><PERSON> đ<PERSON> trên blog", "info": "<PERSON><PERSON> trống nếu bạn muốn tải bài đăng trước đó hoặc kế tiếp trên blog theo mặc định."}, "posts": {"next": "<PERSON><PERSON><PERSON> đăng kế tiếp", "previous": "<PERSON><PERSON><PERSON> đăng trư<PERSON>c đó"}}}, "main-page": {"settings": {"center": {"label": "<PERSON><PERSON><PERSON> gi<PERSON>a nội dung trên máy t<PERSON>h"}}}, "main-footer": {"blocks": {"payment": {"name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> toán", "info": "<PERSON><PERSON><PERSON> biểu tượng hiển thị được xác định theo mụ<PERSON> [cài đặt thanh toán](/admin/settings/payments) trong cửa hàng của bạn cũng như khu vực và đơn vị tiền tệ của khách hàng.", "custom_icons": "Custom icons", "custom_icons_info": "Upload 114 x 72px .png images", "only_show_custom_icons": "Only show the custom icons"}, "text": {"name": "<PERSON><PERSON>n bản &amp; biểu tượng mạng xã hội"}}}, "customers": {"reset-password": {"name": "Đặt lại mật khẩu"}, "order": {"name": "<PERSON><PERSON> đặt hàng"}, "register": {"name": "<PERSON><PERSON> đ<PERSON>ng ký"}, "activate-account": {"name": "<PERSON><PERSON> k<PERSON>ch ho<PERSON>t tài <PERSON>n"}, "login": {"name": "<PERSON><PERSON> đ<PERSON>h<PERSON>p", "shop_login_button": {"enable": "<PERSON><PERSON><PERSON> nhập bằng Shop"}}, "account": {"name": "<PERSON><PERSON> tà<PERSON>n"}, "addresses": {"name": "Địa chỉ"}}, "headings": {"heading": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "subheading": "Ti<PERSON><PERSON> đề phụ", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "subtitle": "<PERSON><PERSON> đề", "caption": "<PERSON><PERSON> th<PERSON>ch", "text_content": "<PERSON><PERSON><PERSON> dung văn bản", "custom_colors": "<PERSON><PERSON><PERSON><PERSON> kế thẻ tùy chỉnh", "text_style": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n"}, "columns": {"name": "<PERSON><PERSON> cục trên máy tính để bàn", "info": "<PERSON><PERSON> cục sẽ tự điều chỉnh cho các thiết bị di động.", "option__0": "1 cột", "option__1": "2 cột", "option__2": "3 cột", "option__3": "4 cột", "option__4": "5 cột", "option__5": "6 cột", "name_mobile": "Mobile layout"}, "promotion-cards": {"name": "Thẻ khuyến mãi", "blocks": {"name": "Thẻ"}}, "faq": {"headings": {"header": "<PERSON><PERSON><PERSON> trang", "content": "<PERSON><PERSON>i dung"}, "settings": {"form": {"header": "<PERSON>i<PERSON>u mẫu liên hệ", "show": "Hiển thị biểu mẫu", "title": "Tiêu đề biểu mẫu"}}}, "product-quick-view": {"name": "<PERSON><PERSON>", "info": "Mẫu này kiểm soát cách xây dựng chế độ xem nhanh sản phẩm. Chỉ có phần này mới sẽ xuất hiện trong cửa sổ hộp thoại."}, "product-card": {"blocks": {"price": "Giá", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "vendor": "<PERSON><PERSON><PERSON><PERSON> bán", "text": {"name": "<PERSON><PERSON><PERSON> bản động", "info": "Sử dụng nguồn động để làm nổi bật một thuộc tính duy nhất bằng cách tạo trường thông tin bổ sung sản phẩm. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/805-collection-page)", "settings": {"text": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> thông tin bổ sung nhãn"}, "size": {"label": "Cỡ chữ", "option__1": "Nhỏ", "option__2": "<PERSON><PERSON><PERSON>", "option__3": "Lớn"}, "color": {"label": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "<PERSON><PERSON>"}, "transform": {"label": "Chuyển đổi văn bản (chữ hoa)"}}}, "icons": {"info": "Sử dụng các nguồn động để làm nổi bật các thuộc tính duy nhất bằng cách tạo trường thông tin bổ sung sản phẩm cho danh sách biểu tượng. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "settings": {"icon": "<PERSON><PERSON><PERSON><PERSON><PERSON> thông tin bổ sung biểu tượng", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> thông tin bổ sung nhãn"}}, "quick_buy": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "rating": "<PERSON><PERSON><PERSON> h<PERSON>"}}, "buttons": {"style": {"label": "<PERSON><PERSON><PERSON>", "option__1": "<PERSON><PERSON><PERSON><PERSON> viền", "option__2": "<PERSON><PERSON><PERSON>"}}}}, "complementary_products": {"name": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> b<PERSON> sung", "settings": {"paragraph": {"content": "<PERSON><PERSON> chọn các sản phẩm b<PERSON> sung, h<PERSON><PERSON> thêm <PERSON>ng dụng Tìm kiếm &amp; <PERSON>h<PERSON>m phá. [Tìm hiểu thêm](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}}}, "words_210": {"text_columns": {"name": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "block": {"name": "<PERSON><PERSON><PERSON>"}}}, "refactor_words": {"seo_late": {"breadcrumbs": {"label": "<PERSON><PERSON><PERSON> thiện mục điều hướng breadcrumb sản phẩm", "info": "Thêm tên bộ sưu tập vào mục điều hướng breadcrumb bằng cách sao chép url của sản phẩm. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/697-seo#breadcrumbs)"}}, "seo": {"name": "SEO", "label": "Tag tiêu đề", "info": "Chỉ định cấp độ tiêu đề để giúp các công cụ tìm kiếm lập chỉ mục cấu trúc trang của bạn.", "microdata": {"label": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> đồ vi dữ liệu", "info": "<PERSON><PERSON> tác này sẽ xóa đánh dấu schema.org khỏi trang. Chỉ tắt nếu bạn đang sử dụng ứng dụng của bên thứ ba cho SEO! [https://shopify-support.krownthemes.com/article/697-seo#microdata)"}}, "late_edits": {"image_with_hotspots": {"image_on_mobile": "<PERSON><PERSON><PERSON>nh trên thiết bị di động", "position_on_mobile": "<PERSON><PERSON> trí trên thiết bị di động", "hotspot": {"mobile_info": "Chỉ khi đã thiết lập hình ảnh trên thiết bị di động"}}, "product-card": {"thumbnails": {"border": "<PERSON><PERSON>u <PERSON> viền media"}}, "labels": {"optional": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c"}, "before-after": {"layout": {"invert": "<PERSON><PERSON><PERSON><PERSON><PERSON> bố cục trên thiết bị di động"}}}, "labels": {"show_button": "<PERSON><PERSON><PERSON> thị nút", "footer_group": "Nhóm chân trang", "header_group": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>u trang", "overlay_group": "<PERSON><PERSON><PERSON><PERSON> lớp phủ", "embellishments": "Trang trí", "optional": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c"}, "text": {"icons_info": "<PERSON><PERSON> trực quan hóa &amp; tả<PERSON> xuống nhiều biểu tượ<PERSON> hơn, xin vui lòng truy cập [liên kết này](https://resources.krownthemes.com/icons/)"}, "borders": {"show_border": "<PERSON><PERSON><PERSON> thị đường viền", "top_border": "<PERSON><PERSON><PERSON><PERSON> viền trên cùng", "bottom_border": "<PERSON><PERSON><PERSON><PERSON> viền dư<PERSON>i cùng"}, "colors": {"heading_background": "<PERSON><PERSON><PERSON> tiêu đề", "shadow": "<PERSON><PERSON><PERSON> thị bóng đổ"}, "social": {"phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "discord": "Discord"}, "settings": {"image_with_hotspots": {"label": "<PERSON><PERSON><PERSON>nh có các điểm nổi bật", "image_on_mobile": "<PERSON><PERSON><PERSON>nh trên thiết bị di động", "position_on_mobile": "<PERSON><PERSON> trí trên thiết bị di động", "hotspot": {"label": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i bật", "mobile_info": "Chỉ khi đã thiết lập hình ảnh trên thiết bị di động", "label_desktop_offset": "<PERSON><PERSON><PERSON><PERSON> nổi bật trên máy tính để bàn", "label_mobile_offset": "<PERSON><PERSON><PERSON><PERSON> nổi bật trên thiết bị di động", "offset_horizontal": "<PERSON><PERSON> d<PERSON> ngang", "offset_vertical": "<PERSON><PERSON> d<PERSON><PERSON> d<PERSON>", "tooltip": {"label": "<PERSON><PERSON> gi<PERSON>i công cụ", "show_tooltip": "<PERSON><PERSON><PERSON> thị chú giải công cụ", "position": {"label": "<PERSON><PERSON> trí", "option_1": "<PERSON><PERSON><PERSON><PERSON>", "option_2": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "option_3": "<PERSON><PERSON><PERSON> t<PERSON>", "option_4": "<PERSON><PERSON><PERSON>"}}}}, "scrolling_images": {"label": "<PERSON><PERSON><PERSON> cu<PERSON>n", "image_size": "<PERSON><PERSON><PERSON> cỡ hình ảnh", "columns": "<PERSON><PERSON><PERSON>"}, "video": {"label": "Video", "info": "<PERSON><PERSON><PERSON> c<PERSON>u định dạng MP4, kh<PERSON><PERSON> có âm thanh"}, "variants_functionality": {"label": "Xử lý các mẫu mã không có sẵn", "option_1": "Ẩn", "option_2": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "option_3": "<PERSON><PERSON><PERSON> thị"}, "auto_height": {"label": "<PERSON><PERSON><PERSON> cao tự động", "info_slider": "Việc chọn tùy chọn này sẽ ghi đè phần cài đặt chiều cao ở trên và làm cho chiều cao của bản trình chiếu tương ứng với hình ảnh bên trong mỗi trang chiếu."}}, "header": {"promotion_block": {"image_link": "<PERSON><PERSON><PERSON> kết h<PERSON>nh <PERSON>nh khu<PERSON>ến mãi"}, "sticky": {"label": "<PERSON><PERSON><PERSON> trang d<PERSON>h", "option_1": "Tắt", "option_2": "<PERSON><PERSON><PERSON> luôn", "option_3": "Chỉ khi cuộn lên"}}, "inventory": {"name": "<PERSON><PERSON><PERSON> hàng trong kho", "settings": {"show_progress_bar": "<PERSON><PERSON><PERSON> thị thanh tiến trình", "low_inventory_threshold": "Ngưỡng hàng trong kho thấp", "show_block": {"always": "<PERSON><PERSON><PERSON> hiển thị", "low": "Chỉ hiển thị khi hàng trong kho giảm xuống dưới ngưỡng"}}}, "breadcrumb": {"name": "Breadcrumb", "info": "<PERSON><PERSON><PERSON> đi<PERSON>u hướng breadcrumb không xuất hiện trên trang chủ"}, "announcement-bar": {"visibility": {"label": "<PERSON><PERSON> độ hiển thị", "option_1": "<PERSON><PERSON><PERSON> c<PERSON> các trang", "option_2": "Chỉ trang chủ", "option_3": "<PERSON><PERSON><PERSON> cả các trang ngoại trừ trang chủ", "option_4": "Chỉ các trang sản phẩm", "option_5": "Chỉ trang giỏ hàng"}, "color": {"border": "<PERSON><PERSON><PERSON> viền"}}, "promotional_banner": {"name": "<PERSON><PERSON><PERSON><PERSON> ngữ quảng cáo", "enable": "<PERSON><PERSON><PERSON> thị biểu ngữ"}, "cookies_banner": {"name": "<PERSON><PERSON>", "enable": "Hi<PERSON>n thị thông báo cookie"}, "before_after": {"name": "So s<PERSON>h h<PERSON>nh <PERSON>nh", "layout": {"label": "Bố cục", "option__1": "<PERSON><PERSON>", "option__2": "D<PERSON><PERSON>"}, "style": {"label": "<PERSON><PERSON><PERSON>u", "option__1": "<PERSON><PERSON><PERSON>", "option__2": "<PERSON><PERSON><PERSON>"}, "image": {"label__1": "<PERSON><PERSON><PERSON>", "label__2": "<PERSON><PERSON><PERSON>nh trên thiết bị di động", "label__3": "<PERSON><PERSON>ã<PERSON>"}}, "cart_upsell": {"name": "<PERSON><PERSON> xuất sản phẩm cá nhân", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "info": "<PERSON><PERSON><PERSON> đề xuất linh động dựa trên mặt hàng trong giỏ hàng của bạn. Chúng thay đổi và cải thiện theo thời gian. [Tìm hiểu thêm](https://help.shopify.com/en/themes/development/recommended-products)"}, "gift_wrapping": {"name": "<PERSON><PERSON><PERSON> quà", "info": "<PERSON><PERSON><PERSON> thiết lập việc gói quà như một sản phẩm. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/696-cart-features#gift-wrap)", "settings": {"label": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>"}}, "custom_code": {"name": "Liquid / HTML Tùy ch<PERSON>h"}, "rating": {"name": "<PERSON><PERSON><PERSON> gi<PERSON>ng dụng", "default": "Mặc định"}, "product-page": {"size_guide": {"label": "Hướng dẫn về kích thước", "page": "<PERSON>rang hướng dẫn về kích thước", "options": {"label": "<PERSON><PERSON><PERSON> ch<PERSON> mở", "option_1": "<PERSON><PERSON><PERSON> sổ bật lên", "option_2": "<PERSON><PERSON><PERSON> c<PERSON>a sổ", "option_3": "<PERSON><PERSON><PERSON> sổ mới"}}, "gallery_resize": {"label": "Tỷ lệ khung hình của hình ảnh", "info": "Video và các loại media khác sẽ được hiển thị ở tỷ lệ khung hình gốc của chúng.", "option_1": "<PERSON><PERSON><PERSON>u chỉnh hình ảnh cho vừa bên trong vùng chứa"}, "gallery_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch bên trong thư viện"}, "gallery_background": {"label": "<PERSON><PERSON><PERSON> thư viện", "info": "Chỉ hiển thị nếu hình ảnh được thiết lập để vừa bên trong vùng chứa."}}, "product-card": {"name": "Thẻ sản phẩm", "labels": {"thumbnail": "Ảnh đại diện của sản phẩm", "caption": "<PERSON><PERSON> thích sản phẩm", "color_swatches": "<PERSON><PERSON><PERSON> chọn màu sản phẩm"}, "thumbnails": {"fit": "Điều chỉnh media cho vừa bên trong vùng chứa", "padding": {"label": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch bên trong vùng ch<PERSON>a", "info": "Chỉ hoạt động nếu media được thiết lập để vừa bên trong vùng chứa."}, "background": {"label": "<PERSON><PERSON><PERSON> v<PERSON>ng ch<PERSON>", "info": "Chỉ hiển thị nếu media được thiết lập để vừa bên trong vùng chứa."}, "color_swatches": "<PERSON><PERSON>n thị nút chọn màu sản phẩm trong thẻ sản phẩm", "color_swatches_on_hover": "Hi<PERSON>n thị nút chọn màu sản phẩm trong thẻ sản phẩm (khi di chuột)", "border": "<PERSON><PERSON>u <PERSON> viền media"}, "color_swatches_label": {"label": "<PERSON><PERSON><PERSON><PERSON> nút chọn màu sản phẩm", "info": "<PERSON><PERSON><PERSON><PERSON> nhiều tên tùy chọn (phân tách bằng dấu phẩy) mà bạn muốn đặt thành các nút chọn màu sản phẩm."}, "badges": {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "show_badges": "<PERSON><PERSON><PERSON> thị huy hiệu", "settings": {"colors": {"text": "<PERSON><PERSON><PERSON> v<PERSON>n bản huy hiệu", "sold_out": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON> bán hết\"", "sale": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON> giá\""}}, "badge_sale": {"name": "<PERSON><PERSON> hi<PERSON> g<PERSON><PERSON> giá", "amount_saved": "<PERSON><PERSON> tiền tiết kiệm đ<PERSON>"}, "regular_badges": {"info": "T<PERSON>m hiểu thêm về huy hiệu sản phẩm [tại đây](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)."}, "sold_out": {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON> b<PERSON> h<PERSON>t", "text_color": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> bản \"<PERSON><PERSON> bán hết\"", "sale_text": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> bản \"<PERSON><PERSON><PERSON><PERSON> giá\""}, "custom_badges": {"name": "<PERSON><PERSON> hi<PERSON>u sản phẩm tùy chỉnh", "info": "Chủ đề này sử dụng huy hiệu sản phẩm tùy chỉnh mà bạn có thể xác định tại đây. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/564-product-pages#product-badges)", "split_info": "Chủ đề này sử dụng huy hiệu sản phẩm tùy chỉnh mà bạn có thể xác định tại đây. [Tìm hiểu thêm](https://shopify-support.krowntheme.com/article/377-collection-pages#product-badges)", "name__1": "<PERSON><PERSON> hi<PERSON>u tùy chỉnh 1", "name__2": "<PERSON><PERSON> hi<PERSON> tùy chỉnh 2", "name__3": "<PERSON><PERSON> <PERSON><PERSON> tùy chỉnh 3", "text": "<PERSON><PERSON><PERSON>", "tags": "Tag", "color": "<PERSON><PERSON><PERSON>", "text_color": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "border_color": "<PERSON><PERSON><PERSON> viền"}}}}, "split-extra-words": {"settings_schema": {"colors": {"headings": {"header_sidebars": "<PERSON><PERSON><PERSON> trang &amp; <PERSON><PERSON> bên", "main": "<PERSON><PERSON>i dung", "footer": "<PERSON><PERSON> trang", "custom_colors": "<PERSON><PERSON><PERSON> s<PERSON>c tùy chỉnh"}, "settings": {"background": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>", "links": "<PERSON><PERSON><PERSON> kết ho<PERSON>t động", "borders": "<PERSON><PERSON><PERSON> thị đường viền"}}, "typography": {"headings": {"headings": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "body": "<PERSON><PERSON>i dung", "logo_menus": "Logo và menu", "buttons": "<PERSON><PERSON><PERSON>"}, "settings": {"font_family": "<PERSON><PERSON> phông chữ", "base_size": "<PERSON><PERSON><PERSON> c<PERSON> sở", "line_height": "<PERSON><PERSON><PERSON> cao dòng", "hr": {"label": "<PERSON><PERSON><PERSON> thị quy tắc ngang", "info": "<PERSON><PERSON>n thị một quy tắc ngang trực quan nhỏ trên một số tiêu đề"}, "border_radius": "<PERSON><PERSON> k<PERSON>h đ<PERSON> viền"}}, "embellishments": {"preloader": {"label": "<PERSON><PERSON><PERSON><PERSON> tải trước đa phương tiện", "info": "Hi<PERSON>n thị trình tải trước hình tròn nhỏ trong khi tập tin đa phương tiện trên cửa hàng của bạn vẫn đang tải."}, "breadcrumb": {"label": "Hiển thị breadcrumb", "info": "<PERSON><PERSON><PERSON> năng định hướng đường dẫn giúp người dùng định hướng trong cửa hàng và chỉ hiển thị trong các trang bộ sư<PERSON> tập, s<PERSON><PERSON> phẩm, tìm kiếm và tài kho<PERSON>n."}}, "cart": {"page": "Mặt hàng trong giỏ hàng", "show_recommendations": "Hiển thị đề xuất giỏ hàng"}, "headings": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "subtitle": "<PERSON><PERSON> đề"}, "product-grid": {"animation_style": {"label": "<PERSON><PERSON><PERSON> thị chú thích (m<PERSON>y t<PERSON>h để bàn)", "options__1": "<PERSON><PERSON><PERSON> thị", "options__2": "<PERSON><PERSON><PERSON> phủ", "info": "<PERSON><PERSON> thích sẽ luôn hiển thị trên thiết bị di động để có trải nghiệm người dùng tốt hơn"}, "overlay_colors": {"background": "<PERSON><PERSON><PERSON> chú thích lớp phủ", "text": "<PERSON><PERSON><PERSON> bản chú thích lớp phủ"}, "aspect_ratio": {"label": "Tỷ lệ khung hình tập tin đa phương tiện của sản phẩm", "options__1": "Cắt xén", "options__2": "<PERSON><PERSON> nhiên"}, "show_secondary_image": {"info": "Chỉ trên máy tính để bàn"}, "quick_buy": {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> nú<PERSON> \"thêm vào giỏ hàng\" ngay tức thì. <PERSON><PERSON><PERSON> sản phẩm có các kiểu mẫu mã, hệ thống sẽ hiển thị cửa sổ bật lên \"mua nhanh\".", "label": "<PERSON><PERSON><PERSON> t<PERSON>h năng mua nhanh"}, "rating": {"label": "<PERSON><PERSON><PERSON> thị xếp hạng (m<PERSON>y t<PERSON>h để bàn)", "options__1": "<PERSON><PERSON><PERSON><PERSON> hiển thị", "options__2": "<PERSON><PERSON><PERSON> thị khi di chuột", "options__3": "<PERSON><PERSON><PERSON> hiển thị", "show_on_mobile": "<PERSON><PERSON><PERSON> thị trên thiết bị di động"}}}, "sections": {"header": {"name": "<PERSON><PERSON><PERSON> trang", "settings": {"logo_height": "<PERSON><PERSON>u cao tối đa của ảnh logo", "menu": "<PERSON><PERSON>", "menu_style": {"label": "<PERSON><PERSON><PERSON> trình đơn máy tính để bàn", "options__1": "<PERSON><PERSON> điển", "options__2": "<PERSON><PERSON><PERSON> k<PERSON>"}, "collections_menu": {"header": "<PERSON><PERSON><PERSON><PERSON> bộ sưu tập", "info": "Trình đơn này có thiết kế táo bạo, đặc biệt là theo phong cách trình đơn cổ điển, trong đó trình đơn này sẽ biến thành một trình đơn khổng lồ với khả năng thêm hình ảnh và quảng cáo.", "settings": {"show_images": {"label": "<PERSON><PERSON><PERSON> th<PERSON>nh bộ sưu tập", "info": "Chỉ áp dụng nếu các mục gốc là bộ sưu tập."}}}, "promotional_block": {"name": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> mãi", "settings": {"show": {"label": "<PERSON><PERSON><PERSON> thị khối khuyến mãi", "info": "Trong kiểu tối <PERSON>, hệ thống sẽ hiển thị ở dưới cùng của ngăn trình đơn. Trong kiểu cổ điển, hệ thống sẽ hiển thị trong trình đơn bộ sưu tập, nế<PERSON> có."}, "title": {"label": "<PERSON>i<PERSON><PERSON> đề khu<PERSON>ến mãi"}, "content": {"label": "<PERSON><PERSON><PERSON> dung khu<PERSON>ến mãi"}, "button": {"label": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>t k<PERSON>n mãi"}, "link": {"label": "<PERSON><PERSON><PERSON> kết nút khuyến mãi"}, "txt_color": {"label": "<PERSON><PERSON><PERSON> văn bản khu<PERSON>ến mãi"}, "bg_color": {"label": "<PERSON><PERSON><PERSON> n<PERSON>n khu<PERSON>ến mãi"}, "image": {"label": "<PERSON><PERSON><PERSON> mãi"}}}, "announcement_bar": {"content": {"info": "Tối đa 50 ký tự"}}}}, "footer": {"blocks": {"menu": {"name": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}}}, "main-product": {"name": "<PERSON><PERSON> sản ph<PERSON>m", "settings": {"header": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> sản phẩm", "info": "Tr<PERSON><PERSON> thiết bị di động, tiêu đề sản phẩm sẽ luôn xuất hiện ở trên cùng, phía trên thư viện sản phẩm.", "show_tax_info": "<PERSON><PERSON><PERSON> thị thông tin thuế", "show_reviews": "<PERSON><PERSON><PERSON> thị xếp hạng sản phẩm", "show_sku": "Hiển thị SKU", "show_barcode": "Hiển thị MÃ VẠCH", "show_vendor": "<PERSON><PERSON><PERSON> thị nhà cung cấp", "show_badge": "<PERSON><PERSON><PERSON> thị huy hiệu sản phẩm"}, "variants": {"label": "<PERSON><PERSON><PERSON> công cụ chọn kiểu mẫu mã", "options__1": "<PERSON><PERSON><PERSON><PERSON>", "options__2": "<PERSON><PERSON><PERSON>"}, "gallery_aspect": {"label": "<PERSON><PERSON><PERSON> ảnh bằng thanh trượt cho vừa với chế độ xem", "info": "<PERSON><PERSON><PERSON><PERSON> thiết bị di động, ảnh sẽ luôn vừa với chế độ xem của thiết bị."}, "color_swatches": {"label": "Hiển thị các mẫu màu (chỉ dành cho kiểu khối)", "info": "Chủ đề này có thể hiển thị ảnh tùy chỉnh đối với các mẫu màu. [Tìm hiểu thêm](https://shopify-support.krownthemes.com/article/554-using-color-swatches)"}}}, "countdown-banner": {"name": "<PERSON><PERSON><PERSON><PERSON> ngữ đếm ng<PERSON>", "settings": {"header": "<PERSON><PERSON><PERSON> hồ đếm <PERSON>", "show_countdown": "<PERSON><PERSON><PERSON> thị đồng hồ đếm ng<PERSON>", "countdown_year": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>m", "countdown_month": "<PERSON><PERSON><PERSON><PERSON> tháng", "countdown_day": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON>y", "countdown_hour": "Cuối giờ", "countdown_timezone": "<PERSON><PERSON><PERSON> giờ", "size": "<PERSON><PERSON><PERSON> cao biểu ngữ"}}, "map": {"settings": {"map": {"api": {"label": "Khóa API Google Maps", "info": "Bạn cần đăng ký [Khóa API Google Maps](http://shopify-support.krownthemes.com/article/385-generate-an-api-key-for-google-maps) to display the map."}}}}}}, "main-cart-footer": {"name": "Tổng số tiền của giỏ hàng", "blocks": {"subtotal_button": {"name": "Tổng số tiền &amp; thanh toán"}}}, "main-cart-items": {"name": "Mặt hàng trong giỏ hàng"}, "main-list-collections": {"name": "<PERSON><PERSON> danh s<PERSON>ch bộ sưu tập", "blocks": {"collection": {"name": "<PERSON><PERSON> s<PERSON>u tập", "settings": {"collection": {"label": "<PERSON><PERSON> s<PERSON>u tập"}, "image": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> bạn muố<PERSON> b<PERSON> sung ảnh tùy chỉnh cho bộ sưu tập."}}}}, "settings": {"header": {"content": "<PERSON><PERSON> s<PERSON>u tập"}, "layout": {"label": "Bố cục", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON> c<PERSON>"}}, "paragraph": {"content": "Liệt kê tất cả các bộ sưu tập của bạn theo mặc định. <PERSON><PERSON> tùy chỉnh danh sách của bạn, h<PERSON><PERSON> chọn \"<PERSON> lựa chọn\" và thêm bộ sưu tập."}, "display_type": {"label": "<PERSON><PERSON><PERSON> bộ sưu tập để hiển thị", "options__1": {"label": "<PERSON><PERSON><PERSON> c<PERSON>"}, "options__2": {"label": "<PERSON>"}}, "sort": {"label": "<PERSON><PERSON><PERSON> xếp các bộ sưu tập theo:", "info": "Chỉ sắp xếp khi bạn chọn \"Tất cả\"", "options__1": {"label": "<PERSON> b<PERSON> chữ cái, <PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON> b<PERSON> chữ cái, <PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>, từ mới đến cũ"}, "options__4": {"label": "<PERSON><PERSON><PERSON>, từ cũ đến mới"}, "options__5": {"label": "<PERSON><PERSON> lượ<PERSON> sản ph<PERSON>, cao đến thấp"}, "options__6": {"label": "<PERSON><PERSON> l<PERSON><PERSON> sản ph<PERSON>, thấp đến cao"}}, "items_per_row": "S<PERSON> mặt hàng trên mỗi hàng"}}, "sidebar": {"name": "<PERSON><PERSON> b<PERSON>n", "settings": {"image": {"label": "Ảnh logo"}, "image_width": {"label": "Chiều rộng <PERSON>nh logo"}, "primary_navigation": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_navigation": {"label": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> phụ"}, "search": {"content": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON> thị tính năng tìm kiếm"}}}, "text-columns-with-icons": {"name": "<PERSON><PERSON><PERSON> văn bản với biểu tượng", "settings": {"content": {"label": "Chỉ hiển thị trên các trang được chọn:"}, "show_on_homepage": {"label": "Trang chủ"}, "show_on_product": {"label": "<PERSON><PERSON> sản ph<PERSON>m"}, "show_on_collection": {"label": "<PERSON><PERSON> bộ s<PERSON><PERSON> tập"}, "show_on_blog": {"label": "Trang blog &amp; b<PERSON><PERSON> vi<PERSON>t"}, "show_on_regular": {"label": "<PERSON><PERSON> thông thường"}, "icons": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON> nhìn trực quan tất cả các biểu tượng có trong chủ đề này, vui lòng truy cập [liên kết này](https://krownthemes.com/krown-icons/)"}}, "blocks": {"icon": {"name": "<PERSON><PERSON><PERSON> bản với biểu tượ<PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "text": {"label": "<PERSON><PERSON><PERSON>"}, "icon": {"label": "<PERSON><PERSON><PERSON> bi<PERSON> t<PERSON>"}}}}}, "footer": {"name": "<PERSON><PERSON> trang", "settings": {"show_payment_icons": {"label": "<PERSON><PERSON><PERSON> thị biểu tư<PERSON>h toán"}, "language_selector": {"content": "<PERSON><PERSON><PERSON> cụ chọn ngôn ngữ", "info": "<PERSON><PERSON> thêm ngôn ngữ, hã<PERSON> truy cập [phần cài đặt ngôn ngữ] của bạn.(/admin/settings/languages)"}, "language_selector_show": {"label": "<PERSON><PERSON><PERSON> thị công cụ chọn ngôn ngữ"}, "country_selector": {"content": "<PERSON><PERSON>ng cụ chọn quốc gia/khu vực", "info": "<PERSON><PERSON> thêm quốc gia/khu vực, hãy truy cập [phần cài đặt thanh toán] của bạn.(/admin/settings/payments)"}, "country_selector_show": {"label": "<PERSON><PERSON><PERSON> thị công cụ chọn quốc gia/khu vực"}}, "blocks": {"text": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "content": {"label": "<PERSON><PERSON>i dung"}, "text_size": {"label": "Cỡ chữ", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Lớn"}}}}, "menus": {"name": "<PERSON><PERSON>", "settings": {"title_1": {"label": "Tiêu đề trình đơn đầu tiên"}, "title_2": {"label": "Ti<PERSON><PERSON> đề trình đơn thứ hai"}, "menu_1": {"label": "<PERSON><PERSON><PERSON><PERSON> đơn đầu tiên", "info": "<PERSON>r<PERSON><PERSON> đơn này sẽ không hiển thị các mục thả xuống"}, "menu_2": {"label": "<PERSON><PERSON><PERSON><PERSON> đơn thứ hai"}}}, "newsletter": {"name": "<PERSON><PERSON><PERSON> ký email"}, "social": {"name": "<PERSON><PERSON><PERSON> kết mạng xã hội"}, "image": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "contact-form": {"name": "<PERSON><PERSON><PERSON><PERSON> mẫu <PERSON><PERSON>n hệ", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}}, "blocks": {"field": {"name": "Trường biểu mẫu", "settings": {"type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON> dòng duy nhất"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> dòng"}}, "required_field": {"label": "<PERSON><PERSON><PERSON> b<PERSON>"}, "labels": {"label": "<PERSON><PERSON>ã<PERSON>", "info": "<PERSON><PERSON><PERSON> đả<PERSON> bảo rằng tất cả các trường của bạn có nhãn không trùng lặp!"}}}, "email": {"name": "Tên &amp; email"}, "button": {"name": "<PERSON><PERSON><PERSON>", "settings": {"label": {"label": "<PERSON><PERSON>ã<PERSON>"}}}}, "presets": {"name": "<PERSON>i<PERSON>u mẫu liên hệ"}}, "image-with-text": {"name": "Ảnh với văn bản", "blocks": {"image": {"name": "Ảnh với văn bản", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "body": {"label": "<PERSON><PERSON><PERSON>"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "url": {"label": "<PERSON><PERSON><PERSON>", "info": "Toàn bộ khối này sẽ chuyển thành liên kết trừ khi có nhãn cho nút"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}}, "settings": {"image_height": {"label": "<PERSON><PERSON><PERSON> cao <PERSON>", "options__1": {"label": "Nhỏ"}, "options__2": {"label": "<PERSON>rung bình"}, "options__3": {"label": "Lớn"}, "options__4": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "text_width": {"label": "<PERSON><PERSON><PERSON> rộng vùng chứa văn bản", "options__1": {"label": "<PERSON>rung bình"}, "options__2": {"label": "Lớn"}, "options__3": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "text_size": {"label": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tiêu đề", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Lớn"}, "options__3": {"label": "Cực lớn"}}, "text_alignment": {"label": "<PERSON><PERSON>n chỉnh văn bản", "options__1": {"label": "Trên cùng bên trái"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng ch<PERSON>h g<PERSON>a"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng bên p<PERSON>i"}, "options__4": {"label": "<PERSON><PERSON><PERSON> bên tr<PERSON>i"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON><PERSON> cùng bên trái"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng ch<PERSON>h g<PERSON>a"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON> cùng bên ph<PERSON>i"}}, "options__5": {"label": "Cực lớn"}}, "presets": {"name": "Ảnh với văn bản"}}, "featured-product": {"name": "<PERSON><PERSON><PERSON> ph<PERSON>m nổi bật", "settings": {"product": {"label": "<PERSON><PERSON><PERSON> sản ph<PERSON>m"}}, "blocks": {"product_link": {"name": "<PERSON><PERSON><PERSON> kết sản ph<PERSON>m"}}}, "featured-collection": {"name": "<PERSON><PERSON> sưu tập nổi bật", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> thị liên kết đến trang bộ sưu tập"}, "layout": {"label": "Bố cục", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Ô lưới"}}, "products_number": {"label": "<PERSON><PERSON> lượng sản phẩm hiển thị tối đa"}, "collection": {"label": "<PERSON><PERSON> s<PERSON>u tập"}}, "presets": {"name": "<PERSON><PERSON> sưu tập nổi bật"}}, "gallery": {"name": "<PERSON><PERSON><PERSON> vi<PERSON>n", "blocks": {"image": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON>"}, "caption": {"label": "<PERSON><PERSON> th<PERSON>ch"}, "featured": {"label": "<PERSON><PERSON>g to ảnh trong lưới"}}}}, "settings": {"aspect_ratio": {"label": "Tỷ lệ khung hình của hình ảnh", "options__1": {"label": "<PERSON><PERSON><PERSON> (4:3)", "group": "Cắt xén"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> (1:1)"}, "options__3": {"label": "<PERSON> (5:6)"}, "options__4": {"label": "<PERSON> (2:3)"}, "options__5": {"label": "<PERSON><PERSON> nhiên", "group": "K<PERSON><PERSON>ng cắt xén"}, "info": "<PERSON>hi sử dụng tỷ lệ khung hình tự nhiên, bạn nhớ thay đổi kích thước hình thu nhỏ của mình ở cùng kích thước để thiết kế dạng lưới hiển thị rõ ràng. Khi sử dụng một trong các cài đặt cắt xén ảnh, tất cả các hình thu nhỏ sẽ được thay đổi thành cùng một kích thước."}, "style_mobile": {"label": "<PERSON><PERSON><PERSON><PERSON> thư viện thành thanh tr<PERSON><PERSON><PERSON> trên thiết bị di động"}, "slider_height": {"label": "<PERSON><PERSON><PERSON> cao thanh tr<PERSON><PERSON><PERSON> trên thiết bị di động", "options__1": {"label": "<PERSON>rung bình"}, "options__2": {"label": "Lớn"}, "options__3": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "lightbox": {"label": "<PERSON><PERSON><PERSON> t<PERSON> năng hộp đ<PERSON>n", "info": "<PERSON><PERSON><PERSON> thị <PERSON>nh lớn hơn khi nh<PERSON>p chuột"}}, "presets": {"name": "<PERSON><PERSON><PERSON> vi<PERSON>n"}}, "heading": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}}, "image": {"name": "<PERSON><PERSON><PERSON>", "mobile_image": "<PERSON><PERSON><PERSON>nh trên thiết bị di động (không bắt buộc)", "fullwidth": "<PERSON><PERSON><PERSON> bộ chiều rộng"}, "apps": {"name": "Ứng dụng", "settings": {"include_margins": {"label": "Đặt lề của phần giống như chủ đề"}}, "presets": {"name": "Ứng dụng"}}, "rich-text": {"name": "<PERSON><PERSON><PERSON> bản gi<PERSON><PERSON> t<PERSON> chất", "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "heading_size": {"label": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tiêu đề", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Lớn"}, "options__3": {"label": "Cực lớn"}}}}, "icon": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "text": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON><PERSON>"}}}, "button": {"name": "<PERSON><PERSON><PERSON>", "settings": {"button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link": {"label": "<PERSON><PERSON><PERSON> k<PERSON>"}, "button_size": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Lớn"}}}}}, "settings": {"text_alignment": {"label": "<PERSON><PERSON>n chỉnh văn bản", "options__1": {"label": "<PERSON><PERSON><PERSON> t<PERSON>"}, "options__2": {"label": "Ở giữa"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "image": {"label": "<PERSON><PERSON><PERSON>"}, "image_position": {"label": "<PERSON><PERSON> trí h<PERSON>nh <PERSON>nh", "options__1": {"label": "<PERSON><PERSON><PERSON> t<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_height": {"label": "<PERSON><PERSON><PERSON> cao <PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Lớn"}, "options__3": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>"}}}, "presets": {"name": "<PERSON><PERSON><PERSON> bản gi<PERSON><PERSON> t<PERSON> chất"}}, "shop-the-look": {"name": "Shop the look", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "subheading": {"label": "Ti<PERSON><PERSON> đề phụ"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}, "blocks": {"product": {"name": "<PERSON><PERSON><PERSON> p<PERSON>m", "settings": {"select_product": {"label": "<PERSON><PERSON><PERSON> sản ph<PERSON>m"}}}}, "presets": {"name": "Shop the look"}}, "testimonials": {"name": "<PERSON><PERSON><PERSON> ch<PERSON>ng thực", "blocks": {"testimonial": {"name": "<PERSON><PERSON><PERSON><PERSON> hàng chứng thực", "settings": {"quote": {"label": "<PERSON><PERSON><PERSON><PERSON> dẫn"}, "author_name": {"label": "<PERSON><PERSON><PERSON>"}, "author_title": {"label": "Ti<PERSON>u đề tác giả"}, "author_avatar": {"label": "Ảnh đại diện của tác giả"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON> ch<PERSON>ng thực"}}, "announcement-bar": {"name": "<PERSON><PERSON> thông báo", "settings": {"bar_show": {"label": "<PERSON><PERSON>n thị thanh thông báo"}, "bar_show_on_homepage": {"label": "Chỉ hiển thị trên trang chủ"}, "bar_show_dismiss": {"label": "<PERSON><PERSON><PERSON> thị nút bỏ qua"}, "bar_message": {"label": "<PERSON><PERSON>i dung"}, "bar_link": {"label": "<PERSON><PERSON><PERSON>"}, "bar_bgcolor": {"label": "<PERSON><PERSON><PERSON>"}, "bar_txtcolor": {"label": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n"}}}, "text-columns-with-images": {"name": "<PERSON><PERSON><PERSON> văn bản vớ<PERSON>nh", "blocks": {"text": {"name": "<PERSON><PERSON><PERSON>", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "text": {"label": "<PERSON><PERSON><PERSON>"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON> văn bản vớ<PERSON>nh"}}, "slider": {"slider_horizontal": {"name": "<PERSON><PERSON><PERSON><PERSON> chi<PERSON>: theo chi<PERSON>u ngang"}, "slider_vertical": {"name": "<PERSON><PERSON><PERSON><PERSON> chi<PERSON>: theo chi<PERSON><PERSON> dọc"}, "settings": {"desktop_height": {"label": "<PERSON><PERSON><PERSON> cao thanh tr<PERSON><PERSON><PERSON> trên máy tính để bàn", "options__1": {"label": "Nhỏ"}, "options__2": {"label": "<PERSON>rung bình"}, "options__3": {"label": "Lớn"}, "options__4": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "mobile_height": {"label": "<PERSON><PERSON><PERSON> cao thanh tr<PERSON><PERSON><PERSON> ngang trên thiết bị di động"}, "text_style": {"header": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n"}, "mobile_design": {"header": "<PERSON><PERSON><PERSON><PERSON> kế trên thiết bị di động", "label": "<PERSON><PERSON><PERSON><PERSON> thanh trư<PERSON><PERSON> dọc thành thanh trượt ngang trên thiết bị di động"}}, "blocks": {"image": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON>"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "subheading": {"label": "Ti<PERSON><PERSON> đề phụ"}, "caption": {"label": "<PERSON><PERSON> th<PERSON>ch"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "link": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> kết sẽ nằm trên văn bản trừ khi có nhãn cho nút."}}}}}, "video-popup": {"name": "Video: c<PERSON><PERSON> sổ bật lên", "settings": {"video": {"label": "URL video"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}, "video-background": {"name": "video: <PERSON><PERSON><PERSON><PERSON> trong n<PERSON>n", "settings": {"video": {"label": "URL video", "info": "Đường dẫn đến tập tin .mp4"}, "image": {"label": "Ảnh dự phòng", "info": "<PERSON><PERSON><PERSON><PERSON> bị di động sẽ sử dụng ảnh dự phòng khi người dùng tắt tính năng tự động phát."}, "size_alignment": {"content": "<PERSON><PERSON><PERSON> th<PERSON>c &amp; <PERSON><PERSON><PERSON> l<PERSON>"}, "video_height": {"label": "<PERSON><PERSON><PERSON> cao video", "options__1": {"label": "<PERSON><PERSON> n<PERSON> (16:9)", "group": "K<PERSON><PERSON>ng cắt xén"}, "options__2": {"label": "Lớn", "group": "Cắt xén"}, "options__3": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>"}}}}, "main-password-header": {"name": "<PERSON>i<PERSON><PERSON> đề mật khẩu"}, "main-password-content": {"name": "<PERSON><PERSON><PERSON> dung mật kh<PERSON>u"}, "main-password-footer": {"name": "<PERSON><PERSON> d<PERSON> mật kh<PERSON>u", "settings": {"show_social": {"label": "<PERSON><PERSON><PERSON> thị các biểu tượng xã hội"}}}, "main-article": {"name": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> trên blog", "blocks": {"featured_image": {"name": "Ảnh n<PERSON>i bật", "settings": {"image_height": {"label": "<PERSON><PERSON><PERSON> cao <PERSON>nh nổi bật", "options__1": {"label": "Vừa vớ<PERSON>"}, "options__2": {"label": "<PERSON>rung bình"}, "options__3": {"label": "Lớn"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "settings": {"blog_show_date": {"label": "<PERSON><PERSON><PERSON> thị ng<PERSON>y"}, "blog_show_author": {"label": "<PERSON><PERSON><PERSON> thị tác giả"}, "blog_show_comments": {"label": "<PERSON><PERSON><PERSON> thị số lư<PERSON>t bình luận"}}}, "content": {"name": "<PERSON><PERSON>i dung"}, "social_sharing": {"name": "<PERSON><PERSON><PERSON> chia sẻ trên mạng xã hội"}, "blog_navigation": {"name": "<PERSON><PERSON><PERSON> kết bài viết liền kề"}}}, "main-blog": {"settings": {"header": {"content": "Thẻ bài viết trên blog"}, "enable_tags": {"label": "<PERSON><PERSON><PERSON> t<PERSON>h năng lọc theo thẻ"}, "post_limit": {"label": "Số lượng bài viết/trang"}}}, "blog-posts": {"name": "<PERSON><PERSON><PERSON> đ<PERSON> trên blog", "blocks": {"title": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "info": {"name": "Thông tin", "settings": {"show_date": {"label": "<PERSON><PERSON><PERSON> thị ng<PERSON>y"}, "show_author": {"label": "<PERSON><PERSON><PERSON> thị tác giả"}}}, "summary": {"name": "<PERSON><PERSON><PERSON><PERSON> trích"}, "link": {"name": "<PERSON><PERSON><PERSON>"}}, "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "blog": {"label": "Blog"}, "post_limit": {"label": "<PERSON><PERSON><PERSON> vi<PERSON>"}, "show_image": {"label": "<PERSON><PERSON><PERSON> thị <PERSON>nh nổi bật"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> thị liên kết đến trang blog"}, "layout": {"label": "Bố cục"}, "option_1": {"label": "<PERSON><PERSON><PERSON>", "group": "Ô lưới"}, "option_2": {"label": "<PERSON> c<PERSON>"}, "option_3": {"label": "<PERSON><PERSON> (2 - 5 cột)", "group": "<PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON> đ<PERSON> trên blog"}}, "custom-colors": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "text": {"label": "<PERSON><PERSON><PERSON> văn bản tùy chỉnh"}, "overlay": {"label": "<PERSON><PERSON><PERSON> phủ n<PERSON>n"}, "background": {"label": "<PERSON><PERSON><PERSON> nền tùy chỉnh"}}, "custom-gutter": {"heading": {"content": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch gi<PERSON><PERSON> các c<PERSON>t"}, "gutter_enabled": {"label": "<PERSON> phép kho<PERSON>ng cách nội dung bên trong"}}, "newsletter": {"name": "<PERSON><PERSON><PERSON> ký email", "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}}}, "paragraph": {"name": "Ti<PERSON><PERSON> đề phụ", "settings": {"paragraph": {"label": "<PERSON><PERSON>"}}}, "email_form": {"name": "Bi<PERSON>u mẫu email"}}, "presets": {"name": "<PERSON><PERSON><PERSON> ký email"}}, "product-recommendations": {"name": "<PERSON><PERSON> xuất sản phẩm", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "header__1": {"content": "<PERSON><PERSON> xuất sản phẩm", "info": "T<PERSON>h năng đề xuất linh động sử dụng thông tin về đơn hàng và sản phẩm để thay đổi và cải thiện theo thời gian. [Tìm hiểu thêm](https://help.shopify.com/en/themes/development/recommended-products)"}}}, "custom-liquid": {"name": "Liquid Tùy chỉnh", "settings": {"custom_liquid": {"label": "Liquid Tùy chỉnh"}}, "presets": {"name": "Liquid Tùy chỉnh"}}, "collection-list": {"name": "<PERSON><PERSON> s<PERSON>ch bộ s<PERSON><PERSON> tập", "presets": {"name": "<PERSON><PERSON> s<PERSON>ch bộ s<PERSON><PERSON> tập"}}, "faq": {"name": "Hỏi đáp thường gặp", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "open_first": {"label": "Mở nút bật/tắt đầu tiên theo mặc định"}}, "blocks": {"text": {"name": "Hỏi đáp thường gặp", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "text": {"label": "<PERSON><PERSON><PERSON>"}, "image": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Hỏi đáp thường gặp"}}, "popup": {"name": "<PERSON><PERSON><PERSON> sổ bật lên", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "content": {"label": "<PERSON><PERSON>i dung"}, "show_newsletter": {"label": "Hiển thị biểu mẫu đăng ký email"}, "functionality": {"content": "<PERSON><PERSON><PERSON>"}, "enable": {"label": "<PERSON><PERSON><PERSON> t<PERSON>h năng cửa sổ bật lên"}, "show_after": {"label": "<PERSON><PERSON><PERSON> thị cửa sổ bật lên sau", "info": "giây"}, "frequency": {"label": "<PERSON><PERSON><PERSON> suất hiển thị cửa sổ bật lên", "options__1": {"label": "Hiển thị mỗi ngày"}, "options__2": {"label": "<PERSON><PERSON><PERSON> thị mỗi tuần"}, "options__3": {"label": "<PERSON><PERSON><PERSON> thị mỗi tháng"}}, "image": {"label": "<PERSON><PERSON><PERSON>", "info": "Bạn nên dùng tập tin 1240 x 400px .jpg. N<PERSON> chỉ xuất hiện trên máy tính để bàn"}}}, "main-search": {"name": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm", "settings": {"products_per_page": {"label": "Kết quả/trang"}}}, "main-collection-product-grid": {"name": "Ô lư<PERSON>i sản phẩm", "settings": {"products_per_page": {"label": "Sản phẩm/trang"}, "enable_filtering": {"label": "<PERSON><PERSON><PERSON> t<PERSON> n<PERSON>ng l<PERSON>c", "info": "[Bộ lọc tùy chỉnh](/admin/menus)"}, "enable_sorting": {"label": "<PERSON><PERSON><PERSON> t<PERSON>h năng sắp xếp"}, "image_filter_layout": {"label": "Bố cục bộ lọc <PERSON>"}, "header__1": {"content": "Lọc và sắp xếp"}}}, "main-collection-banner": {"name": "<PERSON><PERSON><PERSON><PERSON> ngữ bộ sưu tập", "settings": {"paragraph": {"content": "<PERSON><PERSON> thay đổi mô tả bộ sưu tập hoặc ảnh bộ sưu tập, hãy [chỉnh sửa bộ sưu tập của bạn.](/admin/collections)"}, "show_collection_description": {"label": "<PERSON><PERSON>n thị mô tả bộ sưu tập"}, "show_collection_image": {"label": "<PERSON><PERSON><PERSON> th<PERSON>nh bộ sưu tập", "info": "<PERSON><PERSON> có kết quả tốt nhất, h<PERSON><PERSON> sử dụng ảnh có tỷ lệ khung hình 16:9."}}}, "main-product": {"name": "Thông tin sản phẩm", "blocks": {"text": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON><PERSON>"}, "text_style": {"label": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "options__1": {"label": "<PERSON><PERSON>i dung"}, "options__2": {"label": "<PERSON><PERSON> đề"}, "options__3": {"label": "Chữ hoa"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "price": {"name": "Giá"}, "tax_info": {"name": "<PERSON><PERSON><PERSON> thị thông tin thuế"}, "sku_barcode": {"name": "SKU / mã vạch"}, "quantity_selector": {"name": "<PERSON><PERSON>ng cụ chọn số lượng"}, "variant_picker": {"name": "<PERSON><PERSON>ng cụ chọn kiểu mẫu mã", "settings": {"show_variant_labels": {"label": "Hiển thị nhãn kiểu mẫu mã"}, "hide_out_of_stock_variants": {"label": "Ẩn kiểu mẫu mã hết hàng"}, "low_inventory_notification": {"label": "<PERSON><PERSON><PERSON><PERSON> báo hàng tồn kho", "info": "<PERSON><PERSON><PERSON> kiểu mẫu mã cần phải bật tính năng theo dõi hàng tồn kho để tính năng này hoạt động. [Tìm hiểu thêm](https://help.shopify.com/en/manual/products/inventory/track_inventory)", "options__1": {"label": "<PERSON><PERSON>ông hiển thị thông tin hàng tồn kho"}, "options__2": {"label": "<PERSON><PERSON><PERSON> thị thông báo nếu hàng tồn kho giảm xuống dướ<PERSON> 5"}, "options__3": {"label": "<PERSON><PERSON><PERSON> hiển thị hàng tồn kho"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON> mua", "settings": {"show_dynamic_checkout": {"label": "<PERSON><PERSON><PERSON> thị nút thanh toán linh động", "info": "<PERSON><PERSON> sử dụng các phương thức thanh toán có sẵn trên cửa hàng của bạn, kh<PERSON>ch hàng sẽ thấy tùy chọn ưu tiên của họ, chẳng hạn như PayPal hoặc Apple Pay. [Tìm hiểu thêm]((https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Hiển thị biểu mẫu thông tin người nhận đối với các sản phẩm thẻ quà tặng", "info": "Bạn có thể chọn gửi trực tiếp các sản phẩm thẻ quà tặng đến người nhận cùng với thông điệp cá nhân."}, "show_quantity_selector": {"label": "<PERSON><PERSON><PERSON> thị công cụ chọn số lượng"}}}, "pickup_availability": {"name": "Tình trạng sẵn có của việc lấy hàng"}, "description": {"name": "<PERSON><PERSON>", "settings": {"product_description_truncated": {"label": "<PERSON><PERSON><PERSON> gọn ph<PERSON>n mô tả", "info": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON> r<PERSON> g<PERSON>n"}, "options__2": {"label": "<PERSON><PERSON><PERSON> thị đoạn trích ng<PERSON>n"}, "options__3": {"label": "<PERSON><PERSON><PERSON> thị đoạn trích trung bình"}, "options__4": {"label": "<PERSON><PERSON><PERSON> thị đoạn trích dài"}}}}, "share": {"name": "<PERSON><PERSON> sẻ", "settings": {"featured_image_info": {"content": "<PERSON><PERSON>u bạn đính kèm liên kết trong các bài viết trên mạng xã hội, trang này sẽ hiển thị ảnh nổi bật dưới dạng ảnh xem trước. [Tìm hiểu thêm](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Tiêu đề và mô tả cửa hàng đư<PERSON><PERSON> đính kèm trong ảnh xem trước. [Tìm hiểu thêm](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "Thẻ có thể thu gọn", "settings": {"heading": {"info": "<PERSON><PERSON> gồm tiêu đề giải thích nội dung.", "label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "content": {"label": "<PERSON><PERSON>i dung của thẻ"}, "page": {"label": "<PERSON><PERSON><PERSON> dung của thẻ từ trang"}, "image": {"label": "Ảnh của thẻ"}}}}, "settings": {"header": {"content": "Media", "info": "T<PERSON>m hiểu thêm về [loại tập tin đa phương tiện](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "<PERSON><PERSON><PERSON> ho<PERSON>t thông tin sản phẩm cố định trên màn hình lớn"}, "enable_video_looping": {"label": "<PERSON><PERSON><PERSON> t<PERSON> năng lặp video"}, "enable_zoom": {"label": "<PERSON><PERSON><PERSON> t<PERSON>h năng thu ph<PERSON>g <PERSON>nh"}, "gallery_gutter": {"label": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> cách gi<PERSON>a các tập tin đa phương tiện"}, "gallery_slider_style": {"label": "<PERSON><PERSON><PERSON> ảnh bằng thanh trượt cho vừa với chế độ xem"}, "gallery_style": {"label": "<PERSON><PERSON><PERSON> th<PERSON> viện", "info": "Mặc định là thanh trượt đối với thiết bị di động", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "gallery_pagination": {"label": "<PERSON><PERSON> trang thư viện", "options__1": {"label": "<PERSON><PERSON><PERSON> ch<PERSON>m"}, "options__2": {"label": "<PERSON><PERSON>nh thu nhỏ"}}}}, "images": {"label_1": "Hình ảnh 1", "label_2": "Hình ảnh 2", "label_3": "Hình ảnh 3"}}, "color-schemes": {"name_plural": "Color schemes", "name_singular": "Color scheme", "default": "Default color scheme", "default_info": "Used to set colors for account pages, 404 pages, and other pages or sections without color scheme settings.", "drawers": "Modals/drawers", "other": "Other colors"}, "local-250": {"labels": {"show_filters_as": "<PERSON><PERSON><PERSON> thị bộ lọc dưới dạng", "expand_filters_by_default": "Mở rộng bộ lọc theo mặc định", "stick_filters_sidebar_to_top": "<PERSON><PERSON> <PERSON>h bên bộ lọc lên trên cùng"}, "options": {"sidebar": "<PERSON><PERSON> b<PERSON>n", "list": "<PERSON><PERSON>"}}, "local-230": {"background_gradient": "<PERSON><PERSON><PERSON>n", "variant_default": {"label": "Chọn mẫu mã đầu tiên còn hàng theo mặc định", "info": "<PERSON><PERSON><PERSON> bạn không chọn mục n<PERSON>, người dùng sẽ phải chọn một mẫu mã còn hàng rồi mới mua được."}, "slider_info": "<PERSON><PERSON><PERSON> kết sẽ được áp dụng cho nút, hoặc cho tiêu đề (nếu không có nút), hoặc cho toàn bộ slide (nếu cả tiêu đề và nút đều trống).", "buy_button_labels": {"label": "<PERSON><PERSON><PERSON><PERSON> nút mua", "option_1": "<PERSON><PERSON> ngay", "option_2": "<PERSON><PERSON><PERSON> tùy chọn"}, "hide_on_mobile": "Ẩn trên thiết bị di động"}, "local-223": {"heading_text_color": "<PERSON><PERSON><PERSON> văn bản tiêu đề", "slider_navigation_color": "<PERSON><PERSON><PERSON> củ<PERSON> các phần tử điều hướng"}, "late_edits": {"badge": {"custom_badge": {"text_color": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n"}, "sold_out": {"name": "<PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON> b<PERSON> h<PERSON>t", "text_color": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> bản \"<PERSON><PERSON> bán hết\"", "sale_text": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> bản \"<PERSON><PERSON><PERSON><PERSON> giá\""}}, "rich-text": {"image_position": {"no_image": {"group": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh", "label": "<PERSON><PERSON><PERSON><PERSON> hiển thị hình <PERSON>nh"}}}}}