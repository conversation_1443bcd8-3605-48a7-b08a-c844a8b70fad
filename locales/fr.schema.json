{"variant_metafields": {"name": "Métafichier de variante", "label": "Clé du métafichier de variante", "info": "Ce thème peut afficher un métafichier de variante sur la page produit. [En savoir plus](https://shopify-support.krownthemes.com/article/884-using-variant-metafields-in-product-pages)."}, "new_color_swatches": "Le type de sélecteur de variante « blocs » offre une prise en charge des échantillons de couleur créés avec des métachamps de catégorie. [En savoir plus](https://help.shopify.com/en/manual/custom-data/metafields/category-metafields/using-category-metafields).", "video_controls": "Afficher les commandes vidéo", "sticky_cart_actions": "Activer les actions du panier fixe", "currency_codes": {"header": "Format de la monnaie", "label": "Afficher les codes de devise", "info": "Exemple: $1.00 USD."}, "a11": {"label": "Accessibilité", "show_sidebars_scrollbar": "Afficher la barre de défilement des volets latéraux", "disable_all_image_animations": "Désactiver toutes les animations d'images"}, "divider": {"label": "Diviseur", "divider_design": "Conception du diviseur", "divider_style_solid": "<PERSON><PERSON>", "divider_style_dotted": "<PERSON><PERSON><PERSON>", "divider_style_dashed": "Tireté", "divider_style_double": "Double", "divider_color": "<PERSON><PERSON><PERSON>", "divider_image": "Image du diviseur", "divider_image_info": "Une image répétitive horizontalement. Remplace le style et la couleur ci-dessus."}, "cart_actions": {"label": "Actions sur le panier à tiroirs", "option_1": "<PERSON><PERSON><PERSON><PERSON> le bouton \"Voir le panier\"", "option_2": "Bouton \"Passer à la caisse\"", "option_3": "Afficher les deux"}, "sticky_atc": {"label": "A<PERSON>ter au panier collant", "enable_sticky_atc": "<PERSON>r l'ajout au panier collant", "sticky_atc_style_wide": "Wide", "sticky_atc_style_floating": "Floating"}, "performance": {"header": "SEO & performance", "name": "Performance", "label": "Précharger les liens au survol", "info": "Augmente la vitesse de chargement perçue des pages."}, "recently_viewed": {"enable_recently_viewed_products": "Activer les produits récemment consultés", "enable_recently_viewed_products_info": "<PERSON><PERSON><PERSON><PERSON>'activ<PERSON>, le thème enregistrera les produits consultés, mais vous devez ajouter la section dans votre magasin pour afficher ces produits.", "recently_viewed_products": "Produits récemment consultés", "recently_viewed_products_info": "Cette section doit avoir la fonctionnalité activée dans les paramètres du thème. Elle ne s'affichera qu'après que les utilisateurs aient visité au moins une page de produit.", "recently_viewed_products_limit": "Limite des produits récemment consultés"}, "rating_apps_update": {"label": "Application de notation", "info": "Les applications tierces peuvent nécessiter des étapes supplémentaires pour être correctement intégrées."}, "local-220": {"preorder": "Afficher l'étiquette du bouton \"Précommander\"", "autorotate": {"heading": "Rotation automatique", "info": "Faire défiler les diapositives automatiquement.", "enable": "Activer la rotation automatique", "interval": "Intervalle", "pause_on_mouseover": "Pause au survol de la souris"}}, "custom-social-icons": {"header": "<PERSON><PERSON>", "info": "Téléchargez une icône personnalisée pour votre réseau social préféré", "icon": {"label": "Icône", "info": "72 x 72px transparent .png"}, "link": {"label": "<PERSON><PERSON>"}}, "local-march-update": {"labels": {"dynamic_content": {"name": "Contenu dynamique", "hide_block": "Masquer le bloc si le contenu dynamique n'est pas présent", "hide_section": "Masquer la section si le contenu dynamique n'est pas présent"}, "buttons": "Boutons", "cards": "<PERSON><PERSON>", "heading": "<PERSON>-tête", "buttons_custom": "Couleurs personnalisées des boutons", "center_heading": "<PERSON><PERSON> l'en<PERSON>tê<PERSON>", "section_design": "Design de la section", "bottom_margin": "Supprimer la marge du bas", "text_spacing": "Espacement du texte", "inherit_card_design": "Hériter des propriétés du design de la carte", "align_button": "<PERSON><PERSON><PERSON> le bouton d'achat au bas de la carte", "custom_colors": "Couleurs personnalisées"}, "shadows": {"label": "Ombre", "label_plural": "<PERSON><PERSON><PERSON>", "offset_x": "Décalage horizontal", "offset_y": "Décalage vertical", "blur": "<PERSON><PERSON>", "hide": "Masquer l'ombre", "hide_button_shadows": "Masquer l'ombre des boutons"}, "blocks": {"countdown_timer": {"name": "Horloge de compte à rebours", "label": "Source dynamique", "info": "Définir une source de temps dynamique pour l'horloge du compte à rebours. [En savoir plus] (https://shopify-support.krownthemes.com/article/642-product-pages#countdown-timer)"}, "progress_slider": {"name": "Graphique à barres de progression", "value": "<PERSON><PERSON>", "height": "<PERSON><PERSON>", "width": "<PERSON><PERSON>", "dynamic_content": {"info": "Utilisez des sources dynamiques pour définir des valeurs uniques en créant des métachamps de produits pour le graphique de progression. [En savoir plus](https://shopify-support.krownthemes.com/article/804-product-page#Progress-chart-S8c6R)", "slider_label": "<PERSON><PERSON><PERSON><PERSON> vale<PERSON>", "dots_label": "Métachamp des points mis en évidence"}}, "progress_dots": {"name": "Graphique à points de progression", "highlight": "Points mis en évidence", "total": "Total des points", "icon": "Icône du point", "size": "Taille du point", "inactive_color": "Couleur inactive", "active_color": "<PERSON>uleur mise en évidence"}, "store_selector": {"default": "Premier magasin par défaut"}, "rating": {"app": "Application d'évaluations", "default_option": "<PERSON><PERSON> <PERSON><PERSON>"}, "space": {"name": "Espace vide"}, "badges": {"name": "Badges de produits"}, "nutritional": {"name": "Informations nutritionnelles", "label_first": "Étiquette de la première colonne", "label_second": "Étiquette de la deuxième colonne", "label_third": "Étiquette de la troisième colonne", "information": {"label": "Informations", "info": "Séparer l'étiquette et la valeur par une virgule. Utiliser des sauts de ligne pour ajouter une nouvelle ligne. Utiliser un trait d'union pour indenter les lignes. [En savoir plus](https://shopify-support.krownthemes.com/article/804-product-page#Nutritional-information-ypnLL)"}, "extra_information": "Informations supplémentaires"}}, "sections": {"progress_sliders": {"name": "Graphiques à barres de progression", "block_name": "<PERSON><PERSON>"}, "header": {"settings": {"promotion": {"header_1": "Promotion 1", "header_2": "Promotion 2", "header_3": "Disposition du menu", "show": "Afficher la promotion", "image": "Image de la promotion", "text": "Texte de la promotion", "width": "<PERSON>ur de la colonne"}}}}}, "settings_schema": {"exit_intent": {"exit_intent_popup": "Popup d'intention de sortie", "exit_intent_popup_info": "Cette section fonctionne uniquement sur ordinateur"}, "colors": {"name": "Couleurs", "settings": {"header__1": {"content": "Barre la<PERSON>"}, "header__2": {"content": "Corps"}, "header__3": {"content": "Pied de page"}, "bg_color": {"label": "<PERSON>ond"}, "txt_color": {"label": "Texte"}, "link_color": {"label": "<PERSON><PERSON>"}}}, "typography": {"name": "Typographie", "settings": {"headings_font": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "base_size": {"label": "<PERSON>lle de <PERSON>"}, "large_size": {"label": "Grands en-têtes", "info": "Affecte les grands titres des sections de la barre de défilement, du texte enrichi et des images avec texte."}, "body_font": {"label": "Corps"}, "nav_size": {"label": "Navigation principale"}}}, "product-grid": {"name": "Grille de produits", "settings": {"aspect_ratio": {"label": "Rapport d'aspect des supports multimédias"}, "show_secondary_image": {"label": "Afficher le deuxième support multimédia du produit au survol de la souris"}, "enhance_featured_products": {"label": "Mettre en valeur les nouveautés", "info": "[En savoir plus](https://shopify-support.krownthemes.com/article/832-product-card)"}, "show_discount": {"label": "Afficher la réduction en tant que...", "options__1": {"label": "Texte"}, "options__2": {"label": "Pourcentage"}}, "caption_placement": {"label": "Placement de la légende", "options__1": {"label": "<PERSON><PERSON>", "group": "Visible au passage de la souris"}, "options__2": {"label": "Sous l'image", "group": "Toujours visible"}}, "grid_color_bg": {"label": "Fond de la légende de la couche"}, "grid_color_text": {"label": "Couleur du texte de la légende de la couche"}, "header__1": {"content": "Évaluation du produit", "info": "Pour afficher les évaluations, ajoutez une application d'évaluation des produits. [Apprendre encore plus](https://apps.shopify.com/search?q=product%20reviews)"}, "header__2": {"content": "Évaluation des produits"}, "show_reviews": {"label": "Afficher la évaluation"}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Image de la favicon", "info": "48 x 48 px .png obligatoire"}}}, "cart-page": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Type de panier", "options__1": {"label": "Page"}, "options__2": {"label": "<PERSON><PERSON>"}}, "cart_notes": {"label": "Activer les notes du panier"}, "cart_buttons": {"label": "Afficher des boutons de paiement supplémentaires"}}}, "embellishments": {"name": "Décorations", "settings": {"show_preloader": {"label": "Préchargeur d'images", "info": "Affiche un petit préchargeur circulaire pendant que les images de votre boutique sont en cours de chargement."}, "show_breadcrumb": {"label": "<PERSON><PERSON><PERSON><PERSON> le fil d'Ariane", "info": "Le fil d'Ariane aide les utilisateurs à naviguer la boutique et s'affiche uniquement sur les pages de collection, de produit et de recherche."}, "show_go_top": {"label": "<PERSON><PERSON><PERSON><PERSON> le bouton 'Aller en haut'"}}}, "search": {"name": "<PERSON><PERSON><PERSON>", "settings": {"predictive_search": {"label": "Activer la recherche prédictive"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le vendeur"}, "show_price": {"label": "<PERSON>ff<PERSON>r le prix"}, "include_articles": {"label": "Inclure les articles dans les résultats de recherche"}, "include_pages": {"label": "Inclure les pages dans les résultats de recherche"}}}, "social": {"name": "Réseaux sociaux"}, "follow_on_shop": {"content": "Suivre sur Shop", "info": "Pour autoriser les clients à suivre votre boutique sur l’application Shop depuis votre boutique en ligne, Shop Pay doit être activé. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)", "label": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "labels": {"hide_block_if_no_content_info": "Masquer le bloc si le contenu n'est pas défini", "popup_page_info": "Remplace le contenu textuel si une page est sélectionnée", "page": "Page", "popup": "Popup", "open_popup": "<PERSON><PERSON><PERSON><PERSON><PERSON> la popup"}}, "sections": {"main-404": {"name": "Principal 404"}, "main-gift-card": {"name": "<PERSON><PERSON> cadeau"}, "main-page": {"name": "Page principale"}, "refactor_words": {"seo": {"name": "SEO", "label": "<PERSON><PERSON> de titre", "info": "<PERSON><PERSON><PERSON> le niveau du titre pour aider les moteurs de recherche à indexer la structure de votre page.", "microdata": {"label": "Désactiver le schéma de microdonnées", "info": "Cela supprimera le balisage schema.org de la page. Ne désactivez cette option que si vous utilisez une application tierce pour le SEO !"}}, "late_edits": {"image_with_hotspots": {"image_on_mobile": "Image sur mobile", "position_on_mobile": "Position sur mobile", "hotspot": {"mobile_info": "Seulement si une image mobile est définie"}}, "product-card": {"thumbnails": {"border": "<PERSON>uleur de bordure du média"}}, "labels": {"optional": "Facultatif"}, "before-after": {"layout": {"invert": "Inverser la mise en page sur les appareils mobiles"}}}, "labels": {"footer_group": "Groupe de pied de page", "header_group": "Groupe d'en-tête", "overlay_group": "Groupe de recouvrement", "embellishments": "Décorations", "show_button": "Show button", "optional": "Optional"}, "text": {"icons_info": "Pour visualiser et télécharger d'autres icônes, veuillez vous rendre sur [ce lien](https://resources.krownthemes.com/icons/)."}, "borders": {"top_border": "Bordure du haut", "bottom_border": "Bordure du bas", "show_border": "Show border"}, "colors": {"heading_background": "Arrière-plan de l'en-tête", "shadow": "Afficher l'ombre"}, "social": {"phone": "Téléphone", "discord": "Discord"}, "settings": {"image_with_hotspots": {"label": "Image avec points d'accès", "hotspot": {"label": "Point d'accès", "label_desktop_offset": "Point d'accès de bureau", "label_mobile_offset": "Point d'accès mobile", "offset_horizontal": "Décalage horizontal", "offset_vertical": "Décalage vertical", "tooltip": {"label": "Info-bulle", "position": {"label": "Position", "option_1": "<PERSON><PERSON>", "option_2": "Bas", "option_3": "G<PERSON><PERSON>", "option_4": "<PERSON><PERSON><PERSON>"}, "show_tooltip": "Show tooltip"}, "mobile_info": "Only if a mobile image is set"}, "image_on_mobile": "Mobile image", "position_on_mobile": "Position on mobile"}, "scrolling_images": {"label": "Défilement des images", "image_size": "<PERSON><PERSON> d'image", "columns": "Columns"}, "video": {"label": "Vidéo", "info": "Format MP4 requis, pas de son"}, "variants_functionality": {"label": "<PERSON><PERSON>rer les variantes indisponibles", "option_1": "Masquer", "option_2": "Désactiver", "option_3": "<PERSON><PERSON><PERSON><PERSON>"}, "auto_height": {"label": "Hauteur automatique", "info_slider": "Si vous cochez cette option, les paramètres de hauteur ci-dessus seront écrasés et la hauteur du diaporama sera adaptée à l'image de chaque diapositive."}}, "header": {"promotion_block": {"image_link": "Lien de l'image de la promotion"}, "sticky": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> collan<PERSON>", "option_1": "Désactivé", "option_2": "Toujours", "option_3": "Seulement pour le défilement vers le haut"}}, "inventory": {"name": "Niveau d'inventaire", "settings": {"show_progress_bar": "Afficher la barre de progression", "low_inventory_threshold": "Seuil d'inventaire bas", "show_block": {"always": "Tou<PERSON><PERSON> afficher", "low": "Afficher uniquement lorsque l'inventaire est inférieur au seuil"}}}, "breadcrumb": {"name": "Fil d'Ariane", "info": "Le fil d'Ariane n'apparaît pas sur la page d'accueil"}, "announcement-bar": {"visibility": {"label": "Visibilité", "option_1": "Toutes les pages", "option_2": "Page d'accueil uniquement", "option_3": "Toutes les pages sauf la page d'accueil", "option_4": "Pages de produits uniquement", "option_5": "Page du panier uniquement"}, "color": {"border": "<PERSON><PERSON><PERSON> de la bordure"}}, "promotional_banner": {"name": "Bannière promotionnelle", "enable": "Afficher la bannière"}, "cookies_banner": {"name": "Cookies", "enable": "Afficher l'avis relatif aux cookies"}, "before_after": {"name": "Comparaison d'images", "layout": {"label": "Mise en page", "option__1": "Horizontal", "option__2": "Vertical"}, "style": {"label": "Style de couleur", "option__1": "<PERSON>", "option__2": "<PERSON><PERSON><PERSON>"}, "image": {"label__1": "Image", "label__2": "Image sur mobile", "label__3": "Étiquette"}}, "cart_upsell": {"name": "Recommandation individuelle de produits ", "product": "Produit", "info": "Les recommandations dynamiques se basent sur les articles de votre panier. Elles changent et s'améliorent avec le temps [En savoir plus](https://help.shopify.com/en/themes/development/recommended-products)"}, "gift_wrapping": {"name": "Emballage cadeau", "info": "L'emballage cadeau doit être configuré comme un produit. [En savoir plus](https://shopify-support.krownthemes.com/article/696-cart-features#gift-wrap)", "settings": {"label": "Texte", "button": "Étiquette de bouton"}}, "custom_code": {"name": "HTML personnalisé / Liquid"}, "rating": {"name": "Application d'évaluations", "default": "<PERSON><PERSON> <PERSON><PERSON>"}, "product-page": {"size_guide": {"label": "Guide des tailles", "page": "Page du guide des tailles", "options": {"label": "Ouv<PERSON>r les options", "option_1": "Fenêtre pop-up", "option_2": "<PERSON><PERSON><PERSON> fenê<PERSON>", "option_3": "Nouvelle fenêtre"}}, "gallery_resize": {"label": "Rapport d'aspect des images", "info": "Les vidéos et autres types de médias s'afficheront dans leur format d'origine.", "option_1": "Adapter les images à l'intérieur du conteneur"}, "gallery_padding": {"label": "Espace intérieur de la galerie"}, "gallery_background": {"label": "Arrière-plan de la galerie", "info": "Visible uniquement si les images sont configurées pour tenir dans le conteneur."}}, "product-card": {"name": "Fiche produit", "labels": {"thumbnail": "Vignette du produit", "caption": "Légende du produit", "color_swatches": "Nuanciers"}, "thumbnails": {"fit": "Adapter le média à l'intérieur du conteneur", "padding": {"label": "Espace intérieur du conteneur", "info": "Fonctionne uniquement si le média est configuré pour tenir dans le conteneur."}, "background": {"label": "Arrière-plan du conteneur", "info": "Visible uniquement si les médias sont configurés pour tenir dans le conteneur."}, "border": "<PERSON><PERSON><PERSON> de la bordure", "color_swatches": "Afficher les nuanciers dans la fiche produit", "color_swatches_on_hover": "Afficher les nuanciers dans la fiche produit (au passage de la souris)"}, "color_swatches_label": {"label": "Étiquettes de nuanciers de couleurs", "info": "Écrivez plusieurs titres de variantes (séparés par des virgules) que vous souhaitez transformer en nuanciers de couleurs."}, "badges": {"name": "Badges de produits", "show_badges": "Afficher les badges", "settings": {"colors": {"text": "Couleur du texte des badges", "sold_out": "Couleur de fond « épuisé »", "sale": "Couleur d'arrière-plan « remise »"}}, "badge_sale": {"name": "Badge de remise", "amount_saved": "<PERSON><PERSON>"}, "regular_badges": {"info": "Retrouvez plus d'informations sur les badges de produits [ici](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)."}, "sold_out": {"name": "Badge épuisé", "text_color": "Couleur du texte « épuisé »", "sale_text": "Couleur du texte « réduction »"}, "custom_badges": {"name": "Badges de produits personnalisés", "info": "Ce thème utilise des badges de produits personnalisés que vous pouvez définir ici. [En savoir plus](https://shopify-support.krownthemes.com/article/564-product-pages#product-badges)", "split_info": "Ce thème utilise des badges de produits personnalisés que vous pouvez définir ici. [En savoir plus](https://shopify-support.krownthemes.com/article/377-collection-pages#product-badges)", "name__1": "Badge personnalisé 1", "name__2": "Badge personnalisé 2", "name__3": "Badge personnalisé 3", "text": "Texte", "tags": "Étiquette", "color": "<PERSON><PERSON><PERSON> de fond", "text_color": "Couleur du texte", "border_color": "<PERSON><PERSON><PERSON> de la bordure"}}}, "seo_late": {"breadcrumbs": {"label": "Improve product breadcrumb navigation", "info": "Adds collection names to the breadcrumb navigation, by duplicating product url's. [Learn more](https://shopify-support.krownthemes.com/article/697-seo#breadcrumbs)"}}}, "local-extra-words": {"settings_schema": {"colors": {"headings": {"header": "<PERSON>-tête", "cards": "<PERSON><PERSON>"}, "settings": {"borders": "Bordure", "hide_border": "Masquer la bordure", "accent": "<PERSON><PERSON><PERSON><PERSON>", "overlay": "Overlay"}}, "typography": {"buttons": {"label": "Poids de la police des boutons", "option__1": "Normal", "option__2": "En gras"}, "menus": {"header": "Menus", "size": "<PERSON><PERSON> de <PERSON> base", "weight": "Poids de la police", "weight_bold": "Gras"}}, "borders": {"name": "Bordures", "main": {"name": "Sections", "info": "Ce paramètre contrôle le style des bordures de toutes les sections du thème."}, "buttons": {"name": "Boutons"}, "forms": {"name": "Formulaires"}, "settings": {"width": "<PERSON><PERSON>", "radius": "Rayon"}}, "layout": {"name": "Mise en page", "sections": {"vertical_space": "Espace vertical entre les sections", "remove_vertical_space": "Supprimer la marge supérieure", "remove_bottom_margin": "Supprimer la marge inférieure"}, "grid": {"name": "Grille", "info": "Affecte les zones avec une disposition multicolonne.", "horizontal_space": "Espace horizontal", "vertical_space": "Espace vertical"}}, "cart": {"shipping": {"name": "Expédition", "show": {"label": "Afficher le montant minimum de l'expédition gratuite", "info": "Pour configurer les frais d'expédition, accédez à vos [paramètres d'expédition](/admin/settings/shipping)."}, "amount": {"label": "Montant minimum d'expédition gratuite", "info": "Écrivez un chiffre, sans lettres ni caractères spéciaux."}, "info": "This feature only works with a single currency and shipping method. If you're using multiple shipping methods or currencies, it might show false values to the customer."}, "settings": {"media": {"header": "Media aspect ratio", "label": "Aspect ratio", "info_cart": "Changes the aspect ratio for small product thumbnails used in the cart, search popup and some other areas that require small product thumbnails."}}}, "aspect_ratio": {"landscape": {"label": "Plus court (3:2)"}}, "maps": {"name": "<PERSON><PERSON>"}, "search": {"predictive_search": {"name": "Recherche prédictive", "info": "La recherche prédictive prend en charge les suggestions de produits, de collections, de pages et d'articles."}}, "product-card": {"name": "Fiche produit", "title-size": {"name": "<PERSON><PERSON> du titre", "options__1": "<PERSON>", "options__2": "Grand"}, "local-pickup": {"name": "Disponibilité locale", "info": "Ce thème affiche la disponibilité locale des produits. [En savoir plus](https://shopify-support.krownthemes.com/article/805-collection-page)"}, "badges": {"name": "Badges de produits par défaut", "settings": {"colors": {"text": "Couleur du texte des badges", "sold_out": "Couleur de fond « épuisé »", "sale": "Couleur d'arrière-plan « remise »"}}, "badge_sale": {"name": "Badge de remise"}, "custom_badges": {"name": "Badges de produits personnalisés", "info": "Ce thème utilise des badges de produits personnalisés que vous pouvez définir ici. [En savoir plus](https://shopify-support.krownthemes.com/article/795-product-card#Product-badges-_KIdK)", "name__1": "Badge personnalisé 1", "name__2": "Badge personnalisé 2", "name__3": "Badge personnalisé 3", "text": "Texte", "tags": "Étiquette", "color": "Couleur d'arrière-plan", "text_color": "Text color"}}, "icons_list": "Liste dynamique d'icônes", "products-list": {"name": "Products list"}}, "embellishments": {"settings": {"show_gotop": {"label": "Show go to top button"}}}}, "sections": {"video": {"name": "Vidéo", "settings": {"video": {"label": "URL de la vidéo"}, "image": {"label": "Image de fond"}}}, "contact-form": {"settings": {"form-fields": {"name": "Champs de formulaire", "show-phone": "Aff<PERSON>r le téléphone", "show-subject": "Afficher le sujet"}}, "blocks": {"contact-info": {"name": "Informations de contact", "settings": {"title": {"label": "Titre"}, "content": {"label": "Contenu"}}}}}, "text-columns-with-icons": {"blocks": {"icon": {"name": "Icône personnalisée", "info": "256 x 256 px"}, "select_icon": {"info": "Pour visualiser et télécharger plus d'i<PERSON><PERSON><PERSON>, ve<PERSON><PERSON><PERSON> visiter [ce lien](https://resources.krownthemes.com/icons/)"}, "icon_color": {"label": "Icône", "info": "Fonctionne uniquement pour les icônes incluses"}}}, "content-toggles": {"name": "Boutons de basculement du contenu", "block": "Contenu"}, "text-columns-with-images": {"blocks": {"image": {"info": "640 x 640 px"}}}, "announcement-bar": {"settings": {"social": {"header": "Icônes sociales", "info": "Pour configurer les profils sociaux, allez dans Paramètres du thème > Social.", "label": "Afficher les icônes sociales"}}, "blocks": {"content": {"name": "Contenu", "settings": {"text": "Texte", "link": "<PERSON><PERSON>", "target": "<PERSON><PERSON><PERSON><PERSON><PERSON> le lien dans une nouvelle fenêtre"}}}}, "newsletter": {"show_icon": "Afficher l'icône"}, "cookies": {"name": "Fenêtre contextuelle sur les cookies", "cookies_info": "Ce site utilise des cookies pour garantir la meilleure expérience utilisateur. [En savoir plus](#)"}, "popups": {"name": "Fenêtres contextuelles", "blocks": {"model": {"model-1": "Cookies", "model-2": "Bulletin d'information", "model-3": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"size": {"label": "<PERSON><PERSON> de la fenêtre contextuelle", "option_1": "<PERSON>", "option_2": "Grand"}}}}, "age-verification": {"name": "Vérification de l'âge", "settings": {"button-text": "Texte du bouton"}}, "stores-map": {"name": "Carte des magasins", "settings": {"map": {"title": "<PERSON><PERSON>"}, "gallery": {"title": "Galerie de magasins"}}}, "store-selector": {"name": "<PERSON><PERSON><PERSON><PERSON> de ma<PERSON>", "settings": {"map": {"label": "Activer la carte dynamique", "info": "Assurez-vous que la clé API Google Maps est correctement définie dans les paramètres du thème."}, "zoom": {"label": "Zoom de la carte", "info": "Choisissez une valeur appropriée pour voir tous les magasins souhaités en une seule fois."}}, "blocks": {"map": {"name": "Emplacement de la carte", "settings": {"address": {"label": "<PERSON><PERSON><PERSON>", "info": "En savoir plus"}, "image": {"label": "Image", "info": "Téléchargez une image statique si vous ne souhaitez pas utiliser la carte dynamique."}, "style": {"label": "Style de carte", "option__1": "Standard", "option__2": "Argenté", "option__3": "<PERSON><PERSON><PERSON>", "option__4": "Sombre", "option__5": "Nuit", "option__6": "Aubergine"}, "pin": {"label": "<PERSON><PERSON>le personnalisée de la carte", "info": "240 x 240 px transparent .png"}}}, "store": {"name": "Ma<PERSON><PERSON>", "settings": {"name": {"label": "Nom", "info": "Le nom du magasin doit correspondre au nom de votre magasin défini dans vos [paramètres de localisation](/admin/settings/locations)."}, "pickup_price": {"label": "Prix de l'enlèvement"}, "pickup_time": {"label": "Heure de l'enlèvement"}, "address": {"label": "<PERSON><PERSON><PERSON> du magasin"}, "image": {"label": "Image du magasin"}, "closing_times": {"label": "Heures de fermeture (facultatif)", "info": "Ajoutez 7 lignes, une pour chaque jour de la semaine, en commençant par le dimanche."}, "timezone": {"label": "<PERSON><PERSON> ho<PERSON>", "info": "Utilisé pour afficher correctement les heures de fermeture"}, "map": {"name": "É<PERSON>le de carte", "info": "Si la carte est activée, vous devez définir une épingle personnalisée pour cette adresse. [Apprenez comment obtenir les coordonnées de votre adresse](https://support.google.com/maps/answer/18539?hl=en)"}, "map_latitude": {"label": "Latitude", "info": "Coordonnée de latitude pour le marqueur. Exemple : 46.7834818"}, "map_longitude": {"label": "Longitude", "info": "Coordonnée de longitude pour le marqueur. Exemple : 23.5464733"}, "get_directions_button": {"label": "<PERSON><PERSON><PERSON><PERSON> le bouton « obtenir un itinéraire »", "info": "Ouvre une carte plus grande dans un nouvel onglet du navigateur."}, "map_pin": {"label": "<PERSON><PERSON><PERSON>", "info": "90 x 90 px transparent .png"}}}}}, "header": {"settings": {"layout": {"label": "Disposition de l'en-tête", "info": "Affecte la position des blocs personnalisés et des actions par défaut", "option__1": "Blocs personnalisés en haut, actions par défaut en bas", "option__2": "Actions par défaut en haut, blocs personnalisés en bas"}, "sticky": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> collan<PERSON>", "info": "Affiche la navigation lorsque l'utilisateur fait défiler la page vers le haut"}}, "blocks": {"info": {"name": "Infos", "style": {"label": "Style", "option__1": "Infos texte", "option__2": "Bouton", "info": "Sur les boutons, seule la légende est visible, en tant qu'étiquette du bouton."}, "custom-icon": {"label": "Icône personnalisée", "info": "Téléchargez une image .png de 76 x 76 px"}, "icon": {"label": "Icône"}, "link_type": {"label": "Ouvrir un lien", "option__1": "À l'intérieur d'une fenêtre modale", "option__2": "Sur la même page", "option__3": "Dans une nouvelle page", "info": "Les fenêtres modales ne fonctionnent qu'avec des liens inter-pages"}}, "store-selector": {"name": "<PERSON><PERSON><PERSON><PERSON> de ma<PERSON>", "content": "Le sélecteur de magasin peut être configuré dans la section « Sélecteur de magasin ». [En savoir plus](https://shopify-support.krownthemes.com/article/799-store-selector)", "info": "Ce thème vous permet de connecter les emplacements de vos magasins réels à un sélecteur de magasin interactif. [En savoir plus](https://shopify-support.krownthemes.com/article/799-store-selector)"}, "mega-menu": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"menu_handle": {"label": "Poignée de menu", "info": "Ce thème utilise des méga-menus. [En savoir plus](https://shopify-support.krownthemes.com/article/798-header-section#mega-menus)"}}}}}, "marquee": {"name": "<PERSON><PERSON>", "settings": {"scroll_direction": "Direction du défilement", "scroll_speed": "Vitesse de d<PERSON>", "scroll_speed_info": "Plus la valeur est grande, plus le défilement est lent", "pause_on_mouseover": "Pause au passage de la souris", "scroll_item": "<PERSON><PERSON><PERSON>", "scroll_item_text": "<PERSON><PERSON>"}}, "image-section": {"name": "Image", "settings": {"image_size": {"label": "Largeur du bureau", "info": "Sur mobile, l'image sera en pleine largeur."}}}, "media-with-text-overlay": {"name": "Média avec texte superposé", "blocks": {"media": "Média", "image": {"name": "Image"}, "link": {"info": "Le titre se transformera en lien à moins qu'il y ait une étiquette pour le bouton"}, "video": {"name": "Vidéo", "label": "Vidéo", "info": "L'image ci-dessus s'affichera si cette vidéo ne peut pas être lue."}}, "settings": {"height": "<PERSON><PERSON> de la carte", "option__1": "<PERSON>", "option__2": "Grand", "option__3": "Très grand", "option__4": "Plein écran", "option__5": "Ré<PERSON>lier"}}, "blog-posts": {"settings": {"emphasize": {"label": "Souligner le premier article", "info": "Seulement sur le bureau"}}, "blocks": {"summary": {"name": "Extrait", "settings": {"excerpt_limit": "Nombre de mots", "excerpt_limit_info": "S'applique si l'article n'a pas d'extrait manuel ajouté dans l'administration."}}}}, "testimonials": {"name": "Témoignages", "blocks": {"name": "Image"}}, "slideshow": {"name": "Diaporama", "block": {"name": "Image"}, "settings": {"caption_size": "Taille de la légende"}}, "rich-text": {"settings": {"image_position": {"label": "Position de l'image", "option__1": "À gauche", "option__2": "Au-dessus du texte", "option__3": "À droite"}, "fullwidth": {"label": "<PERSON><PERSON>e largeur", "info": "Étendez l'arrière-plan de cette section pour remplir l'écran."}, "height": {"label": "<PERSON><PERSON> de la carte", "info": "Hauteur minimale de la carte sur le bureau. Sur mobile, la hauteur dépendra du contenu."}, "crop": {"label": "Remplir la zone de l'image", "info": "L'image sera recadrée pour remplir toute la hauteur de la carte sur le bureau. Sur mobile, l'image sera toujours entièrement affichée."}, "remove_margin": {"label": "Supprimer la marge supérieure de la section"}}}, "main-header": {"settings": {"mobile": {"name": "Navigation mobile", "info": "Ces options n'affectent que la visibilité à l'intérieur du tiroir de navigation mobile.", "header_actions": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de magasin et les blocs d'information", "header_info_blocks": {"header": "Blocs d'information dans l'en-tête", "label_1": "Affichez le sélecteur de magasin et les blocs d'information dans l'en-tête sur les appareils mobiles.", "label_2": "Positionner les blocs d'informations en haut de la première section de la page d'accueil", "label_2_info": "S'intègre bien lorsque la première section est un diaporama pleine largeur"}}, "promotion_block": {"title": {"label": "Titre", "size": "<PERSON><PERSON> du titre"}, "subtitle": {"label": "Sous-titre", "size": "<PERSON><PERSON> du sous-titre"}, "button": {"label": "Étiquette du bouton", "size": "<PERSON><PERSON> du <PERSON>on", "link": "<PERSON>n du bouton", "style": "Style du bouton"}}, "header_actions": {"header": "Blocs d'informations d'en-tête sur mobile", "show_in_drawer": "Afficher dans le tiroir de navigation"}}}, "main-cart": {"blocks": {"shipping-calculator": {"name": "Calculateur d'expédition"}, "related-products": {"info": "The product recommendations are based on products that are commonly purchased together or products in related collections. If there are no recommendations, the block will not appear."}}}, "main-search": {"settings": {"blogs": {"name": "Résultats des articles"}, "products": {"name": "Résultats des produits", "info": "Le contenu de la fiche produit doit être défini à l'aide des blocs de section."}}}, "main-product": {"name": "Page produit", "settings": {"gallery_pagination": "Pagination du curseur de la galerie", "show_border": "Afficher la bordure autour de la galerie", "gallery_border": "Gallery border", "gallery_border_color": "Gallery border color"}, "blocks": {"pickup_availability": {"name": "Disponibilité de l'enlèvement", "info": "Ce thème affiche la disponibilité du ramassage en fonction du magasin sélectionné. En savoir plus", "settings": {"style": "Style", "option__1": "Compact", "option__2": "<PERSON><PERSON><PERSON>"}}, "buy_buttons": {"settings": {"show_price": "<PERSON>ff<PERSON>r le prix"}}, "related": {"name": "Produits associés", "settings": {"products": "Produits"}}, "tax_info": {"name": "Informations sur la taxe"}, "icons": {"name": "Liste des icônes", "info": "Pour visualiser et télécharger les icônes incluses dans le thème, ve<PERSON><PERSON><PERSON> visiter [ce lien](https://resources.krownthemes.com/icons/).", "help": "Ce thème vous permet d'ajouter des icônes de produits personnalisées grâce à un contenu dynamique. [En savoir plus](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "headers": {"icon_1": "Icône 1", "icon_2": "Icône 2", "icon_3": "Icône 3", "icon_4": "Icône 4", "icon_5": "Icône 5", "icon_6": "Icône 6"}, "settings": {"icon": "Icône ", "icon_info": "96 x 96 px", "label": "Étiquette"}}}}, "main-blog": {"name": "Blog principal"}, "main-article": {"name": "Article", "settings": {"show_tags": "Afficher les balises", "enhance_product_links": {"label": "Améliorer les liens vers les produits", "info": "Tous les liens vers les produits ouvriront la fenêtre modale d'achat rapide du produit."}}}, "main-article-comments": {"name": "Commentaires sur les articles", "info": "Pour activer les commentaires, allez dans les [paramètres du blog](/admin/blogs)."}, "main-article-navigation": {"name": "Navigation dans les articles", "settings": {"header": {"content": "Articles du blog", "info": "Laissez vide si vous souhaitez charger l'article précédent ou suivant du blog par défaut."}, "posts": {"next": "Article suivant", "previous": "Article précédent"}}}, "main-page": {"settings": {"center": {"label": "Contenu central sur le bureau"}}}, "main-footer": {"blocks": {"payment": {"name": "Icônes de paiement", "info": "Les icônes qui s'affichent sont déterminées par les [paramètres de paiement] de votre magasin (/admin/settings/payments) ainsi que par la région et la devise du client.", "custom_icons": "Custom icons", "custom_icons_info": "Upload 114 x 72px .png images", "only_show_custom_icons": "Only show the custom icons"}, "text": {"name": "Text & social icons"}}}, "customers": {"reset-password": {"name": "Réinitialiser le mot de passe"}, "order": {"name": "Page de commande"}, "register": {"name": "Page d'inscription"}, "activate-account": {"name": "Page d'activation du compte"}, "login": {"name": "Page de connexion", "shop_login_button": {"enable": "Activer la connexion avec Shop"}}, "account": {"name": "Page du compte"}, "addresses": {"name": "Adresses"}}, "headings": {"heading": "Rubrique", "subheading": "Sous-titre", "title": "Titre", "subtitle": "Sous-titre", "caption": "Légende", "text_content": "Contenu du texte", "custom_colors": "Couleurs personnalisées", "text_style": "Style du texte"}, "columns": {"name": "Mise en page pour le bureau", "info": "La mise en page s'adapte aux appareils mobiles.", "option__0": "1 colonne", "option__1": "2 colonnes", "option__2": "3 colonnes", "option__3": "4 colonnes", "option__4": "5 colonnes", "option__5": "6 colonnes", "name_mobile": "Mobile layout"}, "promotion-cards": {"name": "Cartes de promotion", "blocks": {"name": "<PERSON><PERSON>"}}, "faq": {"headings": {"header": "<PERSON>-tête", "content": "Contenu"}, "settings": {"form": {"header": "Formulaire de contact", "show": "Formulaire de présentation", "title": "Titre du formulaire"}}}, "product-quick-view": {"name": "Vue rapide", "info": "Ce modèle contrôle la façon dont la vue rapide du produit est construite. Seule cette section apparaîtra dans la fenêtre modale."}, "product-card": {"blocks": {"price": "Prix", "title": "Titre", "vendor": "Fournisseur", "text": {"name": "Texte dynamique", "info": "Utilisez une source dynamique pour mettre en évidence un attribut unique en créant un méta-champ de produit. [En savoir plus](https://shopify-support.krownthemes.com/article/805-collection-page)", "settings": {"text": {"label": "Méta-champ d'étiquette"}, "size": {"label": "<PERSON>lle du texte", "option__1": "<PERSON>", "option__2": "Normal", "option__3": "Large"}, "color": {"label": "Couleur du texte", "option__1": "Primaire", "option__2": "Secondaire"}, "transform": {"label": "Transformation du texte (majuscules)"}}}, "icons": {"info": "Utilisez des sources dynamiques pour mettre en évidence des attributs uniques en créant des méta-champs de produit pour la liste d'icônes. [En savoir plus](https://shopify-support.krownthemes.com/article/639-theme-settings#dynamic-icons)", "settings": {"icon": "Méta-champ d'i<PERSON>", "label": "Méta-champ d'étiquette"}}, "quick_buy": "Achat rapide", "rating": "Évaluation"}}, "buttons": {"style": {"label": "Style de bouton", "option__1": "<PERSON>tour", "option__2": "Solide"}}}}, "complementary_products": {"name": "Produits complémentaires", "settings": {"paragraph": {"content": "Pour sélectionner des produits complémentaires, ajoutez l'application Search & Discovery. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}}}, "split-extra-words": {"settings_schema": {"colors": {"headings": {"header_sidebars": "En-tête et encadrés", "main": "Corps", "footer": "Pied de page", "custom_colors": "Couleurs personnalisées"}, "settings": {"background": "<PERSON>ond", "text": "Texte", "links": "Liens actifs", "borders": "Afficher les bordures"}}, "typography": {"headings": {"headings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Corps", "logo_menus": "Logo et menus", "buttons": "Boutons"}, "settings": {"font_family": "Famille de polices", "base_size": "<PERSON>lle de <PERSON>", "line_height": "<PERSON><PERSON> <PERSON> ligne", "hr": {"label": "Afficher les règles horizontales", "info": "Affiche une petite règle horizontale visuelle sur certains titres"}, "border_radius": "Rayon de la bordure"}}, "embellishments": {"preloader": {"label": "Précha<PERSON>ur de média<PERSON>", "info": "Affiche un petit préchargeur circulaire pendant que les médias sur votre boutique sont en cours de chargement."}, "breadcrumb": {"label": "<PERSON><PERSON><PERSON><PERSON> le fil d'Ariane", "info": "Le fil d'Ariane aide les utilisateurs à naviguer dans la boutique et n'apparaît que sur les pages de collections, produits, recherches et comptes."}}, "cart": {"page": "Articles du panier", "show_recommendations": "Afficher les recommandations du panier"}, "headings": {"title": "Titre", "subtitle": "Sous-titre"}, "product-grid": {"animation_style": {"label": "Affichage des légendes (bureau)", "options__1": "Visible", "options__2": "<PERSON><PERSON>", "info": "Sur mobile, la légende sera toujours visible pour une meilleure ergonomie"}, "overlay_colors": {"background": "Fond de la légende de la couche", "text": "Texte de légende superposé"}, "aspect_ratio": {"label": "Aspect média du produit", "options__1": "Retaillée", "options__2": "Naturelle"}, "show_secondary_image": {"info": "Seulement sur le bureau"}, "quick_buy": {"name": "Achat rapide", "info": "Ajoute un bouton « ajouter au panier » instantané. Si le produit a des variantes, une fenêtre pop-up d'achat rapide s'affichera.", "label": "Autoriser l'achat rapide"}, "rating": {"label": "Affichage des notes (bureau)", "options__1": "Ne pas afficher", "options__2": "Afficher au passage de la souris", "options__3": "Toujours visible", "show_on_mobile": "Afficher sur mobile"}}}, "sections": {"header": {"name": "<PERSON>-tête", "settings": {"logo_height": "Hauteur maximale de l'image du logo", "menu": "<PERSON><PERSON>", "menu_style": {"label": "Style du menu du bureau", "options__1": "Classique", "options__2": "<PERSON><PERSON>"}, "collections_menu": {"header": "Menu collections", "info": "Le design est audacieux, notamment dans le style de menu classique, où il se transforme en un méga-menu avec la possibilité d'ajouter des images et une promotion.", "settings": {"show_images": {"label": "Afficher les images de la collection", "info": "Ne s'applique que si l'élément parent est une collection."}}}, "promotional_block": {"name": "Bloc promotionnel", "settings": {"show": {"label": "Afficher le bloc promotionnel", "info": "En style minimal, il apparaît en bas du tiroir du menu. En style classique, il apparaît dans le menu des collections, s'il y en a un."}, "title": {"label": "Titre de la promotion"}, "content": {"label": "Contenu de la promotion"}, "button": {"label": "Libellé du bouton de la promotion"}, "link": {"label": "Lien du bouton de la promotion"}, "txt_color": {"label": "Couleur du texte de la promotion"}, "bg_color": {"label": "Couleur de l'arrière-plan de la promotion"}, "image": {"label": "Image de la promotion"}}}, "announcement_bar": {"content": {"info": "50 caractères maximum"}}}}, "footer": {"blocks": {"menu": {"name": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}}}, "main-product": {"name": "Page du produit", "settings": {"header": {"label": "En-tête de produit", "info": "Sur les appareils mobiles, l'en-tête du produit apparaît toujours en haut, au-dessus de la galerie de produits.", "show_tax_info": "Afficher les informations des taxes", "show_reviews": "Afficher la note du produit", "show_sku": "Afficher l'UGS", "show_barcode": "Afficher le code-barre", "show_vendor": "<PERSON><PERSON><PERSON><PERSON> le vendeur", "show_badge": "Afficher le badge du produit"}, "variants": {"label": "Type de sélecteur de variantes", "options__1": "Blocs", "options__2": "<PERSON><PERSON><PERSON><PERSON>"}, "gallery_aspect": {"label": "Mettre à l'échelle les images de la barre de défilement pour les adapter à la fenêtre d'affichage", "info": "Sur mobile, les images sont toujours adaptées à la fenêtre d'affichage de l'appareil."}, "color_swatches": {"label": "Afficher les échantillons de couleurs (uniquement pour le style bloc)", "info": "Ce thème peut afficher des images personnalisées pour les échantillons de couleurs. [Learn more](https://shopify-support.krownthemes.com/article/554-using-color-swatches)"}}}, "countdown-banner": {"name": "Bannière de compte à rebours", "settings": {"header": "Horloge de compte à rebours", "show_countdown": "Afficher l'horloge du compte à rebours", "countdown_year": "<PERSON><PERSON>", "countdown_month": "<PERSON><PERSON> de fin", "countdown_day": "<PERSON>ur de fin", "countdown_hour": "Heure de fin", "countdown_timezone": "<PERSON><PERSON> ho<PERSON>", "size": "<PERSON><PERSON> <PERSON>"}}, "map": {"settings": {"map": {"api": {"label": "Clé API Google Maps", "info": "Vous devrez enregistrer un [Google Maps API Key](http://shopify-support.krownthemes.com/article/385-generate-an-api-key-for-google-maps) pour afficher la carte."}}}}}}, "main-cart-footer": {"name": "Sous-total du panier", "blocks": {"subtotal_button": {"name": "Sous-total et paiement"}}}, "main-cart-items": {"name": "Éléments du panier"}, "main-list-collections": {"name": "Page de liste des collections", "blocks": {"collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}, "image": {"label": "Image", "info": "Si vous souhaitez ajouter une image personnalisée pour la collection."}}}}, "settings": {"header": {"content": "Collections"}, "layout": {"label": "Mise en page", "options__1": {"label": "Une colonne"}, "options__2": {"label": "Deux colonnes"}}, "paragraph": {"content": "Toutes vos collections sont publiées par défaut. Pour personnaliser votre liste, choisissez « Sélectionné » et ajoutez des collections."}, "display_type": {"label": "Sélectionnez les collections à afficher", "options__1": {"label": "Toutes"}, "options__2": {"label": "Sélectionné"}}, "sort": {"label": "Trier les collections par :", "info": "Le tri s'applique uniquement lorsque l'option « Tous » est sélectionnée", "options__1": {"label": "Alphabétique (A-Z)"}, "options__2": {"label": "Alphabétique (Z-A)"}, "options__3": {"label": "Date, récente à ancienne"}, "options__4": {"label": "Date, ancienne à récente"}, "options__5": {"label": "Nombre de produits par ordre décroissant"}, "options__6": {"label": "Nombre de produits par ordre croissant"}}, "items_per_row": "Nombre d'articles par ligne"}}, "sidebar": {"name": "Barre la<PERSON>", "settings": {"image": {"label": "Image du logo"}, "image_width": {"label": "Largeur de l'image du logo"}, "primary_navigation": {"label": "Navigation principale"}, "secondary_navigation": {"label": "Navigation secondaire"}, "search": {"content": "<PERSON><PERSON><PERSON>", "label": "Afficher la recherche"}}}, "text-columns-with-icons": {"name": "Colonnes de texte avec icônes", "settings": {"content": {"label": "Afficher uniquement sur les pages sélectionnées :"}, "show_on_homepage": {"label": "Page d'accueil"}, "show_on_product": {"label": "Pages de produits"}, "show_on_collection": {"label": "Pages de collections"}, "show_on_blog": {"label": "Pages de blog et d'articles"}, "show_on_regular": {"label": "Pages normales"}, "icons": {"label": "Icônes", "info": "Pour visualiser toutes les icônes comprises dans le thème, veuil<PERSON>z consulter [ce lien](https://krownthemes.com/krown-icons/)"}}, "blocks": {"icon": {"name": "Texte avec icône", "settings": {"title": {"label": "<PERSON>-tête"}, "text": {"label": "Texte"}, "icon": {"label": "Sélectionner une icône"}}}}}, "footer": {"name": "Pied de page", "settings": {"show_payment_icons": {"label": "Afficher les icônes de paiement"}, "language_selector": {"content": "<PERSON><PERSON><PERSON><PERSON> de langue", "info": "Pour ajouter une langue, rendez-vous dans vos [paramètres de langue.](/admin/settings/languages)"}, "language_selector_show": {"label": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de langue"}, "country_selector": {"content": "Sélecteur de pays/région", "info": "Pour ajouter un pays/une région, allez à vos [paramètres de paiement.](/admin/settings/payments)"}, "country_selector_show": {"label": "Aff<PERSON>r le sélecteur de pays/région"}}, "blocks": {"text": {"name": "Texte", "settings": {"title": {"label": "<PERSON>-tête"}, "content": {"label": "Contenu"}, "text_size": {"label": "<PERSON>lle du texte", "options__1": {"label": "Normal"}, "options__2": {"label": "Large"}}}}, "menus": {"name": "Menus", "settings": {"title_1": {"label": "En-tête du premier menu"}, "title_2": {"label": "En-tête du deuxième menu"}, "menu_1": {"label": "Premier menu", "info": "Ce menu n'affiche pas les articles de la liste déroulante"}, "menu_2": {"label": "Deuxième menu"}}}, "newsletter": {"name": "Inscription par mail"}, "social": {"name": "Liens de réseaux sociaux"}, "image": {"name": "Image", "settings": {"image": {"label": "Sélectionner une image"}}}}}, "contact-form": {"name": "Formulaire de contact", "settings": {"title": {"label": "<PERSON>-tête"}}, "blocks": {"field": {"name": "Champ du formulaire", "settings": {"type": {"label": "Type", "options__1": {"label": "Ligne unique"}, "options__2": {"label": "Plusieurs lignes"}}, "required_field": {"label": "Obligatoire"}, "labels": {"label": "Étiquette", "info": "V<PERSON><PERSON>z à ce que tous vos champs aient une étiquette unique !"}}}, "email": {"name": "Nom et mail"}, "button": {"name": "Bouton d'envoi", "settings": {"label": {"label": "Étiquette"}}}}, "presets": {"name": "Formulaire de contact"}}, "image-with-text": {"name": "Images avec texte", "blocks": {"image": {"name": "Image avec texte", "settings": {"title": {"label": "<PERSON>-tête"}, "body": {"label": "Texte"}, "button_label": {"label": "Étiquette de bouton"}, "url": {"label": "<PERSON><PERSON>", "info": "Le bloc entier se transformera en lien à moins qu'il n'y ait une étiquette pour le bouton"}, "image": {"label": "Image de fond"}}}}, "settings": {"image_height": {"label": "<PERSON>ur de l'image", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Large"}, "options__4": {"label": "Pleine page"}}, "text_width": {"label": "Largeur du conteneur de texte", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Large"}, "options__3": {"label": "Pleine page"}}, "text_size": {"label": "<PERSON><PERSON> de l'en-tête", "options__1": {"label": "Normal"}, "options__2": {"label": "Large"}, "options__3": {"label": "Très grand"}}, "text_alignment": {"label": "Alignement du texte", "options__1": {"label": "Coin supérieur gauche"}, "options__2": {"label": "Centre supérieur"}, "options__3": {"label": "Coin supérieur droit"}, "options__4": {"label": "Milieu gauche"}, "options__5": {"label": "Milieu central"}, "options__6": {"label": "Milieu droit"}, "options__7": {"label": "Coin inférieur gauche"}, "options__8": {"label": "Centre inférieur"}, "options__9": {"label": "Coin inférieur droit"}}, "options__5": {"label": "Extra-large"}}, "presets": {"name": "Images avec texte"}}, "featured-product": {"name": "Nouveauté", "settings": {"product": {"label": "Sélectionner un produit"}}, "blocks": {"product_link": {"name": "Lien du produit"}}}, "featured-collection": {"name": "Collection vedette", "settings": {"title": {"label": "<PERSON>-tête"}, "show_view_all": {"label": "Afficher le lien vers la page de la collection"}, "layout": {"label": "Mise en page", "options__1": {"label": "Barre de d<PERSON>"}, "options__2": {"label": "Grille"}}, "products_number": {"label": "Nombre maximal de produits affichés"}, "collection": {"label": "Collection"}}, "presets": {"name": "Présenter une collection"}}, "gallery": {"name": "Galerie", "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}, "caption": {"label": "Légende"}, "featured": {"label": "Agrandir l'image dans la grille"}}}}, "settings": {"aspect_ratio": {"label": "Rapport d'aspect des images", "options__1": {"label": "<PERSON><PERSON> (4:3)", "group": "Retaillée"}, "options__2": {"label": "Carrée (1:1)"}, "options__3": {"label": "<PERSON> (5:6)"}, "options__4": {"label": "Plus grande (2:3)"}, "options__5": {"label": "Naturelle", "group": "Non retaillée"}, "info": "Lorsque vous utilisez le rapport d'aspect naturel, veillez à redimensionner vos vignettes à la même taille pour un design de grille soignée. En utilisant l'un des paramètres d'image retaillée, toutes les vignettes seront redimensionnées à la même taille."}, "style_mobile": {"label": "Transformer la galerie en barre de défilement sur mobile"}, "slider_height": {"label": "<PERSON><PERSON> de la barre de défilement sur mobile", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Large"}, "options__3": {"label": "Pleine page"}}, "lightbox": {"label": "Activer la lightbox", "info": "Affiche une image plus grande en cas de clic"}}, "presets": {"name": "Galerie"}}, "heading": {"name": "<PERSON>-tête", "settings": {"title": {"label": "Titre"}}, "presets": {"name": "<PERSON>-tête"}}, "image": {"name": "Image", "mobile_image": "Image mobile (facultatif)", "fullwidth": "<PERSON><PERSON>e largeur"}, "apps": {"name": "Applications", "settings": {"include_margins": {"label": "Rendre les marges des sections identiques à celles du thème"}}, "presets": {"name": "Applications"}}, "rich-text": {"name": "Texte enrichi", "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}, "heading_size": {"label": "<PERSON><PERSON> de l'en-tête", "options__1": {"label": "Normal"}, "options__2": {"label": "Large"}, "options__3": {"label": "Très grand"}}}}, "icon": {"name": "Icône"}, "text": {"name": "Texte", "settings": {"text": {"label": "Texte"}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Étiquette de bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "button_size": {"label": "<PERSON><PERSON> du <PERSON>on", "options__1": {"label": "Normal"}, "options__2": {"label": "Large"}}}}}, "settings": {"text_alignment": {"label": "Alignement du texte", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Milieu"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "image": {"label": "Image"}, "image_position": {"label": "Position de l'image", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_height": {"label": "<PERSON>ur de l'image", "options__1": {"label": "Normal"}, "options__2": {"label": "Large"}, "options__3": {"label": "Pleine page"}}}, "presets": {"name": "Texte enrichi"}}, "shop-the-look": {"name": "Dé<PERSON>uvrir le look", "settings": {"heading": {"label": "<PERSON>-tête"}, "subheading": {"label": "Sous-titre"}, "image": {"label": "Image de fond"}}, "blocks": {"product": {"name": "Produit", "settings": {"select_product": {"label": "Sélectionner un produit"}}}}, "presets": {"name": "Dé<PERSON>uvrir le look"}}, "testimonials": {"name": "Témoignages", "blocks": {"testimonial": {"name": "Témoignage", "settings": {"quote": {"label": "Citation"}, "author_name": {"label": "Nom de l'auteur"}, "author_title": {"label": "Titre de l'auteur"}, "author_avatar": {"label": "Avatar de l'auteur"}}}}, "presets": {"name": "Témoignages"}}, "announcement-bar": {"name": "Barre d'annonce", "settings": {"bar_show": {"label": "Afficher la barre d'annonce"}, "bar_show_on_homepage": {"label": "Afficher uniquement sur la page d'accueil"}, "bar_show_dismiss": {"label": "<PERSON><PERSON><PERSON><PERSON> le bouton d'annulation"}, "bar_message": {"label": "Contenu"}, "bar_link": {"label": "<PERSON><PERSON>"}, "bar_bgcolor": {"label": "<PERSON><PERSON><PERSON> de fond"}, "bar_txtcolor": {"label": "Couleur du texte"}}}, "text-columns-with-images": {"name": "Colonnes de texte avec images", "blocks": {"text": {"name": "Texte", "settings": {"title": {"label": "<PERSON>-tête"}, "text": {"label": "Texte"}, "image": {"label": "Image"}}}}, "presets": {"name": "Colonnes de texte avec images"}}, "slider": {"slider_horizontal": {"name": "Diaporama : horizontal"}, "slider_vertical": {"name": "Diaporama : vertical"}, "settings": {"desktop_height": {"label": "<PERSON>ur de la barre de défilement du bureau", "options__1": {"label": "Petite"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Large"}, "options__4": {"label": "Pleine page"}}, "mobile_height": {"label": "<PERSON><PERSON> de la barre de défilement sur mobile"}, "text_style": {"header": "Style du texte"}, "mobile_design": {"header": "Design mobile", "label": "Transformer la barre de défilement verticale en barre de défilement horizontale sur mobile"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}, "heading": {"label": "<PERSON>-tête"}, "subheading": {"label": "Sous-titre"}, "caption": {"label": "Légende"}, "button_label": {"label": "Étiquette de bouton"}, "link": {"label": "<PERSON><PERSON>", "info": "Le lien sera sur le texte, à moins qu'il n'y ait une étiquette pour le bouton."}}}}}, "video-popup": {"name": "Vidéo : pop-up", "settings": {"video": {"label": "URL de la vidéo"}, "image": {"label": "Image de fond"}}}, "video-background": {"name": "Vidéo : arrière-plan", "settings": {"video": {"label": "URL de la vidéo", "info": "<PERSON>emin d'accès à un fichier .mp4"}, "image": {"label": "Image de repli", "info": "Une image de repli sera utilisée sur les appareils mobiles où la lecture automatique peut être désactivée."}, "size_alignment": {"content": "Taille et alignement"}, "video_height": {"label": "<PERSON>ur de la vidéo", "options__1": {"label": "<PERSON><PERSON> (16:9)", "group": "Non retaillée"}, "options__2": {"label": "Large", "group": "Retaillée"}, "options__3": {"label": "Pleine page"}}}}, "main-password-header": {"name": "En-tête du mot de passe"}, "main-password-content": {"name": "Contenu du mot de passe"}, "main-password-footer": {"name": "Pied de page du mot de passe", "settings": {"show_social": {"label": "Afficher les icônes sociales"}}}, "main-article": {"name": "Article de blog", "blocks": {"featured_image": {"name": "Image vedette", "settings": {"image_height": {"label": "<PERSON>ur de l'image vedette", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Large"}}}}, "title": {"name": "Titre", "settings": {"blog_show_date": {"label": "Affiche<PERSON> la date"}, "blog_show_author": {"label": "Afficher l'auteur"}, "blog_show_comments": {"label": "Afficher le nombre de commentaires"}}}, "content": {"name": "Contenu"}, "social_sharing": {"name": "Boutons de partage social"}, "blog_navigation": {"name": "Afficher les liens vers les articles adjacents"}}}, "main-blog": {"settings": {"header": {"content": "Carte de l'article de blog"}, "enable_tags": {"label": "Activer le filtrage par tags"}, "post_limit": {"label": "Nombre d'articles par page"}}}, "blog-posts": {"name": "Articles de blog", "blocks": {"title": {"name": "Titre"}, "info": {"name": "Info", "settings": {"show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}}}, "summary": {"name": "Extrait"}, "link": {"name": "<PERSON><PERSON>"}}, "settings": {"title": {"label": "<PERSON>-tête"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Articles"}, "show_image": {"label": "Afficher l'image vedette"}, "show_view_all": {"label": "Afficher le lien vers la page du blog"}, "layout": {"label": "Mise en page"}, "option_1": {"label": "Une colonne", "group": "Grille"}, "option_2": {"label": "Deux colonnes"}, "option_3": {"label": "Flexible (2 à 5 colonnes)", "group": "Barre de d<PERSON>"}}, "presets": {"name": "Articles de blog"}}, "custom-colors": {"heading": {"label": "<PERSON>-tête"}, "text": {"label": "Couleur de texte personnalisée"}, "overlay": {"label": "<PERSON><PERSON><PERSON> de la couche"}, "background": {"label": "Couleur de fond personnalisée"}}, "custom-gutter": {"heading": {"content": "Intercalaire colonnes"}, "gutter_enabled": {"label": "Activer l'espacement du contenu intérieur"}}, "newsletter": {"name": "Inscription par mail", "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}}}, "paragraph": {"name": "Sous-titre", "settings": {"paragraph": {"label": "Description"}}}, "email_form": {"name": "Formulaire par mail"}}, "presets": {"name": "Inscription par mail"}}, "product-recommendations": {"name": "Recommandations de produits", "settings": {"heading": {"label": "<PERSON>-tête"}, "header__1": {"content": "Recommandations de produits", "info": "Les recommandations dynamiques utilisent les informations sur les commandes et les produits pour évoluer et s'améliorer au fil du temps. [En savoir plus](https://help.shopify.com/en/themes/development/recommended-products)"}}}, "custom-liquid": {"name": "Liquid personnalisé", "settings": {"custom_liquid": {"label": "Liquid personnalisé"}}, "presets": {"name": "Liquid personnalisé"}}, "collection-list": {"name": "Liste des collections", "presets": {"name": "Liste des collections"}}, "faq": {"name": "FAQ", "settings": {"title": {"label": "<PERSON>-tête"}, "open_first": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> le premier basculeur par défaut"}}, "blocks": {"text": {"name": "FAQ", "settings": {"title": {"label": "Titre"}, "text": {"label": "Texte"}, "image": {"label": "Image"}}}}, "presets": {"name": "FAQ"}}, "popup": {"name": "Fenêtre pop-up", "settings": {"title": {"label": "<PERSON>-tête"}, "content": {"label": "Contenu"}, "show_newsletter": {"label": "Afficher le formulaire d'inscription par mail"}, "functionality": {"content": "Fonctionnalité"}, "enable": {"label": "Activer la pop-up"}, "show_after": {"label": "Afficher la pop-up après", "info": "s"}, "frequency": {"label": "Fréquence des pop-up", "options__1": {"label": "Afficher chaque jour"}, "options__2": {"label": "Afficher chaque semaine"}, "options__3": {"label": "Afficher chaque mois"}}, "image": {"label": "Image", "info": "1240 x 400px .jpg recommandé. Il apparaît uniquement sur le bureau"}}}, "main-search": {"name": "Résultats de la recherche", "settings": {"products_per_page": {"label": "Résultats par page"}}}, "main-collection-product-grid": {"name": "Grille de produits", "settings": {"products_per_page": {"label": "Produits par page"}, "enable_filtering": {"label": "<PERSON>r le filtrage", "info": "[Personnaliser les filtres](/admin/menus)"}, "enable_sorting": {"label": "<PERSON><PERSON> le tri"}, "image_filter_layout": {"label": "Disposition du filtre d'image"}, "header__1": {"content": "Filtrage et tri"}}}, "main-collection-banner": {"name": "Bannière de collection", "settings": {"paragraph": {"content": "Pour modifier les descriptions ou les images des collections, [modifiez vos collections.](/admin/collections)"}, "show_collection_description": {"label": "Afficher la description de la collection"}, "show_collection_image": {"label": "Afficher l'image de la collection", "info": "Pour de meilleurs résultats, utilisez une image avec un rapport d'aspect de 16:9."}}}, "main-product": {"name": "Informations sur le produit", "blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style du texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titre"}, "price": {"name": "Prix"}, "tax_info": {"name": "Données fiscales"}, "sku_barcode": {"name": "UGS/code-barres"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"show_variant_labels": {"label": "Afficher les étiquettes des variantes"}, "hide_out_of_stock_variants": {"label": "Masquer les variantes en rupture de stock"}, "low_inventory_notification": {"label": "Notification d'inventaire", "info": "Le suivi de l'inventaire doit être activé pour les variantes afin que cette fonctionnalité puisse fonctionner. [En savoir plus](https://help.shopify.com/en/manual/products/inventory/track_inventory)", "options__1": {"label": "Ne pas afficher les informations relatives à l'inventaire"}, "options__2": {"label": "Afficher un avis si le stock passe en dessous de cinq unités"}, "options__3": {"label": "Toujours afficher l'inventaire"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Afficher les boutons de paiement dynamiques", "info": "Grâce aux modes de paiement disponibles sur votre boutique, les clients voient leur option préférée, comme PayPal ou Apple Pay. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Afficher le formulaire du destinataire pour les cartes‑cadeaux en tant que produit", "info": "Lorsque cette option est activée, les cartes‑cadeaux en tant que produit peuvent être envoyées à un destinataire, accompagnées d’un message personnel."}, "show_quantity_selector": {"label": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de quantité"}}}, "pickup_availability": {"name": "Disponibilité de collecte"}, "description": {"name": "Description", "settings": {"product_description_truncated": {"label": "Tronquer la description", "info": "Tronquer", "options__1": {"label": "Ne pas tronquer"}, "options__2": {"label": "Afficher un petit extrait"}, "options__3": {"label": "Afficher un extrait moyen"}, "options__4": {"label": "Afficher un grand extrait"}}}}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans une publication sur vos réseaux sociaux, l'image vedette de la page s'affichera comme image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Le titre et la description de la boutique sont inclus dans l'image d'aperçu. [En savoir plus](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "collapsible_tab": {"name": "Onglet pliable", "settings": {"heading": {"info": "<PERSON><PERSON><PERSON><PERSON> un en-tête qui explique le contenu.", "label": "<PERSON>-tête"}, "content": {"label": "Contenu de l'onglet"}, "page": {"label": "Contenu de l'onglet de la page"}, "image": {"label": "Image de l'onglet"}}}}, "settings": {"header": {"content": "Supports multimédias", "info": "En savoir plus sur les [types de supports multipédias](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Activer l'affichage d'informations sur les produits populaires sur les grands écrans"}, "enable_video_looping": {"label": "Activer la lecture en boucle de la vidéo"}, "enable_zoom": {"label": "Activer le zoom sur l'image"}, "gallery_gutter": {"label": "Ajouter un espace entre les supports multimédias"}, "gallery_slider_style": {"label": "Mettre à l'échelle les images de curseur pour qu'elles s'adaptent à la fenêtre"}, "gallery_style": {"label": "Style de la galerie", "info": "La valeur par défaut est la barre de défilement pour les appareils mobiles", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Barre de d<PERSON>"}}, "gallery_pagination": {"label": "Pagination de la galerie", "options__1": {"label": "Points"}, "options__2": {"label": "Vignettes"}}}}, "words_210": {"text_columns": {"name": "Text columns", "block": {"name": "Text"}}}, "images": {"label_1": "Image 1", "label_2": "Image 2", "label_3": "Image 3"}}, "color-schemes": {"name_plural": "Color schemes", "name_singular": "Color scheme", "default": "Default color scheme", "default_info": "Used to set colors for account pages, 404 pages, and other pages or sections without color scheme settings.", "drawers": "Modals/drawers", "other": "Other colors"}, "local-250": {"labels": {"show_filters_as": "Afficher les filtres sous forme de", "expand_filters_by_default": "Déployer les filtres par défaut", "stick_filters_sidebar_to_top": "Fixer la barre latérale des filtres en haut"}, "options": {"sidebar": "Barre la<PERSON>", "list": "Liste"}}, "local-230": {"background_gradient": "Dégradé de <PERSON>", "variant_default": {"label": "Sélectionner par défaut la première variante disponible", "info": "Si cette option n'est pas cochée, l'utilisateur devra sélectionner une variante disponible avant de pouvoir acheter."}, "slider_info": "Le lien sera appliqué sur le bouton, ou sur le titre (s'il n'y a pas de bouton), ou sur la diapositive entière (si le titre et le bouton sont tous les deux vides).", "buy_button_labels": {"label": "Étiquettes des boutons d'achat", "option_1": "Acheter maintenant", "option_2": "Choisir les options"}, "hide_on_mobile": "Masquer sur les appareils mobiles"}, "local-223": {"heading_text_color": "Couleur du texte d'en-tête", "slider_navigation_color": "Couleur des éléments de navigation"}, "late_edits": {"badge": {"custom_badge": {"text_color": "Couleur de texte"}, "sold_out": {"name": "Badge épuisé", "text_color": "Couleur de texte 'épuisé'", "sale_text": "<PERSON><PERSON><PERSON> de texte 'remise'"}}, "rich-text": {"image_position": {"no_image": {"group": "Pas d'image", "label": "Ne montrez pas d'image"}}}}}