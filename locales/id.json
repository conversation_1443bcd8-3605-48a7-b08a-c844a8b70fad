{"general": {"404": {"title": "Halaman tidak ditemukan", "subtitle": "<PERSON><PERSON> yang Anda cari tidak dapat ditemukan", "search_placeholder": "Cari produk...", "continue_browsing": "Lanjutkan menjelajah"}, "sharing": {"title": "Bagikan", "facebook": "Facebook", "twitter": "X (Twitter)", "pinterest": "Pinterest"}, "newsletter": {"email_label": "Masukkan email Anda", "submit_label": "<PERSON><PERSON>", "confirmation": "<PERSON><PERSON> kasih sudah berlang<PERSON>an!"}, "contact_form": {"title": "<PERSON><PERSON><PERSON><PERSON> kami", "name_label": "<PERSON><PERSON>", "email_label": "Email", "phone_label": "Nomor Telepon", "message_label": "<PERSON><PERSON>", "subject_label": "Subjek", "submit_label": "<PERSON><PERSON>", "success_message": "<PERSON><PERSON> kasih sudah menghubungi kami! Kami akan menghubungi Anda sesegera mungkin."}, "password_page": {"login_form_heading": "Masuk ke toko menggunakan kata sandi", "login_form_password_label": "<PERSON>a sandi", "login_form_password_placeholder": "<PERSON>a sandi <PERSON>", "login_form_submit": "<PERSON><PERSON><PERSON>", "signup_form_heading": "<PERSON>i tahu kapan kami buka", "signup_form_email_label": "Email", "signup_form_email_placeholder": "<PERSON><PERSON>", "signup_form_submit": "<PERSON><PERSON>", "signup_form_success": "<PERSON><PERSON> akan men<PERSON>a email tepat sebelum kami buka!", "admin_link_html": "<PERSON><PERSON><PERSON><PERSON> pemilik toko? <a href=\"/admin\"><PERSON><PERSON><PERSON> di sini</a>", "password_link": "<PERSON><PERSON>k menggunakan kata sandi", "powered_by_shopify_html": "Toko ini akan didukung oleh {{ shopify }}"}, "prefixes": {"by": "oleh", "or": "atau"}, "account_link": {"logged_in": "<PERSON><PERSON><PERSON> saya", "logged_out": "<PERSON><PERSON><PERSON>"}, "breadcrumb": {"homepage": "Be<PERSON><PERSON>", "collections": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON> hasil untuk \"{{ terms }}\"", "blog": "Blog", "account": "<PERSON><PERSON><PERSON>", "cart": "Keranjang"}, "date_format": {"month_day_year": "%b %d, %Y", "days": "<PERSON>", "hours": "Jam", "minutes": "Menit", "seconds": "<PERSON><PERSON>"}, "copyright": "Hak cipta", "meta": {"tags": "Ditandai \"{{ tags }}\"", "page": "<PERSON><PERSON> {{ page }}"}, "share_on": "Bagikan di", "accessibility_labels": {"menu": "<PERSON><PERSON>", "item_added": "Item ditambahkan ke keranjang Anda", "increase_quantity": "Tambah kuantitas", "decrease_quantity": "Kurangi kuantitas", "open_menu": "Buka menu", "open_filters": "Buka filter", "open_search": "<PERSON><PERSON>", "open_cart": "<PERSON><PERSON> keranjang", "close_sidebar": "Tutup bilah samping", "close_popup": "<PERSON><PERSON><PERSON> se<PERSON>", "skip_to_content": "Langsung ke konten", "next": "Berikutnya", "previous": "Sebelumnya", "close": "<PERSON><PERSON><PERSON>", "back": "Kembali", "play_video": "Putar video", "language_dropdown_label": "Bahasa", "country_dropdown_label": "Negara/kawasan", "quantity": "Kuantitas", "price": {"regular": "Harga normal", "sale": "<PERSON><PERSON> diskon", "unit": "<PERSON><PERSON> satuan"}, "share": {"facebook": "Bagikan di Facebook", "twitter": "Bagikan di X", "pinterest": "Bagikan di Pinterest"}, "rating": "nilai", "rating_info": "<PERSON><PERSON>: {{ rating_value }} dari {{ rating_max }}", "form_error": "<PERSON><PERSON><PERSON>", "go_to_top": "Pergi ke atas", "top": "Atas", "time_left": "<PERSON><PERSON><PERSON> ters<PERSON>"}, "cookies_popup": {"title": "<PERSON><PERSON>", "content": "Situs web ini menggunakan cookie untuk memastikan Anda mendapatkan pengalaman terbaik di perangkat Anda.", "button_label": "Terima semua cookie", "button_label_decline": "Tolak semua cookie"}, "onboarding": {"product_title": "<PERSON><PERSON><PERSON> produk", "collection_title": "<PERSON><PERSON><PERSON>", "author_name": "<PERSON><PERSON>", "article_title": "<PERSON><PERSON><PERSON> art<PERSON>l", "seven_comments": "7 komentar"}}, "collections": {"title": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Terapkan", "clear": "Hapus", "clear_all": "<PERSON><PERSON> semua", "empty": "Tidak ada produk yang di<PERSON>ukan", "from": "<PERSON><PERSON>", "filter_and_sort": "Filter dan urutkan", "filter_by_label": "Filter:", "filter_button": "Filter", "filters_selected": {"one": "{{ count }} dipilih", "other": "{{ count }} dipilih"}, "max_price_html": "Harga tertinggi adalah {{ price }}", "product_count": {"one": "{{ product_count }} dari {{ count }} produk", "other": "{{ product_count }} dari {{ count }} produk"}, "product_count_simple": {"one": "{{ count }} produk", "other": "{{ count }} produk"}, "reset": "<PERSON><PERSON>", "sort_button": "Urut<PERSON>", "sort_by_label": "Urutkan berdasarkan:", "to": "<PERSON>", "use_fewer_filters_html": "<PERSON><PERSON><PERSON> lebih sedikit filter atau <a class=\"{{ class }}\" href=\"{{ link }}\">hapus semua</a>", "view_all_products": "<PERSON><PERSON> semua produk", "no_collections": "<PERSON><PERSON>, saat ini tidak ada koleksi di toko ini.", "match_all_label": "Cocokkan semua", "view_all_collections": "<PERSON><PERSON> semua k<PERSON>i"}, "products": {"grid": {"on_sale_from_html": "Diobral dari {{ price }}", "from_text_html": "Dar<PERSON> {{ price }}", "sold_out_product": "<PERSON><PERSON><PERSON><PERSON>", "on_sale_product": "Diobral", "new_product": "<PERSON><PERSON>", "preorder_product": "Pre Order", "no_products_text": "Saat ini tidak ada produk dalam koleksi ini.", "save_amount_html": "Hemat {{ amount }}", "save_up_to_amount_html": "<PERSON><PERSON> hingga {{ amount }}", "save_percent": "Hingga {{ percent }}", "choose_variant_first": "<PERSON><PERSON>h opsi", "select_variant": "<PERSON><PERSON><PERSON><PERSON> {{ variant }}", "quick_buy": "<PERSON><PERSON>", "quick_view": "<PERSON><PERSON>"}, "page": {"sku": "SKU: ", "barcode": "ISBN: ", "more_description_label": "<PERSON><PERSON><PERSON><PERSON> lebih banyak", "less_description_label": "<PERSON><PERSON><PERSON><PERSON> lebih sedikit", "inventory": {"sold_out_variant": "<PERSON>bis terjual", "unavailable_variant": "Tidak tersedia", "one_product": "<PERSON>ya tersisa satu produk!", "few_products": "Ada {{ count }} produk yang tersisa", "many_products": "Ada {{ count }} produk yang tersisa", "enough_products": "Stok Tersedia", "no_products": "Tidak ada produk yang tersisa", "preorder": "Produk ini sudah habis, tetapi <PERSON>a masih bisa memesannya."}, "add_to_cart_button": "Tambahkan ke keranjang", "choose_options_button": "<PERSON><PERSON>h opsi", "preorder_button": "Pre-Order", "share_link": "Bagikan", "reviews_count": {"one": "{{ count }} ul<PERSON>n", "other": "{{ count }} ul<PERSON>n", "none": "Tidak ada ulasan"}, "loading_reviews": "memuat lebih banyak ulasan", "include_taxes": "Termasuk pajak.", "shipping_policy_html": "<a href=\"{{ link }}\">Pengiriman</a> dihitung saat checkout.", "view_in_space": "<PERSON><PERSON>", "view_in_space_label": "<PERSON><PERSON>, muat barang di jendela augmented reality", "sales_amount_html": "<PERSON><PERSON> men<PERSON> {{ amount }}", "variants": "<PERSON><PERSON>"}, "featured_product": {"mobile_title": "<PERSON><PERSON><PERSON>", "view_product_details": "Lihat detail produk"}}, "cart": {"title": "Keranjang Belanja", "total": "Total", "subtotal": "Subtotal", "discount": "Diskon", "discounts": "Diskon", "checkout": "Checkout", "update": "<PERSON><PERSON><PERSON> k<PERSON>", "note": "<PERSON><PERSON><PERSON><PERSON> pesanan", "policies": {"taxes_and_shipping_policy_at_checkout_html": "<PERSON><PERSON> dan <a href=\"{{ link }}\">pengiriman</a> dihitung saat checkout", "taxes_included_but_shipping_at_checkout": "Sudah termasuk pajak dan biaya pengiriman dihitung saat checkout", "taxes_included_and_shipping_policy_html": "Termasuk pajak. <a href=\"{{ link }}\">Pengiriman</a> dihitung saat checkout.", "taxes_and_shipping_at_checkout": "<PERSON><PERSON> dan pengiriman dihitung saat checkout"}, "empty": "Keranjang Anda saat ini kosong.", "continue_browsing": "Lanjutkan menjelajah", "remove_item": "Hapus", "add_error": "<PERSON><PERSON><PERSON> {{ title }} ada di keranjang <PERSON>.", "general_error": "<PERSON> kesalahan. <PERSON><PERSON> se<PERSON>kan halaman dan coba lagi.", "view_cart": "<PERSON><PERSON>", "items_count": {"one": "{{ count }} produk di keranjang <PERSON>a", "other": "{{ count }} produk di keranjang <PERSON>a"}, "added_items_count": {"one": "{{ count }} produk ditambahkan ke keranjang Anda", "other": "{{ count }} produk ditambahkan ke keranjang Anda"}, "free_shipping_remaining_html": "Anda membutuhkan {{ remaining_amount }} lagi untuk mendapatkan pengiriman gratis.", "free_shipping_eligible": "Anda berhak mendapatkan pengiriman gratis!", "shipping_calculator": {"title": "<PERSON><PERSON><PERSON><PERSON>", "form_button_label": "<PERSON><PERSON><PERSON><PERSON>", "results_heading_one": "Ada satu ongkos kirim untuk alamat <PERSON>a", "results_heading_multiple": "Ongkos kirim untuk alamat <PERSON>"}, "table": {"product": "Produk", "quantity": "Kuantitas", "total": "Total"}}, "blog": {"grid": {"comments_count": {"one": "{{ count }} komentar", "other": "{{ count }} komentar"}, "tags_label": "Tag", "read_more_label": "Teruskan membaca", "no_articles_text": "Saat ini tidak ada artikel di blog ini.", "tags_dropdown": {"all": "<PERSON><PERSON><PERSON>"}}, "article": {"comments_list_title": "Komentar", "comment_under_moderation": "Komentar Anda berhasil diposting. Ka<PERSON> akan men<PERSON> sebentar lagi, karena blog kami dimoderasi.", "comment_posted": "<PERSON><PERSON><PERSON> Anda berhasil diposting! <PERSON><PERSON> kasih!", "no_comments_message": "Tidak ada komentar untuk artikel ini. J<PERSON>lah orang pertama yang mening<PERSON>kan pesan!", "comments_form_title": "<PERSON><PERSON>", "comments_form_name_label": "<PERSON><PERSON>", "comments_form_email_label": "Email", "comments_form_message_label": "<PERSON><PERSON>", "comments_form_submit_label": "<PERSON><PERSON>", "comments_notice": "Harap diperhatikan: komentar harus disetujui sebelum diterbitkan", "previous_article_link": "Artikel sebelum<PERSON>", "next_article_link": "<PERSON><PERSON><PERSON> be<PERSON>"}, "view_all_articles": "Kunjungi blog"}, "search": {"form": {"placeholder": "Cari...", "responsive_placeholder": "Ke<PERSON>k kata kunci dan tekan enter...", "submit": "<PERSON><PERSON>", "collection_results_title": "<PERSON><PERSON><PERSON><PERSON>", "page_results_title": "<PERSON><PERSON>", "article_results_title": "Postingan Blog", "search_for_html": "<PERSON><PERSON> \"{{ terms }}\"", "product_results_title": "Produk"}, "page": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON>n bilah pencarian di bawah ini untuk menemukan produk:", "no_results": "Tidak ada hasil yang dapat ditemukan untuk \"{{ terms }}\"", "results": "{{ count }} hasil ditemukan untuk \"{{ terms }}\"", "products_found": "Produk ({{ count }})", "pages_and_articles_found": "<PERSON><PERSON> dan artikel untuk \"{{ terms }}\"", "results_count": {"zero": "Tidak ada hasil", "one": "Menampilkan {{ count }} hasil", "other": "Menampilkan {{ offset }} - {{ page_size }} dari {{ count }} hasil"}, "form_placeholder": "<PERSON><PERSON><PERSON>n kata kunci di sini", "search_button_label": "<PERSON><PERSON>", "search_again_button_label": "Cari lagi", "continue_browsing": "Lanjutkan menjelajah"}}, "customers": {"account_page": {"title": "<PERSON><PERSON><PERSON> saya", "subtitle": "<PERSON>i<PERSON><PERSON> Pesanan", "orders_table": {"order": "<PERSON><PERSON><PERSON>", "date": "Tanggal", "payment_status": "Status Pembayaran", "fulfillment_status": "Status Pemenuhan", "total": "Total"}, "no_orders_message": "Anda belum membuat pesanan.", "account_details_title": "Detail Akun", "account_details_subtitle": "<PERSON><PERSON><PERSON>", "view_addresses_link": "<PERSON><PERSON>"}, "account_activation_page": {"title": "Aktifkan Akun", "subtitle": "Buat kata sandi untuk mengaktifkan akun Anda.", "form_password_label": "<PERSON>a sandi", "form_password_confirm_label": "<PERSON>n<PERSON><PERSON><PERSON>", "form_activate_button": "Aktifkan Akun", "form_decline_button": "<PERSON><PERSON>"}, "login_page": {"title": "<PERSON><PERSON>", "form_email_label": "Email", "form_password_label": "<PERSON>a sandi", "form_login_button": "<PERSON><PERSON><PERSON>", "form_logout_button": "<PERSON><PERSON><PERSON>", "forgot_password_check": "Lupa kata sandi Anda?", "new_customer_button": "Pelanggan Baru? Daftar!", "guest_check": "Lanjutkan sebagai tamu", "guest_button": "<PERSON><PERSON> mulai", "password_reset": {"title": "Atur ulang kata sandi Anda", "subtitle": "<PERSON><PERSON> akan men<PERSON>kan email untuk mengatur ulang kata sandi Anda.", "email_label": "Email", "submit_button": "<PERSON><PERSON>", "cancel_button": "<PERSON><PERSON>", "success_message": "<PERSON><PERSON> telah <PERSON> email dengan tautan untuk memperbarui kata sandi <PERSON>a."}}, "password_reset_page": {"title": "Atur ulang kata sandi Anda", "subtitle": "<PERSON><PERSON>kkan kata sandi baru untuk {{ email }}", "password_label": "<PERSON>a sandi", "password_confirm_label": "<PERSON>n<PERSON><PERSON><PERSON>", "submit": "Atur Ulang Kata Sandi"}, "register_page": {"title": "<PERSON><PERSON><PERSON> akun", "login_text": "Sudah punya akun?", "form": {"first_name_label": "<PERSON><PERSON>", "last_name_label": "<PERSON><PERSON>", "email_label": "Email", "password_label": "<PERSON>a sandi", "submit_button": "Buat"}}, "addresses_page": {"title": "<PERSON><PERSON><PERSON> saya", "subtitle": "<PERSON><PERSON>", "add_address_link": "Tambah<PERSON>", "edit_address_link": "Edit", "delete_address_link": "Hapus", "add_address_title": "Tambah<PERSON>", "edit_address_title": "<PERSON>", "default_address": "(<PERSON><PERSON><PERSON>)", "form": {"first_name_label": "<PERSON><PERSON>", "last_name_label": "<PERSON><PERSON>", "company_label": "<PERSON><PERSON><PERSON><PERSON>", "address_1_label": "Alamat Baris 1", "address_2_label": "Alamat Baris 2", "city_label": "Kota", "country_label": "Negara", "province_label": "<PERSON><PERSON><PERSON>", "zip_label": "Kode Pos", "phone_label": "Telepon", "add_button": "Tambah<PERSON>", "update_button": "<PERSON><PERSON><PERSON>", "set_as_default_check": "Tetapkan sebagai Al<PERSON>?", "delete_check": "Anda yakin ingin menghapus alamat ini?"}, "return": "<PERSON><PERSON><PERSON> ke halaman akun"}, "orders_page": {"title": "<PERSON><PERSON><PERSON> saya", "subtitle": "<PERSON><PERSON><PERSON>", "placed_order": "Dibuat pada", "cancelled": "<PERSON><PERSON><PERSON> pada {{ date }}", "cancelled_reason": "Alasan: {{ reason }}", "orders_table": {"product": "Produk", "sku": "SKU", "price": "<PERSON><PERSON>", "qty": "Kuantitas", "total": "Total", "fulfilled_at": "<PERSON><PERSON><PERSON>i pada", "subtotal": "Subtotal", "discount": "Diskon", "shipping": "Pen<PERSON><PERSON>", "tax": "<PERSON><PERSON>"}, "billing_title": "Informasi <PERSON>", "billing_status": "Status Pembayaran", "shipping_title": "Informasi Pengiriman", "shipping_status": "Status Pemenuhan:"}}, "gift_card": {"title": "Ini kartu hadiah <PERSON><PERSON>!", "disabled": "Ini bukan kartu hadiah yang valid", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada {{ expiry }}", "active": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada {{ expiry }}", "balance_left": "tersisa", "redeem": "<PERSON>akan kode ini saat checkout untuk menukarkan kartu hadiah <PERSON>a", "shop_link": "<PERSON><PERSON>", "print": "Cetak", "recipient": {"checkbox": "Saya ingin mengirim ini sebagai hadiah", "email_label": "<PERSON><PERSON>", "name_label": "<PERSON><PERSON> (opsional)", "message_label": "<PERSON><PERSON> (opsional)", "max_characters": "{{ max_chars }} karakter maks"}}, "store_availability": {"view_store_info": "<PERSON><PERSON> informasi toko", "check_other_stores": "<PERSON><PERSON><PERSON> ketersed<PERSON> di toko lain", "pick_up_available": "Tersedia pengambilan", "pick_up_currently_unavailable": "Pengambilan tidak tersedia saat ini", "pick_up_available_at_html": "Pengambilan tersedia di <strong>{{ location_name }}</strong>", "pick_up_unavailable_at_html": "Pengambilan saat ini tidak tersedia di <strong>{{ location_name }}</strong>", "invalid_location_address": "Hubungi toko untuk mengonfirma<PERSON> alamat", "refresh": "Segarkan", "general": {"available_for_pickup": "Tersedia untuk pengambilan", "unavailable_for_pickup": "Tidak tersedia untuk pengambilan"}, "store_selector": {"picking_up": "Mau mengambil sendiri?", "my_store": "<PERSON><PERSON> saya", "select_store_label": "<PERSON><PERSON><PERSON> toko", "select_pickup_location_title": "<PERSON><PERSON><PERSON> lokasi pen<PERSON>", "single_pickup_location_title": "Lok<PERSON>", "set_store_label": "Tetapkan sebagai toko saya", "change_store_label": "Ubah toko pilihan", "google_maps_link_label": "Dapatkan petunjuk arah"}, "compact_widget": {"checking_availability": "Memeriksa ketersediaan lokal", "available_at_selected_store": "Tersedia untuk pengambilan di {{ store }}", "unavailable_at_selected_store": "Stok habis di {{ store }}", "choose_location": "<PERSON><PERSON>h toko untuk melihat ketersediaan lokal"}, "extended_widget": {"available_for_pickup": "Tersedia untuk pengambilan di", "unavailable_for_pickup": "Produk ini tidak tersedia untuk diambil di toko mana pun", "view_store_info": "Detail toko", "check_other_stores": "<PERSON><PERSON><PERSON> ketersed<PERSON> di toko lain"}}}