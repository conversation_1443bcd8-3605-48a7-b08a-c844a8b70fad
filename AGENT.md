# Agent Guidelines for dnatural-turbo

## Build/Test Commands
- `pnpm build` - Build all apps
- `pnpm dev` - Start all dev servers
- `pnpm lint` - Lint all packages
- `pnpm check-types` - TypeScript type checking
- `pnpm format` - Format with Prettier
- `pnpm build --filter=<app-name>` - Build specific app
- `pnpm dev --filter=<app-name>` - Dev specific app
- **Testing**: Apps have test deps (Vitest, Playwright) but no test files exist yet

## Architecture
- **Monorepo**: Turborepo with pnpm workspaces
- **Apps**: `convodash` (Nuxt.js), `convokernel` (Hono), `vendor-notifier` (Remix/Shopify)
- **Packages**: `@repo/eslint-config`, `@repo/typescript-config`
- **Node**: >=18 (convokernel requires ^24)

## Code Style
- **Language**: TypeScript with ES modules
- **Formatting**: Prettier (120 chars, 2 spaces, no semicolons, single quotes, trailing commas)
- **Prefer**: Plain objects over classes, functional components with hooks
- **Types**: Avoid `any`, use `unknown` for unknowns
- **Imports**: Use ES module syntax
- **Comments**: Only high-value comments explaining "why", not "what"

## Framework Guidelines
- **New frontend**: Use Nuxt.js
- **New backend**: Use Hono
- **React/Remix**: Functional components, hooks, pure renders, no direct state mutation
- **Testing**: Co-locate test files with source, follow existing patterns
- **Git**: Main branch is "main"

## Service-Specific Guidelines
- **ConvoKernel**: See [apps/convokernel/AGENT.md](apps/convokernel/AGENT.md) for detailed architecture and development guidelines
