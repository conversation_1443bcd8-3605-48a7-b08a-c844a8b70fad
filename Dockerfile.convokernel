FROM node:24-alpine AS base

ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0
RUN corepack enable
RUN corepack prepare pnpm@9.0.0 --activate

FROM base AS full_deps_builder
WORKDIR /app

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

COPY apps/convodash/package.json ./apps/convodash/
COPY apps/convokernel/package.json ./apps/convokernel/
COPY apps/vendor-notifier/package.json ./apps/vendor-notifier/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/

RUN pnpm install --ignore-scripts --frozen-lockfile --child-concurrency=4

COPY . .

ENV NODE_ENV=production

RUN pnpm turbo run build --filter=convokernel

# --- Deploy Stage: Use pnpm deploy to create a lean package --- #
FROM base AS deploy_package
WORKDIR /app

COPY --from=full_deps_builder /app .

RUN pnpm --filter=convokernel deploy --legacy /app/deployed_app --prod

# --- Runner Stage: Final image (distroless, nonroot) --- #
FROM gcr.io/distroless/nodejs24-debian12 AS runner
WORKDIR /app

ENV NODE_ENV=production

COPY --from=deploy_package /app/deployed_app .

EXPOSE 3000

CMD ["node", "dist/index.js"]
