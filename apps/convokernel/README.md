# ConvoKernel

A secure, modular Hono-based API service with JWT authentication, built with TypeScript and designed for scalability.

## Features

- **Modern Web Framework**: Built with [Hono](https://hono.dev/) - Fast, lightweight, and edge-ready
- **JWT Authentication**: Secure cookie-based authentication using the `jose` library
- **Modular Architecture**: Clean separation of concerns with dedicated modules
- **TypeScript**: Full type safety throughout the application
- **Development Tools**: Hot-reload development with Rollup watch mode
- **Production Ready**: CORS support, security headers, and environment-based configuration
- **Development Mode**: Bypass authentication for testing with custom headers

## Quick Start

```bash
# Install dependencies (from monorepo root)
pnpm install

# Start development server
pnpm dev --filter=convokernel

# Build for production
pnpm build --filter=convokernel

# Start production server
pnpm start --filter=convokernel
```

## Architecture

The application follows a clean, modular architecture:

```
src/
├── auth/                   # Authentication module
│   ├── index.ts           # Module exports
│   ├── jwt.ts             # JWT utilities (sign, verify)
│   ├── middleware.ts      # Auth middleware
│   └── routes.ts          # Auth endpoints (/auth/*)
├── api/                   # API module
│   ├── index.ts           # Module exports
│   └── routes.ts          # Protected API endpoints (/api/*)
├── config/                # Configuration module
│   └── cors.ts            # CORS configuration
├── types.ts               # TypeScript type definitions
└── index.ts               # Main application entry point
```

## Authentication

ConvoKernel uses JWT-based authentication with secure HTTP-only cookies.

### Environment Variables

```bash
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ISSUER=convokernel-api
JWT_EXPIRES_IN=24h

# CORS Configuration (production only)
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# General
NODE_ENV=production
PORT=3000
```

### Authentication Flow

1. **Register**: `POST /auth/register` - Create new user account
2. **Login**: `POST /auth/login` - Authenticate and receive cookie
3. **Access Protected Routes**: Cookie automatically included in requests
4. **Refresh Token**: `POST /auth/refresh` - Extend session
5. **Logout**: `POST /auth/logout` - Clear authentication cookie

### Demo Credentials

For testing purposes:
- Email: `<EMAIL>`
- Password: `password123`

## API Endpoints

### Public Endpoints
- `GET /` - Service information and available endpoints
- `GET /health` - Health check and system status
- `POST /auth/login` - User authentication
- `POST /auth/register` - User registration

### Protected Endpoints (Require Authentication)
- `GET /api/user` - Get current user information
- `GET /api/profile` - Get user profile
- `PUT /api/profile` - Update user profile
- `POST /auth/refresh` - Refresh authentication token
- `POST /auth/logout` - End user session

## Development Mode

In development (`NODE_ENV !== 'production'`), you can bypass authentication:

```bash
# Access protected endpoints without authentication
curl -X GET http://localhost:3000/api/user \
  -H "X-Dev-Auth-User-Id: dev-user-123"
```

This is useful for testing and integration work.

## Security Features

- **JWT Tokens**: Secure token generation and verification using `jose`
- **HTTP-Only Cookies**: Prevent XSS attacks by making tokens inaccessible to JavaScript
- **CORS Protection**: Configurable CORS policy for production environments
- **Secure Headers**: Production-ready security configurations
- **Environment Separation**: Different behavior for development vs production

## Development Commands

```bash
# Development server with hot-reload
pnpm dev --filter=convokernel

# Build for production
pnpm build --filter=convokernel

# Start production server
pnpm start --filter=convokernel

# Type checking
pnpm check-types --filter=convokernel

# Linting
pnpm lint --filter=convokernel
```

## Testing Examples

### Authentication
```bash
# Register a new user
curl -X POST http://localhost:3000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>","password":"secure123"}' \
  --cookie-jar cookies.txt

# Login with demo credentials
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  --cookie-jar cookies.txt

# Access protected endpoint
curl -X GET http://localhost:3000/api/profile \
  --cookie cookies.txt
```

### Health Monitoring
```bash
curl http://localhost:3000/health
```

## Production Deployment

1. Set environment variables (especially `JWT_SECRET`)
2. Configure `ALLOWED_ORIGINS` for CORS
3. Set `NODE_ENV=production`
4. Build the application: `pnpm build`
5. Start with: `pnpm start`

## Module Structure

### Authentication Module (`src/auth/`)
- **JWT utilities**: Token generation, verification, and configuration
- **Middleware**: Request authentication and user context
- **Routes**: Login, register, refresh, and logout endpoints

### API Module (`src/api/`)
- **Protected routes**: User management and profile endpoints
- **Business logic**: Core application functionality

### Configuration Module (`src/config/`)
- **CORS setup**: Production-ready cross-origin configuration
- **Environment handling**: Development vs production behavior

This modular approach ensures maintainability, testability, and scalability as the application grows.