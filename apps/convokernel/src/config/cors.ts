import { cors } from 'hono/cors'
import { AppType } from '..'

/**
 * CORS configuration for production environments
 */
export function maybeConfigureCorsMiddleware(app: AppType) {
  if (shouldEnableCors()) {
    app.use(
      '*',
      cors({
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ['https://yourdomain.com'],
        allowHeaders: ['Content-Type', 'X-Requested-With', 'Cookie'],
        allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        credentials: true,
        exposeHeaders: ['Set-Cookie'],
      }),
    )
  }
}

/**
 * Check if CORS should be enabled (production only)
 */
function shouldEnableCors(): boolean {
  return process.env.NODE_ENV === 'production'
}

/**
 * Get allowed origins from environment
 */
export function getAllowedOrigins(): string[] {
  return process.env.ALLOWED_ORIGINS?.split(',') || ['https://yourdomain.com']
}
