import { Hono } from 'hono'
import { getAuthenticatedUserId, isDevAuth } from '../auth/middleware'
import { Variables, ProfileUpdateRequest } from '../types'

const api = new Hono<{ Variables: Variables }>()

/**
 * Get current user info
 * GET /api/user
 */
api.get('/user', (c) => {
  const userId = getAuthenticatedUserId(c)
  const authMethod = isDevAuth(c)

  return c.json({
    userId,
    authMethod: authMethod ? 'dev-header' : 'jwt',
    message: 'User data retrieved successfully',
    timestamp: new Date().toISOString(),
  })
})

/**
 * Get user profile
 * GET /api/profile
 */
api.get('/profile', (c) => {
  const userId = getAuthenticatedUserId(c)
  const authMethod = isDevAuth(c)

  return c.json({
    profile: {
      id: userId,
      name: `User ${userId}`,
      email: `${userId}@example.com`,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString(),
    },
    authMethod: authMethod ? 'dev-header' : 'jwt',
  })
})

/**
 * Update user profile
 * PUT /api/profile
 */
api.put('/profile', async (c) => {
  const userId = getAuthenticatedUserId(c)
  const body = (await c.req.json().catch(() => ({}))) as ProfileUpdateRequest

  const { name, email } = body

  // TODO: Implement actual profile update logic
  // This is a placeholder - in a real app, you'd:
  // 1. Validate the input data
  // 2. Update the database
  // 3. Return the updated profile

  return c.json({
    message: 'Profile updated successfully',
    profile: {
      id: userId,
      name: name || `User ${userId}`,
      email: email || `${userId}@example.com`,
      updatedAt: new Date().toISOString(),
    },
  })
})

export { api }
