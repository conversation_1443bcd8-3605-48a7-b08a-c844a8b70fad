import { Context } from 'hono'

export interface JWTPayload {
  sub: string // user ID
  iat: number
  exp: number
  iss: string
}

export interface User {
  id: string
  email: string
  name: string
  createdAt?: string
  updatedAt?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  name: string
}

export interface AuthResponse {
  message: string
  token: string
  user: User
}

export interface ProfileUpdateRequest {
  name?: string
  email?: string
}

export interface ApiResponse<T = any> {
  message?: string
  data?: T
  error?: string
  code?: string
  timestamp?: string
}

export interface AuthContextVariables {
  userId: string
  isDevAuth: boolean
}

export type Variables = Partial<AuthContextVariables>
export type ContextType = Context<{ Variables: Variables }>
