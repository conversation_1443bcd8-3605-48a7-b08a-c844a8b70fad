import { Next } from 'hono'
import { getCookie } from 'hono/cookie'
import { verifyJWT } from './jwt'
import { ContextType } from '../types'

const PUBLIC_PATHS = ['/', '/health', '/auth/login', '/auth/register']

/**
 * Authentication middleware for Hono
 * Handles JWT cookie authentication and dev mode bypass
 */
export async function authMiddleware(c: ContextType, next: Next): Promise<Response | void> {
  // Skip auth for public endpoints
  if (PUBLIC_PATHS.includes(c.req.path)) {
    return next()
  }

  // In development, allow bypassing cookie auth with header
  if (process.env.NODE_ENV !== 'production') {
    const devUserId = c.req.header('X-Dev-Auth-User-Id')
    if (devUserId) {
      c.set('userId', devUserId)
      c.set('isDevAuth', true)
      return next()
    }
  }

  // Use cookie authentication only
  const token = getCookie(c, 'SESSTOK')

  if (!token) {
    return c.json({ error: 'AUTHENTICATION_REQUIRED' }, 401)
  }

  // Verify JWT token
  try {
    const payload = await verifyJWT(token)
    if (!payload || !payload.sub) {
      return c.json({ error: 'AUTHENTICATION_TOKEN_INVALID' }, 401)
    }

    c.set('userId', payload.sub)
    c.set('isDevAuth', false)
    return next()
  } catch (error) {
    console.error('Authentication failed:', error)
    return c.json({ error: 'Authentication failed', code: 'AUTH_FAILED' }, 401)
  }
}

/**
 * Get authenticated user ID from context
 * @param c - Hono context
 * @returns string | undefined - User ID if authenticated
 */
export function getAuthenticatedUserId(c: ContextType): string | undefined {
  return c.get('userId')
}

/**
 * Check if current auth is from dev mode
 * @param c - Hono context
 * @returns boolean - True if using dev auth
 */
export function isDevAuth(c: ContextType): boolean {
  return c.get('isDevAuth') || false
}
