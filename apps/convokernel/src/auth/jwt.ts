import { SignJWT, jwtVerify } from 'jose'
import { JWTPayload } from '../types'

// JWT Configuration
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
)
const JWT_ISSUER = process.env.JWT_ISSUER || 'convokernel-api'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h'

// Helper function to convert duration string to seconds
function parseDuration(duration: string): number {
  const matches = duration.match(/^(\d+)([smhd])$/)
  if (!matches) return 24 * 60 * 60 // default 24 hours

  const [, amount, unit] = matches
  const multipliers = { s: 1, m: 60, h: 3600, d: 86400 }
  return parseInt(amount) * multipliers[unit as keyof typeof multipliers]
}

/**
 * Generate a JWT token for a user
 * @param userId - The user ID to include in the token
 * @returns Promise<string> - The signed JWT token
 */
export async function generateJWT(userId: string): Promise<string> {
  const expirationTime = Math.floor(Date.now() / 1000) + parseDuration(JWT_EXPIRES_IN)

  return await new SignJWT({ sub: userId })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setIssuer(JWT_ISSUER)
    .setExpirationTime(expirationTime)
    .sign(JWT_SECRET)
}

/**
 * Verify and decode a JWT token
 * @param token - The JWT token to verify
 * @returns Promise<JWTPayload | null> - The decoded payload or null if invalid
 */
export async function verifyJWT(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET, {
      issuer: JWT_ISSUER,
    })

    return payload as JWTPayload
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

/**
 * Get JWT configuration values
 */
export function getJWTConfig() {
  return {
    issuer: JWT_ISSUER,
    expiresIn: JWT_EXPIRES_IN,
    expiresInSeconds: parseDuration(JWT_EXPIRES_IN),
  }
}
