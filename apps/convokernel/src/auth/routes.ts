import { <PERSON>o } from 'hono'
import { setCookie } from 'hono/cookie'
import { generateJWT, getJWTConfig } from './jwt'
import { LoginRequest, RegisterRequest, Variables } from '../types'
import { getAuthenticatedUserId } from './middleware'

const auth = new Hono<{ Variables: Variables }>()

/**
 * User login endpoint
 * POST /auth/login
 */
auth.post('/login', async (c) => {
  const body = (await c.req.json().catch(() => ({}))) as LoginRequest
  const { email, password } = body

  if (!email || !password) {
    return c.json({ error: 'LOGIN_CREDENTIALS_REQUIRED' }, 400)
  }

  // TODO: Implement actual user authentication logic
  // This is a placeholder - in a real app, you'd:
  // 1. Hash the password and compare with stored hash
  // 2. Query your database for the user
  // 3. Validate credentials

  // Mock authentication logic
  if (email === '<EMAIL>' && password === 'password123') {
    const userId = 'user_123'
    const token = await generateJWT(userId)
    const config = getJWTConfig()

    // Set cookie for browser clients
    setCookie(c, 'SESSTOK', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: config.expiresInSeconds,
    })

    return c.body(null)
  }

  return c.json({ error: 'Invalid credentials' }, 401)
})

/**
 * User registration endpoint
 * POST /auth/register
 */
auth.post('/register', async (c) => {
  const body = (await c.req.json().catch(() => ({}))) as RegisterRequest
  const { email, password, name } = body

  if (!email || !password || !name) {
    return c.json({ error: 'Email, password, and name are required' }, 400)
  }

  // TODO: Implement actual user registration logic
  // This is a placeholder - in a real app, you'd:
  // 1. Validate email format and uniqueness
  // 2. Hash the password
  // 3. Save user to database
  // 4. Send welcome email, etc.

  // Mock registration logic
  const userId = `user_${Date.now()}`
  const token = await generateJWT(userId)
  const config = getJWTConfig()

  // Set cookie for browser clients
  setCookie(c, 'SESSTOK', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: config.expiresInSeconds,
  })

  return c.body(null)
})

/**
 * Token refresh endpoint
 * POST /auth/refresh
 */
auth.post('/refresh', async (c) => {
  const userId = getAuthenticatedUserId(c)

  if (!userId) {
    return c.json({ error: 'AUTHENTICATION_REQUIRED' }, 401)
  }

  try {
    const newToken = await generateJWT(userId)
    const config = getJWTConfig()

    // Update cookie
    setCookie(c, 'SESSTOK', newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: config.expiresInSeconds,
    })

    return c.body(null)
  } catch (error) {
    console.error('Token refresh failed:', error)
    return c.json({ error: 'TOKEN_REFRESH_FAILED' }, 500)
  }
})

/**
 * User logout endpoint
 * POST /auth/logout
 */
auth.post('/logout', (c) => {
  // Clear the auth cookie
  setCookie(c, 'SESSTOK', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 0, // Expire immediately
  })

  return c.json({ message: 'Logout successful' })
})

export { auth }
