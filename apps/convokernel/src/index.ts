import { serve } from '@hono/node-server'
import { Hono } from 'hono'
import { apiRoutes } from './api'
import { authMiddleware, authRoutes, getJWTConfig } from './auth'
import { maybeConfigureCorsMiddleware } from './config/cors'
import { Variables } from './types'

const app = new Hono<{ Variables: Variables }>()
export type AppType = typeof app

maybeConfigureCorsMiddleware(app)
// Authentication middleware
app.use('*', authMiddleware)

app.route('/auth', authRoutes)
app.route('/api', apiRoutes)

const port = process.env.PORT ? parseInt(process.env.PORT) : 3000
const jwtConfig = getJWTConfig()

console.log(`🚀 ConvoKernel server starting on port ${port}`)
console.log(`📝 Environment: ${process.env.NODE_ENV || 'development'}`)
console.log(`🔐 JWT Issuer: ${jwtConfig.issuer}`)
console.log(`⏰ JWT Expires In: ${jwtConfig.expiresIn}`)

if (process.env.NODE_ENV !== 'production') {
  console.log(`🔧 Dev mode: Use 'X-Dev-Auth-User-Id' header to bypass auth`)
}

serve({
  fetch: app.fetch,
  port,
})
