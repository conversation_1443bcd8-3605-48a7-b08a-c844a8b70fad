{"name": "convokernel", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": "^24"}, "deployment": {"containerize": true, "useDistroless": true, "startupCommand": "node dist/index.js"}, "scripts": {"dev": "rollup -c --watch", "build": "rollup -c", "start": "node dist/index.js", "check-types": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:files": "eslint"}, "dependencies": {"@hono/node-server": "^1.13.7", "hono": "^4.6.11", "jose": "^6.0.11"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-run": "^3.1.0", "@rollup/plugin-typescript": "^12.1.2", "@types/node": "^24", "eslint": "^9.18.0", "rollup": "^4.31.0", "typescript": "^5.8.3"}}