{
  "compilerOptions": {
    "target": "ES2024",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2024"],
    "skipLibCheck": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,
    "moduleDetection": "force",
    "noEmit": false,
    "outDir": "./dist",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
