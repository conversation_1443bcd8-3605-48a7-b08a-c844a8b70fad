CREATE TABLE "Session" (
	"id" text PRIMARY KEY NOT NULL,
	"shop" text NOT NULL,
	"state" text NOT NULL,
	"isOnline" boolean DEFAULT false NOT NULL,
	"scope" text,
	"expires" timestamp,
	"accessToken" text NOT NULL,
	"userId" bigint,
	"firstName" text,
	"lastName" text,
	"email" text,
	"accountOwner" boolean DEFAULT false NOT NULL,
	"locale" text,
	"collaborator" boolean DEFAULT false,
	"emailVerified" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "VendorEmail" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"shop" text NOT NULL,
	"vendor" text NOT NULL,
	"email" text NOT NULL,
	"ccEmail" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
