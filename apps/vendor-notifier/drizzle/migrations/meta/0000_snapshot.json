{"id": "f7858cfa-546f-40e2-8914-6c5161939528", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.Session": {"name": "Session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "shop": {"name": "shop", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "isOnline": {"name": "isOnline", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "bigint", "primaryKey": false, "notNull": false}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "accountOwner": {"name": "accountOwner", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "locale": {"name": "locale", "type": "text", "primaryKey": false, "notNull": false}, "collaborator": {"name": "collaborator", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.VendorEmail": {"name": "VendorEmail", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "shop": {"name": "shop", "type": "text", "primaryKey": false, "notNull": true}, "vendor": {"name": "vendor", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "ccEmail": {"name": "ccEmail", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}