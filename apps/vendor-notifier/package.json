{"name": "vendor-notifier", "private": true, "deployment": {"containerize": true, "startupCommand": "node_modules/@remix-run/serve/dist/cli.js build/server/index.js"}, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "NODE_OPTIONS='--import ./instrumentation.server.mjs' remix-serve ./build/server/index.js", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate", "drizzle:push": "drizzle-kit push", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint", "lint:files": "eslint --cache --cache-location ./node_modules/.cache/eslint", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:coverage:ui": "vitest --coverage --ui", "test:debug": "VITEST_DEBUG=1 vitest", "test:ci": "vitest run --reporter=junit --reporter=github-actions --coverage", "shopify": "shopify", "graphql-codegen": "graphql-codegen", "vite": "vite"}, "type": "module", "engines": {"node": "^24"}, "dependencies": {"@remix-run/dev": "^2.16.1", "@remix-run/fs-routes": "^2.16.1", "@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@sentry/remix": "^9", "@sentry/vite-plugin": "^3.4.0", "@shopify/app-bridge-react": "^4.1.6", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-drizzle": "^2.1.2", "csv-stringify": "^6.5.2", "form-data": "^4.0.2", "isbot": "^5.1.0", "mailgun.js": "^12.0.1", "postgres": "^3.4.7", "react": "^18.2.0", "react-dom": "^18.2.0", "vite-tsconfig-paths": "^5.0.1"}, "devDependencies": {"@electric-sql/pglite": "^0.3.5", "@remix-run/eslint-config": "^2.16.1", "@remix-run/route-config": "^2.16.1", "@shopify/api-codegen-preset": "^1.1.1", "@types/eslint": "^9.6.1", "@types/form-data": "^2.5.2", "@types/node": "^22.2.0", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.0", "@vitest/coverage-v8": "^2.0.0", "@vitest/ui": "^2.0.0", "better-sqlite3": "^11.0.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "prettier": "^3.2.4", "typescript": "^5.2.2", "vite": "^6.2.2", "vitest": "^2.0.0"}, "trustedDependencies": ["@shopify/plugin-cloudflare"], "files": ["build/", "public/build/", "drizzle/", "instrumentation.server.mjs"], "author": "mruhf"}