# Vendor Notifier App

This Shopify app allows store owners to map product vendors to email addresses. When orders are created, paid, edited or cancelled, vendors receive emails with CSV attachments containing only their relevant line items.

## Features

- Admin UI for managing vendor-to-email mappings
- Webhook listeners for order events (create, paid, edited, cancelled)
- Automatic CSV generation for vendor-specific line items
- Mailgun integration for reliable email delivery

## Setup Instructions

### Environment Variables

Create a `.env` file with the following variables:

```
# Mailgun Configuration
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain
EMAIL_FROM=<EMAIL>
```

### Webhook Registration

The app automatically registers the following webhooks with Shopify:

- orders/create
- orders/paid
- orders/edited
- orders/cancelled

### Database Migrations

The app uses Drizzle ORM with PostgreSQL. Run database migrations with:

```
cd apps/vendor-notifier
pnpm drizzle:migrate
```

## Usage

1. Open the Vendor Emails page in the app
2. Add vendor-to-email mappings for each vendor you want to notify
3. When orders are placed/updated with products from these vendors, they'll automatically receive email notifications with CSV attachments

## Important Notes

- Vendor names must match exactly as they appear in your Shopify products
- Email notifications are sent individually to each vendor containing only their line items
- The CSV file includes order details, customer information, and line item details
