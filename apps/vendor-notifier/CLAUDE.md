# vendor-notifier Application

## Overview

This is a Shopify embedded app built with Remix, designed to automatically notify product vendors when orders are placed, paid, edited, or cancelled. The app sends vendor-specific CSV files containing only their relevant line items via email.

## Technology Stack

- **Framework**: Remix with Vite
- **Shopify Integration**: @shopify/shopify-app-remix v3.7.0
- **UI Library**: @shopify/polaris v12.0.0
- **Database**: PostgreSQL with Drizzle ORM
- **Email Service**: Mailgun
- **Error Tracking**: Sentry
- **Runtime**: Node.js >=18.20

## Key Features

1. **Vendor Email Management**: Admin UI for mapping vendors to email addresses
2. **Webhook Processing**: Handles Shopify order events (create, paid, edited, cancelled)
3. **CSV Generation**: Creates vendor-specific CSV files with order details
4. **Email Notifications**: Sends emails with CSV attachments via Mailgun
5. **CC Email Support**: Optional CC email addresses for vendor notifications

## Project Structure

```
vendor-notifier/
├── app/
│   ├── routes/              # Remix routes
│   │   ├── _index/         # Home page
│   │   ├── app.*.tsx       # App-specific routes
│   │   ├── auth.*.tsx      # Authentication routes
│   │   └── webhooks.*.tsx  # Webhook handlers
│   ├── models/             # Data models
│   │   └── vendor-email.server.ts
│   ├── utils/              # Utilities
│   │   └── email.server.ts # Email sending logic
│   ├── db/                 # Database configuration
│   │   ├── index.ts        # Database client
│   │   └── schema.ts       # Database schema
│   ├── shopify.server.ts   # Shopify configuration
│   ├── root.tsx            # Root component
│   └── routes.ts           # Route configuration
├── drizzle/
│   └── migrations/         # Database migrations
├── extensions/             # Shopify extensions
├── public/                 # Static assets
├── shopify.app.toml       # Shopify app configuration
├── shopify.web.toml       # Shopify web configuration
├── vite.config.ts         # Vite configuration
└── instrumentation.server.mjs # Sentry initialization
```

## Database Schema

### Session Table
- Stores Shopify session data for authenticated shops
- Includes user information and access tokens

### VendorEmail Table
- Maps vendor names to email addresses
- Supports optional CC email addresses
- Unique constraint on shop + vendor combination

## Shopify Configuration

### App Settings (shopify.app.toml)
- **Client ID**: cadd66e6aa35f688542a940dd8a3818b
- **App URL**: https://vendor-notifier.shopify.app.dnatural.de
- **Embedded**: Yes
- **API Version**: 2025-04

### Access Scopes
- `read_orders`: Access order information
- `read_products`: Access product details

### Webhooks
Automatically registered webhooks:
- `orders/create`
- `orders/edited`
- `orders/cancelled`

## Development Scripts

- `dev`: Start Shopify app development server
- `build`: Build for production (Remix Vite build)
- `start`: Start production server with instrumentation
- `docker-start`: Setup database and start server (for Docker)
- `drizzle:generate`: Generate Drizzle migrations
- `drizzle:migrate`: Run Drizzle migrations
- `drizzle:push`: Push schema changes to database
- `lint`: Run ESLint
- `graphql-codegen`: Generate GraphQL types

## Environment Variables

Required environment variables:
```
DATABASE_URL=postgresql://...
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain
EMAIL_FROM=<EMAIL>
SENTRY_AUTH_TOKEN=your_sentry_token (for build-time source maps)
```

## Key Implementation Details

### Webhook Processing
- Receives order webhooks at `/webhooks/orders`
- Extracts vendor information from line items
- Generates CSV files with order details
- Sends emails to mapped vendor addresses

### Email System
- Uses Mailgun for reliable email delivery
- Supports attachments (CSV files)
- Includes optional CC addresses
- CSV format includes order details, customer info, and line items

### Development Considerations
- Uses Vite for fast development builds
- HMR configured for local and remote development
- Sentry integration for error tracking
- Drizzle ORM for type-safe database access

## Docker Support
- Dockerfile.vendor-notifier available in root
- Includes database setup and migration in docker-start script
- Production-ready container configuration

## Important Notes
- Vendor names must match exactly as they appear in Shopify products
- The app handles session storage via Drizzle
- All vendor emails are shop-specific (multi-tenant)
- CSV generation happens synchronously during webhook processing