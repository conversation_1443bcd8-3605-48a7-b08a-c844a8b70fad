# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "cadd66e6aa35f688542a940dd8a3818b"
name = "Vendor Notifier"
handle = "vendor-notifier"
application_url = "https://vendor-notifier.shopify.app.dnatural.de"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "orders/create", "orders/edited", "orders/cancelled" ]
  uri = "/webhooks/orders"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders,read_products"

[auth]
redirect_urls = [ "https://vendor-notifier.shopify.app.dnatural.de/auth/callback" ]

[pos]
embedded = false
