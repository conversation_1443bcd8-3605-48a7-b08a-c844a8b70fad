import { type ActionFunctionArgs } from '@remix-run/node'
import { authenticate } from '../shopify.server'
import { getVendorEmailByVendor } from '../models/vendor-email.server'
import { generateCsvForVendor, sendEmailWithCsv } from '../utils/email.server'

function formatOrderData(payload: any) {
  return {
    id: payload.id,
    name: payload.name,
    created_at: payload.created_at,
    customer: {
      first_name: payload.shipping_address?.first_name || payload.customer?.first_name || '',
      last_name: payload.shipping_address?.last_name || payload.customer?.last_name || '',
      email: payload.customer?.email || payload.email || '',
    },
    shipping_address: payload.shipping_address,
    line_items: payload.line_items || [],
  }
}

async function processVendors(order: any, shop: string, topic: string) {
  const vendors = new Set<string>()
  for (const item of order.line_items) {
    const vendor = item.vendor
    if (!vendor) continue
    vendors.add(vendor)
  }

  if (vendors.size === 0) {
    console.log('No vendors found in the order, skipping...')
    throw new Response(null, { status: 200 })
  }

  console.log('Vendors: ', vendors)

  const results = []

  for (const vendor of vendors) {
    const vendorEmail = await getVendorEmailByVendor(shop, vendor)
    if (!vendorEmail) continue
    const csvContent = generateCsvForVendor(order, vendor)
    if (!csvContent) continue
    const { subject, message } = generateEmailContent(topic, order, vendor)
    const result = await sendEmailWithCsv(
      vendorEmail.email,
      vendorEmail.ccEmail,
      subject,
      message,
      csvContent,
      order.id,
    )

    results.push({
      vendor,
      email: vendorEmail.email,
      success: result.status,
    })
  }

  return results
}

function generateEmailContent(topic: string, order: any, vendorName: string) {
  let subject = ''
  let message = ''

  switch (topic) {
    case 'ORDERS_CREATE':
      subject = `New Order: ${order.id}`
      message = `A new order has been created that contains products from ${vendorName}. Please see the attached CSV file for details.`
      break
    // case 'ORDERS_PAID':
    //   subject = `Paid Order: ${order.name}`
    //   message = `An order has been paid that contains products from ${vendorName}. Please see the attached CSV file for details.`
    //   break
    // case 'ORDERS_EDITED':
    //   subject = `Updated Order: ${order.name}`
    //   message = `An order has been updated that contains products from ${vendorName}. Please see the attached CSV file for details of the updated order.`
    //   break
    // case 'ORDERS_CANCELLED':
    //   subject = `Cancelled Order: ${order.name}`
    //   message = `An order has been cancelled that contains products from ${vendorName}. Please see the attached CSV file for details of the cancelled order.`
    //   break
    default:
      console.log(`Unknown topic: ${topic}, skipping...`)
      throw new Response(null, { status: 200 })
  }

  return { subject, message }
}

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, payload } = await authenticate.webhook(request)

  console.log(`Processing ${topic} webhook for shop ${shop}`)

  const order = formatOrderData(payload)

  const results = await processVendors(order, shop, topic)

  console.log(`Successfully processed ${topic} webhook:`, {
    order: order.name,
    notificationsSent: results.length,
  })

  if (results && results.some((r) => !r.success)) {
    // Partial failures
    return new Response(
      JSON.stringify({
        message: 'Webhook processed, but some vendor notifications failed.',
        details: results.filter((r) => !r.success),
        results,
      }),
      {
        status: 207,
      },
    )
  } else {
    // All successful, or results is undefined/empty (e.g. no vendors found or all processed successfully)
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Webhook processed successfully.',
        results,
      }),
      {
        status: 200,
      },
    )
  }
}
