import { Page, Layout, Card, BlockStack, Text, Link } from '@shopify/polaris'
import { TitleBar } from '@shopify/app-bridge-react'

export default function Index() {
  return (
    <Page>
      <TitleBar title="Vendor Notifier Home" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <Text
                  variant="headingMd"
                  as="h2"
                >
                  Welcome to Vendor Notifier!
                </Text>
                <Text
                  variant="bodyMd"
                  as="p"
                >
                  This app helps you automatically notify your vendors about new orders.
                </Text>
                <Text
                  variant="bodyMd"
                  as="p"
                >
                  To get started, configure your vendor email mappings. This will allow the app to know which email
                  address to send notifications to for products from specific vendors.
                </Text>
                <Link url="/app/settings">Manage Vendor Emails</Link>
              </BlockStack>
            </Card>
          </Layout.Section>
          <Layout.Section>
            <Card>
              <BlockStack gap="300">
                <Text
                  variant="headingMd"
                  as="h3"
                >
                  How it Works
                </Text>
                <Text
                  variant="bodyMd"
                  as="p"
                >
                  1. Configure vendor names and their corresponding email addresses in the "Manage Vendor Emails"
                  section.
                </Text>
                <Text
                  variant="bodyMd"
                  as="p"
                >
                  2. Ensure your products have a "Vendor" field that matches the configured vendor names.
                </Text>
                <Text
                  variant="bodyMd"
                  as="p"
                >
                  3. When a new order comes in, the app will check the items. If an item's vendor is in your configured
                  list, an email notification with the relevant line items will be sent to that vendor.
                </Text>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  )
}
