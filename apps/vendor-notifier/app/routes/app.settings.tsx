import { type ActionFunctionArgs, type LoaderFunctionArgs } from '@remix-run/node'
import { useFetcher, useLoaderData } from '@remix-run/react'

import {
  Page,
  Layout,
  Card,
  BlockStack,
  Text,
  DataTable,
  ButtonGroup,
  Button,
  EmptyState,
  TextField,
  FormLayout,
  InlineStack,
  Autocomplete,
} from '@shopify/polaris'
import { useAppBridge } from '@shopify/app-bridge-react'
import { useState, useEffect, useCallback, useMemo, useRef } from 'react'

import { authenticate } from '../shopify.server'
import { upsertVendorEmail, deleteVendorEmail, getVendorEmails, type VendorEmail } from '../models/vendor-email.server'

const PRODUCT_VENDORS_QUERY = /* GraphQL */ `
  query ProductVendors {
    shop {
      productVendors(first: 250) {
        nodes
      }
    }
  }
`

type ActionOkResponse = {
  ok: true
  intent: 'upsert' | 'delete'
  message?: string
  itemId?: string // For add/edit, reflecting the ID of the item affected
}

type ActionErrorResponse = {
  ok: false
  intent: 'upsert' | 'delete'
  errors: {
    vendor?: string
    email?: string
    ccEmail?: string
    form?: string
  }
}

type AppSettingsActionData = ActionOkResponse | ActionErrorResponse

export const loader = async ({
  request,
}: LoaderFunctionArgs): Promise<{ vendorEmails: VendorEmail[]; productVendors: string[] }> => {
  const {
    admin,
    session: { shop },
  } = await authenticate.admin(request) // Get admin client and session

  const vendorEmailsPromise = getVendorEmails(shop)
  const productVendorsPromise = admin
    .graphql(PRODUCT_VENDORS_QUERY)
    .then((response) => response.json())
    .then(({ data }) => data.shop.productVendors.nodes || [])

  const [vendorEmails, productVendors] = await Promise.all([vendorEmailsPromise, productVendorsPromise])

  const existingVendors = new Set(vendorEmails.map((ve) => ve.vendor))
  const filteredProductVendors = productVendors.filter((pv: string) => !existingVendors.has(pv))
  return { vendorEmails, productVendors: filteredProductVendors }
}

export const action = async ({ request }: ActionFunctionArgs) => {
  const {
    session: { shop },
  } = await authenticate.admin(request)
  const formData = await request.formData()
  const intent = formData.get('intent')

  if (intent === 'upsert') {
    const id = formData.get('id') as string | undefined
    const vendor = formData.get('vendor') as string
    const email = formData.get('email') as string
    const ccEmail = formData.get('ccEmail') as string

    if (!vendor || vendor.trim() === '' || !email || email.trim() === '') {
      const errors: Record<string, string> = {}
      if (!vendor || vendor.trim() === '') errors.vendor = 'Vendor is required.'
      if (!email || email.trim() === '') errors.email = 'Email is required.'
      errors.form = 'Vendor and Email are required.'
      const payload = { errors, ok: false, intent, itemId: id }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 400 })
    }
    if (!/^\S+@\S+\.\S+$/.test(email)) {
      const payload = { errors: { email: 'Invalid email format.' }, ok: false, intent, itemId: id }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 400 })
    }
    if (ccEmail && !/^\S+@\S+\.\S+$/.test(ccEmail)) {
      const payload = { errors: { ccEmail: 'Invalid email format.' }, ok: false, intent, itemId: id }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 400 })
    }

    try {
      await upsertVendorEmail(shop, vendor, email, ccEmail, id)
      const message = id ? 'Vendor email updated successfully.' : 'Vendor email added successfully.'
      const payload = { ok: true, message, intent, itemId: id }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 200 })
    } catch (error: any) {
      const commonMessage = id ? 'Failed to update vendor email.' : 'Failed to add vendor email.'
      const payload = { errors: { form: error.message || commonMessage }, ok: false, intent, itemId: id }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 500 })
    }
  }

  if (intent === 'delete') {
    const id = formData.get('id') as string
    if (!id) {
      const payload = { errors: { form: 'ID is required for deletion.' }, ok: false, intent }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 400 })
    }
    try {
      await deleteVendorEmail(id)
      const payload = { ok: true, message: 'Vendor email deleted successfully.', intent, itemId: id }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 200 })
    } catch (error: any) {
      const payload = {
        errors: { form: error.message || 'Failed to delete vendor email.' },
        ok: false,
        intent,
        itemId: id,
      }
      return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 500 })
    }
  }

  const payload = { errors: { form: 'Invalid intent specified.' }, ok: false, intent: String(intent || 'unknown') }
  return new Response(JSON.stringify(payload), { headers: { 'Content-Type': 'application/json' }, status: 400 })
}

export default function SettingsPage() {
  const { vendorEmails, productVendors } = useLoaderData<typeof loader>()
  const fetcher = useFetcher<AppSettingsActionData>()
  const app = useAppBridge()

  const [showAddForm, setShowAddForm] = useState(false)
  const [newVendor, setNewVendor] = useState('')
  const [newEmail, setNewEmail] = useState('')
  const [newCcEmail, setNewCcEmail] = useState('')
  const [editingItemId, setEditingItemId] = useState<string | null>(null)
  const [editedEmail, setEditedEmail] = useState('')
  const [editedCcEmail, setEditedCcEmail] = useState('')

  const [newVendorQuery, setNewVendorQuery] = useState('')

  const isLoading = fetcher.state !== 'idle'

  const lastItemSubmittedForEditId = useRef<string | null>(null)

  const vendorOptions = useMemo(() => {
    return productVendors.map((pv) => ({ value: pv, label: pv }))
  }, [productVendors])

  const [filteredOptions, setFilteredOptions] = useState(vendorOptions)

  const handleNewVendorQueryChange = useCallback(
    (value: string) => {
      setNewVendorQuery(value)
      setNewVendor(value)
      if (value === '') {
        setFilteredOptions(vendorOptions)
        return
      }
      const filterRegex = new RegExp(value, 'i')
      setFilteredOptions(vendorOptions.filter((option) => option.label.match(filterRegex)))
    },
    [vendorOptions],
  )

  useEffect(() => {
    setNewVendorQuery(newVendor)
    if (newVendor === '') setFilteredOptions(vendorOptions)
  }, [newVendor, vendorOptions])

  useEffect(() => {
    if (fetcher.data && fetcher.state === 'idle') {
      const responseData = fetcher.data as AppSettingsActionData
      if (responseData.ok === true) {
        app.toast.show(responseData.message || 'Action successful!', {
          duration: 10000,
        })

        if (responseData.intent === 'upsert') {
          setNewVendor('')
          setNewEmail('')
          setNewCcEmail('')
          setShowAddForm(false)
        }
        if (
          responseData.intent === 'upsert' &&
          responseData.itemId &&
          responseData.itemId === editingItemId &&
          responseData.itemId === lastItemSubmittedForEditId.current
        ) {
          lastItemSubmittedForEditId.current = null
          setEditingItemId(null)
        }
      } else if (responseData.ok === false) {
        let errorMessage = 'An unexpected error occurred.'
        if (responseData.errors) {
          const errors = responseData.errors
          if (errors.form) {
            errorMessage = errors.form
          } else if (errors.vendor && responseData.intent === 'upsert') {
            errorMessage = errors.vendor
          } else if (errors.email && responseData.intent === 'upsert') {
            errorMessage = errors.email
          }
        }
        app.toast.show(errorMessage, {
          isError: true,
          duration: 10000,
        })
      }
    }
  }, [fetcher.data, fetcher.state, editingItemId, app])

  const handleAddNewItemClick = () => {
    setShowAddForm(true)
    setEditingItemId(null)
  }

  const handleCancelAddForm = () => {
    setShowAddForm(false)
    setNewVendor('')
    setNewEmail('')
    setNewCcEmail('')
  }

  const handleSaveNewItem = () => {
    if (newVendor && newEmail) {
      if (!/^\S+@\S+\.\S+$/.test(newEmail)) {
        app.toast.show('Invalid email format for new email.', { isError: true })
        return
      }
      if (newCcEmail && !/^\S+@\S+\.\S+$/.test(newCcEmail)) {
        app.toast.show('Invalid email format for new CC email.', { isError: true })
        return
      }
      fetcher.submit({ intent: 'upsert', vendor: newVendor, email: newEmail, ccEmail: newCcEmail }, { method: 'post' })
    } else {
      app.toast.show('Vendor and Email cannot be empty.', { isError: true })
    }
  }

  const handleEditItem = (item: VendorEmail) => {
    if (isLoading || editingItemId) return
    setEditingItemId(item.id)
    setEditedEmail(item.email)
    setEditedCcEmail(item.ccEmail)
    setShowAddForm(false)
  }

  const handleCancelEdit = () => {
    setEditingItemId(null)
  }

  const handleSaveEdit = (item: VendorEmail) => {
    if (editedEmail) {
      if (!/^\S+@\S+\.\S+$/.test(editedEmail)) {
        app.toast.show('Invalid email format for edited email.', { isError: true })
        return
      }
      if (editedCcEmail && !/^\S+@\S+\.\S+$/.test(editedCcEmail)) {
        app.toast.show('Invalid email format for edited CC email.', { isError: true })
        return
      }
      lastItemSubmittedForEditId.current = item.id
      fetcher.submit(
        { intent: 'upsert', id: item.id, vendor: item.vendor, email: editedEmail, ccEmail: editedCcEmail },
        { method: 'post' },
      )
    } else {
      app.toast.show('Email cannot be empty.', {
        isError: true,
        duration: 10000,
      })
    }
  }

  const rows = vendorEmails.map((item) => {
    if (item.id === editingItemId) {
      return [
        item.vendor,
        <TextField
          key={`${item.id}-email-edit`}
          value={editedEmail}
          onChange={setEditedEmail}
          label="Email"
          labelHidden
          type="email"
          autoComplete="off"
          disabled={isLoading}
          error={
            fetcher.data &&
            fetcher.data.ok === false &&
            fetcher.data.intent === 'upsert' &&
            editingItemId === item.id &&
            fetcher.data.errors?.email
              ? fetcher.data.errors.email
              : undefined
          }
        />,
        <TextField
          key={`${item.id}-cc-email-edit`}
          value={editedCcEmail}
          onChange={setEditedCcEmail}
          label="CC Email"
          labelHidden
          type="email"
          autoComplete="off"
          disabled={isLoading}
          error={
            fetcher.data &&
            fetcher.data.ok === false &&
            fetcher.data.intent === 'upsert' &&
            editingItemId === item.id &&
            fetcher.data.errors?.ccEmail
              ? fetcher.data.errors.ccEmail
              : undefined
          }
        />,
        <InlineStack
          key={`${item.id}-edit-actions`}
          align="end"
        >
          <ButtonGroup key={`${item.id}-edit-actions`}>
            <Button
              onClick={() => handleSaveEdit(item)}
              variant="primary"
              disabled={isLoading || !editedEmail}
            >
              Save
            </Button>
            <Button
              onClick={handleCancelEdit}
              disabled={isLoading}
            >
              Cancel
            </Button>
          </ButtonGroup>
        </InlineStack>,
      ]
    } else {
      return [
        item.vendor,
        item.email,
        item.ccEmail,
        <InlineStack
          key={item.id}
          align="end"
        >
          <ButtonGroup key={item.id}>
            <Button
              onClick={() => handleEditItem(item)}
              disabled={isLoading || !!editingItemId}
            >
              Edit
            </Button>
            <fetcher.Form
              method="post"
              onSubmit={(event: React.FormEvent<HTMLFormElement>) => {
                if (isLoading || !!editingItemId) event.preventDefault()
              }}
            >
              <input
                type="hidden"
                name="id"
                value={item.id}
              />
              <input
                type="hidden"
                name="intent"
                value="delete"
              />
              <Button
                submit
                variant="primary"
                tone="critical"
                disabled={isLoading || !!editingItemId}
              >
                Delete
              </Button>
            </fetcher.Form>
          </ButtonGroup>
        </InlineStack>,
      ]
    }
  })

  return (
    <Page
      title="Settings"
      primaryAction={{
        content: 'Add New Vendor Email',
        onAction: handleAddNewItemClick,
        disabled: isLoading || !!editingItemId || showAddForm,
      }}
    >
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <Text
                  as="h2"
                  variant="headingMd"
                >
                  Vendor Email Configuration
                </Text>
                {showAddForm && !editingItemId && (
                  <FormLayout>
                    <Autocomplete
                      options={filteredOptions}
                      selected={newVendor ? [newVendor] : []}
                      onSelect={(selectedValues) => {
                        const val = selectedValues[0] || ''
                        setNewVendor(val)
                        setNewVendorQuery(val)
                      }}
                      textField={
                        <Autocomplete.TextField
                          onChange={handleNewVendorQueryChange}
                          value={newVendorQuery}
                          label="Vendor"
                          autoComplete="off"
                          error={
                            fetcher.data &&
                            fetcher.data.ok === false &&
                            fetcher.data.intent === 'upsert' &&
                            fetcher.data.errors?.vendor
                              ? fetcher.data.errors.vendor
                              : undefined
                          }
                          disabled={isLoading}
                        />
                      }
                    />
                    <TextField
                      label="Email"
                      type="email"
                      value={newEmail}
                      onChange={setNewEmail}
                      autoComplete="off"
                      error={
                        fetcher.data &&
                        fetcher.data.ok === false &&
                        fetcher.data.intent === 'upsert' &&
                        fetcher.data.errors?.email
                          ? fetcher.data.errors.email
                          : undefined
                      }
                      disabled={isLoading}
                    />
                    <TextField
                      label="CC Email"
                      type="email"
                      value={newCcEmail}
                      onChange={setNewCcEmail}
                      autoComplete="off"
                      error={
                        fetcher.data &&
                        fetcher.data.ok === false &&
                        fetcher.data.intent === 'upsert' &&
                        fetcher.data.errors?.ccEmail
                          ? fetcher.data.errors.ccEmail
                          : undefined
                      }
                      disabled={isLoading}
                    />
                    <InlineStack align="end">
                      <ButtonGroup>
                        <Button
                          onClick={handleCancelAddForm}
                          disabled={isLoading}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleSaveNewItem}
                          variant="primary"
                          disabled={isLoading || !newVendor || !newEmail}
                        >
                          Save New
                        </Button>
                      </ButtonGroup>
                    </InlineStack>
                  </FormLayout>
                )}
                {vendorEmails.length > 0 ? (
                  <>
                    <style>
                      {`
                      table {
                        width: 100%;
                      }
                      td, th {
                        width: calc(1/4) !important;
                      }
                      `}
                    </style>
                    <DataTable
                      columnContentTypes={['text', 'text', 'text', 'numeric']}
                      headings={['Vendor', 'Email', 'CC Email', 'Actions']}
                      rows={rows}
                      verticalAlign="baseline"
                      truncate={false}
                    />
                  </>
                ) : (
                  !showAddForm &&
                  !editingItemId && (
                    <EmptyState
                      heading="No vendor emails configured"
                      action={{
                        content: 'Add Vendor Email',
                        onAction: handleAddNewItemClick,
                        disabled: isLoading || !!editingItemId,
                      }}
                      image="https://cdn.shopify.com/s/files/1/0262/4074/files/emptystate-files.svg"
                    >
                      <p>Add vendor emails to notify them when new orders come in.</p>
                    </EmptyState>
                  )
                )}
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  )
}
