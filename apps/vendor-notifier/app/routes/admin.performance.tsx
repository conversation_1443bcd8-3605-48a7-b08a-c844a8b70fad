import type { LoaderFunctionArgs } from '@remix-run/node'
import { performanceMonitor } from '../utils/performance.server.js'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url)
  const action = url.searchParams.get('action')
  const limit = parseInt(url.searchParams.get('limit') || '100')

  try {
    switch (action) {
      case 'stats':
        return Response.json({
          stats: performanceMonitor.getStats(),
          timestamp: new Date().toISOString(),
        })

      case 'slow':
        const threshold = parseInt(url.searchParams.get('threshold') || '100')
        return Response.json({
          slowQueries: performanceMonitor.getSlowQueries(threshold),
          threshold,
          timestamp: new Date().toISOString(),
        })

      case 'failed':
        return Response.json({
          failedQueries: performanceMonitor.getFailedQueries(),
          timestamp: new Date().toISOString(),
        })

      case 'reset':
        performanceMonitor.reset()
        return Response.json({
          message: 'Performance metrics reset',
          timestamp: new Date().toISOString(),
        })

      default:
        return Response.json({
          metrics: performanceMonitor.getMetrics(limit),
          stats: performanceMonitor.getStats(),
          timestamp: new Date().toISOString(),
        })
    }
  } catch (error) {
    console.error('Performance monitoring error:', error)
    return Response.json(
      {
        error: 'Failed to retrieve performance metrics',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}
