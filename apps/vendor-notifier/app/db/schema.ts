import { pgTable, text, timestamp, boolean, bigint, uuid, unique } from 'drizzle-orm/pg-core'

// Session table for Shopify app authentication
export const sessions = pgTable('Session', {
  id: text('id').primaryKey(),
  shop: text('shop').notNull(),
  state: text('state').notNull(),
  isOnline: boolean('isOnline').default(false).notNull(),
  scope: text('scope'),
  expires: timestamp('expires'),
  accessToken: text('accessToken').notNull(),
  userId: bigint('userId', { mode: 'bigint' }),
  firstName: text('firstName'),
  lastName: text('lastName'),
  email: text('email'),
  accountOwner: boolean('accountOwner').default(false).notNull(),
  locale: text('locale'),
  collaborator: boolean('collaborator').default(false),
  emailVerified: boolean('emailVerified').default(false),
})

// VendorEmail table for vendor notification mappings
export const vendorEmails = pgTable(
  'VendorEmail',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    shop: text('shop').notNull(),
    vendor: text('vendor').notNull(),
    email: text('email').notNull(),
    ccEmail: text('ccEmail'),
    createdAt: timestamp('createdAt').defaultNow().notNull(),
    updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  },
  (table) => ({
    shopVendorUnique: unique().on(table.shop, table.vendor),
  }),
)

// TypeScript types for VendorEmail operations
export type VendorEmail = typeof vendorEmails.$inferSelect
export type NewVendorEmail = typeof vendorEmails.$inferInsert
