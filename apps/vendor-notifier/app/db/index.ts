import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import * as schema from './schema.ts'

const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required')
}

const client = postgres(connectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
  max_lifetime: 60 * 30,
  prepare: true,
  transform: {
    undefined: null,
  },
})

export const db = drizzle(client, {
  schema,
  logger: process.env.NODE_ENV === 'development',
})

export type Database = typeof db
export * from './schema'

// Database health check function


// Graceful shutdown handlers
const gracefulShutdown = async (signal: string) => {
  console.log(`Received ${signal}, closing database connections...`)
  try {
    await client.end()
    console.log('Database connections closed successfully')
    process.exit(0)
  } catch (error) {
    console.error('Error during database shutdown:', error)
    process.exit(1)
  }
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
process.on('SIGINT', () => gracefulShutdown('SIGINT'))
process.on('beforeExit', () => {
  client.end()
})
