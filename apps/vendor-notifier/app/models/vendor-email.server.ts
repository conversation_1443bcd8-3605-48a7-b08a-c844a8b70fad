import { eq, and, asc } from 'drizzle-orm'
import { db } from '../db/index.js'
import { vendorEmails, type VendorEmail, type NewVendorEmail } from '../db/schema.js'
import { withPerformanceMonitoring } from '../utils/performance.server.js'

export type { VendorEmail, NewVendorEmail }

export async function getVendorEmails(shop: string): Promise<VendorEmail[]> {
  return withPerformanceMonitoring(`getVendorEmails:${shop}`, async () => {
    try {
      console.log(`Fetching vendor emails for shop: ${shop}`)
      const result = await db.select().from(vendorEmails).where(eq(vendorEmails.shop, shop)).orderBy(asc(vendorEmails.vendor))
      console.log(`Found ${result.length} vendor emails for shop: ${shop}`)
      return result
    } catch (error) {
      console.error(`Failed to fetch vendor emails for shop ${shop}:`, error)
      throw new Error(`Could not retrieve vendor emails for shop: ${shop}`)
    }
  })
}

export async function getVendorEmail(id: string): Promise<VendorEmail | undefined> {
  try {
    console.log(`Fetching vendor email by ID: ${id}`)
    const result = await db.select().from(vendorEmails).where(eq(vendorEmails.id, id)).limit(1)

    if (result.length === 0) {
      console.log(`Vendor email not found for ID: ${id}`)
      return undefined
    }

    console.log(`Found vendor email for ID: ${id}`)
    return result[0]
  } catch (error) {
    console.error(`Failed to fetch vendor email by ID ${id}:`, error)
    throw new Error(`Could not retrieve vendor email with ID: ${id}`)
  }
}

export async function getVendorEmailByVendor(shop: string, vendor: string): Promise<VendorEmail | null> {
  return withPerformanceMonitoring(`getVendorEmailByVendor:${shop}:${vendor}`, async () => {
    try {
      console.log(`Fetching vendor email for shop: ${shop}, vendor: ${vendor}`)
      const result = await db
        .select()
        .from(vendorEmails)
        .where(and(eq(vendorEmails.shop, shop), eq(vendorEmails.vendor, vendor)))
        .limit(1)

      if (result.length === 0) {
        console.log(`No vendor email found for shop: ${shop}, vendor: ${vendor}`)
        return null
      }

      console.log(`Found vendor email for shop: ${shop}, vendor: ${vendor}`)
      return result[0]
    } catch (error) {
      console.error(`Failed to fetch vendor email for shop ${shop}, vendor ${vendor}:`, error)
      throw new Error(`Could not retrieve vendor email for shop: ${shop}, vendor: ${vendor}`)
    }
  })
}

async function createVendorEmail(shop: string, vendor: string, email: string, ccEmail?: string): Promise<VendorEmail> {
  try {
    console.log(`Creating vendor email for shop: ${shop}, vendor: ${vendor}`)
    const newVendorEmail: NewVendorEmail = {
      shop,
      vendor,
      email,
      ccEmail,
    }

    const result = await db.insert(vendorEmails).values(newVendorEmail).returning()

    if (result.length === 0) {
      throw new Error('Failed to create vendor email - no result returned')
    }

    console.log(`Successfully created vendor email with ID: ${result[0].id}`)
    return result[0]
  } catch (error) {
    console.error(`Failed to create vendor email for shop ${shop}, vendor ${vendor}:`, error)

    // Check for unique constraint violation
    if (error instanceof Error && error.message.includes('unique')) {
      throw new Error(`Vendor email already exists for shop: ${shop}, vendor: ${vendor}`)
    }

    throw new Error(`Could not create vendor email for shop: ${shop}, vendor: ${vendor}`)
  }
}

async function updateVendorEmail(id: string, vendor: string, email: string, ccEmail?: string): Promise<VendorEmail> {
  try {
    console.log(`Updating vendor email with ID: ${id}`)
    const result = await db
      .update(vendorEmails)
      .set({
        vendor,
        email,
        ccEmail,
        updatedAt: new Date(),
      })
      .where(eq(vendorEmails.id, id))
      .returning()

    if (result.length === 0) {
      console.log(`Vendor email not found for ID: ${id}`)
      throw new Error(`Vendor email with ID ${id} not found.`)
    }

    console.log(`Successfully updated vendor email with ID: ${id}`)
    return result[0]
  } catch (error) {
    console.error(`Failed to update vendor email with ID ${id}:`, error)

    if (error instanceof Error && error.message.includes('not found')) {
      throw error // Re-throw not found errors as-is
    }

    throw new Error(`Could not update vendor email with ID: ${id}`)
  }
}

export async function upsertVendorEmail(
  shop: string,
  vendor: string,
  email: string,
  ccEmail?: string,
  id?: string,
): Promise<VendorEmail> {
  if (id) {
    return updateVendorEmail(id, vendor, email, ccEmail)
  } else {
    return createVendorEmail(shop, vendor, email, ccEmail)
  }
}

export async function updateVendorEmailById(id: string, data: { vendor: string; email: string }): Promise<VendorEmail> {
  try {
    const result = await db
      .update(vendorEmails)
      .set({
        vendor: data.vendor,
        email: data.email,
        updatedAt: new Date(),
      })
      .where(eq(vendorEmails.id, id))
      .returning()

    if (result.length === 0) {
      throw new Error(`Vendor email with ID ${id} not found.`)
    }

    return result[0]
  } catch (error) {
    console.error(`Failed to update vendor email with id ${id}:`, error)
    throw new Error(`Could not update vendor email with ID ${id}.`)
  }
}

export async function deleteVendorEmail(id: string): Promise<VendorEmail> {
  const result = await db.delete(vendorEmails).where(eq(vendorEmails.id, id)).returning()

  if (result.length === 0) {
    throw new Error(`Vendor email with ID ${id} not found.`)
  }

  return result[0]
}
