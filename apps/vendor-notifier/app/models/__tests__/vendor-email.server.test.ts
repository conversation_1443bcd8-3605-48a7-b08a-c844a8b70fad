import { describe, it, expect, beforeEach, beforeAll } from 'vitest'

import {
  getVendorEmails,
  getVendorEmail,
  getVendorEmailByVendor,
  upsertVendorEmail,
  updateVendorEmailById,
} from '../vendor-email.server.js'

describe('VendorEmail Model Integration Tests', () => {
  const testShop = 'test-shop.myshopify.com'
  const testVendor = 'Test Vendor'
  const testEmail = '<EMAIL>'
  const testCcEmail = '<EMAIL>'

  beforeEach(async () => {
    const dbModule = await import('../../db/index.js') as any
    await dbModule._clearDatabase()
  })

  describe('Database Connection', () => {
    it('should have a working database connection', async () => {
      const result = await getVendorEmails('test-shop.myshopify.com')
      expect(Array.isArray(result)).toBe(true)
    })
  })

  describe('getVendorEmails', () => {
    it('should return empty array when no vendor emails exist', async () => {
      const result = await getVendorEmails(testShop)
      expect(result).toEqual([])
    })

    it('should return vendor emails for a shop', async () => {
      const vendorEmail = await upsertVendorEmail(testShop, testVendor, testEmail)

      const result = await getVendorEmails(testShop)
      expect(result).toHaveLength(1)
      const [returnedVendorEmail] = result
      expect(returnedVendorEmail).toEqual(vendorEmail)
    })

    it('should return vendor emails sorted by vendor name', async () => {
      const vendor1 = await upsertVendorEmail(testShop, 'Z Vendor', '<EMAIL>')
      const vendor2 = await upsertVendorEmail(testShop, 'A Vendor', '<EMAIL>')

      const result = await getVendorEmails(testShop)
      expect(result).toHaveLength(2)
      const [returnedVendorEmail1, returnedVendorEmail2] = result
      expect(returnedVendorEmail1).toEqual(vendor2)
      expect(returnedVendorEmail2).toEqual(vendor1)
    })
  })

  describe('getVendorEmail', () => {
    it('should throw error for non-existent ID', async () => {
      await expect(getVendorEmail('non-existent-id')).rejects.toThrow('Could not retrieve vendor email with ID: non-existent-id')
    })

    it('should return vendor email by ID', async () => {
      const vendorEmail = await upsertVendorEmail(testShop, testVendor, testEmail)

      const returnedVendorEmail = await getVendorEmail(vendorEmail.id)
      expect(returnedVendorEmail).toEqual(vendorEmail)
    })
  })

  describe('getVendorEmailByVendor', () => {
    it('should return null for non-existent vendor', async () => {
      const result = await getVendorEmailByVendor(testShop, 'Non-existent Vendor')
      expect(result).toBeNull()
    })

    it('should return vendor email by shop and vendor', async () => {
      const vendorEmail = await upsertVendorEmail(testShop, testVendor, testEmail)

      const returnedVendorEmail = await getVendorEmailByVendor(testShop, testVendor)
      expect(returnedVendorEmail).toEqual(vendorEmail)
    })
  })

  describe('upsertVendorEmail', () => {
    it('should create new vendor email', async () => {
      const createdVendorEmail = await upsertVendorEmail(testShop, testVendor, testEmail, testCcEmail)

      expect(createdVendorEmail.id).toBeDefined()
      expect(createdVendorEmail.shop).toBe(testShop)
      expect(createdVendorEmail.vendor).toBe(testVendor)
      expect(createdVendorEmail.email).toBe(testEmail)
      expect(createdVendorEmail.ccEmail).toBe(testCcEmail)
      expect(createdVendorEmail.createdAt).toBeDefined()
      expect(createdVendorEmail.updatedAt).toBeDefined()
    })

    it('should update existing vendor email when ID provided', async () => {
      const original = await upsertVendorEmail(testShop, testVendor, testEmail)

      // Add a small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10))

      const updated = await upsertVendorEmail(testShop, 'Updated Vendor', '<EMAIL>', '<EMAIL>', original.id)

      expect(updated.id).toBe(original.id)
      expect(updated.vendor).toBe('Updated Vendor')
      expect(updated.email).toBe('<EMAIL>')
      expect(updated.ccEmail).toBe('<EMAIL>')
      expect(updated.updatedAt.getTime()).toBeGreaterThan(original.updatedAt.getTime())
    })

    it('should enforce unique constraint on shop+vendor combination', async () => {
      await upsertVendorEmail(testShop, testVendor, testEmail)

      await expect(
        upsertVendorEmail(testShop, testVendor, '<EMAIL>')
      ).rejects.toThrow()
    })
  })

  describe('updateVendorEmailById', () => {
    it('should throw error for non-existent ID', async () => {
      await expect(
        updateVendorEmailById('non-existent-id', { vendor: 'New Vendor', email: '<EMAIL>' })
      ).rejects.toThrow('Could not update vendor email with ID non-existent-id.')
    })

    it('should update vendor email by ID', async () => {
      const original = await upsertVendorEmail(testShop, testVendor, testEmail)

      // Add a small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10))

      const updated = await updateVendorEmailById(original.id, {
        vendor: 'Updated Vendor',
        email: '<EMAIL>'
      })

      expect(updated.id).toBe(original.id)
      expect(updated.vendor).toBe('Updated Vendor')
      expect(updated.email).toBe('<EMAIL>')
      expect(updated.updatedAt.getTime()).toBeGreaterThan(original.updatedAt.getTime())
    })
  })
})
