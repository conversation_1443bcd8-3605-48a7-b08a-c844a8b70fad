import { describe, it, expect } from 'vitest'

describe('Simple Vitest Test', () => {
  it('should run basic assertions', () => {
    expect(1 + 1).toBe(2)
    expect('hello').toBe('hello')
    expect([1, 2, 3]).toHaveLength(3)
  })

  it('should handle async operations', async () => {
    const result = await Promise.resolve('async test')
    expect(result).toBe('async test')
  })

  it('should work with objects', () => {
    const obj = { name: 'test', value: 42 }
    expect(obj).toHaveProperty('name', 'test')
    expect(obj).toHaveProperty('value', 42)
  })
})
