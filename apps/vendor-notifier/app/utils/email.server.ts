import { stringify } from 'csv-stringify/sync'
import formData from 'form-data'
import Mailgun from 'mailgun.js'
import type { MailgunMessageData, MessagesSendResult } from 'mailgun.js/definitions'

type LineItem = {
  id: string
  name: string
  quantity: number
  sku: string
  variant_title?: string
  vendor: string
}

type OrderData = {
  id: string
  name: string
  created_at: string
  customer: {
    first_name: string
    last_name: string
    email: string
  }
  shipping_address?: {
    address1: string
    address2?: string
    city: string
    province?: string
    zip: string
    country: string
  }
  line_items: LineItem[]
}

export function generateCsvForVendor(orderData: OrderData, vendorName: string): string {
  // Filter line items for this vendor
  const vendorLineItems = orderData.line_items.filter((item) => item.vendor === vendorName)

  if (vendorLineItems.length === 0) {
    return ''
  }

  // Prepare CSV rows
  const csvRows = vendorLineItems.map((item) => ({
    order_id: orderData.id,
    order_date: orderData.created_at,
    customer_name: `${orderData.customer.first_name} ${orderData.customer.last_name}`,
    customer_email: orderData.customer.email,
    address1: orderData.shipping_address?.address1 || '',
    address2: orderData.shipping_address?.address2 || '',
    city: orderData.shipping_address?.city || '',
    province: orderData.shipping_address?.province || '',
    postal_code: orderData.shipping_address?.zip || '',
    country: orderData.shipping_address?.country || '',
    product_name: item.name,
    variant: item.variant_title || 'Default',
    sku: item.sku || '',
    quantity: item.quantity,
  }))

  // Generate CSV string
  return stringify(csvRows, {
    header: true,
    columns: [
      'order_id',
      'order_date',
      'customer_name',
      'customer_email',
      'address1',
      'address2',
      'city',
      'province',
      'postal_code',
      'country',
      'product_name',
      'variant',
      'sku',
      'quantity',
    ],
  })
}

export async function sendEmailWithCsv(
  to: string,
  cc: string | undefined,
  subject: string,
  text: string,
  csvContent: string,
  orderId: string,
): Promise<MessagesSendResult> {
  const mailgun = new Mailgun(formData)
  const mg = mailgun.client({
    username: 'api',
    key: process.env.MAILGUN_API_KEY || '',
    url: process.env.MAILGUN_API_ENDPOINT || '',
  })

  const fileName = `order_${orderId}_${new Date().toISOString().split('T')[0]}.csv`

  const emailData: MailgunMessageData = {
    from: process.env.MAILGUN_EMAIL_FROM || '',
    'h:Reply-To': process.env.MAILGUN_EMAIL_REPLY_TO || '',
    to,
    cc,
    subject,
    text,
    attachment: {
      data: Buffer.from(csvContent),
      filename: fileName,
      contentType: 'text/csv',
    },
  }

  return mg.messages.create(process.env.MAILGUN_DOMAIN || '', emailData)
}
