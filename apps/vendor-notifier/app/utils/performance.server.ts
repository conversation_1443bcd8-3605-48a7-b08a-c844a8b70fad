// Performance monitoring utilities for database operations

interface QueryMetrics {
  operation: string
  duration: number
  timestamp: Date
  success: boolean
  error?: string
}

class PerformanceMonitor {
  private metrics: QueryMetrics[] = []
  private readonly maxMetrics = 1000 // Keep last 1000 metrics in memory

  async measureQuery<T>(
    operation: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now()
    const timestamp = new Date()
    
    try {
      const result = await queryFn()
      const duration = performance.now() - startTime
      
      this.recordMetric({
        operation,
        duration,
        timestamp,
        success: true,
      })
      
      // Log slow queries (> 100ms)
      if (duration > 100) {
        console.warn(`Slow query detected: ${operation} took ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      this.recordMetric({
        operation,
        duration,
        timestamp,
        success: false,
        error: errorMessage,
      })
      
      console.error(`Query failed: ${operation} (${duration.toFixed(2)}ms) - ${errorMessage}`)
      throw error
    }
  }

  private recordMetric(metric: QueryMetrics) {
    this.metrics.push(metric)
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }
  }

  getMetrics(limit = 100): QueryMetrics[] {
    return this.metrics.slice(-limit)
  }

  getAverageQueryTime(operation?: string): number {
    const filteredMetrics = operation 
      ? this.metrics.filter(m => m.operation === operation && m.success)
      : this.metrics.filter(m => m.success)
    
    if (filteredMetrics.length === 0) return 0
    
    const totalTime = filteredMetrics.reduce((sum, m) => sum + m.duration, 0)
    return totalTime / filteredMetrics.length
  }

  getSlowQueries(threshold = 100): QueryMetrics[] {
    return this.metrics.filter(m => m.duration > threshold)
  }

  getFailedQueries(): QueryMetrics[] {
    return this.metrics.filter(m => !m.success)
  }

  getStats() {
    const total = this.metrics.length
    const successful = this.metrics.filter(m => m.success).length
    const failed = total - successful
    const avgTime = this.getAverageQueryTime()
    const slowQueries = this.getSlowQueries().length

    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      averageQueryTime: avgTime,
      slowQueries,
    }
  }

  reset() {
    this.metrics = []
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// Helper function to wrap database operations with performance monitoring
export function withPerformanceMonitoring<T>(
  operation: string,
  queryFn: () => Promise<T>
): Promise<T> {
  return performanceMonitor.measureQuery(operation, queryFn)
}
