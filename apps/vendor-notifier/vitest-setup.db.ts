// Database setup file for Vitest - must be imported before any database modules
import { vi } from 'vitest'

// Set environment variable before any imports to prevent errors
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/vendor_notifier_test'

// Mock the database module with PGlite for real PostgreSQL testing
vi.mock('./app/db/index.js', async (importOriginal) => {
  const originalModule = await importOriginal<typeof import('./app/db/index.js')>()
  const { PGlite } = await vi.importActual<typeof import('@electric-sql/pglite')>('@electric-sql/pglite')
  const { drizzle } = await vi.importActual<typeof import('drizzle-orm/pglite')>('drizzle-orm/pglite')

  // Create in-memory PostgreSQL instance
  const client = new PGlite()
  const testDb = drizzle(client, { schema: originalModule })

  // Apply schema using SQL from migrations
  await client.exec(`
    CREATE TABLE "Session" (
      "id" text PRIMARY KEY NOT NULL,
      "shop" text NOT NULL,
      "state" text NOT NULL,
      "isOnline" boolean DEFAULT false NOT NULL,
      "scope" text,
      "expires" timestamp,
      "accessToken" text NOT NULL,
      "userId" bigint,
      "firstName" text,
      "lastName" text,
      "email" text,
      "accountOwner" boolean DEFAULT false NOT NULL,
      "locale" text,
      "collaborator" boolean DEFAULT false,
      "emailVerified" boolean DEFAULT false
    );

    CREATE TABLE "VendorEmail" (
      "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
      "shop" text NOT NULL,
      "vendor" text NOT NULL,
      "email" text NOT NULL,
      "ccEmail" text,
      "createdAt" timestamp DEFAULT now() NOT NULL,
      "updatedAt" timestamp DEFAULT now() NOT NULL
    );

    ALTER TABLE "VendorEmail" ADD CONSTRAINT "VendorEmail_shop_vendor_unique" UNIQUE("shop", "vendor");
  `)

  return { 
    ...originalModule,
    db: testDb,
    // Helper function to clear database between tests
    _clearDatabase: async () => {
      await client.exec('DELETE FROM "VendorEmail"')
      await client.exec('DELETE FROM "Session"')
    }
  }
})
