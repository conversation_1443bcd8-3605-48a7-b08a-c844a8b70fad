// General test environment setup for Vitest
import { beforeAll, beforeEach, afterEach, afterAll, vi } from 'vitest'

// Global test environment setup
beforeAll(async () => {
  // Environment variables setup - not needed since we're using PGlite
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/vendor_notifier_test'

  // Set NODE_ENV to test
  process.env.NODE_ENV = 'test'

  // Set test-specific environment variables
  process.env.SHOPIFY_API_KEY = 'test_api_key'
  process.env.SHOPIFY_API_SECRET = 'test_api_secret'
  process.env.SHOPIFY_SCOPES = 'read_products,write_products'
  process.env.SHOPIFY_APP_URL = 'https://test.ngrok.io'
  process.env.MAILGUN_API_KEY = 'test_mailgun_key'
  process.env.MAILGUN_DOMAIN = 'test.mailgun.org'

  // Disable logging during tests (but keep error logs for debugging)
  const originalConsole = { ...console }
  console.log = vi.fn()
  console.info = vi.fn()
  console.warn = vi.fn()
  console.debug = vi.fn()

  // Keep error logs for debugging test failures
  console.error = (...args: any[]) => {
    if (process.env.VITEST_DEBUG) {
      originalConsole.error(...args)
    }
  }

  // Global timeout for async operations
  vi.setConfig({
    testTimeout: 10000,
    hookTimeout: 10000,
  })
})

// Setup before each test
beforeEach(() => {
  // Clear all mocks before each test
  vi.clearAllMocks()

  // Reset any runtime configuration changes
  vi.resetConfig()
})

// Cleanup after each test
afterEach(() => {
  // Restore all mocks after each test
  vi.restoreAllMocks()

  // Clear any timers
  vi.clearAllTimers()
})

// Global cleanup
afterAll(async () => {
  // Restore original console methods
  vi.restoreAllMocks()
})
