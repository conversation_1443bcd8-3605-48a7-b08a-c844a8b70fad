<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui'

defineProps<{
  collapsed?: boolean
}>()

// This ref holds the list of available instances.
// The data is currently placeholder.
const instances = ref([
  {
    label: 'Alle Instanzen',
  },
])

// This ref holds the currently selected instance.
const selectedInstance = ref(instances.value[0])

// The items for the dropdown menu.
const items = computed<DropdownMenuItem[][]>(() => {
  return [
    // The list of instances to switch between.
    instances.value.map((instance) => ({
      ...instance,
      // Update the selected instance on click.
      onSelect() {
        selectedInstance.value = instance
      },
    })),
    // The action buttons, translated to German.
    [
      {
        label: 'Neue Instanz',
        icon: 'i-lucide-circle-plus',
      },
      {
        label: 'Instanzen verwalten',
        icon: 'i-lucide-cog',
      },
    ],
  ]
})
</script>

<template>
  <UDropdownMenu
    :items="items"
    :content="{ align: 'center', collisionPadding: 12 }"
    :ui="{ content: collapsed ? 'w-40' : 'w-(--reka-dropdown-menu-trigger-width)' }"
  >
    <UButton
      v-bind="{
        ...selectedInstance,
        label: collapsed ? undefined : selectedInstance?.label,
        trailingIcon: collapsed ? undefined : 'i-lucide-chevrons-up-down',
      }"
      color="neutral"
      variant="ghost"
      block
      :square="collapsed"
      class="data-[state=open]:bg-elevated"
      :class="[!collapsed && 'py-2']"
      :ui="{
        trailingIcon: 'text-dimmed',
      }"
    />
  </UDropdownMenu>
</template>
