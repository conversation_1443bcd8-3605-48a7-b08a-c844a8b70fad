<script setup lang="ts">
import { sub } from 'date-fns'
import type { Period, Range } from '~/types'

const { isNotificationsSlideoverOpen } = useDashboard()

// Date range for the dashboard analytics
const range = shallowRef<Range>({
  start: sub(new Date(), { days: 14 }),
  end: new Date(),
})
const period = ref<Period>('daily')

// Placeholder data for the dashboard widgets, translated to German
const usageStats = ref([
  { label: 'Gesamte Anfragen', value: '1,234', change: '+12.5%' },
  { label: 'Erfolgreiche Antworten', value: '1,150', change: '+10.2%' },
  { label: 'Fallback-Rate', value: '6.8%', change: '-1.5%' },
  { label: 'Durchschn. Bewertung', value: '4.5 / 5', change: '+0.2' },
])

const commonTopics = ref([
  { name: 'Schlafstörungen', count: 234 },
  { name: '<PERSON><PERSON> <PERSON>', count: 198 },
  { name: 'Stressmanagement', count: 156 },
  { name: 'Gesunde Ernährung', count: 123 },
  { name: 'Magnesiummangel', count: 98 },
])

const recentQuestions = ref([
  { question: 'Wie kann ich besser einschlafen?', time: 'vor 2 Minuten' },
  { question: 'Was sind die Symptome von Vitamin-D-Mangel?', time: 'vor 5 Minuten' },
  { question: 'Hilft Magnesium bei Muskelkrämpfen?', time: 'vor 12 Minuten' },
  { question: 'Welche Lebensmittel enthalten viel Eisen?', time: 'vor 28 Minuten' },
])

const fallbackQuestions = ref([
  { question: 'Kann ich Ashwagandha in der Schwangerschaft nehmen?', count: 12 },
  { question: 'Was ist die beste Diät zum Abnehmen?', count: 8 },
  { question: 'Wie dosiere ich CBD-Öl für meinen Hund?', count: 5 },
])
</script>

<template>
  <UDashboardPanel id="home">
    <template #header>
      <UDashboardNavbar title="Dashboard">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UTooltip
            text="Benachrichtigungen"
            :shortcuts="['N']"
          >
            <UButton
              color="neutral"
              variant="ghost"
              square
              @click="isNotificationsSlideoverOpen = true"
            >
              <UChip
                color="error"
                inset
              >
                <UIcon
                  name="i-lucide-bell"
                  class="size-5 shrink-0"
                />
              </UChip>
            </UButton>
          </UTooltip>
        </template>
      </UDashboardNavbar>

      <UDashboardToolbar>
        <template #left>
          <!-- Date range picker for filtering analytics -->
          <HomeDateRangePicker
            v-model="range"
            class="-ms-1"
          />
          <HomePeriodSelect
            v-model="period"
            :range="range"
          />
        </template>
      </UDashboardToolbar>
    </template>

    <template #body>
      <!-- Key usage statistics -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4 mb-8">
        <UCard
          v-for="stat in usageStats"
          :key="stat.label"
        >
          <div class="flex items-center justify-between">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
              {{ stat.label }}
            </p>
            <UBadge
              :label="stat.change"
              color="primary"
              variant="subtle"
            />
          </div>
          <p class="mt-1 text-2xl font-semibold">
            {{ stat.value }}
          </p>
        </UCard>
      </div>

      <!-- Chart showing requests over time -->
      <div class="mb-8">
        <HomeChart
          :period="period"
          :range="range"
        />
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Common Topics Card -->
        <UCard>
          <template #header>
            <h3 class="text-base font-semibold">Häufige Themen</h3>
          </template>
          <ul>
            <li
              v-for="topic in commonTopics"
              :key="topic.name"
              class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
            >
              <span>{{ topic.name }}</span>
              <span class="font-medium">{{ topic.count }} Anfragen</span>
            </li>
          </ul>
        </UCard>

        <!-- Fallback Questions Card -->
        <UCard>
          <template #header>
            <h3 class="text-base font-semibold">Nicht beantwortete Fragen</h3>
          </template>
          <ul>
            <li
              v-for="fb in fallbackQuestions"
              :key="fb.question"
              class="py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
            >
              <p>{{ fb.question }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ fb.count }} mal gefragt</p>
            </li>
          </ul>
        </UCard>

        <!-- Recent Questions Card -->
        <UCard class="lg:col-span-2">
          <template #header>
            <h3 class="text-base font-semibold">Neueste Anfragen</h3>
          </template>
          <ul>
            <li
              v-for="q in recentQuestions"
              :key="q.question"
              class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
            >
              <span>{{ q.question }}</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ q.time }}</span>
            </li>
          </ul>
        </UCard>
      </div>
    </template>
  </UDashboardPanel>
</template>
