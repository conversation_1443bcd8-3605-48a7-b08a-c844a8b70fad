<script setup lang="ts">
import { ref } from 'vue'
import type { Ref } from 'vue'

// Define the structure for a source type for better type safety.
interface SourceType {
  id: number
  name: string
  description: string
}

// A ref to hold the list of source types, with German content.
// In a real application, this data would be fetched from an API.
const sourceTypes: Ref<SourceType[]> = ref([
  { id: 1, name: 'DGE-/EFSA-Aussagen', description: '<PERSON>chtlich geprüfte, offiziell erlaubte Aussagen.' },
  { id: 2, name: 'Peer-reviewed Studien', description: 'Mit "laut wissenschaftlicher Studien" gekennzeichnet.' },
  { id: 3, name: 'Fachliteratur', description: '<PERSON><PERSON> "laut anerkannter Fachliteratur" gekennzeichnet.' },
])

// Use Nuxt's useToast composable for user feedback.
const toast = useToast()

/**
 * Moves an element within an array from a given index to another.
 * @param array The array to modify.
 * @param fromIndex The index of the element to move.
 * @param toIndex The target index for the element.
 */
function move(array: any[], fromIndex: number, toIndex: number) {
  // Ensure the target index is within the array bounds.
  if (toIndex < 0 || toIndex >= array.length) {
    return
  }
  // Remove the item from its original position and insert it at the new position.
  const item = array.splice(fromIndex, 1)[0]
  array.splice(toIndex, 0, item)
}

/**
 * Moves a source type up in the priority list.
 * @param index The current index of the source type.
 */
function moveUp(index: number) {
  move(sourceTypes.value, index, index - 1)
}

/**
 * Moves a source type down in the priority list.
 * @param index The current index of the source type.
 */
function moveDown(index: number) {
  move(sourceTypes.value, index, index + 1)
}

/**
 * Handles saving the new prioritization order.
 * In a real application, this would make an API call to the backend.
 */
function savePrioritization() {
  console.log(
    'Saving prioritization:',
    sourceTypes.value.map((st) => st.name),
  )
  toast.add({
    title: 'Priorisierung gespeichert!',
    description: 'Die Reihenfolge der Datenquellen wurde erfolgreich aktualisiert.',
    color: 'success',
  })
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardHeader>
        <template #title> Priorisierung der Datenquellen </template>
        <template #description>
          Legen Sie die Reihenfolge fest, in der die Datenquellen für die Beantwortung von Anfragen herangezogen werden.
        </template>
      </UDashboardHeader>

      <UCard>
        <template #header>
          <h3 class="text-base font-semibold">Antwort-Hierarchie</h3>
        </template>

        <div class="flex flex-col gap-4">
          <div
            v-for="(sourceType, index) in sourceTypes"
            :key="sourceType.id"
            class="flex items-center justify-between rounded-lg border border-gray-200 p-4 dark:border-gray-700"
          >
            <div class="flex items-center gap-4">
              <div class="text-lg font-bold text-gray-500 dark:text-gray-400">
                {{ index + 1 }}
              </div>
              <div>
                <p class="font-semibold">
                  {{ sourceType.name }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ sourceType.description }}
                </p>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <UButton
                icon="i-lucide-arrow-up"
                size="sm"
                color="neutral"
                variant="ghost"
                :disabled="index === 0"
                @click="moveUp(index)"
              />
              <UButton
                icon="i-lucide-arrow-down"
                size="sm"
                color="neutral"
                variant="ghost"
                :disabled="index === sourceTypes.length - 1"
                @click="moveDown(index)"
              />
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end">
            <UButton
              color="primary"
              @click="savePrioritization"
            >
              Priorisierung speichern
            </UButton>
          </div>
        </template>
      </UCard>
    </UDashboardPanel>
  </UDashboardPage>
</template>
