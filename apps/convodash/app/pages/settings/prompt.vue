<script setup lang="ts">
// Default system prompt in German. This would typically be fetched from a database.
const systemPrompt = ref(
  'Du bist ein freundlicher und hilfsbereiter KI-Gesundheitsassistent. Antworte nur auf Basis der dir zur Verfügung gestellten wissenschaftlichen Quellen. Gib niemals medizinischen Rat oder Diagnosen. Formuliere deine Antworten klar, verständlich und ohne Heilversprechen. Jede fachliche Aussage muss mit einer Quelle belegt werden.',
)

// Use Nuxt's useToast composable for user feedback.
const toast = useToast()

// Function to handle saving the prompt.
// In a real application, this would make an API call to the backend.
function savePrompt() {
  console.log('Saving prompt:', systemPrompt.value)
  toast.add({
    title: 'Prompt gespeichert!',
    description: 'Der System-Prompt wurde erfolgreich aktualisiert.',
    color: 'success',
  })
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardHeader>
        <template #title> System-Prompt </template>
        <template #description>
          Passe den System-Prompt an, um das Verhalten und die Persönlichkeit des Chatbots zu steuern.
        </template>
      </UDashboardHeader>

      <UCard>
        <template #header>
          <h3 class="text-base font-semibold">Prompt-Konfiguration</h3>
        </template>

        <UFormGroup
          label="System-Prompt"
          description="Dieser Text gibt dem KI-Modell die grundlegenden Anweisungen für sein Verhalten in jeder Konversation."
          class="grid grid-cols-1 gap-2"
        >
          <UTextarea
            v-model="systemPrompt"
            :rows="10"
            placeholder="Beschreibe hier die Rolle und die Anweisungen für den KI-Assistenten..."
            size="lg"
          />
        </UFormGroup>

        <template #footer>
          <div class="flex justify-end">
            <UButton
              color="primary"
              @click="savePrompt"
            >
              Prompt speichern
            </UButton>
          </div>
        </template>
      </UCard>
    </UDashboardPanel>
  </UDashboardPage>
</template>
