<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import type { FormSubmitEvent } from '@nuxt/ui'
import * as z from 'zod'

// Types
interface SourceType {
  id: number
  name: string
  description: string
  priority: number
}

interface Source {
  id: number
  title: string
  type: string
  author: string
  date: string
  url?: string
  notes?: string
  status: 'active' | 'inactive' | 'pending'
}

// Mock data
const sourceTypes = ref<SourceType[]>([
  {
    id: 1,
    name: 'DGE-/EFSA-Aussagen',
    description: 'Recht<PERSON> geprüfte, offiziell erlaubte Aussagen mit höchster Priorität.',
    priority: 1,
  },
  {
    id: 2,
    name: 'Peer-reviewed Studien',
    description: 'Mit "laut wissenschaftlicher Studien" gekennzeichnet.',
    priority: 2,
  },
  {
    id: 3,
    name: 'Fachliteratur',
    description: 'Mit "laut anerkannter Fachliteratur" gekennzeichnet.',
    priority: 3,
  },
])

const sources = ref<Source[]>([
  {
    id: 1,
    title: 'Vitamin D and health',
    type: 'Peer-reviewed Studien',
    author: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>.',
    date: '2007-03-16',
    url: 'https://pubmed.ncbi.nlm.nih.gov/17368179/',
    status: 'active',
  },
  {
    id: 2,
    title: 'Dietary Reference Intakes for Calcium and Vitamin D',
    type: 'Fachliteratur',
    author: 'Institute of Medicine',
    date: '2011-11-29',
    status: 'active',
  },
  {
    id: 3,
    title: 'Scientific Opinion on Dietary Reference Values for vitamin D',
    type: 'DGE-/EFSA-Aussagen',
    author: 'EFSA Panel on Dietetic Products',
    date: '2016-10-25',
    url: 'https://efsa.onlinelibrary.wiley.com/doi/abs/10.2903/j.efsa.2016.4547',
    status: 'active',
  },
])

// Modal states
const isSourceTypeModalOpen = ref(false)
const isSourceModalOpen = ref(false)
const isDeleteModalOpen = ref(false)
const selectedItem = ref<SourceType | Source | null>(null)
const editingItem = ref<SourceType | Source | null>(null)

// Search and filter
const sourceSearch = ref('')
const sourceTypeFilter = ref('all')

// Toast
const toast = useToast()

// Schemas
const sourceTypeSchema = z.object({
  name: z.string().min(2, 'Name muss mindestens 2 Zeichen lang sein'),
  description: z.string().min(10, 'Beschreibung muss mindestens 10 Zeichen lang sein'),
  priority: z.number().min(1).max(10),
})

const sourceSchema = z.object({
  title: z.string().min(5, 'Titel muss mindestens 5 Zeichen lang sein'),
  type: z.string().min(1, 'Typ ist erforderlich'),
  author: z.string().min(2, 'Autor ist erforderlich'),
  date: z.string().min(1, 'Datum ist erforderlich'),
  url: z.string().url('Ungültige URL').optional().or(z.literal('')),
  notes: z.string().optional(),
  status: z.enum(['active', 'inactive', 'pending']),
})

type SourceTypeSchema = z.output<typeof sourceTypeSchema>
type SourceSchema = z.output<typeof sourceSchema>

// Form states
const sourceTypeState = reactive({
  name: '',
  description: '',
  priority: 1,
})

const sourceState = reactive({
  title: '',
  type: '',
  author: '',
  date: '',
  url: '',
  notes: '',
  status: 'active' as 'active' | 'inactive' | 'pending',
})

// Computed
const filteredSources = computed(() => {
  let filtered = sources.value

  if (sourceSearch.value) {
    const search = sourceSearch.value.toLowerCase()
    filtered = filtered.filter(
      (source) =>
        source.title.toLowerCase().includes(search) ||
        source.author.toLowerCase().includes(search) ||
        source.type.toLowerCase().includes(search),
    )
  }

  if (sourceTypeFilter.value !== 'all') {
    filtered = filtered.filter((source) => source.type === sourceTypeFilter.value)
  }

  return filtered
})

const sourceTypeOptions = computed(() => [
  { label: 'Alle Typen', value: 'all' },
  ...sourceTypes.value.map((type) => ({ label: type.name, value: type.name })),
])

const sourceTypeSelectOptions = computed(() =>
  sourceTypes.value.map((type) => ({ label: type.name, value: type.name })),
)

// Methods
function closeAllModals() {
  isSourceTypeModalOpen.value = false
  isSourceModalOpen.value = false
  isDeleteModalOpen.value = false
}

function openSourceTypeModal(item?: SourceType) {
  closeAllModals()
  if (item) {
    editingItem.value = item
    sourceTypeState.name = item.name
    sourceTypeState.description = item.description
    sourceTypeState.priority = item.priority
  } else {
    editingItem.value = null
    sourceTypeState.name = ''
    sourceTypeState.description = ''
    sourceTypeState.priority = sourceTypes.value.length + 1
  }
  isSourceTypeModalOpen.value = true
}

function openSourceModal(item?: Source) {
  closeAllModals()
  if (item) {
    editingItem.value = item
    sourceState.title = item.title
    sourceState.type = item.type
    sourceState.author = item.author
    sourceState.date = item.date
    sourceState.url = item.url || ''
    sourceState.notes = item.notes || ''
    sourceState.status = item.status as 'active' | 'inactive' | 'pending'
  } else {
    editingItem.value = null
    sourceState.title = ''
    sourceState.type = 'DGE-/EFSA-Aussagen'
    sourceState.author = ''
    sourceState.date = new Date().toISOString().split('T')[0] || new Date().toISOString()
    sourceState.url = ''
    sourceState.notes = ''
    sourceState.status = 'active'
  }
  isSourceModalOpen.value = true
}

function openDeleteModal(item: SourceType | Source) {
  closeAllModals()
  selectedItem.value = item
  isDeleteModalOpen.value = true
}

async function onSourceTypeSubmit(event: FormSubmitEvent<SourceTypeSchema>) {
  try {
    if (editingItem.value) {
      // Update existing
      const index = sourceTypes.value.findIndex((t) => t.id === (editingItem.value as SourceType).id)
      if (index !== -1 && sourceTypes.value[index]) {
        sourceTypes.value[index].name = event.data.name
        sourceTypes.value[index].description = event.data.description
        sourceTypes.value[index].priority = event.data.priority
      }
      toast.add({
        title: 'Erfolg',
        description: 'Quellentyp erfolgreich aktualisiert',
        color: 'success',
      })
    } else {
      // Create new
      const newId = Math.max(...sourceTypes.value.map((t) => t.id), 0) + 1
      const newSourceType: SourceType = {
        id: newId,
        name: event.data.name,
        description: event.data.description,
        priority: event.data.priority,
      }
      sourceTypes.value.push(newSourceType)
      toast.add({
        title: 'Erfolg',
        description: 'Neuer Quellentyp erfolgreich hinzugefügt',
        color: 'success',
      })
    }
    isSourceTypeModalOpen.value = false
  } catch (error) {
    toast.add({
      title: 'Fehler',
      description: 'Ein Fehler ist aufgetreten',
      color: 'error',
    })
  }
}

async function onSourceSubmit(event: FormSubmitEvent<SourceSchema>) {
  try {
    if (editingItem.value) {
      // Update existing
      const index = sources.value.findIndex((s) => s.id === (editingItem.value as Source).id)
      if (index !== -1 && sources.value[index]) {
        sources.value[index].title = event.data.title
        sources.value[index].type = event.data.type
        sources.value[index].author = event.data.author
        sources.value[index].date = event.data.date
        sources.value[index].url = event.data.url ?? undefined
        sources.value[index].notes = event.data.notes ?? undefined
        sources.value[index].status = event.data.status
      }
      toast.add({
        title: 'Erfolg',
        description: 'Quelle erfolgreich aktualisiert',
        color: 'success',
      })
    } else {
      // Create new
      const newId = Math.max(...sources.value.map((s) => s.id), 0) + 1
      const newSource: Source = {
        id: newId,
        title: event.data.title,
        type: event.data.type,
        author: event.data.author,
        date: event.data.date,
        url: event.data.url,
        notes: event.data.notes,
        status: event.data.status,
      }
      sources.value.push(newSource)
      toast.add({
        title: 'Erfolg',
        description: 'Neue Quelle erfolgreich hinzugefügt',
        color: 'success',
      })
    }
    isSourceModalOpen.value = false
  } catch (error) {
    toast.add({
      title: 'Fehler',
      description: 'Ein Fehler ist aufgetreten',
      color: 'error',
    })
  }
}

function confirmDelete() {
  if (!selectedItem.value) return

  if ('priority' in selectedItem.value) {
    // Delete source type
    const index = sourceTypes.value.findIndex((t) => t.id === selectedItem.value!.id)
    if (index !== -1) {
      sourceTypes.value.splice(index, 1)
      toast.add({
        title: 'Gelöscht',
        description: 'Quellentyp erfolgreich gelöscht',
        color: 'success',
      })
    }
  } else {
    // Delete source
    const index = sources.value.findIndex((s) => s.id === selectedItem.value!.id)
    if (index !== -1) {
      sources.value.splice(index, 1)
      toast.add({
        title: 'Gelöscht',
        description: 'Quelle erfolgreich gelöscht',
        color: 'success',
      })
    }
  }

  isDeleteModalOpen.value = false
  selectedItem.value = null
}

function getStatusColor(status: string): 'success' | 'error' | 'warning' | 'neutral' {
  switch (status) {
    case 'active':
      return 'success'
    case 'inactive':
      return 'error'
    case 'pending':
      return 'warning'
    default:
      return 'neutral'
  }
}

function getStatusLabel(status: string) {
  switch (status) {
    case 'active':
      return 'Aktiv'
    case 'inactive':
      return 'Inaktiv'
    case 'pending':
      return 'Ausstehend'
    default:
      return status
  }
}

definePageMeta({
  title: 'Wissensbasis & Quellen',
})
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="Wissensbasis & Quellen">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #description>
          Verwalten Sie die Informationsquellen und deren Priorisierung für den KI-Assistenten.
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="space-y-8">
        <!-- Source Types Section -->
        <div>
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Quellentypen</h2>
              <p class="text-sm text-gray-500 mt-1">
                Definieren Sie die verschiedenen Arten von Quellen und deren Priorität
              </p>
            </div>
            <UButton
              label="Neuen Typ hinzufügen"
              icon="i-heroicons-plus"
              color="success"
              @click="openSourceTypeModal()"
            />
          </div>

          <div class="grid gap-4">
            <UCard
              v-for="sourceType in sourceTypes"
              :key="sourceType.id"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <UBadge
                      :label="`Priorität ${sourceType.priority}`"
                      color="success"
                      variant="subtle"
                      size="sm"
                    />
                    <h3 class="font-semibold">{{ sourceType.name }}</h3>
                  </div>
                  <p class="text-sm text-gray-500">{{ sourceType.description }}</p>
                </div>

                <UDropdown
                  :items="[
                    [
                      {
                        label: 'Bearbeiten',
                        icon: 'i-heroicons-pencil-square',
                        click: () => openSourceTypeModal(sourceType),
                      },
                    ],
                    [
                      {
                        label: 'Löschen',
                        icon: 'i-heroicons-trash',
                        click: () => openDeleteModal(sourceType),
                      },
                    ],
                  ]"
                >
                  <UButton
                    color="neutral"
                    variant="ghost"
                    icon="i-heroicons-ellipsis-horizontal"
                  />
                </UDropdown>
              </div>
            </UCard>
          </div>
        </div>

        <!-- Sources Section -->
        <div>
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Quellen</h2>
              <p class="text-sm text-gray-500 mt-1">Verwalten Sie Ihre Informationsquellen und deren Details</p>
            </div>
            <UButton
              label="Neue Quelle hinzufügen"
              icon="i-heroicons-plus"
              color="success"
              @click="openSourceModal()"
            />
          </div>

          <!-- Filters -->
          <div class="flex flex-wrap items-center gap-4 mb-6">
            <UInput
              v-model="sourceSearch"
              placeholder="Quellen durchsuchen..."
              icon="i-heroicons-magnifying-glass"
              class="max-w-sm"
            />
            <USelect
              v-model="sourceTypeFilter"
              :options="sourceTypeOptions"
              placeholder="Nach Typ filtern"
              class="min-w-48"
            />
          </div>

          <div class="grid gap-4">
            <UCard
              v-for="source in filteredSources"
              :key="source.id"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <UBadge
                      :label="source.type"
                      color="success"
                      variant="subtle"
                      size="sm"
                    />
                    <UBadge
                      :label="getStatusLabel(source.status)"
                      :color="getStatusColor(source.status)"
                      variant="subtle"
                      size="sm"
                    />
                  </div>
                  <h3 class="font-semibold text-gray-900 dark:text-white mb-1">{{ source.title }}</h3>
                  <p class="text-sm text-gray-500 mb-2">
                    von {{ source.author }} • {{ new Date(source.date).toLocaleDateString('de-DE') }}
                  </p>
                  <div
                    v-if="source.url"
                    class="text-xs text-gray-500"
                  >
                    <ULink
                      :to="source.url"
                      target="_blank"
                      class="hover:text-primary-500"
                    >
                      {{ source.url }}
                    </ULink>
                  </div>
                  <div
                    v-if="source.notes"
                    class="text-sm mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded"
                  >
                    {{ source.notes }}
                  </div>
                </div>

                <UDropdown
                  :items="[
                    [
                      {
                        label: 'Bearbeiten',
                        icon: 'i-heroicons-pencil-square',
                        click: () => openSourceModal(source),
                      },
                    ],
                    [
                      {
                        label: 'Löschen',
                        icon: 'i-heroicons-trash',
                        click: () => openDeleteModal(source),
                      },
                    ],
                  ]"
                >
                  <UButton
                    color="neutral"
                    variant="ghost"
                    icon="i-heroicons-ellipsis-horizontal"
                  />
                </UDropdown>
              </div>
            </UCard>

            <div
              v-if="filteredSources.length === 0"
              class="text-center py-8"
            >
              <div class="text-gray-500">
                <UIcon
                  name="i-heroicons-magnifying-glass-minus"
                  class="w-8 h-8 mx-auto mb-4"
                />
                <p>Keine Quellen gefunden</p>
                <p class="text-sm mt-1">Versuchen Sie eine andere Suche oder fügen Sie eine neue Quelle hinzu.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </UDashboardPanel>

  <!-- Modals (outside of UDashboardPanel) -->
  <!-- Source Type Modal -->
  <UModal 
    v-model="isSourceTypeModalOpen"
    overlay
    class="max-w-2xl"
  >
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
            {{ editingItem ? 'Quellentyp bearbeiten' : 'Neuen Quellentyp hinzufügen' }}
          </h3>
          <UButton color="neutral" variant="ghost" icon="i-heroicons-x-mark-20-solid" class="-my-1" @click="isSourceTypeModalOpen = false" />
        </div>
      </template>
      <UForm
        :schema="sourceTypeSchema"
        :state="sourceTypeState"
        class="space-y-4"
        @submit="onSourceTypeSubmit"
      >
        <UFormGroup
          label="Name"
          name="name"
          required
        >
          <UInput
            v-model="sourceTypeState.name"
            placeholder="z.B. DGE-/EFSA-Aussagen"
          />
        </UFormGroup>

        <UFormGroup
          label="Beschreibung"
          name="description"
          required
        >
          <UTextarea
            v-model="sourceTypeState.description"
            placeholder="Beschreiben Sie den Quellentyp..."
            :rows="3"
          />
        </UFormGroup>

        <UFormGroup
          label="Priorität"
          name="priority"
          required
        >
          <UInput
            v-model.number="sourceTypeState.priority"
            type="number"
            min="1"
            max="10"
          />
        </UFormGroup>

        <div class="flex justify-end gap-3 pt-4">
          <UButton
            label="Abbrechen"
            color="neutral"
            variant="outline"
            @click="isSourceTypeModalOpen = false"
          />
          <UButton
            :label="editingItem ? 'Aktualisieren' : 'Hinzufügen'"
            color="primary"
            type="submit"
          />
        </div>
      </UForm>
    </UCard>
  </UModal>

  <!-- Source Modal -->
  <UModal 
    v-model="isSourceModalOpen"
    overlay
    class="max-w-2xl"
  >
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
            {{ editingItem ? 'Quelle bearbeiten' : 'Neue Quelle hinzufügen' }}
          </h3>
          <UButton color="neutral" variant="ghost" icon="i-heroicons-x-mark-20-solid" class="-my-1" @click="isSourceModalOpen = false" />
        </div>
      </template>
      <UForm
        :schema="sourceSchema"
        :state="sourceState"
        class="space-y-4"
        @submit="onSourceSubmit"
      >
        <UFormGroup
          label="Titel"
          name="title"
          required
        >
          <UInput
            v-model="sourceState.title"
            placeholder="Titel der Quelle"
          />
        </UFormGroup>

        <div class="grid grid-cols-2 gap-4">
          <UFormGroup
            label="Typ"
            name="type"
            required
          >
            <USelect
              v-model="sourceState.type"
              :options="sourceTypeSelectOptions"
              placeholder="Quellentyp auswählen"
            />
          </UFormGroup>

          <UFormGroup
            label="Status"
            name="status"
            required
          >
            <USelect
              v-model="sourceState.status"
              :options="[
                { label: 'Aktiv', value: 'active' },
                { label: 'Inaktiv', value: 'inactive' },
                { label: 'Ausstehend', value: 'pending' },
              ]"
            />
          </UFormGroup>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <UFormGroup
            label="Autor/Quelle"
            name="author"
            required
          >
            <UInput
              v-model="sourceState.author"
              placeholder="Autor oder Quelle"
            />
          </UFormGroup>

          <UFormGroup
            label="Datum"
            name="date"
            required
          >
            <UInput
              v-model="sourceState.date"
              type="date"
            />
          </UFormGroup>
        </div>

        <UFormGroup
          label="URL"
          name="url"
        >
          <UInput
            v-model="sourceState.url"
            placeholder="https://example.com"
            type="url"
          />
        </UFormGroup>

        <UFormGroup
          label="Notizen"
          name="notes"
        >
          <UTextarea
            v-model="sourceState.notes"
            placeholder="Zusätzliche Notizen zur Quelle..."
            :rows="3"
          />
        </UFormGroup>

        <div class="flex justify-end gap-3 pt-4">
          <UButton
            label="Abbrechen"
            color="neutral"
            variant="outline"
            @click="isSourceModalOpen = false"
          />
          <UButton
            :label="editingItem ? 'Aktualisieren' : 'Hinzufügen'"
            color="primary"
            type="submit"
          />
        </div>
      </UForm>
    </UCard>
  </UModal>

  <!-- Delete Confirmation Modal -->
  <UModal 
    v-model="isDeleteModalOpen"
    overlay
    class="max-w-md"
  >
    <UCard>
      <template #header>
        <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
          Löschen bestätigen
        </h3>
      </template>

      <div class="space-y-4">
        <div class="flex items-start gap-3 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-500" />
          <div>
            <p class="font-medium">
              Sind Sie sicher, dass Sie
              <span class="text-red-500">"{{ (selectedItem as any)?.title || (selectedItem as any)?.name }}"</span>
              löschen möchten?
            </p>
            <p class="text-sm text-gray-500 mt-1">Diese Aktion kann nicht rückgängig gemacht werden.</p>
          </div>
        </div>

        <div class="flex justify-end gap-3">
          <UButton
            label="Abbrechen"
            color="gray"
            variant="outline"
            @click="isDeleteModalOpen = false"
          />
          <UButton
            label="Löschen"
            color="error"
            @click="confirmDelete"
          />
        </div>
      </div>
    </UCard>
  </UModal>
</template>
