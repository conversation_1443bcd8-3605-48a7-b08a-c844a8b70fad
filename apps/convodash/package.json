{"name": "convodash", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "typecheck": "nuxt typecheck"}, "dependencies": {"@iconify-json/lucide": "^1.2.51", "@iconify-json/simple-icons": "^1.2.39", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.14.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.8", "@nuxt/ui-pro": "^3.1.3", "@nuxtjs/robots": "^5.2.11", "@unhead/vue": "^2.0.11", "@unovis/ts": "^1.5.2", "@unovis/vue": "^1.5.2", "@vueuse/nuxt": "^13.4.0", "date-fns": "^4.1.0", "nuxt": "^3.17.5", "zod": "^3.25.67"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "@nuxt/test-utils": "3.19.1", "@vue/test-utils": "^2.4.6", "eslint": "^9.29.0", "happy-dom": "^18.0.1", "playwright-core": "^1.53.2", "typescript": "^5.8.3", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}, "resolutions": {"unimport": "4.1.1"}, "pnpm": {"ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "maplibre-gl", "vue-demi"]}}