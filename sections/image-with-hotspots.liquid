{{ 'section-image-with-hotspots.css' | asset_url | stylesheet_tag }}

<div id="section-{{ section.id }}" class="container--large {% unless section.settings.remove_margin %} container--vertical-space {% endunless %} {% if section.settings.section_background_color != "rgba(0,0,0,0)" or section.settings.section_background_gradient != blank %} container--has-background {% endif %} remove-empty-space">

  {%- render 'section-heading', heading: section.settings.heading, subheading: section.settings.subheading, heading_tag: section.settings.seo_h_tag, layout: section.settings.section_heading_layout -%}

  {%- style -%}
    #section-{{ section.id }} .hotspot-tooltip,
    #section-{{ section.id }} .image-hotspots__spot--bullet::after {
      background-color: {{ section.settings.color_background }};
    }
    #section-{{ section.id }} .hotspot-tooltip::after {
      border-color: {{ section.settings.color_background }};
    }
    #section-{{ section.id }} .hotspot-tooltip {
      color: {{ section.settings.color_text }};
      --color-text-main: {{ section.settings.color_text }};
    }
  {%- endstyle -%}

  {%- liquid
    if section.index == 1
      assign preload = true
    else
      assign preload = false
    endif
  -%}

  <div class="image-with-hotspots-wrapper element--overflow-hidden">
    <div class="image-with-hotspots" style="width:100%">

      <div class="{% if section.settings.image_mobile %} element--hide-on-small {% endif %}">
        {%- if section.settings.image == blank -%}
          <span class="onboarding-svg">{{ 'collection-1' | placeholder_svg_tag }}</span>
        {%- else -%}
          {%- render 'lazy-image', image: section.settings.image, sizes: 'sizes="(max-width: 1360px) calc(100vw - 20px), 1280px"', preload: preload -%}
        {%- endif -%}
      </div>
      
      {%- if section.settings.image_mobile -%}
        <div class="element--hide-on-desk ">
          {%- render 'lazy-image', image: section.settings.image_mobile, sizes: 'sizes="(max-width: 1360px) calc(100vw - 20px), 1280px"', preload: preload -%}
        </div>
      {%- endif -%}

    </div>

    <image-hotspots class="image-hotspots text-size--small">

      {%- for block in section.blocks -%}

        {%- style -%}
          #spot-{{ block.id }} {
            top: {{ block.settings.vertical_offset_desktop }}%;
            left: {{ block.settings.horizontal_offset_desktop }}%;
          }
          {%- if section.settings.image_mobile -%}
          @media screen and (max-width: 767px) {
            #spot-{{ block.id }} {
              top: {{ block.settings.vertical_offset_mobile }}%;
              left: {{ block.settings.horizontal_offset_mobile }}%;
              {% if block.settings.mobile_hide %}
                display: none !important;
              {% endif %}
            }
          }
          {%- endif -%}
        {%- endstyle -%}
          
        {%- case block.type -%}

          {%- when 'product' -%}
            <div id="spot-{{ block.id }}" class="image-hotspots__spot" {{ block.shopify_attributes }}>
              <span class="image-hotspots__spot--bullet">&#8226;</span>
              <div class="image-hotspots__spot--content image-hotspots__spot--content-{{ block.settings.tooltip_position_desktop }} image-hotspots__spot--content-{{ block.settings.tooltip_position_mobile }}-mobile">
                {%- assign product = block.settings.product -%}
                <a href="{{ product.url }}" class="hotspot-tooltip">
                  <div class="hotspot-tooltip--product">
                    
                    <div class="hotspot-tooltip--image">
                      {%- liquid 
                        unless product == blank
                          render 'lazy-image-small', image: product.featured_media, aspect_ratio: settings.cart_image_ratio, fit: settings.cart_image_fit
                        else
                          echo 'product-1' | placeholder_svg_tag
                        endunless
                      -%}
                    </div>

                    <div class="caption">
                      <div>

                        <span class="price">
                          <span style="display:flex;flex-direction:row">

                            {%- liquid

                              unless product == blank

                                if settings.show_currency_codes
                                  assign iso_code = localization.country.currency.iso_code | prepend: ' ' 
                                endif

                                assign price = product.price | money | append: iso_code

                                if product.price_varies
                                  if product.compare_at_price > product.price
                                    echo 'products.grid.on_sale_from_html' | t: price: price
                                  else
                                    echo 'products.grid.from_text_html' | t: price: price
                                  endif
                                else
                                  if product.compare_at_price > product.price
                                    echo price
                                    echo '<del>'
                                      echo product.compare_at_price | money | append: iso_code
                                    echo '</del>'
                                  else 
                                    echo price
                                  endif
                                endif

                              else 
                                echo 9999 | money
                              endunless

                            -%}
                          </span>
                        </span>

                        <span class="title"><span class="text-animation--underline-thin">
                          {%- liquid 
                            unless product == blank
                              echo product.title 
                            else
                              echo 'general.onboarding.product_title' | t
                            endunless
                          -%}
                        </span></span>

                      </div>
                    </div>
                  </div>
                </a>
              </div>
            </div>

          {%- when 'text' -%}
            <div id="spot-{{ block.id }}" class="image-hotspots__spot" {{ block.shopify_attributes }}>
              <span class="image-hotspots__spot--bullet">&#8226;</span>
              <div class="image-hotspots__spot--content image-hotspots__spot--content-{{ block.settings.tooltip_position_desktop }} image-hotspots__spot--content-{{ block.settings.tooltip_position_mobile }}-mobile">
                <div class="hotspot-tooltip">
                  <div class="hotspot-tooltip--rte">
                    {%- if block.settings.title != blank -%}
                      <span class="title">{{ block.settings.title | escape }}</span>
                    {%- endif -%}
                    {%- if block.settings.description != blank -%}
                      <div class="description text-line-height--medium remove-empty-space rte">
                        {{ block.settings.description }}
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>

        {%- endcase -%}

      {%- endfor -%}

    </image-hotspots>

  </div>
</div>

<script src="{{ 'component-image-hotspots.js' | asset_url }}" defer></script>

{%- if section.settings.section_background_color != "rgba(0,0,0,0)" -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background-color: {{ section.settings.section_background_color }};
    }
  {% endstyle %}
{%- endif -%}
{%- if section.settings.section_background_gradient != blank -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background: {{ section.settings.section_background_gradient }};
    }
  {% endstyle %}
{%- endif -%}
{%- if section.settings.section_heading_color != "rgba(0,0,0,0)" -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      --color-text-main: {{ section.settings.section_heading_color }};
    }
  {% endstyle %}
{%- endif -%}

{% schema %}
  {
    "name": "t:sections.refactor_words.settings.image_with_hotspots.label",
    "max_blocks": 10,
    "settings": [
      {
        "type": "header",
        "content": "t:local-march-update.labels.heading"
      },
      {
        "type": "text",
        "id": "heading",
        "label": "t:sections.local-extra-words.sections.headings.heading",
        "default": "Image with hotspots"
      },
      {
        "type": "text",
        "id": "subheading",
        "label": "t:sections.local-extra-words.sections.headings.subheading",
        "default": "Promote your best products"
      },
      {
        "type": "select",
        "id": "section_heading_layout",
        "label": "t:sections.rich-text.settings.text_alignment.label",
        "options": [
          {
            "value": "section-heading--left",
            "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
          },
          {
            "value": "section-heading--center",
            "label": "t:sections.rich-text.settings.text_alignment.options__2.label"
          }
        ],
        "default": "section-heading--left"
      },
      {
        "type": "image_picker",
        "id": "image",
        "label": "t:sections.main-list-collections.blocks.collection.settings.image.label"
      },
      {
        "type": "image_picker",
        "id": "image_mobile",
        "label": "t:sections.refactor_words.settings.image_with_hotspots.image_on_mobile",
        "info": "t:sections.refactor_words.labels.optional"
      },
      {
        "type": "header",
        "content": "t:sections.split-extra-words.settings_schema.colors.headings.custom_colors"
      },
      {
        "type": "color",
        "id": "color_text",
        "label": "t:sections.split-extra-words.settings_schema.colors.settings.text",
        "default": "#000000"
      },
      {
        "type": "color",
        "id": "color_background",
        "label": "t:sections.split-extra-words.settings_schema.colors.settings.background",
        "default": "#ffffff"
      },
      {
        "type": "header",
        "content": "t:local-march-update.labels.section_design"
      },
      {
        "type": "color",
        "id": "section_background_color",
        "label": "t:sections.local-extra-words.settings_schema.product-card.badges.custom_badges.color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color_background",
        "id": "section_background_gradient",
        "label": "t:local-230.background_gradient"
      },
      {
        "type": "color",
        "id": "section_heading_color",
        "label": "t:local-223.heading_text_color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "checkbox",
        "id": "remove_margin",
        "label": "t:sections.local-extra-words.sections.rich-text.settings.remove_margin.label",
        "default": false
      },
      {
        "type": "header",
        "content": "t:sections.refactor_words.seo.name"
      },
      {
        "type": "select",
        "id": "seo_h_tag",
        "label": "t:sections.refactor_words.seo.label",
        "info": "t:sections.refactor_words.seo.info",
        "options": [
          {
            "value": "h1",
            "label": "H1"
          },
          {
            "value": "h2",
            "label": "H2"
          },
          {
            "value": "h3",
            "label": "H3"
          },
          {
            "value": "h4",
            "label": "H4"
          },
          {
            "value": "span",
            "label": "span"
          }
        ],
        "default": "h3"
      }
    ],
    "blocks": [
      {
        "type": "product",
        "name": "t:sections.shop-the-look.blocks.product.name",
        "settings": [
          {
            "type": "product",
            "id": "product",
            "label": "t:sections.shop-the-look.blocks.product.settings.select_product.label"
          },
          {
            "type": "header",
            "content": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.label_desktop_offset"
          },
          {
            "type": "range",
            "id": "vertical_offset_desktop",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_vertical",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          },
          {
            "type": "range",
            "id": "horizontal_offset_desktop",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_horizontal",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          }, 
          {
            "type": "header",
            "content": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.label_mobile_offset",
            "info": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.mobile_info"
          }, 
          {
            "type": "range",
            "id": "vertical_offset_mobile",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_vertical",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          }, 
          {
            "type": "range",
            "id": "horizontal_offset_mobile",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_horizontal",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          }, 
          {
            "type": "checkbox",
            "id": "mobile_hide",
            "label": "t:local-230.hide_on_mobile",
            "default": false
          },
          {
            "type": "header",
            "content": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.label"
          }, 
          {
            "type": "select",
            "id": "tooltip_position_desktop",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.label",
            "options": [
              {
                "value": "top",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_1"
              }, 
              {
                "value": "bottom",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_2"
              }, 
              {
                "value": "left",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_3"
              }, 
              {
                "value": "right",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_4"
              }
            ],
            "default": "top"
          },
          {
            "type": "select",
            "id": "tooltip_position_mobile",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.position_on_mobile",
            "options": [
              {
                "value": "top",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_1"
              }, 
              {
                "value": "bottom",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_2"
              }, 
              {
                "value": "left",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_3"
              }, 
              {
                "value": "right",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_4"
              }
            ],
            "default": "top"
          }
        ]
      }, 
      {
        "type": "text",
        "name": "t:sections.rich-text.blocks.text.name",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "t:sections.split-extra-words.settings_schema.headings.title",
            "default": "Title"
          },
          {
            "type": "richtext",
            "id": "description",
            "label": "t:sections.footer.blocks.text.settings.content.label",
            "default": "<p>Describe the item</p>"
          },
          {
            "type": "header",
            "content": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.label_desktop_offset"
          },
          {
            "type": "range",
            "id": "vertical_offset_desktop",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_vertical",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          }, 
          {
            "type": "range",
            "id": "horizontal_offset_desktop",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_horizontal",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          }, 
          {
            "type": "header",
            "content": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.label_mobile_offset",
            "info": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.mobile_info"
          }, 
          {
            "type": "range",
            "id": "vertical_offset_mobile",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_vertical",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          }, 
          {
            "type": "range",
            "id": "horizontal_offset_mobile",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.offset_horizontal",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 50
          }, 
          {
            "type": "checkbox",
            "id": "mobile_hide",
            "label": "t:local-230.hide_on_mobile",
            "default": false
          },
          {
            "type": "header",
            "content": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.label"
          }, 
          {
            "type": "select",
            "id": "tooltip_position_desktop",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.label",
            "options": [
              {
                "value": "top",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_1"
              }, 
              {
                "value": "bottom",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_2"
              }, 
              {
                "value": "left",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_3"
              }, 
              {
                "value": "right",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_4"
              }
            ],
            "default": "top"
          },
          {
            "type": "select",
            "id": "tooltip_position_mobile",
            "label": "t:sections.refactor_words.settings.image_with_hotspots.position_on_mobile",
            "options": [
              {
                "value": "top",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_1"
              }, 
              {
                "value": "bottom",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_2"
              }, 
              {
                "value": "left",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_3"
              }, 
              {
                "value": "right",
                "label": "t:sections.refactor_words.settings.image_with_hotspots.hotspot.tooltip.position.option_4"
              }
            ],
            "default": "top"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "t:sections.refactor_words.settings.image_with_hotspots.label",
        "blocks": [
          {
            "type": "product"
          }
        ]
      }
    ],
    "disabled_on": {
      "groups": ["header"]
    }
  }
{% endschema %}