{{ 'section-main-article.css' | asset_url | stylesheet_tag }}
{{ 'component-product-item.css' | asset_url | stylesheet_tag }}

{%- assign date_format = 'general.date_format.month_day_year' | t -%}

{%- for block in section.blocks -%}

  {%- liquid
    if forloop.first 
      assign container_space_class = 'container--vertical-space-small'
    else
      assign container_space_class = 'container--vertical-space-xsmall'
    endif
  -%}

  {%- case block.type -%}

    {%- when 'title' -%}
    
      <div class="container container--medium {{ container_space_class }} main-page" {{ block.shopify_attributes }}>
        <header>
          <div class="rte article">
            <h1 class="h2">{{ article.title | escape }}</h1>
            <div class="blog-item__meta">

              {%- if block.settings.blog_show_date -%}
                <span class="blog-item__date text-size--small">{{ article.published_at | date: date_format }}</span>
              {%- endif -%}

              {%- if block.settings.blog_show_author -%}
                <span class="blog-item__author text-size--small">{{ article.author | escape }}</span>
              {%- endif -%}

              {%- if block.settings.blog_show_comments -%}
                <span class="blog-item__date text-size--small">{{ 'blog.grid.comments_count' | t: count: article.comments_count }}</span>
              {%- endif -%}
              
            </div>
          </div>
        </header>
      </div>
    
    {%- when 'featured_image' -%}

      {%- if article.image -%}
        <div class="container--{% if block.settings.is_fullwidth %}large{% else %}medium{% endif %} {{ container_space_class }} container--no-margin-if-not-first" {{ block.shopify_attributes }}>
          <div class="article">
            <div class="article__featured-image element--border-radius">
              {%- liquid
                if block.settings.aspect_ratio == 'natural'
                  assign aspect_ratio = article.image.aspect_ratio
                else 
                  assign aspect_ratio = block.settings.aspect_ratio
                endif
                assign sizes = 'sizes="(max-width: 1360px) 100vw, 1280px"'
                render 'lazy-image', image: article.image.src, alt: article.title, ratio: aspect_ratio, sizes: sizes, preload: true
              %}
            </div>
          </div>
        </div>
      {%- endif -%}
    
    {%- when 'content' -%}

      <div class="container container--medium {{ container_space_class }} main-page" {{ block.shopify_attributes }}>

        <div class="article {% if block.settings.has_social_buttons or block.settings.has_tags or block.settings.cta_products != blank %} article--indent-for-meta {% endif %}" >

          {%- if block.settings.has_social_buttons or block.settings.has_tags or block.settings.cta_products != blank -%}

            <div class="article__meta">
              <div class="article__meta__sticky">

                {%- if block.settings.cta_products != blank -%}
                  <div class="article__cta-products grid grid-1 palm-1">
                    {%- for product in block.settings.cta_products -%}
                      {%- render 'simple-product-item',
                        product: product,
                        blank_product: false,
                        rendered_from_info_article: true,
                        show_title: block.settings.product_card_show_title,
                        show_vendor: block.settings.product_card_show_vendor,
                        show_price: block.settings.product_card_show_price,
                        show_rating: block.settings.product_card_show_rating,
                        show_quick_buy: block.settings.product_card_show_quick_buy,
                        show_local_availability: block.settings.product_card_show_local_availability,
                        show_icons: block.settings.product_card_show_icons,
                        show_badges: block.settings.show_badges,
                        layout: 'grid-1',
                        mobile_layout: 'palm-1',
                        preload: true,
                        product_icon_1_image: block.settings.product_icon_1_image,
                        product_icon_1_label: block.settings.product_icon_1_label,
                        product_icon_2_image: block.settings.product_icon_2_image,
                        product_icon_2_label: block.settings.product_icon_2_label,
                        product_icon_3_image: block.settings.product_icon_3_image,
                        product_icon_3_label: block.settings.product_icon_3_label,
                        product_icon_4_image: block.settings.product_icon_4_image,
                        product_icon_4_label: block.settings.product_icon_4_label,
                        product_icon_5_image: block.settings.product_icon_5_image,
                        product_icon_5_label: block.settings.product_icon_5_label,
                        product_icon_6_image: block.settings.product_icon_6_image,
                        product_icon_6_label: block.settings.product_icon_6_label,
                      -%}
                    {%- endfor -%}
                  </div>
                {%- endif -%}

                {%- if block.settings.has_social_buttons -%}

                  <div class="article__meta-social">
                    <span class="text-size--large">{{ 'general.sharing.title' | t }}</span>
                    <div class="social-icons gutter-top--small">

                      {%- liquid
                        assign share_link = shop.url | append: article.url
                        assign share_title = article.title | url_param_escape
                        if article.image
                          assign share_image = article.image | image_url: width: 1024
                        else  
                          assign share_image = page_image | image_url: width: 1024
                        endif
                      -%}

                      <a href="//www.facebook.com/sharer.php?u={{ share_link }}" target="_blank">
                        <span class="visually-hidden">{{ 'general.sharing.facebook' | t }}</span>
                        <span class="icon" aria-hidden="true">{%- render 'theme-symbols', icon: 'facebook' -%}</span>
                      </a>

                      <a href="//twitter.com/intent/tweet?text={{ share_title }}&amp;url={{ share_link }}" target="_blank">
                        <span class="visually-hidden">{{ 'general.sharing.twitter' | t }}</span>
                        <span class="icon" aria-hidden="true">{%- render 'theme-symbols', icon: 'twitter' -%}</span
                      ></a>

                      <a href="//pinterest.com/pin/create/button/?url={{ share_link }}&amp;media={{ share_image }}&amp;description={{ share_title }}" target="_blank">
                        <span class="visually-hidden">{{ 'general.sharing.pinterest' | t }}</span>
                        <span class="icon" aria-hidden="true">{%- render 'theme-symbols', icon: 'pinterest' -%}</span>
                      </a>

                    </div>
                  </div>

                {%- endif -%}

                {%- if block.settings.has_tags -%}

                  {%- unless article.tags == blank -%}
                    <div class="article__meta-tags">
                      <span class="text-size--large">{{ 'blog.grid.tags_label' | t }}</span>
                      <div class="tags gutter-top--small">
                        {% for tag in article.tags %}
                          <a class="button button--outline button--small" href="{{ blog.url }}/tagged/{{ tag.handle }}">{{ tag }}</a>
                        {% endfor %}
                      </div>
                    </div>
                  {%- endunless -%}
          
                {%- endif -%}

              </div>
            </div>
          {%- endif -%}
          
          <div class="article__content rte">
            {{ article.content }}
          </div>

          {%- if block.settings.enhance_product_links -%}
            <script>
              document.querySelectorAll('.article__content a[href]').forEach((link, i)=>{
                if ( link.getAttribute('href').includes('products') ) {
                  const enhancedLink = document.createElement('quick-view-product');
                  enhancedLink.innerHTML = `<a href="${link.href}" data-js-product-add-to-cart data-id="product-{{ block.id }}-${i}">
                    ${link.textContent}
                  </a>`
                  link.parentNode.replaceChild(enhancedLink, link)
                }
              })
            </script> 
          {%- endif -%}

        </div>
      </div>

  {%- endcase -%}

{%- endfor -%}

{% comment %} TODO: Untranslated label {% endcomment %}
{% schema %}
{
  "name": "t:sections.local-extra-words.sections.main-article.name",
  "tag": "article",
  "class": "section section--article",
  "blocks": [
    {
      "type": "featured_image",
      "name": "t:sections.main-article.blocks.featured_image.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "aspect_ratio",
          "label": "t:settings_schema.product-grid.settings.aspect_ratio.label",
          "options": [
            {
              "value": "natural",
              "label": "t:sections.gallery.settings.aspect_ratio.options__5.label",
              "group": "t:sections.gallery.settings.aspect_ratio.options__5.group"
            },
            {
              "value": "1.33333",
              "label": "t:sections.gallery.settings.aspect_ratio.options__1.label",
              "group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
            },
            {
              "value": "1",
              "label": "t:sections.gallery.settings.aspect_ratio.options__2.label",
              "group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
            },
            {
              "value": "0.83333",
              "label": "t:sections.gallery.settings.aspect_ratio.options__3.label",
              "group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
            }
          ],
          "default": "natural"
        },
        {
          "type": "checkbox",
          "id": "is_fullwidth",
          "label": "t:sections.local-extra-words.sections.rich-text.settings.fullwidth.label",
          "default": true
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-article.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "blog_show_date",
          "label": "t:sections.main-article.blocks.title.settings.blog_show_date.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "blog_show_author",
          "label": "t:sections.main-article.blocks.title.settings.blog_show_author.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "blog_show_comments",
          "label": "t:sections.main-article.blocks.title.settings.blog_show_comments.label",
          "default": false
        }
      ]
    },
    {
      "type": "content",
      "name": "t:sections.main-article.blocks.content.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "has_social_buttons",
          "label": "t:sections.main-article.blocks.social_sharing.name",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "has_tags",
          "label": "t:sections.local-extra-words.sections.main-article.settings.show_tags",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "enhance_product_links",
          "label": "t:sections.local-extra-words.sections.main-article.settings.enhance_product_links.label",
          "info": "t:sections.local-extra-words.sections.main-article.settings.enhance_product_links.info",
          "default": false
        },
        {
          "type": "header",
          "content": "Product Cards"
        },
        {
          "type": "product_list",
          "id": "cta_products",
          "label": "CTA Products"
        },
        {
          "type": "checkbox",
          "id": "product_card_show_title",
          "label": "t:sections.local-extra-words.sections.product-card.blocks.title",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "product_card_show_price",
          "label": "t:sections.local-extra-words.sections.product-card.blocks.price",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "product_card_show_vendor",
          "label": "t:sections.local-extra-words.sections.product-card.blocks.vendor",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "product_card_show_rating",
          "label": "t:sections.local-extra-words.sections.product-card.blocks.rating",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "product_card_show_quick_buy",
          "label": "t:sections.local-extra-words.sections.product-card.blocks.quick_buy",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "product_card_show_local_availability",
          "label": "t:sections.local-extra-words.settings_schema.product-card.local-pickup.name",
          "default": false,
          "info": "t:sections.local-extra-words.settings_schema.product-card.local-pickup.info"
        },
        {
          "type": "checkbox",
          "id": "product_card_show_icons",
          "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.name",
          "default": false,
          "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.help"
        }
      ]
    }
  ]
}
{% endschema %}
