<div class="container--large container--vertical-space">

  {%- if section.settings.title != blank -%}
    <h1 class="title h2">{{ section.settings.title | escape }}</h1>
  {%- endif -%}

  {%- if section.settings.content != blank -%}
    <div class="rte">
      {{ section.settings.content }}
    </div>
  {%- endif -%}

  <p>{{ shop.password_message }}</p>

</div>

{% schema %}
	{
    "name": "t:sections.main-password-content.name",
    "settings": [
      {
        "type": "text",
        "id": "title",
        "label": "t:sections.popup.settings.title.label",
        "default": "Opening soon"
      },
      {
        "type": "richtext",
        "id": "content",
        "label": "t:sections.popup.settings.content.label"
      }
    ]
  }
{% endschema %}