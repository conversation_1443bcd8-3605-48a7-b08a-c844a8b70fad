{{ 'section-before-after.css' | asset_url | stylesheet_tag }}

<div id="section-{{ section.id }}">
  
  <div class="container--large {% unless section.settings.remove_margin %} container--vertical-space {% endunless %} {% if section.settings.section_background_color != "rgba(0,0,0,0)" or section.settings.section_background_gradient != blank %} container--has-background {% endif %} remove-empty-space">

    {%- render 'section-heading', heading: section.settings.heading, subheading: section.settings.subheading, button: section.settings.show_view_all, button_label: 'collections.view_all_collections', link: routes.collections_url, heading_tag: section.settings.seo_h_tag, layout: section.settings.section_heading_layout -%}

    {%- liquid
      if section.index == 1
        assign preload = true
      else
        assign preload = false
      endif
    -%}
  
    <before-after data-layout="{{ section.settings.layout }}" class="{%- if section.settings.invert_layout -%}invert-layout{%- endif -%}">

      {%- if section.blocks.size > 0 -%}

        <div class="before-after {{ section.settings.desktop_height }} {{ section.settings.mobile_height }} before-after--color-style-{{ section.settings.color_style }}">

          {%- for block in section.blocks -%}

            <div class="img {% if forloop.index == 2 %} foreground-img {% else %} background-img {% endif %}" id="{{ block.id }}" {{ block.shopify_attributes }}> 

              <div class="before-after__label-wrapper">
                {% if block.settings.label != blank %}
                <span class="before-after__label">
                  {{ block.settings.label | escape }}
                </span>
                {% endif %}
              
              {% liquid
              assign desktop_image_class = ""
              if block.settings.image_mobile != blank
                assign desktop_image_class = "element--hide-on-mobile"
              endif

              unless block.settings.image == blank

                render 'lazy-image', image: block.settings.image, type: 'background', image_alignment: true, id: block.id, class: desktop_image_class, sizes: 'sizes="100vw"', preload: preload

              else
                render 'lazy-svg', image: 'product-1', class: 'svg-placeholder svg-placeholder--background' 
              endunless
              %}

              {%- liquid
                if block.settings.image_mobile != blank
                  assign image_block = block.id | prepend: 'mobile-'
                  render 'lazy-image', image: block.settings.image_mobile, type: 'background', image_alignment: true, id: image_block, class: 'element--hide-on-desk', sizes: 'sizes="100vw"', preload: preload
                endif
              -%}

              </div>
            </div>
          {%- endfor -%}

          <input type="range" min="0" max="100" value="50" class="before-after__slider" orient="{{ section.settings.layout }}">
          <span class="before-after--handle">
            <span class="before-after--handle-icon">
              {%- render 'theme-symbols', icon: 'arrow-left-right' -%}
            </span>
          </span>

        </div>

      {%- endif -%}

    </before-after>

  </div>
  
</div>

{%- if section.settings.section_background_color != "rgba(0,0,0,0)" -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background-color: {{ section.settings.section_background_color }};
    }
  {% endstyle %}
{%- endif -%}
{%- if section.settings.section_background_gradient != blank -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background: {{ section.settings.section_background_gradient }};
    }
  {% endstyle %}
{%- endif -%}
{%- if section.settings.section_heading_color != "rgba(0,0,0,0)" -%}
  {% style %}
    #shopify-section-{{ section.id }} .section-heading {
      --color-text-main: {{ section.settings.section_heading_color }};
    }
  {% endstyle %}
{%- endif -%}

<script src="{{ 'component-before-after.js' | asset_url }}" defer></script>

{% schema %}
{
  "name": "t:sections.refactor_words.before_after.name",
  "class": "mount-video-background supports-absolute-header",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.local-extra-words.sections.headings.heading"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.local-extra-words.sections.headings.heading",
      "default": "Before & After"
    },
    {
      "type": "inline_richtext",
      "id": "subheading",
      "label": "t:sections.local-extra-words.sections.headings.subheading",
      "default": "Compare your <strong>best</strong> offers"
    },
    {
      "type": "select",
      "id": "section_heading_layout",
      "label": "t:sections.rich-text.settings.text_alignment.label",
      "options": [
        {
          "value": "section-heading--left",
          "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
        },
        {
          "value": "section-heading--center",
          "label": "t:sections.rich-text.settings.text_alignment.options__2.label"
        }
      ],
      "default": "section-heading--left"
    },
    {
      "type": "header",
      "content": "t:sections.local-extra-words.settings_schema.layout.name"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.local-extra-words.settings_schema.layout.name",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.refactor_words.before_after.layout.option__1"
        },
        {
          "value": "vertical",
          "label": "t:sections.refactor_words.before_after.layout.option__2"
        }
      ],
      "default": "horizontal"
    },
    {
      "type": "checkbox",
      "id": "invert_layout",
      "label": "t:sections.refactor_words.late_edits.before-after.layout.invert",
      "default": false
    },
    {
      "type": "select",
      "id": "desktop_height",
      "label": "Desktop height",
      "options": [
        {
          "value": "before-after--desktop-small",
          "label": "t:sections.image-with-text.settings.image_height.options__1.label"
        },
        {
          "value": "before-after--desktop-medium",
          "label": "t:sections.image-with-text.settings.image_height.options__2.label"
        },
        {
          "value": "before-after--desktop-large",
          "label": "t:sections.image-with-text.settings.image_height.options__3.label"
        }
      ],
      "default": "before-after--desktop-medium"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Mobile height",
      "options": [
        {
          "value": "before-after--mobile-small",
          "label": "t:sections.image-with-text.settings.image_height.options__1.label"
        },
        {
          "value": "before-after--mobile-medium",
          "label": "t:sections.image-with-text.settings.image_height.options__2.label"
        },
        {
          "value": "before-after--mobile-large",
          "label": "t:sections.image-with-text.settings.image_height.options__3.label"
        }
      ],
      "default": "before-after--mobile-medium"
    },
    {
      "type": "header",
      "content": "t:settings_schema.colors.name"
    },
    {
      "type": "select",
      "id": "color_style",
      "label": "t:sections.refactor_words.before_after.style.label",
      "options": [
        {
          "value": "dark",
          "label": "t:sections.refactor_words.before_after.style.option__2"
        },
        {
          "value": "light",
          "label": "t:sections.refactor_words.before_after.style.option__1"
        }
      ],
      "default": "dark"
    },
    {
      "type": "header",
      "content": "t:local-march-update.labels.section_design"
    },
    {
      "type": "color",
      "id": "section_background_color",
      "label": "t:sections.local-extra-words.settings_schema.product-card.badges.custom_badges.color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color_background",
      "id": "section_background_gradient",
      "label": "t:local-230.background_gradient"
    },
    {
      "type": "color",
      "id": "section_heading_color",
      "label": "t:local-223.heading_text_color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "checkbox",
      "id": "remove_margin",
      "label": "t:sections.local-extra-words.sections.rich-text.settings.remove_margin.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.refactor_words.seo.name"
    },
    {
      "type": "select",
      "id": "seo_h_tag",
      "label": "t:sections.refactor_words.seo.label",
      "info": "t:sections.refactor_words.seo.info",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        },
        {
          "value": "span",
          "label": "span"
        }
      ],
      "default": "h2"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "limit": 2,
      "name": "t:sections.local-extra-words.sections.media-with-text-overlay.blocks.image.name",
      "settings": [
        {
          "id": "image",
          "type": "image_picker",
          "label": "t:sections.gallery.blocks.image.settings.image.label"
        },
        {
          "id": "image_mobile",
          "type": "image_picker",
          "label": "t:sections.image.mobile_image"
        },
        {
          "type": "header",
          "content": "t:sections.local-extra-words.sections.headings.text_content"
        },
        {
          "type": "text",
          "id": "label",
          "label": "t:sections.refactor_words.before_after.image.label__3",
          "default": "Label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.refactor_words.before_after.name",
      "blocks": [
        { "type": "image", "settings": {
          "label": "Before"
        } },
        { "type": "image", "settings": {
          "label": "After"
        } }
      ]
    }
  ],
  "disabled_on": {
    "groups": ["header"]
  }
}
{% endschema %}
