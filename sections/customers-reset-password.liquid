{{ 'section-account.css' | asset_url | stylesheet_tag }}

<div class="container--large container--vertical-space" id="section-reset-password">

	<h1 class="h2">{{ 'customers.password_reset_page.title' | t }}</h1>

	<div class="account gutter-top--regular">

		{%- form 'reset_customer_password', class: 'account__form-block' -%}

			<span class="alert alert--note">
				{% # theme-check-disable %}{{ 'customers.password_reset_page.subtitle' | t: email: email }}{% # theme-check-enable %}
			</span>
			
			{%- render 'form-errors', form: form -%}
			
			<div class="account__form-row">
				<label for="ResetPassword" class="text-size--small">{{ 'customers.password_reset_page.password_label' | t }}</label>
				<input type="password" value="" name="customer[password]" id="ResetPassword" class="input-full{% if form.errors contains 'password' %} error{% endif %}">
			</div>
			
			<div class="account__form-row">
				<label for="PasswordConfirmation" class="text-size--small">{{ 'customers.password_reset_page.password_confirm_label' | t }}</label>
				<input type="password" value="" name="customer[password_confirmation]" id="PasswordConfirmation" class="input-full{% if form.errors contains 'password_confirmation' %} error{% endif %}">
			</div>

			<div class="account__form-row gutter-top--small">
				<input type="submit" class="button button--solid button--regular button--regular-mobile" value="{{ 'customers.password_reset_page.submit' | t }}">
			</div>

		{%- endform -%}

	</div>

</div>

{% schema %}
  {
    "name": "t:sections.local-extra-words.sections.customers.reset-password.name",
    "class": "section--remove-bottom-margin-after"
  }
{% endschema %}