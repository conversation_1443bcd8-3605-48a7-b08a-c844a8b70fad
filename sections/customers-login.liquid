{{ 'section-account.css' | asset_url | stylesheet_tag }}

{%- if section.settings.enable_shop_login_button == false -%}
  {%- assign hide_shop_login_button = true -%}
{%- endif -%}

<div class="container--large container--vertical-space">
	<div class="account">

			{%- form 'customer_login' -%}

				<h1 class="h2 gutter-bottom--regular">{{ 'customers.login_page.title' | t }}</h1>
			
				<div id="resetSuccess" style="display:none;">
					{%- liquid
						assign message = 'customers.login_page.password_reset.success_message' | t
						render 'form-success', message: message
					-%}
				</div>

				{%- render 'form-errors', form: form -%}

				<div class="account__form-block ">

					<div class="account__form-row">
						<label for="customer_email" class="hidden">{{ 'customers.login_page.form_email_label' | t }}</label>
						<input type="email" value="" name="customer[email]" id="customer_email" placeholder="{{ 'customers.login_page.form_email_label' | t }}" {% if form.errors contains "email" %} class="error"{% endif %} autocorrect="off" autocapitalize="off" autofocus>
					</div>
					
					{%- if form.password_needed -%}
						<div class="account__form-row">
							<label for="customer_password" class="hidden">{{ 'customers.login_page.form_password_label' | t }}</label>
							<input type="password" value="" name="customer[password]" id="customer_password" placeholder="{{ 'customers.login_page.form_password_label' | t }}" {% if form.errors contains "password" %} class="error"{% endif %}>
						</div>
					{%- endif -%}

					<div class="form-actions gutter-top--small">

						<div class="form-actions__login">

							<button type="submit" class="button button--solid button--regular button--regular-mobile"  >{{ 'customers.login_page.form_login_button' | t }}</button>

							<div name="sign-in-with-shop-provider" style="{% if hide_shop_login_button %}display:none;{% endif %}--buttons-radius:var(--border-radius-buttons)">
								{{ shop | login_button: hide_button: hide_shop_login_button }}
							</div>

						</div>

						<div class="
							{% if hide_shop_login_button %} 
								gutter-top--small text-align--right
							{% else %}
							 	gutter-top--regular
							{% endif %}
						">
							{% if form.password_needed %}
								<span style="display: block;" class="text-size--small"><a href="#recover" onclick="showRecoverPasswordForm();return false;">{{ 'customers.login_page.forgot_password_check' | t }}</a></span>
							{% endif %}
							<span style="display: block;"><strong>{{ 'customers.login_page.new_customer_button' | t | customer_register_link }}</strong></span>
						</div>

					</div>
				</div>
			{%- endform -%}

			<div id="recover_password" style="display: none;">

				<h3 class="h2 gutter-bottom--regular">{{ 'customers.login_page.password_reset.title' | t }}</h3>
				
				<span class="alert alert--note">
					{{ 'customers.login_page.password_reset.subtitle' | t }}
				</span>

				{% form 'recover_customer_password', class: 'simple-form' %}

					{%- render 'form-errors', form: form -%}

					{% if form.posted_successfully? %}
						{% assign reset_success = true %}
					{% endif %}

					<div class="account__form-block ">

						<div class="account__form-row">
							<label for="recover-email" class="hidden">{{ 'customers.login_page.password_reset.email_label' | t }}</label>
							<input type="email" value="" name="email" id="recover-email" placeholder="{{ 'customers.login_page.password_reset.email_label' | t }}" autocorrect="off" autocapitalize="off">
						</div>

						<div class="account__form-row account__form-row--flex form-actions gutter-top--small">

							<button type="submit" class="button button--solid button--regular  button--regular-mobile">{{ 'customers.login_page.password_reset.submit_button' | t }}</button>

							<a href="#" class="button button--outline button--regular button--regular-mobile" onclick="hideRecoverPasswordForm();return false;">{{ 'customers.login_page.password_reset.cancel_button' | t }}</a>

						</div>
					</div>
				{% endform %}

			</div>

			{% if shop.checkout.guest_login %}

				<h3 class="title h0" style="margin-top: 2em;">{{ 'customers.login_page.guest_check' | t }}</h3>

				{% form 'guest_login' %}
					<button type="submit" class="button button--solid button--wide" >{{ 'customers.login_page.guest_button' | t }}</button>
				{% endform %}
				
			{% endif %}

	</div>

</div>

<script>

	function showRecoverPasswordForm() {
		document.getElementById('recover_password').style.display = 'block';
		document.getElementById('customer_login').style.display='none';
	}

	function hideRecoverPasswordForm() {
		document.getElementById('recover_password').style.display = 'none';
		document.getElementById('customer_login').style.display = 'block';
	}

	if (window.location.hash == '#recover') { showRecoverPasswordForm() }

</script>

{% if reset_success %}
  <style type="text/css">
    #resetSuccess { 
      display: block !important;
      margin-top: 25px;
    }
  </style>
{% endif %}

{% schema %}
  {
    "name": "t:sections.local-extra-words.sections.customers.login.name",
    "class": "section--remove-bottom-margin-after",
		"settings": [
      {
        "type": "checkbox",
        "id": "enable_shop_login_button",
        "label": "t:sections.local-extra-words.sections.customers.login.shop_login_button.enable",
        "default": false
      }
    ]
  }
{% endschema %}