{%- if predictive_search.performed -%}

	<div class="search-results" data-js-search-results-holder>

    {%- if predictive_search.resources.queries.size > 0 or predictive_search.resources.collections.size > 0 -%}
      <div class="search-block">
        {%- for query in predictive_search.resources.queries -%}
          <a class="search-item" href="{{ query.url }}" data-js-search-item tabindex="-1">
            <span class="text-weight--bold"><span class="text-animation--underline-thin">{{ query.styled_text }}</span></span>
          </a>
        {%- endfor -%}
        {%- for collection in predictive_search.resources.collections -%}
          <a class="search-item" href="{{ collection.url }}" data-js-search-item tabindex="-1">
            <span class="text-weight--bold"><span class="text-animation--underline-thin">{{ collection.title }}</span></span>
          </a>
        {%- endfor -%}
      </div>
    {%- endif -%}

		{%- if predictive_search.resources.products.size > 0 -%}

      <div class="search-block">

        <span class="search-title">{{ 'search.form.product_results_title' | t }}</span>

        {%- for product in predictive_search.resources.products -%}

          <div class="search-item cart-item" data-js-search-item>

            <a tabindex="-1" href="{{ product.url }}" title="{{ product.title | escape }}" class="cart-item__thumbnail element--border-width-clamped element--border-radius">
              {%- render 'lazy-image-small', image: product.featured_media, aspect_ratio: settings.cart_image_ratio, fit: settings.cart_image_fit -%}
            </a>

            <div class="cart-item__content">

              <div>

                {%- liquid
                  if settings.show_currency_codes
                    assign iso_code = localization.country.currency.iso_code | prepend: ' ' 
                  endif
                -%}

                <a tabindex="-1" href="{{ product.url }}" class="cart-item__title">
                  <span class="text-animation--underline-thin">{{ product.title | escape }}</span>
                </a>

                <div class="cart-item__price">
                  {%- assign variant = product.selected_or_first_available_variant -%}
                  <span class="price">
                    {%- if product.price_varies -%}
                      {%- liquid
                        assign price = product.price | money | append: iso_code
                        if product.compare_at_price > product.price or product.compare_at_price_varies
                          echo 'products.grid.on_sale_from_html' | t: price: price 
                        else
                          echo 'products.grid.from_text_html' | t: price: price 
                        endif
                      -%}
                    {%- else -%}
                      {{ variant.price | money | append: iso_code }}
                      {%- if variant.compare_at_price > variant.price -%}
                        <span class="visually-hidden">{{ 'general.accessibility_labels.price.sale' | t }}</span>
                        <span><del>{{ variant.compare_at_price | money | append: iso_code }}</del></span>
                      {%- endif -%}
                      {%- if variant.unit_price_measurement -%}
                        <span class="cart-item__unit-price text-size--small text-color--opacity" style="display:block">
                          {{ variant.unit_price | money | append: iso_code }} / 
                          {% if variant.unit_price_measurement.reference_value != 1 %}
                            {{ variant.unit_price_measurement.reference_value }}
                          {% endif %}
                          {{ variant.unit_price_measurement.reference_unit }}
                        </span>
                      {%- endif -%}
                    {%- endif -%}
                  </span>
                </div>

              </div>

            </div>

          </div>

			  {%- endfor -%}

      </div>

		{%- endif -%}

		{%- if predictive_search.resources.pages.size > 0 -%}
      <div class="search-block">
        <span class="search-title">{{ 'search.form.page_results_title' | t }}</span>
        {%- for page in predictive_search.resources.pages -%}
          <a 
            href="{{ page.url }}" tabindex="-1" 
            class="search-item"
            data-js-search-item
          >
            <span class="text-weight--bold"><span class="text-animation--underline-thin">{{ page.title | escape }}</span></span>
          </a>
        {%- endfor -%}
      </div>
		{%- endif -%}

		{%- if predictive_search.resources.articles.size > 0 -%}
      <div class="search-block">
        <span class="search-title">{{ 'search.form.article_results_title' | t }}</span>
        {%- for article in predictive_search.resources.articles -%}
          <a 
            href="{{ article.url }}" tabindex="-1" 
            class="search-item"
            data-js-search-item
          >
            <span class="text-weight--bold"><span class="text-animation--underline-thin">{{ article.title | escape }}</span></span>
          </a>
        {%- endfor -%}
      </div>
    {%- endif -%}

	</div>

  <div class="search-link">
  	<a tabindex="-1" class="search-more button button--solid button--regular button--fullwidth" data-js-search-item href="{{ routes.search_url }}?q={{ predictive_search.terms | url_encode }}">{{ 'search.form.search_for_html' | t: terms: predictive_search.terms }}</a>
  </div>

{%- endif -%}