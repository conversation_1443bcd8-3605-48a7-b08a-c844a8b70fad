{%- liquid
  assign logo_height = section.settings.logo_height
  if logo_height > 40
    assign logo_mobile_height = 40
  else 
    assign logo_mobile_height = logo_height
  endif
-%}
{% style %}
  .header__top {
    --header-logo: {{ logo_height }}px;
  }
  @media screen and (max-width: 767px) {
    .header__top {
      --header-logo: {{ logo_mobile_height }}px;
    }
  }
{% endstyle %}

<main-header id="site-header" class="site-header">
  <div class="header-container header-container--top {% if section.settings.show_search %} hide-border-on-portable {% endif %}">
    <div class="header__top container--large header__top--password-page">

      <div id="logo" class="logo">

          {%- if section.settings.logo_src -%}

            <a class="logo-img" title="{{ shop.name | escape }}" href="{{ routes.root_url }}" style="height:var(--header-logo)">
              <img src="{{ section.settings.logo_src | image_url }}" alt="{{ shop.name | escape }}" width="{{ section.settings.logo_src.width }}" height="{{ section.settings.logo_src.height }}" />
            </a>

          {%- else -%}

            <a class="logo-txt" title="{{ shop.name | escape }}" href="{{ routes.root_url }}">{{ shop.name | escape }}</a>

          {%- endif -%}

      </div>

      <a href="#0" class="modal-login-cta" aria-expanded="false" aria-controls="modal-login" onclick="document.getElementById('modal-login').show()">
        {{ 'general.password_page.login_form_heading' | t }}
        {%- render 'theme-symbols', icon: 'locked' -%}
      </a>
      
    </div>
  </div>
</main-header>
  

	<modal-box id="modal-login" class="modal" 
		data-options='{
			"enabled": false,
			"showOnce": false
		}'
		style="display:none"
		tabindex="-1" role="dialog" aria-modal="true" 
	>
		<div class="container--small">
			<div class="modal-content">
				<div class="modal-heading">
					<div class="modal-heading__text remove-empty-space">
						<span class="text-size--xlarge text-weight--bold">{{ 'general.password_page.login_form_heading' | t }}</span>
					</div>
					<div class="modal-heading__actions">
						<button class="modal-close" data-js-close>{%- render 'theme-symbols', icon: 'close' -%}</button>
					</div>
				</div>
				<div class="rte">

					{%- form 'storefront_password' -%}
            <div class="password-input-group">
              {%- render 'form-errors', form: form -%}
              <div class="form-row">
                <label for="password" class="hidden">{{ 'general.password_page.login_form_password_label' | t }}</label>
                <input type="password" name="password" id="password" placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}" autofocus>
              </div>
              
              <div class="form-row">
                <input class="button button--solid" type="submit" name="commit" value="{{ 'general.password_page.login_form_submit' | t }}">
              </div>
            </div>
				  {%- endform -%}

				</div>
			</div>
		</div>
		<span class="modal-background" data-js-close></span>
	</modal-box>

	<script type="text/javascript">

		window.onload = function(){
			const modal = document.getElementById('modal-login');
			const errors = document.querySelector('.alert--error');
			if ( errors != null ) {
				setTimeout(function(){
					modal.show();
				}, 200);
			}	
		}
	
	</script>

{% schema %}
{
  "name": "t:sections.main-password-header.name",
  "class": "main-password-wrapper main-password-header",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo_src",
      "label": "t:sections.sidebar.settings.image.label"
    },
    {
      "type": "range",
      "id": "logo_height",
      "label": "t:sections.split-extra-words.sections.header.settings.logo_height",
      "unit": "px",
      "min": 25,
      "max": 140,
      "step": 5,
      "default": 80
    }
  ]
}
{% endschema %}