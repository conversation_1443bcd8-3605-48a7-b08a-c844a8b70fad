{{ 'section-rich-text.css' | asset_url | stylesheet_tag }}

<div class="{% unless section.settings.is_fullwidth %} container--large {% else %} container--fullwidth {% endunless %} {% unless section.settings.remove_margin %} container--vertical-space {% endunless %} {% if section.settings.section_background_color != "rgba(0,0,0,0)" or section.settings.section_background_gradient != blank %} container--has-background {% endif %}">

  {%- render 'section-heading', heading: section.settings.heading, subheading: section.settings.subheading, heading_tag: section.settings.seo_h_tag, layout: section.settings.section_heading_layout -%}

  <div id="element-{{ section.id }}" class="card 
    {% if section.settings.is_fullwidth %} card--no-radius card--no-sideborders card--no-shadow {% endif %} 
    {% unless section.settings.image_position contains "top" %} 
      element--height-{{ section.settings.height }} 
    {% else %}
      rich-text--image-on-top 
    {% endunless %} 
  rich-text">

    <div class="rich-text__container rich-text__container--image-{{ section.settings.image_position }} {% if section.settings.is_fullwidth %} rich-text__container--fullwidth {% if section.settings.image_position contains 'top' %} container--large {% endif %} {% endif %}{% if section.settings.emphasize_text_and_image_sep %} rich-text--emphasize-text-and-image-sep {% endif %}{% if section.settings.show_video and section.settings.video != blank %} rich-text--has-video {% endif %}">

      {%- unless section.settings.image_position contains "no-image" -%}
        <div class="rich-text__image
          {% unless section.settings.image_crop %} rich-text__image--fit {% endunless %} 
          {% if section.settings.image_position contains "top" %} element--height-{{ section.settings.height }} {% endif %}" 
        aria-hidden="true">

          {%- liquid
            if section.index == 1
              assign preload = true
            else
              assign preload = false
            endif
          -%}

          {%- unless section.settings.image == blank or section.settings.show_video and section.settings.video != blank -%}
            {%- if section.settings.mobile_image -%}
              {%- render 'lazy-image', image: section.settings.mobile_image, type: '', alt: section.settings.title, image_alignment: true, class: 'element--hide-on-desk', id: section.id | prepend: 'mobile-', preload: preload -%}
              {%- render 'lazy-image', image: section.settings.image, type: '', alt: section.settings.title, image_alignment: true, class: 'element--hide-on-small', id: section.id | prepend: 'desktop-', preload: preload -%}
            {%- else -%}
              {%- render 'lazy-image', image: section.settings.image, type: '', alt: section.settings.title, image_alignment: true, id: section.id, preload: preload -%}
            {%- endif -%}
          {%- elsif section.settings.video != blank -%}
            <video-popup id="video-popup--{{ section.settings.video.id }}" class="video-popup" style="display:block">

              <template>
                {%- if section.settings.video.type == 'youtube' -%}
                  <iframe sandbox="allow-scripts allow-same-origin" referrerpolicy="no-referrer" src="https://www.youtube-nocookie.com/embed/{{ section.settings.video.id }}?enablejsapi=1&autoplay=1&rel=0" class="js-youtube video-popup__iframe" allow="autoplay; encrypted-media" allowfullscreen title="{{ section.settings.heading | escape }}"></iframe>
                {%- else -%}
                  <iframe sandbox="allow-scripts allow-same-origin" referrerpolicy="no-referrer" src="https://player.vimeo.com/video/{{ section.settings.video.id }}?autoplay=1&dnt=1" class="js-vimeo video-popup__iframe" allow="autoplay; encrypted-media" allowfullscreen title="{{ section.settings.heading | escape }}"></iframe>
                {%- endif -%}
              </template>
    
              <div class="video-popup__container" data-js-video-popup-container>
    
                <div class="video-popup__background" aria-hidden="true">
                  {%- render 'lazy-image', image: section.settings.image, id: section.settings.video.id, type: 'background' -%}
                  
                  <div class="video-popup__disclaimer text-size--xxsmall">
                    <span class="cookieconsent-optout">
                      Um das Video abzuspielen, benötigen wir Ihre Zustimmung für alle Cookie-Kategorien, da durch das Abspielen des Videos eine Verbindung zu {% if section.settings.video.type == 'youtube' %}YouTube{% else %}Vimeo{% endif %} hergestellt wird.
                    </span>
                    <span class="cookieconsent-optin-marketing cookieconsent-optin-preferences cookieconsent-optin-statistics">
                      Durch das Abspielen des Videos wird eine Verbindung zu {% if section.settings.video.type == 'youtube' %}YouTube{% else %}Vimeo{% endif %} hergestellt.
                    </span>
                    <span>
                      <br>Es gelten die <a href="{% if section.settings.video.type == 'youtube' %}https://policies.google.com/privacy{% else %}https://vimeo.com/privacy{% endif %}?hl={{ request.locale }}" target="_blank">Datenschutzbestimmungen von {% if section.settings.video.type == 'youtube' %}YouTube{% else %}Vimeo{% endif %}</a>.
                    </span>
                  </div>

                  <a class="video-popup__link" data-js-video-popup-link>
                    <span class="video-popup__play" aria-hidden="true"></span>
                  </a>

                </div>
    
              </div>
    
              <button class="video-popup__close" data-js-video-popup-close>
                <span class="visually-hidden">{{ 'general.accessibility_labels.close' | t }}</span>
                <span aria-hidden="true" class="exit">{%- render 'theme-symbols', icon: 'close' -%}</span>
              </button>
    
            </video-popup>
          {%- else -%}
            {{ 'image' | placeholder_svg_tag }}
          {%- endunless -%}
        </div>
      {%- endunless -%}

      <div class="rich-text__text align-content align-content--{{ section.settings.text_alignment }} align-content--vertical-middle">
        <div class="card__text 
          {% unless section.settings.is_fullwidth %} gutter--large {% endunless %} 
          {% if section.settings.is_fullwidth and section.settings.image_position contains "top" %} gutter-top--xlarge gutter-bottom--xlarge {% endif %} 
        spacing--large remove-empty-space">

          {%- for block in section.blocks -%}
            {%- case block.type %}

              {%- when 'custom_liquid' -%}
                <div {{ block.shopify_attributes }}>
                  {{ block.settings.custom_liquid }}
                </div>

              {%- when 'title' -%}
                <{{ block.settings.seo_h_tag }} 
                  class="{{ block.settings.title_size }} rte rich-text__title"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.title }}
                </{{ block.settings.seo_h_tag }}>

              {%- when 'text' -%}
                <div 
                  class="rte text-size--{{ block.settings.text_size }}"
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.text }}
                </div>

              {%- when 'button' -%}
                <div 
                  class="increased-spacing" 
                  {{ block.shopify_attributes }}
                >
                  <a href="{{ block.settings.link }}" {% unless block.settings.open_in_new_window == false %} target="_blank" {% endunless %} class="button button--{{ block.settings.button_size }} button--{{ block.settings.button_style }}">
                    {{ block.settings.button_label }}
                  </a>
                </div>

              {%- when 'space' -%}
                <div class="empty-space" {{ block.shopify_attributes }}>&nbsp;</div>

            {%- endcase %}
          {%- endfor -%}

        </div>  

      </div>

      {%- if section.settings.emphasize_text_and_image_sep == false -%}
        {%- render 'custom-colors', id: section.id, text: section.settings.color_text_main, background: section.settings.color_background_main, accent: section.settings.color_accent_main, borders: section.settings.color_borders_main, shadow: section.settings.color_shadow_main, hide_borders: section.settings.color_hide_borders, hide_shadow: section.settings.color_hide_shadow -%}
      {%- else -%}
        {%- render 'custom-colors', id: section.id, text: section.settings.color_text_main, accent: section.settings.color_accent_main, borders: section.settings.color_borders_main, shadow: section.settings.color_shadow_main, hide_borders: section.settings.color_hide_borders, hide_shadow: section.settings.color_hide_shadow -%}
      {%- endif -%}

    </div>
  </div>
</div>

{%- if section.settings.section_background_color != "rgba(0,0,0,0)" -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background-color: {{ section.settings.section_background_color }};
    }
  {% endstyle %}
{%- endif -%}
{%- if section.settings.section_background_gradient != blank -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background: {{ section.settings.section_background_gradient }};
    }
  {% endstyle %}
  {%- if section.settings.emphasize_text_and_image_sep -%}
    .rich-text--emphasize-text-and-image-sep .rich-text__text,
    .rich-text--emphasize-text-and-image-sep .rich-text__image {
      background-color: {{ section.settings.color_background_main }};
      border-radius: 20px;
    }
  {%- endif -%}
{%- endif -%}

{%- if section.settings.text_image_ratio != blank -%}
  {%- style -%}
    #element-{{ section.id }} {
      {%- if section.settings.image_position == "right" -%}
        --rich-text-grid-template-columns: {{ section.settings.text_image_ratio }};
      {%- elsif section.settings.image_position == "left" -%}
        --rich-text-grid-template-columns: {{ section.settings.text_image_ratio | split: ' ' | reverse | join: ' ' }};
      {%- endif -%}
    }
  {%- endstyle -%}
{%- endif -%}

{% if section.settings.show_video and section.settings.video != blank %}
  {{ 'component-video.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'component-video.js' | asset_url }}" defer></script>
{% endif %}

{% schema %}
{
  "name": "t:sections.rich-text.name",
  "class": "can-be-fullwidth no-fullwidth-padding",
  "max_blocks": 9,
  "settings": [
    {
      "type": "checkbox",
      "id": "show_video",
      "label": "Show video instead of image",
      "default": false
    },
    {
      "id": "image",
      "type": "image_picker",
      "label": "t:sections.gallery.blocks.image.settings.image.label",
      "info": "This image will be used as the background image for the video if a video is specified."
    },
    {
      "id": "mobile_image",
      "type": "image_picker",
      "label": "t:sections.image.mobile_image"
    },
    {
      "type": "video_url",
      "id": "video",
      "label": "Video",
      "accept": [
        "youtube", "vimeo"
      ]
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "t:sections.local-extra-words.sections.rich-text.settings.image_position.label",
      "options": [
        {
          "value": "left",
          "label": "t:sections.local-extra-words.sections.rich-text.settings.image_position.option__1"
        },
        {
          "value": "top",
          "label": "t:sections.local-extra-words.sections.rich-text.settings.image_position.option__2"
        },
        {
          "value": "right",
          "label": "t:sections.local-extra-words.sections.rich-text.settings.image_position.option__3"
        },
        {
          "value": "top no-image",
          "label": "t:late_edits.rich-text.image_position.no_image.label",
          "group": "t:late_edits.rich-text.image_position.no_image.group"
        }
      ],
      "default": "right"
    },
    {
      "type": "select",
      "id": "height",
      "label": "t:sections.local-extra-words.sections.rich-text.settings.height.label",
      "info": "t:sections.local-extra-words.sections.rich-text.settings.height.info",
      "options": [
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.image_height.options__1.label"
        },
        {
          "value": "regular",
          "label": "t:sections.image-with-text.settings.image_height.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.image_height.options__3.label"
        }
      ],
      "default": "regular"
    },
    {
      "type": "checkbox",
      "id": "image_crop",
      "label": "t:sections.local-extra-words.sections.rich-text.settings.crop.label",
      "info": "t:sections.local-extra-words.sections.rich-text.settings.crop.info",
      "default": true
    },
    {
      "type": "header",
      "content": "t:local-march-update.labels.heading"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.local-extra-words.sections.headings.heading"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "t:sections.local-extra-words.sections.headings.subheading"
    },
    {
      "type": "select",
      "id": "section_heading_layout",
      "label": "t:sections.rich-text.settings.text_alignment.label",
      "options": [
        {
          "value": "section-heading--left",
          "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
        },
        {
          "value": "section-heading--center",
          "label": "t:sections.rich-text.settings.text_alignment.options__2.label"
        }
      ],
      "default": "section-heading--left"
    },
    {
      "type": "select",
      "id": "seo_h_tag",
      "label": "t:sections.refactor_words.seo.label",
      "info": "t:sections.refactor_words.seo.info",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        },
        {
          "value": "span",
          "label": "span"
        }
      ],
      "default": "h2"
    },
    {
      "type": "header",
      "content": "t:sections.main-article.blocks.content.name"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "t:sections.image-with-text.settings.text_alignment.label",
      "options": [
        {
          "value": "horizontal-left",
          "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
        },
        {
          "value": "horizontal-center",
          "label": "t:sections.rich-text.settings.text_alignment.options__2.label"
        },
        {
          "value": "horizontal-right",
          "label": "t:sections.rich-text.settings.text_alignment.options__3.label"
        }
      ],
      "default": "horizontal-center"
    },
    {
      "type": "header",
      "content": "t:local-march-update.labels.section_design"
    },
    {
      "type": "select",
      "id": "text_image_ratio",
      "label": "Text to Image Ratio (Desktop)",
      "options": [
        {
          "value": "3fr 1fr",
          "label": "3:1"
        },
        {
          "value": "2fr 1fr",
          "label": "2:1"
        },
        {
          "value": "1fr 1fr",
          "label": "1:1"
        }
      ],
      "default": "1fr 1fr"
    },
    {
      "type": "checkbox",
      "id": "is_fullwidth",
      "label": "t:sections.local-extra-words.sections.rich-text.settings.fullwidth.label",
      "default": false,
      "info": "t:sections.local-extra-words.sections.rich-text.settings.fullwidth.info"
    },
    {
      "type": "checkbox",
      "id": "emphasize_text_and_image_sep",
      "label": "Emphasize text and image separation",
      "default": false
    },
    {
      "type": "color",
      "id": "section_background_color",
      "label": "t:sections.local-extra-words.settings_schema.product-card.badges.custom_badges.color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color_background",
      "id": "section_background_gradient",
      "label": "t:local-230.background_gradient"
    },
    {
      "type": "checkbox",
      "id": "remove_margin",
      "label": "t:sections.local-extra-words.sections.rich-text.settings.remove_margin.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.local-extra-words.sections.headings.custom_colors"
    },
    {
      "type": "color",
      "id": "color_background_main",
      "label": "t:sections.split-extra-words.settings_schema.colors.settings.background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "color_text_main",
      "label": "t:sections.split-extra-words.settings_schema.colors.settings.text",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "color_accent_main",
      "label": "t:sections.local-extra-words.settings_schema.colors.settings.accent",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "color_borders_main",
      "label": "t:sections.local-extra-words.settings_schema.colors.settings.borders",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "color_shadow_main",
      "label": "t:local-march-update.shadows.label",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "checkbox",
      "id": "color_hide_borders",
      "label": "t:sections.local-extra-words.settings_schema.colors.settings.hide_border",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "color_hide_shadow",
      "label": "t:local-march-update.shadows.hide",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "title",
      "name": "t:sections.rich-text.blocks.heading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "t:sections.rich-text.blocks.heading.settings.heading.label",
          "default": "Talk about your brand"
        },
        {
          "type": "select",
          "id": "title_size",
          "label": "t:sections.rich-text.blocks.heading.settings.heading_size.label",
          "options": [
            {
              "value": "h3",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__1.label"
            },
            {
              "value": "h2",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__2.label"
            },
            {
              "value": "h1",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__3.label"
            }
          ],
          "default": "h2"
        },
        {
          "type": "header",
          "content": "t:sections.refactor_words.seo.name"
        },
        {
          "type": "select",
          "id": "seo_h_tag",
          "label": "t:sections.refactor_words.seo.label",
          "info": "t:sections.refactor_words.seo.info",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "span",
              "label": "span"
            }
          ],
          "default": "h2"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.rich-text.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "t:sections.rich-text.blocks.text.settings.text.label",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements or welcome customers to your store.</p>"
        },
        {
          "type": "select",
          "id": "text_size",
          "label": "t:sections.footer.blocks.text.settings.text_size.label",
          "options": [
            {
              "value": "regular",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__1.label"
            },
            {
              "value": "large",
              "label": "t:sections.rich-text.blocks.heading.settings.heading_size.options__2.label"
            }
          ],
          "default": "regular"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.rich-text.blocks.button.name",
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "label": "t:sections.rich-text.blocks.button.settings.button_label.label",
          "default": "Show more"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.rich-text.blocks.button.settings.button_link.label"
        },
        {
          "type": "checkbox",
          "id": "open_in_new_window",
          "label": "t:sections.local-extra-words.sections.announcement-bar.blocks.content.settings.target",
          "default": false
        },
        {
          "type": "select",
          "id": "button_size",
          "label": "t:sections.rich-text.blocks.button.settings.button_size.label",
          "options": [
            {
              "value": "regular",
              "label": "t:sections.rich-text.blocks.button.settings.button_size.options__1.label"
            },
            {
              "value": "large",
              "label": "t:sections.rich-text.blocks.button.settings.button_size.options__2.label"
            }
          ],
          "default": "large"
        },
        {
          "id": "button_style",
          "label": "t:sections.local-extra-words.sections.buttons.style.label",
          "type": "select",
          "options": [
            {
              "value": "outline",
              "label": "t:sections.local-extra-words.sections.buttons.style.option__1"
            },
            {
              "value": "solid",
              "label": "t:sections.local-extra-words.sections.buttons.style.option__2"
            }
          ],
          "default": "outline"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.refactor_words.custom_code.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label"
        }
      ]
    },
    {
      "type": "space",
      "name": "t:local-march-update.blocks.space.name"
    }
  ],
  "presets": [
    {
      "name": "t:sections.rich-text.name",
      "blocks": [
        { "type": "title" },
        { "type": "text" },
        { "type": "button" }
      ]
    }
  ]
}
{% endschema %}
