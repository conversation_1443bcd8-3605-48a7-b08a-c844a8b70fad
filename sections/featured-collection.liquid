{{ 'component-product-item.css' | asset_url | stylesheet_tag }}
{{ 'featured-collection.js' | asset_url | script_tag }}

{%- liquid 

  assign use_collection_tabs = section.settings.use_collection_tabs
  assign collection_tabs = 0

  if section.settings.collection != blank 
    assign collection_tabs = collection_tabs | plus: 1
  endif

  if section.settings.collection_1 != blank 
    assign collection_tabs = collection_tabs | plus: 1
  endif

  if section.settings.collection_2 != blank 
    assign collection_tabs = collection_tabs | plus: 1
  endif

  if section.settings.collection_3 != blank 
    assign collection_tabs = collection_tabs | plus: 1
  endif

  if use_collection_tabs == true
    assign section_tag = "collection-tabs"
  else
    assign section_tag = "div"
  endif

-%}

<div class="container--large {% unless section.settings.remove_margin %} container--vertical-space {% endunless %} {% if section.settings.section_background_color != "rgba(0,0,0,0)" or section.settings.section_background_gradient != blank %} container--has-background {% endif %} remove-empty-space">

  <{{ section_tag }}>

    {%- render 'section-heading', heading: section.settings.heading, subheading: section.settings.subheading, button: section.settings.show_view_all, button_label: 'collections.view_all_products', link: collections[section.settings.collection].url, heading_tag: section.settings.seo_h_tag, layout: section.settings.section_heading_layout -%}

    {%- if collection_tabs > 1 and use_collection_tabs == true -%}
      <ul class="collection-tabs gutter-bottom--regular">
        {%- if section.settings.collection != blank -%}
          <li class="collection-tabs_tab">
            <button role="tab" aria-controls="collection-{{ section.id }}-0" data-collection-url="{{ section.settings.collection.url }}" class="button button--small">{{ section.settings.collection.title | escape }}</button>
          </li>
        {%- endif -%}
        {%- if section.settings.collection_1 != blank -%}
          <li class="collection-tabs_tab">
            <button role="tab" aria-controls="collection-{{ section.id }}-1" data-collection-url="{{ section.settings.collection_1.url  }}" class="button button--small">{{ section.settings.collection_1.title | escape }}</button>
          </li>
        {%- endif -%}
        {%- if section.settings.collection_2 != blank -%}
          <li class="collection-tabs_tab">
            <button role="tab" aria-controls="collection-{{ section.id }}-2" data-collection-url="{{ section.settings.collection_2.url  }}" class="button button--small">{{ section.settings.collection_2.title | escape }}</button>
          </li>
        {%- endif -%}
        {%- if section.settings.collection_3 != blank -%}
          <li class="collection-tabs_tab">
            <button role="tab" aria-controls="collection-{{ section.id }}-3" data-collection-url="{{ section.settings.collection_3.url  }}" class="button button--small">{{ section.settings.collection_3.title | escape }}</button>
          </li>
        {%- endif -%}
      </ul>
    {%- endif -%}
    
    <div class="{% unless use_collection_tabs == true %}collections-wrapper{% endunless %}">

      {%- liquid

        if collection_tabs > 0

          assign first_panel = true

          if section.settings.collection != blank
            unless use_collection_tabs == true
              render 'collection-panel', panel_id: 0, collection: section.settings.collection, collection_tabs: collection_tabs, active: first_panel
            else
              render 'collection-panel', panel_id: 0, collection: section.settings.collection, collection_tabs: collection_tabs, active: true
            endunless
            assign first_panel = false
          endif 

          if section.settings.collection_1 != blank
            unless use_collection_tabs == true
              render 'collection-panel', panel_id: 1, collection: section.settings.collection_1, collection_tabs: collection_tabs, active: first_panel
            else
              render 'collection-panel', panel_id: 1, collection: section.settings.collection_1, collection_tabs: collection_tabs, active: true
            endunless
            assign first_panel = false
          endif 
          
          if section.settings.collection_2 != blank
            unless use_collection_tabs == true
              render 'collection-panel', panel_id: 2, collection: section.settings.collection_2, collection_tabs: collection_tabs, active: first_panel
            else
              render 'collection-panel', panel_id: 2, collection: section.settings.collection_2, collection_tabs: collection_tabs, active: true
            endunless
            assign first_panel = false
          endif 

          if section.settings.collection_3 != blank
            unless use_collection_tabs == true
              render 'collection-panel', panel_id: 3, collection: section.settings.collection_3, collection_tabs: collection_tabs, active: first_panel
            else
              render 'collection-panel', panel_id: 3, collection: section.settings.collection_3, collection_tabs: collection_tabs, active: true
            endunless
            assign first_panel = false
          endif 
        else
          render 'collection-panel', panel_id: -1, collection_tabs: collection_tabs
        endif 

      -%}
     
    </div>
    
    {%- if section.settings.show_view_all and section.settings.section_heading_layout contains 'center' -%}
      <div class="gutter-top--large align-content align-content--horizontal-center">
        <a class="button button--outline button--regular js-view-collection-link" href="{{ collections[section.settings.collection].url }}">{{ 'collections.view_all_products' | t }}</a>
      </div>
    {%- endif -%}

  </{{ section_tag }}>
  
</div>

{%- if collection_tabs > 1 -%}
  <script src="{{ 'component-collection-tabs.js' | asset_url }}" defer></script>
{%- endif -%}

{%- if section.settings.section_background_color != "rgba(0,0,0,0)" -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background-color: {{ section.settings.section_background_color }};
    }
  {% endstyle %}
{%- endif -%}
{%- if section.settings.section_background_gradient != blank -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      background: {{ section.settings.section_background_gradient }};
    }
  {% endstyle %}
{%- endif -%}
{%- if section.settings.section_heading_color != "rgba(0,0,0,0)" -%}
  {% style %}
    #shopify-section-{{ section.id }} {
      --color-text-main: {{ section.settings.section_heading_color }};
    }
  {% endstyle %}
{%- endif -%}

{% schema %}
  {
    "name": "t:sections.featured-collection.name",
    "class": "mount-css-slider mount-collection-tabs",
    "blocks": [
      {
        "type": "price",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.price",
        "limit": 1
      },
      {
        "type": "title",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.title",
        "limit": 1
      },
      {
        "type": "quick_buy",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.quick_buy",
        "limit": 1
      },
      {
        "type": "vendor",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.vendor",
        "limit": 1
      },
      {
        "type": "rating",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.rating",
        "limit": 1
      },
      {
        "type": "local_availability",
        "name": "t:sections.local-extra-words.settings_schema.product-card.local-pickup.name",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.local-extra-words.settings_schema.product-card.local-pickup.info"
          }
        ]
      },
      {
        "type": "icons",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.icons.name",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.help"
          }
        ]
      },
      {
        "type": "text",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.text.name",
        "settings": [
          {
            "type": "text",
            "id": "metafield",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.text.label",
            "info": "custom.product_text"
          },
          {
            "type": "select",
            "id": "text_size",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.label",
            "options": [
              {
                "value": "text-size--xsmall",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__1"
              },
              {
                "value": "text-size--regular",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__2"
              },
              {
                "value": "text-size--large",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__3"
              }
            ],
            "default": "text-size--regular"
          },
          {
            "type": "color",
            "id": "text_color",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.color.label",
            "default": "#6A6A6A"
          },
          {
            "type": "checkbox",
            "id": "text_transform",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.transform.label",
            "default": false
          }
        ]
      },
      {
        "type": "product_link",
        "name": "t:sections.featured-product.blocks.product_link.name",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "button_label",
            "label": "t:sections.local-extra-words.sections.main-header.settings.promotion_block.button.label",
            "default": "View product"
          }
        ]
      }
    ],
    "settings": [
      {
        "type": "header",
        "content": "t:local-march-update.labels.heading"
      },
      {
        "type": "text",
        "id": "heading",
        "label": "t:sections.local-extra-words.sections.headings.heading",
        "default": "Featured collection"
      },
      {
        "type": "text",
        "id": "subheading",
        "label": "t:sections.local-extra-words.sections.headings.subheading",
        "default": "Showcase your best products"
      },
      {
        "type": "checkbox",
        "id": "show_view_all",
        "label": "t:sections.featured-collection.settings.show_view_all.label",
        "default": true
      },
      {
        "type": "select",
        "id": "section_heading_layout",
        "label": "t:sections.rich-text.settings.text_alignment.label",
        "options": [
          {
            "value": "section-heading--left",
            "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
          },
          {
            "value": "section-heading--center",
            "label": "t:sections.rich-text.settings.text_alignment.options__2.label"
          }
        ],
        "default": "section-heading--left"
      },
      {
        "type": "header",
        "content": "t:sections.main-article.blocks.content.name"
      },
      {
        "type": "select",
        "id": "style",
        "label": "t:sections.featured-collection.settings.layout.label",
        "options": [
          {
            "label": "t:sections.featured-collection.settings.layout.options__1.label",
            "value": "slider"
          },
          {
            "label": "t:sections.featured-collection.settings.layout.options__2.label",
            "value": "grid"
          }
        ],
        "default": "grid"
      },
      {
        "type": "select",
        "id": "layout_desktop",
        "label": "t:sections.local-extra-words.sections.columns.name",
        "options": [
          {
            "value": "three-columns",
            "label": "t:sections.local-extra-words.sections.columns.option__2"
          },
          {
            "value": "four-columns",
            "label": "t:sections.local-extra-words.sections.columns.option__3"
          }
        ],
        "default": "four-columns"
      },
      {
        "type": "select",
        "id": "layout_mobile",
        "label": "t:sections.local-extra-words.sections.columns.name_mobile",
        "options": [
          {
            "value": "grid-palm-1",
            "label": "t:sections.local-extra-words.sections.columns.option__0"
          },
          {
            "value": "grid-palm-2",
            "label": "t:sections.local-extra-words.sections.columns.option__1"
          }
        ],
        "default": "grid-palm-1"
      },
      {
        "type": "range",
        "id": "products_number",
        "label": "t:sections.featured-collection.settings.products_number.label",
        "min": 4,
        "max": 16,
        "step": 1,
        "default": 8
      },
      {
        "type": "header",
        "content": "t:sections.main-list-collections.settings.header.content"
      },
      {
        "type": "collection",
        "id": "collection",
        "label": "t:sections.featured-collection.settings.collection.label"
      },
      {
        "type": "collection",
        "id": "collection_1",
        "label": "t:sections.featured-collection.settings.collection.label"
      },
      {
        "type": "collection",
        "id": "collection_2",
        "label": "t:sections.featured-collection.settings.collection.label"
      },
      {
        "type": "collection",
        "id": "collection_3",
        "label": "t:sections.featured-collection.settings.collection.label"
      },
      {
        "type": "header",
        "content": "t:local-march-update.labels.section_design"
      },
      {
        "type": "checkbox",
        "id": "use_collection_tabs",
        "label": "Use collection tabs"
      },
      {
        "type": "color",
        "id": "section_background_color",
        "label": "t:sections.local-extra-words.settings_schema.product-card.badges.custom_badges.color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color_background",
        "id": "section_background_gradient",
        "label": "t:local-230.background_gradient"
      },
      {
        "type": "color",
        "id": "section_heading_color",
        "label": "t:local-223.heading_text_color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "checkbox",
        "id": "remove_margin",
        "label": "t:sections.local-extra-words.sections.rich-text.settings.remove_margin.label",
        "default": false
      },
      {
        "type": "header",
        "content": "t:sections.refactor_words.seo.name"
      },
      {
        "type": "select",
        "id": "seo_h_tag",
        "label": "t:sections.refactor_words.seo.label",
        "info": "t:sections.refactor_words.seo.info",
        "options": [
          {
            "value": "h1",
            "label": "H1"
          },
          {
            "value": "h2",
            "label": "H2"
          },
          {
            "value": "h3",
            "label": "H3"
          },
          {
            "value": "h4",
            "label": "H4"
          },
          {
            "value": "span",
            "label": "span"
          }
        ],
        "default": "h2"
      }
    ],
    "presets": [{
      "name": "t:sections.featured-collection.presets.name",
      "blocks": [
        { "type": "price" },
        { "type": "title" },
        { "type": "quick_buy" }
      ]
    }],
    "disabled_on": {
      "groups": ["header"]
    }
  }
{% endschema %}