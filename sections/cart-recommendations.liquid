{%- if recommendations.performed and recommendations.products_count > 0 -%}
	<div class="cart-recommendations-sidebar" data-js-cart-recommendations-performed>
		<div class="text-weight--bold gutter-bottom--small gutter-top--small">{{ settings.cart_recommendations_heading | escape }}</div>
		<css-slider
      data-options='{
        "selector": ".product-item",
        "groupCells": true,
        "indexNav": true,
        "navigationDOM": "<span class=\"css-slider-button css-slider-prev\" style=\"display:none\"><svg fill=\"none\" height=\"13\" viewBox=\"0 0 8 13\" width=\"8\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"#000\" ><path d=\"m7.91419 1.41431-6.48529 6.48528-1.4142102-1.41422 6.4852802-6.48527702z\"/><path d=\"m6.48528 12.9849-6.48528027-6.48532 1.41421027-1.41421 6.48528 6.48523z\"/></g></svg></span><span class=\"css-slider-button css-slider-next\" style=\"display:none\"><svg width=\"9\" height=\"13\" viewBox=\"0 0 9 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.914169 11.5857L7.39945 5.10041L8.81367 6.51463L2.32838 12.9999L0.914169 11.5857Z\" fill=\"black\"/><path d=\"M2.34307 0.0151367L8.82835 6.50042L7.41414 7.91463L0.928857 1.42935L2.34307 0.0151367Z\" fill=\"black\"/></svg></span>"
      }'
      class="css-slider css-slider--bottom-navigation gutter-bottom--small"
      id="css-slider-{{ section.id }}"
    > 
      {%- render 'custom-shadow', section_id: section.id -%}
      <div class="grid grid--slider grid-2 grid--align-items-stretch">
        {%- for product in recommendations.products -%}
          <div class="product-item" data-js-product-item>

            <a href="{{ product.url }}" class="element--border-width-clamped element--border-radius element--has-border" style="display:block">
              <figure class="lazy-image lazy-image--animation {% if settings.cart_image_fit %} lazy-image--fit {% endif %}" data-ratio style="padding-top: {{ 100 | divided_by: settings.cart_image_ratio }}%">
                <img 
                  {% unless settings.cart_image_fit %}
                    {%- liquid 
                      assign divide_ratio = 1 | divided_by: settings.cart_image_ratio
                      assign height_170 = 170 | times: divide_ratio | round
                      assign height_340 = 340 | times: divide_ratio | round
                      assign height_510 = 510 | times: divide_ratio | round
                    -%}
                    src="{{ product.featured_media | image_url: width: 170, height: height_170, crop: 'center' }}" 
                    srcset="
                      {{ product.featured_media | image_url: width: 170, height: height_170, crop: 'center' }} 170w, 
                      {{ product.featured_media | image_url: width: 340, height: height_340, crop: 'center' }} 340w, 
                      {{ product.featured_media | image_url: width: 510, height: height_510, crop: 'center' }} 510w
                    "
                  {% else %}
                    src="{{ product.featured_media | image_url: width: 170 }}" 
                    srcset="
                      {{ product.featured_media | image_url: width: 170 }} 170w, 
                      {{ product.featured_media | image_url: width: 340 }} 340w, 
                      {{ product.featured_media | image_url: width: 510 }} 510w
                    "
                  {% endunless %}
                  class="element--border-radius"
                  alt="{{ product.featured_media.alt | escape }}"
                  loading="lazy"
                  sizes="170px"
                  width="170" height="{{ height_170 }}"
                  onload="this.parentNode.classList.add('lazyloaded')"
                />
              </figure>
            </a>

            <div class="product-item__text gutter-top--small">

              <div class="text-size--small" style="margin-bottom:.5em">
                {%- render 'product-price', target: product, variant: product.selected_or_first_available_variant, product_price_varies: product.price_varies -%}
              </div>
        
              <a href="{{ product.url }}" class="text-line-height--small text-weight--bold">
                <span class="text-animation--underline-thin">{{ product.title | escape }}</span>
              </a>	

              {%- if settings.enable_recommendations_quick_buy -%}
                <div class="actions">
                  {%- if product.available -%}
                    {%- if product.has_only_default_variant -%}
                      <quick-add-to-cart>
                        <product-form data-ajax-cart>
                          {%- form 'product', product -%}
                            <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                            <button class="quick-add-to-cart" type="submit" name="add" tabindex="-1" onclick="this.closest('.product-item').classList.add('working');setTimeout(()=>{document.getElementById('site-cart-sidebar').scrollTo({top: 0, behavior: 'smooth'})},1000)">
                              <span class="text-size--small">{{ 'products.grid.quick_buy' | t }}</span>
                            </button>
                          {%- endform -%} 
                        </product-form>
                      </quick-add-to-cart>
                    {%- else -%}
                      <quick-view-product>
                        <a class="quick-add-to-cart" href="{{ product.url }}" tabindex="-1" onclick="this.closest('.product-item').classList.add('working');setTimeout(()=>{document.getElementById('site-cart-sidebar').hide();this.closest('.product-item').classList.remove('working')},500);">
                          <span class="text-size--small">{{ 'products.page.choose_options_button' | t }}</span>
                        </a>
                      </quick-view-product>
                    {%- endif -%}
                  {%- endif -%}
                </div>
              {%- endif -%}
            
            </div>

          </div>
        {%- endfor -%}
      </div>
	  </css-slider>
 	</div>
{%- endif -%}