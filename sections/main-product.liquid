{{ 'section-main-product.css' | asset_url | stylesheet_tag }}

{%- liquid
  unless product.has_only_default_variant
    assign default_to_first_variant = section.settings.default_to_first_variant
  else 
    assign default_to_first_variant = true
  endunless
-%}

<product-page 
  id="product-{{ section.id }}" 
  class="container container--large container--vertical-space-small 
  grid grid--layout grid--gap-xlarge grid-2 grid-lap-1 grid--no-stretch 
  main-product product-component--default-to-first-variant-{{ default_to_first_variant }}" 
  data-collection="{{ collection.handle }}" data-id="{{ product.id }}" 
  data-availability="{{ product.available }}" 
  data-js-product-component
>

  {%- assign current_variant = product.selected_or_first_available_variant -%}

  <div class="product-gallery product-gallery--{{ section.settings.gallery_style }}" data-js-product-gallery>

    {%- if product.media.size > 1 -%}
      <css-slider data-options='{
        "selector": ".product-gallery-item",
        "autoHeight": true,
        {%- if section.settings.gallery_style == 'scroll' -%}
          "watchCSS": true,
        {%- endif -%}
        {%- if section.settings.gallery_pagination == 'thumbnails' -%} 
          "thumbnails": false, 
          "navigation": false,
        {%- else -%}
          "indexNav": true,
          "navigationDOM": "<span class=\"css-slider-button css-slider-prev\" style=\"display:none\"><svg fill=\"none\" height=\"13\" viewBox=\"0 0 8 13\" width=\"8\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"#000\" ><path d=\"m7.91419 1.41431-6.48529 6.48528-1.4142102-1.41422 6.4852802-6.48527702z\"/><path d=\"m6.48528 12.9849-6.48528027-6.48532 1.41421027-1.41421 6.48528 6.48523z\"/></g></svg></span><span class=\"css-slider-button css-slider-next\" style=\"display:none\"><svg width=\"9\" height=\"13\" viewBox=\"0 0 9 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.914169 11.5857L7.39945 5.10041L8.81367 6.51463L2.32838 12.9999L0.914169 11.5857Z\" fill=\"black\"/><path d=\"M2.34307 0.0151367L8.82835 6.50042L7.41414 7.91463L0.928857 1.42935L2.34307 0.0151367Z\" fill=\"black\"/></svg></span>",
        {%- endif -%}
        "listenScroll": true
      }'
        class="css-slider--bottom-navigation element--border-radius" 
        id="css-slider-{{ section.id }}"
      >
      {%- render 'custom-shadow', section_id: section.id -%} 
      {%- if settings.shadow_cards_x != 0 -%}
        {% style %}
          #css-slider-{{ section.id }} .css-slider-holder .css-slide {
            width: calc(100% - {{ settings.shadow_cards_x | abs }}px - {{ settings.shadow_cards_blur }}px);
          }
        {% endstyle %}
      {%- endif -%}
      {%- if settings.shadow_cards_y != 0 -%}
        {% style %}
          #css-slider-{{ section.id }} .css-slider-viewport  {
            padding-bottom: calc({{ settings.shadow_cards_y | abs }}px + {{ settings.shadow_cards_blur }}px);
            box-sizing: content-box;
          }
        {% endstyle %}
      {%- endif -%}
    {%- endif -%}

      <div class="grid {% if section.settings.gallery_style == 'slider' %} grid--slider {% else %} grid--layout portable--grid--slider {% endif %} grid-1 grid--gap-small">

        {% style %}
          #product-{{ section.id }} .product-gallery-item .lazy-image img {
            padding: {{ section.settings.gallery_padding }}% !important;
          }
        {% endstyle %}

        {%- if product.media.size == 0 -%} 
          <div class="product-gallery-item" style="padding-top: 100%">
            {%- render 'lazy-svg', image: 'product-1', ratio: 1, class: 'svg-placeholder svg-placeholder--background' %}
          </div>
        {%- else -%}
          {%- liquid
            for media in product.media
              render 'product-media', media: media, aspect_ratio: section.settings.gallery_ratio, fit: section.settings.gallery_fit, enable_zoom: section.settings.enable_zoom, loop: section.settings.enable_video_looping, index: forloop.index0
              if media.media_type == "external_video" or media.media_type == "video"
                assign video_script = true 
              endif
            endfor
          -%}
        {%- endif -%}

      </div>

    {%- if product.media.size > 1 -%} </css-slider> {%- endif -%}

    {%- if section.settings.gallery_pagination == 'thumbnails' and section.blocks.size > 1 -%}

      <div class="product-gallery__thumbnails {% if section.settings.gallery_style == 'scroll' %} hide portable-show {% endif %}" aria-hidden="true">
        <div class="product-gallery__thumbnails-holder">
          {%- for media in product.media -%}
            <button class="thumbnail element--border-radius" data-index="{{ forloop.index0 }}" {% if section.settings.gallery_pagination == 'thumbnails' %} tabindex="0" {% endif %}>
              {%- render 'lazy-image-small', image: media.preview_image, aspect_ratio: settings.cart_image_ratio, fit: settings.cart_image_fit, class: 'thumbnail__image' -%}
              {%- if media.media_type == 'video' or media.media_type == 'external_video' -%}
                <span class="thumbnail__badge">{% render 'theme-symbols', icon: 'badge-video-thumbnail' %}</span>
              {%- elsif media.media_type == 'model' -%}
                <span class="thumbnail__badge">{% render 'theme-symbols', icon: 'badge-model-thumbnail' -%}</span>
              {%- endif -%}
            </button>
          {%- endfor -%}
        </div>
      </div>

    {%- endif -%}

    {%- assign first_3d_model = product.media | where: "media_type", "model" | first -%}

    {%- if first_3d_model -%}
      <div class="fullwidth text-align--center">
        <button
          title="{{ 'products.page.view_in_space_label' | t }}"
          class="button button--small button--icon button--outline product-gallery__view-in-space"
          data-shopify-xr
          data-shopify-first-model3d-id="{{ first_3d_model.id }}"
          data-shopify-model3d-id="{{ first_3d_model.id }}"
          data-shopify-title="{{ product.title | escape }}"
          data-shopify-xr-hidden
        >
          <span aria-hidden="true" class="button__icon">{%- render 'theme-symbols', icon: 'model-button' -%}</span>
          <span class='product-gallery__view-in-space-text'>{{ 'products.page.view_in_space' | t }}</span>
        </button>

        <link id="ModelViewerStyle" rel="stylesheet" href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css" media="print" onload="this.media='all'">
        <script src="{{ 'component-product-model.js' | asset_url }}" defer></script>

      </div>
      
    {%- endif -%}

  </div>
    
  {{ 'component-toggle.css' | asset_url | stylesheet_tag }}

	<div class="product-text remove-empty-space">

    {%- assign product_form_id = 'product-form-' | append: section.id -%}

    {%- for block in section.blocks -%}
      {%- case block.type -%}

        {%- when '@app' -%}
          {%- render block -%}

        {%- when 'variant_metafield' -%}
          <div class="product-variant-metafield show-block-if-variant-selected rte" 
            data-key="{{ block.settings.key }}" 
            data-js-variant-metafield
            {{ block.shopify_attributes }}
            data-update-block="variant-metafield"
          >
            {%- assign metafield_reference = block.settings.key -%}
            {%- if metafield_reference != blank -%}
              {%- assign metafield_keys = metafield_reference | split: '.' -%}
              {%- assign metafield_value = current_variant.metafields[metafield_keys[0]][metafield_keys[1]] -%}
              {%- if metafield_value != blank -%}
                <div class="product-variant-metafield__container">
                  {%- if block.settings.title != blank %}
                    <span class="product-variant-metafield__title text-weight--medium">{{ block.settings.title | escape }}</span>
                  {%- endif -%}
                  {{ metafield_value | metafield_tag }}
                </div>
              {%- endif -%}              
            {%- endif -%}
          </div>

        {%- when 'image' -%}
          <div class="product-image-block product-text-element--with-borders" id="image-{{ block.id }}" {{ block.shopify_attributes }}>

            {% style %}
              #image-{{ block.id }} figure,
              #image-{{ block.id }} img {
                border-radius: {{ block.settings.image_border_radius }}px;
              }
            {% endstyle %}

            <div class="element--overflow-hidden">

              {%- if block.settings.link -%}
                <a href="{{ block.settings.link }}" {% unless block.settings.open_in_new_window == false %} target="_blank" {% endunless %}>
              {%- endif -%}  

              {%- unless block.settings.image == blank -%}

                {%- liquid

                  if block.settings.link != blank
                    assign animation_class = 'lazy-image--animation'
                  endif

                  if block.settings.mobile_image != blank

                    assign desk_image_class = animation_class | append: ' lap-hide'
                    assign mobile_image_class = animation_class | append: ' hide lap-show'
                    render 'lazy-image', image: block.settings.mobile_image, sizes: 'sizes="(max-width: 767px) calc(100vw - 20px), (max-width: 1280px) 50vw, 620px"', class: mobile_image_class

                  endif 

                  render 'lazy-image', image: block.settings.image, sizes: 'sizes="(max-width: 767px) calc(100vw - 20px), (max-width: 1280px) 50vw, 620px"', class: desk_image_class

                -%}

              {%- else -%}
                {%- render 'lazy-svg', image: 'hero-apparel-1', class: 'onboardig-svg' -%}
              {%- endunless -%}

              {%- if block.settings.link -%}
                </a>
              {%- endif -%}

            </div>
          </div>
        
        {%- when 'progress-slider' -%}
          {{ 'component-progress.css' | asset_url | stylesheet_tag }}
          
          <div id="progress-slider-{{ block.id }}" {{ block.shopify_attributes }}>
            <p class="text-size--large text-weight--bold">{{ block.settings.title | escape }}</p>
            <div class="progress-slider__wrapper">
              {%- if  block.settings.caption != blank -%}
                <span class="progress-slider__caption">{{ block.settings.caption | escape }}</span>
              {%- endif -%}
              <div class="progress-slider">
                <span class="progress-slider__active"></span>
              </div>
            </div>

            {% liquid
              assign color_active = settings.color_text_main 
              if block.settings.slider_color_active != 'rgba(0,0,0,0)'
                assign color_active = block.settings.slider_color_active 
              endif

              assign color_default = settings.color_text_main | color_modify: 'alpha', 0.07
              if block.settings.slider_color_default != 'rgba(0,0,0,0)'
                assign color_default = block.settings.slider_color_default
              endif
            %}
            
            {%- liquid
              assign slider_value = block.settings.slider_value 
              if block.settings.dynamic_content != blank
                assign metafield_keys = block.settings.dynamic_content | split: '.'
                assign metafield_value = product.metafields[metafield_keys[0]][metafield_keys[1]]
                if metafield_value != blank
                  assign slider_value = metafield_value
                endif
              endif
            -%}

            {% style %}
              #progress-slider-{{ block.id }} .progress-slider {
                background-color: {{ color_default }};
                height: {{ block.settings.slider_height }}px;
                width: {{ block.settings.slider_width }}px;
              }

              #progress-slider-{{ block.id }} .progress-slider__active {
                background-color: {{ color_active }};
                width: {{ slider_value }}%;
              }
            {% endstyle %}
          </div>

        
        {%- when 'progress-dots' -%}
          {{ 'component-progress.css' | asset_url | stylesheet_tag }}
          
          <div id="progress-dots-{{ block.id }}" {{ block.shopify_attributes }}>
            <p class="text-size--large text-weight--bold">{{ block.settings.title | escape }}</p>

            <div class="progress-dots__wrapper">

              {%- if block.settings.caption != blank -%}
                <p class="progress-dots__caption">{{ block.settings.caption | escape }}</p>
              {%- endif -%}

              <div class="progress-dots">

                {%- liquid

                  assign total_dots = block.settings.total_dots
                  assign active_dots = block.settings.active_dots | at_most: total_dots

                  if block.settings.dynamic_content != blank
                    assign metafield_keys = block.settings.dynamic_content | split: '.'
                    assign metafield_value = product.metafields[metafield_keys[0]][metafield_keys[1]]
                    if metafield_value != blank
                      assign active_dots = metafield_value | at_most: total_dots
                    endif
                  endif

                  assign inactive_dots = block.settings.total_dots | minus: active_dots
                  
                -%}

                {%- for i in (1..active_dots) -%}
                  <span class="progress-dots__item progress-dots__item-active"></span>
                {%- endfor -%}
                
                {%- for i in (1..inactive_dots) -%}
                  <span class="progress-dots__item"></span>
                {%- endfor -%}

              </div>
            </div>

            {% assign active_icon_name = block.settings.dot_icon | append: "-active" %}
            {% capture active_icon_content %}{% render 'theme-icons', icon: active_icon_name %}{% endcapture %}

            {% liquid
              assign active_icon_size = active_icon_content | size
              if active_icon_size == 0
                assign active_icon_name = block.settings.dot_icon
              else 
                assign active_icon_name = block.settings.dot_icon | append: "-active"
              endif

              assign color_active = settings.color_text_main | url_encode 
              if block.settings.icon_color_active != 'rgba(0,0,0,0)'
                assign color_active = block.settings.icon_color_active | url_encode 
              endif

              assign color_default = settings.color_text_main | color_modify: 'alpha', 0.15
              if block.settings.icon_color_default != 'rgba(0,0,0,0)'
                assign color_default = block.settings.icon_color_default | url_encode 
              endif
            %}
            {% style %}
              #progress-dots-{{ block.id }} .progress-dots__item {
                background-image: url('data:image/svg+xml;utf8,{%- render 'theme-icons', icon: block.settings.dot_icon color_1: color_default color_2: 'white' -%}');
                height: {{ block.settings.dot_size }}px;
                width: {{ block.settings.dot_size }}px;
              }
              #progress-dots-{{ block.id }} .progress-dots__item-active {
                background-image: url('data:image/svg+xml;utf8,{%- render 'theme-icons', icon: active_icon_name color_1: color_active color_2: 'white' -%}');
                height: {{ block.settings.dot_size }}px;
                width: {{ block.settings.dot_size }}px;
              }
            {% endstyle %}
          </div>

        {%- when 'vendor' -%} 
          <span 
            class="product__subtitle {{ block.settings.text_size }}"
            {{ block.shopify_attributes }}
          >
            <a href="{{ product.vendor | url_for_vendor }}"><span class="text-animation--underline-thin">{{ product.vendor | escape }}</span></a>
          </span>

        {%- when 'space' -%}
          <div class="empty-space" {{ block.shopify_attributes }}>&nbsp;</div>

        {%- when 'badges' -%}
          {{ 'component-product-item.css' | asset_url | stylesheet_tag }}
          <div class="product__badges" {{ block.shopify_attributes }}>
            {%- render 'product-badges', product: product, text_class: 'text-size--small' -%}
          </div>

        {%- when 'text' -%} 
          <span 
            class="product__subtitle {{ block.settings.text_size }}"
            style="
              {% if block.settings.text_color != 'rgba(0,0,0,0)' %} color: {{ block.settings.text_color }}; {% endif %}
              {% if block.settings.text_transform %} text-transform: uppercase {% endif %}
            " 
            {{ block.shopify_attributes }}
          >
            {{ block.settings.text }}
          </span>

        {%- when 'title' -%}
          <h1 class="product__title h2" {{ block.shopify_attributes }}>{{ product.title | escape }}</h1>

        {%- when 'price' -%}

          <div class="text-size--xlarge" {{ block.shopify_attributes }} data-update-block="price-compact">
            <span class="show-block-if-variant-selected">
              {%- render 'product-price', variant: current_variant, target: current_variant, id: section.id -%}
            </span>
            {%- unless default_to_first_variant -%}
              <span class="show-block-if-variant-not-selected-yet">
                {%- render 'product-price', variant: current_variant, target: product, product_price_varies: product.price_varies, id: section.id -%}
              </span>
            {%- endunless -%}
          </div>

        {%- when 'tax_info' -%}

          {%- if cart.taxes_included or shop.shipping_policy.body != blank -%}
            <div class="product-policies text-size--small" data-product-policies {{ block.shopify_attributes }}>
              {% if cart.taxes_included %}
                {{ 'products.page.include_taxes' | t }}
              {%- endif -%}
              {%- if shop.shipping_policy.body != blank -%}
                {{ 'products.page.shipping_policy_html' | t: link: shop.shipping_policy.url }}
              {%- endif -%}
            </div>
          {%- endif -%}

        {%- when 'sku_barcode' -%}

          <div class="product__sku-barcode text-size--small show-block-if-variant-selected" 
            data-update-block="sku-barcode"
            {{ block.shopify_attributes }}
          >
            {%- if current_variant.sku != blank -%}
              <span class="product__sku">
                {{ 'products.page.sku' | t }}{{ current_variant.sku | escape }}
              </span>
            {%- endif -%}
            {%- if current_variant.barcode != blank -%}
              <span class="product__barcode">
                  {{ 'products.page.barcode' | t }}{{ current_variant.barcode | escape }}
              </span>
            {%- endif -%}
          </div>

        {%- when 'description' -%}

          {%- if product.description != blank -%}
            <div class="product__description rte" {{ block.shopify_attributes }}>
              {{ product.description }}
            </div>
          {%- endif -%}

        {%- when 'nutrition' -%}
          {%- liquid
            assign show_block = true
            if block.settings.hide_block_if == true and block.settings.content == blank
              assign show_block = false
            endif
            assign nutrition_script = true 
          -%}
          {%- if show_block -%}
            <toggle-tab class="toggle toggle--table{% unless block.settings.blend_in %} element--border-radius element--has-border{% else %} toggle--table--blend-in{% endunless %}" {{ block.shopify_attributes }}>
              <span class="toggle__title" data-js-title tabindex="0" aria-expanded="false" role="button" aria-controls="toggle-{{ block.id }}">
                <span class="toggle__icon-title">
                  {%- if block.settings.custom_icon != blank -%}
                    <img src="{{ block.settings.custom_icon | image_url: width: 76, height: 76, crop: 'center' }}" width="{{ block.settings.custom_icon.width }}" height="{{ block.settings.custom_icon.height }}" style="width:24px;height:24px;border-radius:0;" alt="{{ block.settings.custom_icon.alt | escape }}">
                  {%- elsif block.settings.blend_in == false -%}
                    {%- render 'theme-symbols', icon: 'table' -%}
                  {%- endif -%}
                  {{ block.settings.heading | escape }}
                </span>
              </span>
              <div id="toggle--{{ block.id }}" data-js-content class="toggle__content rte remove-empty-space" role="region">
                
                <nutritional-info
                  data-title-label-first="{{ block.settings.label_first | escape }}"
                  data-title-label-second="{{ block.settings.label_second | escape }}"
                  data-title-label-third="{{ block.settings.label_third | escape }}"
                >
                  {{ block.settings.content }}
                </nutritional-info>

                {%- unless block.settings.content_extra == blank -%}
                  <div class="rte">
                    {{ block.settings.content_extra }}
                  </div>
                {%- endunless -%}
                
              </div>
            </toggle-tab>
          {%- endif -%}

        {%- when 'variant_picker' -%}

          {%- render 'product-variant-picker', product: product, current_variant: current_variant, block: block, id: section.id, default_to_first_variant: default_to_first_variant -%}

        {%- when 'buy_buttons' -%}

          <div class="product-actions" {{ block.shopify_attributes }}>

            {%- if block.settings.show_price -%}
              <div data-update-block="price-extended" class="product-actions__price show-block-if-variant-selected">
                {%- render 'product-price', variant: current_variant, target: current_variant, text_size_class: 'text-size--xlarge', show_saving: true, id: section.id -%}
              </div>
            {%- endif -%}

            {%- if product.metafields.custom.info_article_url != blank -%}
              {%- liquid
                assign raw_url = product.metafields.custom.info_article_url
                assign cleaned_url = raw_url | remove_first: shop.url
                assign cleaned_url = cleaned_url | remove_first: 'http://' | remove_first: 'https://'
                assign cleaned_url = cleaned_url | remove_first: 'www.'
                assign cleaned_url = cleaned_url | remove_first: 'dnatural.de'
                assign cleaned_url = cleaned_url | remove_first: '/'
                assign info_article_url = cleaned_url | prepend: '/'
              -%}
              
              <a class="button button--outline button--regular button--fullwidth" style="margin-top: 1rem;" href="{{ info_article_url }}">
                {%- comment -%}Untranslated label{% endcomment -%}
                Zum Artikel
              </a>
            {%- else -%}
              <product-form 
                id="add-to-cart-{{ section.id }}" class="product-form"
                {% if settings.cart_action == 'overlay' %} data-ajax-cart data-js-product-form {% endif %}
              >
                
                {%- form 'product', product, id: product_form_id, class: 'form', novalidate: 'novalidate', data-type: 'add-to-cart-form' -%}

                  <span data-update-block="variant-id-main"><input type="hidden" name="id" value="{{ current_variant.id }}"></span>

                  {%- liquid
                    if product.gift_card? and block.settings.show_gift_card_recipient
                      render 'gift-card-recipent-form', product: product
                    endif
                  -%}

                  <div class="product__cart-functions">

                    <div class="flex-buttons shopify-buttons--not-{{ block.settings.style_buttons }}">

                      <style>
                        #add-to-cart-{{ section.id }} .flex-buttons {
                          {%- if block.settings.color_text_buttons != 'rgba(0,0,0,0)' -%}
                            --color-text-main: {{ block.settings.color_text_buttons }};
                            {%- liquid 
                              assign brightness_product_button = block.settings.color_text_buttons | color_brightness
                              if brightness_product_button > 150 
                                assign color_foreground_product_button = '#000' 
                              else 
                                assign color_foreground_product_button = '#fff' 
                              endif 
                            -%}
                            --color-foreground-main: {{ color_foreground_product_button }};
                          {%- endif -%}
                          {%- if block.settings.color_accent_buttons != 'rgba(0,0,0,0)' -%}
                            --color-accent-main: {{ block.settings.color_accent_buttons }};
                            {%- liquid
                              assign brightness_accent_main = block.settings.color_accent_buttons | color_brightness
                              if brightness_accent_main > 150 
                                assign color_foreground_accent_button = '#000' 
                              else 
                                assign color_foreground_accent_button = '#fff' 
                              endif 
                            -%}
                            --color-foreground-accent-main: {{ color_foreground_accent_button }};
                          {%- endif -%}
                        }
                      </style>

                      {%- liquid 
                        if block.settings.show_quantity_selector
                          render 'product-quantity', variant: current_variant, id: section.id, default_to_first_variant: default_to_first_variant
                        endif
                      -%}

                      <button type="submit" name="add" class="add-to-cart button button--{{ block.settings.style_buttons }} button--product button--loader 
                        {% unless current_variant.available %} disabled {% endunless %}
                        {% unless default_to_first_variant %} disabled {% endunless %}" 
                        data-js-product-add-to-cart
                      >
                        <span class="button__text" data-js-product-add-to-cart-text {% if block.settings.show_preorder %} data-show-preorder-wording {% endif %}>
                          {%- liquid
                            unless default_to_first_variant
                              if product.available
                                echo 'products.grid.choose_variant_first' | t
                              else
                                echo 'products.page.inventory.sold_out_variant' | t
                              endif
                            else
                              if current_variant.available
                                unless block.settings.show_preorder
                                  echo 'products.page.add_to_cart_button' | t
                                else
                                  echo 'products.page.preorder_button' | t
                                endunless
                              elsif current_variant == null
                                echo 'products.page.inventory.unavailable_variant' | t
                              else
                                echo 'products.page.inventory.sold_out_variant' | t
                              endif
                            endunless
                          -%}
                        </span>
                        <span class="button__preloader">
                          <svg class="button__preloader-element" viewBox="25 25 50 50"><circle cx="50" cy="50" r="20" fill="none" stroke-width="4"/></svg>
                        </span>
                      </button>

                      {%- liquid
                        if product.gift_card? and block.settings.show_gift_card_recipient
                          assign show_dynamic_checkout = false
                        else 
                          assign show_dynamic_checkout = block.settings.show_dynamic_checkout
                        endif
                        if show_dynamic_checkout
                          echo form | payment_button
                        endif
                      -%}

                    </div>
                    
                  </div>

                {%- endform -%}

              </product-form>

              {%- form 'product', product, id: 'product-form-installment' -%}
                <span data-update-block="variant-id-installment"><input type="hidden" name="id" value="{{ current_variant.id }}"></span>
                {{ form | payment_terms }}
              {%- endform -%}
            {%- endif -%}

          </div>

        {%- when 'collapsible_tab' -%}

          {%- liquid
            assign show_block = true
            if block.settings.hide_block_if == true 
              if block.settings.page == blank and block.settings.image == blank and block.settings.content == blank
                assign show_block = false
              endif
            endif
          -%}
          {%- if show_block -%}
            <toggle-tab class="toggle" {{ block.shopify_attributes }}>
              <span class="toggle__title" data-js-title tabindex="0" aria-expanded="false" role="button" aria-controls="toggle-{{ block.id }}">{{ block.settings.heading | escape }}</span>
              <div id="toggle--{{ block.id }}" data-js-content class="toggle__content rte remove-empty-space" role="region">
                {%- liquid
                  echo block.settings.content
                  unless block.settings.page == blank
                    echo block.settings.page.content
                  endunless
                  unless block.settings.image == blank
                    assign sizes = 'sizes="(max-width: 948px) calc(100vw - 20px), (min-width: 949px) and (max-width: 1023px) calc(100vw - 200px), 25vw"'
                    render 'lazy-image', image: block.settings.image, type: 'image', alt: block.settings.title, sizes: sizes
                  endunless
                -%}
              </div>
            </toggle-tab>
          {%- endif -%}

        {%- when 'pickup_availability' -%} 

          {%- if block.settings.style == 'compact' -%}
            
            <div class="no-js-hidden" {{ block.shopify_attributes }} 
              data-update-block="pickup-availability-compact show-block-if-variant-selected"
            >
              <pickup-availability-compact
                class="no-js-hidden"
                data-base-url="{{ shop.url }}{{ routes.root_url }}"
                data-variant-id="{{ current_variant.id }}"
                data-has-only-default-variant="{{ product.has_only_default_variant }}"
                data-id="{{ section.id }}"
                data-static
                {{ block.shopify_attributes }}
              >
                <span class="alert alert--note alert--circle alert--circle-loading">
                  {{ 'store_availability.compact_widget.checking_availability' | t }}
                </span>
                <div style="display:none" data-js-pickup-availability-data>{%- render 'pickup-availability-data', current_variant: current_variant -%}</div>
              </pickup-availability-compact>
            </div>

          {%- else -%}

            <div 
              class="pickup-availability-widget element--border-radius no-js-hidden show-block-if-variant-selected"
              {{ block.shopify_attributes }} 
              data-update-block="pickup-availability"
            >
              {%- if current_variant != null -%}
                <pickup-availability-widget data-id="{{ section.id }}">
                  {%- render 'pickup-availability-widget', product: product, current_variant: current_variant, include_sidebar: true -%}
                </pickup-availability-widget>
              {%- endif -%}
            </div>

          {%- endif -%}

        {%- when 'complementary' -%}

          <product-recommendations data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ block.settings.products_number }}&intent=complementary" {{ block.shopify_attributes }} style="display:block!important" class="product-related">

            {%- if recommendations.performed and recommendations.products_count > 0 -%}

              <span class="product-related-title text-size--large text-weight--bold">{{ block.settings.heading | escape }}</span>

              {%- for product in recommendations.products -%}

                <div class="product-related-item" data-js-product-item>

                  <a href="{{ product.url }}" class="product-related-item__image element--border-width-clamped element--border-radius">
                    {%- render 'lazy-image-small', image: product.featured_media, aspect_ratio: settings.cart_image_ratio, fit: settings.cart_image_fit -%}
                  </a>

                  <div class="product-related-item__text">
                    <a class="product-related-item__title text-size--large text-weight--bold" href="{{ product.url }}">
                      <span class="text-animation--underline-thin">{{ product.title | escape }}</span>
                    </a>
                    {%- if block.settings.show_price -%}
                      <span class="product-related-item__price">
                        {%- render 'product-price', variant: product.selected_or_first_available_variant, target: product.selected_or_first_available_variant, product_price_varies: product.price_varies -%}
                      </span>
                    {%- endif -%}
                    {%- if block.settings.show_rating -%}
                      <div class="product-item__ratings">
                        {%- render 'rating', vendor: settings.reviews_app, product: product, hide_no_reviews_on_mobile: true -%}
                      </div>
                    {%- endif -%}
                  </div>  

                  {%- if block.settings.show_quick_buy -%}
                    <div class="product-related-item__button">
                      {%- render 'quick-buy', product: product, button_classes: 'button--small' -%}
                    </div>
                  {%- endif -%}

                </div>
                    
              {%- endfor -%}

            {%- endif -%}

          </product-recommendations>

        {%- when 'rating' -%}
          <div {{ block.shopify_attributes }}>
            {%- render 'rating', vendor: settings.reviews_app, product: product -%}
          </div>

        {%- when 'custom_liquid' -%}
        <div class="product-custom-liquid" {{ block.shopify_attributes }}>
          {{ block.settings.custom_liquid }}
        </div>
        
        {%- when 'button' -%}
          {%- if block.settings.text and block.settings.text != blank -%}
          <div class="custom-button" {{ block.shopify_attributes }}>
            <a href="{{ block.settings.link }}" 
              {% unless block.settings.open_in_new_window == false %} target="_blank" {% endunless %}
              class="button button--regular button--{{ block.settings.style }}"
            >
              <span>{{ block.settings.text | escape }}</span>
            </a>
          </div>
          {%- endif -%}
        
        {%- when 'popup' -%}
          {%- liquid
            assign show_block = true
            if block.settings.hide_block_if == true and block.settings.page == blank and block.settings.content == blank
              assign show_block = false
            endif
          -%}

          {%- if show_block -%}
            <div class="custom-button" {{ block.shopify_attributes }}>
              
              <button 
                class="button button--regular button--{{ block.settings.style }}" 
                onclick="document.getElementById('modal-common').show('#modal-{{ block.id }}')"
                aria-controls="modal-common" aria-expanded="false"
              >
                <span>{{ block.settings.text | escape }}</span>
              </button>

              <div id="modal-{{ block.id }}" style="display:none">
                <div class="element--vertically-centered">
                  <div class="container--compact">
                    <div class="modal-content">
              
                      <div class="modal-heading">
                        <div class="modal-heading__text remove-empty-space">
                          <span class="h4 popup-title">{{ block.settings.title | escape }}</span>
                        </div>
                        <div class="modal-heading__actions">
                          <button class="modal-close" data-js-close>{%- render 'theme-symbols', icon: 'close' -%}</button>
                        </div>
                      </div>
              
                      <div class="rte text-align--left">
                        {%- liquid
                          if block.settings.page != blank
                            echo block.settings.page.content
                          else
                            echo block.settings.content
                          endif 
                        -%}
                      </div>  
                    </div>
                  </div>
                </div>
                <span class="modal-background" data-js-close></span>
              </div>

            </div>
          {% endif %}

        {%- when 'related' -%}

          {%- liquid
            assign show_block = true
            if block.settings.hide_block_if == true and block.settings.product_list == blank
              assign show_block = false
            endif
          -%}
          {%- if show_block -%}
            <div class="product-related" {{ block.shopify_attributes }}>

              {%- unless block.settings.title == blank -%}
                <span class="product-related-title text-size--large text-weight--bold">{{ block.settings.title | escape }}</span>
              {%- endunless -%}

              {%- for product in block.settings.product_list -%}

                <div class="product-related-item">

                  <a href="{{ product.url }}" class="product-related-item__image element--border-width-clamped element--border-radius">
                    {%- render 'lazy-image-small', image: product.featured_media, aspect_ratio: settings.cart_image_ratio, fit: settings.cart_image_fit -%}
                  </a>

                  <div class="product-related-item__text">
                    <a class="product-related-item__title text-size--large text-weight--bold" href="{{ product.url }}">
                      <span class="text-animation--underline-thin">{{ product.title | escape }}</span>
                    </a>
                    <span class="product-related-item__price">
                      {%- render 'product-price', variant: product.selected_or_first_available_variant, target: product.selected_or_first_available_variant, product_price_varies: product.price_varies -%}
                    </span>
                  </div>

                  <div class="product-related-item__button">
                    {%- render 'quick-buy', product: product, button_classes: 'button--small' -%}
                  </div>

                </div>

              {%- endfor -%}

            </div>
          {%- endif -%}

        {%- when 'icons' -%}
          
          {%- liquid
            assign show_block = true
            if block.settings.hide_block_if == true and block.settings.icon_1_image == blank and block.settings.icon_1_label == blank
              assign show_block = false
            endif
          -%}

          {%- if show_block -%}
            <div class="product-icons-list" {{ block.shopify_attributes }}>

              {%- unless block.settings.title == blank -%}
                <p class="text-size--large text-weight--bold">{{ block.settings.title | escape }}</p>
              {%- endunless -%}

              {%- capture product_icon_list -%}
                {%- liquid
                  render 'product-icon-label', icon: block.settings.icon_1_image, label: block.settings.icon_1_label, style: 'tooltip'
                  render 'product-icon-label', icon: block.settings.icon_2_image, label: block.settings.icon_2_label, style: 'tooltip'
                  render 'product-icon-label', icon: block.settings.icon_3_image, label: block.settings.icon_3_label, style: 'tooltip'
                  render 'product-icon-label', icon: block.settings.icon_4_image, label: block.settings.icon_4_label, style: 'tooltip'
                  render 'product-icon-label', icon: block.settings.icon_5_image, label: block.settings.icon_5_label, style: 'tooltip'
                  render 'product-icon-label', icon: block.settings.icon_6_image, label: block.settings.icon_6_label, style: 'tooltip'
                -%}
              {%- endcapture -%}

              {%- unless product_icon_list == blank -%}
                <div class="product-icons-list-container">{{ product_icon_list }}</div>
              {%- endunless -%}

            </div>
          {%- endif -%}

        {%- when 'share' -%}
          <div class="product-sharing" {{ block.shopify_attributes }}>

            {%- liquid
              assign share_link = shop.url | append: product.url
              assign share_title = product.title | url_param_escape
              assign share_image = product.featured_media | image_url: width: 1024
            -%}

            <div class="site-sharing">
              <p class="text-size--large text-weight--bold">{{ 'general.sharing.title' | t }}</p>

                <a href="//www.facebook.com/sharer.php?u={{ share_link }}" target="_blank" title="{{ 'general.accessibility_labels.share.facebook' | t }}" class="text-with-icon text-with-icon--small">
                  <span aria-hidden="true" class="text-with-icon__icon">{% render 'theme-symbols', icon: 'facebook' %}</span>
                  <span class="text-animation--underline-thin">{{ 'general.sharing.facebook' | t }}</span>
                </a> 

                <a href="//twitter.com/intent/tweet?text={{ share_title }}&amp;url={{ share_link }}" target="_blank" title="{{ 'general.accessibility_labels.share.twitter' | t }}" class="text-with-icon text-with-icon--small">
                  <span aria-hidden="true" class="text-with-icon__icon">{% render 'theme-symbols', icon: 'twitter' %}</span>
                  <span class="text-animation--underline-thin">{{ 'general.sharing.twitter' | t }}</span>
                </a>

                <a href="//pinterest.com/pin/create/button/?url={{ share_link }}&amp;media={{ share_image }}&amp;description={{ share_title }}" target="_blank" title="{{ 'general.accessibility_labels.share.pinterest' | t }}" class="text-with-icon text-with-icon--small">
                  <span aria-hidden="true" class="text-with-icon__icon">{% render 'theme-symbols', icon: 'pinterest' %}</span>
                  <span class="text-animation--underline-thin">{{ 'general.sharing.pinterest' | t }}</span>
                </a> 

            </div>
          </div>

      {%- endcase -%}

    {%- endfor -%}

 	</div>

  {%- if first_3d_model -%}
    <script type="application/json" id="ProductJSON-{{ product.id }}">
      {{ product.media | where: 'media_type', 'model' | json }}
    </script>
  {%- endif -%}

</product-page>

<script src="{{ 'component-toggle.js' | asset_url }}" defer></script>

{%- if section.settings.enable_zoom -%}
  <script src="{{ 'component-product-image-zoom.js' | asset_url }}" defer></script>
{%- endif -%}

<script src="{{ 'section-main-product.js' | asset_url }}" defer></script>

{%- if video_script -%}
  {{ 'component-video.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'component-video.js' | asset_url }}" defer></script>
{%- endif -%}
{%- if nutrition_script -%}
  <script src="{{ 'component-nutritional-info.js' | asset_url }}" defer></script>
{%- endif -%}

{%- if section.settings.sticky_add_to_cart and product.available -%}

  {%- liquid
    if settings.show_currency_codes
      assign iso_code = localization.country.currency.iso_code | prepend: ' ' 
    endif
  -%}

  <sticky-add-to-cart
    {% if product.has_only_default_variant %} data-single {% else %} data-multiple {% endif %}
    data-id="product-{{ section.id }}"
    class="sticky-add-to-cart sticky-add-to-cart--{{ section.settings.sticky_atc_style }}"
    style="
    --color-background-main: {{ section.settings.sticky_bar_bgcolor }};
    --color-text-main: {{ section.settings.sticky_bar_txtcolor }};
    "
  > 
    <div class="container container--large">
      <div class="sticky-add-to-cart__inner">
        <div class="sticky-add-to-cart__title"><span class="text-size--xlarge text-weight--bold">{{ product.title | escape }}</span></div>
        <div class="sticky-add-to-cart__actions">
          <div class="sticky-add-to-cart__price  text-size--large">
            {%- if product.price_varies -%}
              <span class="sticky-add-to-cart__price-varies">
                {%- liquid
                  assign price = current_variant.price | money | append: iso_code
                  if current_variant.compare_at_price > current_variant.price or current_variant.compare_at_price_varies
                    echo 'products.grid.on_sale_from_html' | t: price: price 
                  else
                    echo 'products.grid.from_text_html' | t: price: price 
                  endif
                -%}
              </span>
            {%- else -%}
              <span class="sticky-add-to-cart__price-original text-weight--bold">{{ current_variant.price | money | append: iso_code }}</span>
              {%- if current_variant.compare_at_price > current_variant.price -%}
                <del class="sticky-add-to-cart__price-compare">{{ current_variant.compare_at_price | money | append: iso_code }}</del>
              {%- endif -%}
            {%- endif -%}
          </div>
          <div class="sticky-add-to-cart__button">
            {%- if product.has_only_default_variant -%}
              <button data-js-atc class="button button button--solid button--regular button--loader ">
                <span class="button__text">{{ 'products.page.add_to_cart_button' | t }}</span>
                <span class="button__preloader">
                  <svg class="button__preloader-element" viewBox="25 25 50 50"><circle cx="50" cy="50" r="20" fill="none" stroke-width="4"/></svg>
                </span>
              </button>
            {%- else -%}
              <button data-js-choose class="button button button--solid button--regular button--loader"><span class="button__text">{{ 'products.page.choose_options_button' | t }}</span></button>
            {%- endif -%}
          </div>
        </div>
      </div>
    </div>
  </sticky-add-to-cart>

{%- endif -%}

{% schema %}
	{
    "name": "t:sections.local-extra-words.sections.main-product.name",
    "class": "mount-css-slider mount-toggles mount-product-page",
    "tag": "section",
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "text",
        "name": "t:sections.main-product.blocks.text.name",
        "settings": [
          {
            "id": "text",
            "type": "richtext",
            "default": "<p>Text block</p>",
            "label": "t:sections.main-product.blocks.text.settings.text.label"
          },
          {
            "type": "select",
            "id": "text_size",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.label",
            "options": [
              {
                "value": "text-size--xsmall",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__1"
              },
              {
                "value": "text-size--regular",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__2"
              },
              {
                "value": "text-size--large",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__3"
              }
            ],
            "default": "text-size--regular"
          },
          {
            "type": "color",
            "id": "text_color",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.color.label",
            "default": "#6A6A6A"
          },
          {
            "type": "checkbox",
            "id": "text_transform",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.transform.label",
            "default": false
          }
        ]
      },
      {
        "type": "vendor",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.vendor",
        "settings": [
          {
            "type": "select",
            "id": "text_size",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.label",
            "options": [
              {
                "value": "text-size--xsmall",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__1"
              },
              {
                "value": "text-size--regular",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__2"
              },
              {
                "value": "text-size--large",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__3"
              }
            ],
            "default": "text-size--regular"
          }
        ]
      },
      {
        "type": "badges",
        "name": "t:local-march-update.blocks.badges.name",
        "limit": 1
      },
      {
        "type": "title",
        "name": "t:sections.main-product.blocks.title.name",
        "limit": 1
      },
      {
        "type": "price",
        "name": "t:sections.main-product.blocks.price.name",
        "limit": 1
      },
      {
        "type": "tax_info",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.tax_info.name",
        "limit": 1
      },
      {
        "type": "sku_barcode",
        "name": "t:sections.main-product.blocks.sku_barcode.name",
        "limit": 1
      },
      {
        "type": "variant_picker",
        "name": "t:sections.main-product.blocks.variant_picker.name",
        "limit": 1,
        "settings": [ 
          {
            "type": "select",
            "id": "variants_style",
            "label": "t:sections.split-extra-words.sections.main-product.settings.variants.label",
            "options": [
              {
                "value": "radio",
                "label": "t:sections.split-extra-words.sections.main-product.settings.variants.options__1"
              },
              {
                "value": "select",
                "label": "t:sections.split-extra-words.sections.main-product.settings.variants.options__2"
              }
            ],
            "default": "radio"
          },
          {
            "type": "paragraph",
            "content": "t:new_color_swatches"
          },
          {
            "type": "select",
            "id": "show_quantities",
            "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.label",
            "info": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.info",
            "options": [
              {
                "value": "no",
                "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.options__1.label"
              },
              {
                "value": "below",
                "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.options__2.label"
              },
              {
                "value": "always",
                "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.options__3.label"
              }
            ],
            "default": "no"
          }
        ]
      },
      {
        "type": "buy_buttons",
        "name": "t:sections.main-product.blocks.buy_buttons.name",
        "limit": 1,
        "settings": [
          {
            "type": "checkbox",
            "id": "show_price",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.buy_buttons.settings.show_price",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_quantity_selector",
            "label": "t:sections.main-product.blocks.buy_buttons.settings.show_quantity_selector.label",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_preorder",
            "default": false,
            "label": "t:local-220.preorder"
          },
          {
            "type": "checkbox",
            "id": "show_dynamic_checkout",
            "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
            "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_gift_card_recipient",
            "default": false,
            "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
            "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.custom_colors"
          },
          {
            "type": "color",
            "id": "color_text_buttons",
            "label": "t:sections.split-extra-words.settings_schema.colors.settings.text",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "color",
            "id": "color_accent_buttons",
            "label": "t:sections.local-extra-words.settings_schema.colors.settings.accent",
            "default": "rgba(0,0,0,0)"
          },
          {
            "id": "style_buttons",
            "label": "t:sections.local-extra-words.sections.buttons.style.label",
            "type": "select",
            "options": [
              {
                "value": "outline",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__1"
              },
              {
                "value": "solid",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__2"
              }
            ],
            "default": "solid"
          }
        ]
      },
      {
        "type": "description",
        "name": "t:sections.main-product.blocks.description.name",
        "limit": 1
      },
      {
        "type": "nutrition",
        "name": "t:local-march-update.blocks.nutritional.name",
        "settings": [
          {
            "id": "heading",
            "type": "text",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label",
            "default": "Nutritional information"
          },
          {
            "type": "image_picker",
            "id": "custom_icon",
            "label": "t:sections.local-extra-words.sections.header.blocks.info.custom-icon.label",
            "info": "t:sections.local-extra-words.sections.header.blocks.info.custom-icon.info"
          },
          {
            "type": "text",
            "id": "label_first",
            "label": "t:local-march-update.blocks.nutritional.label_first",
            "default": "Typical values"
          },
          {
            "type": "text",
            "id": "label_second",
            "label": "t:local-march-update.blocks.nutritional.label_second",
            "default": "Per 100g"
          },
          {
            "type": "text",
            "id": "label_third",
            "label": "t:local-march-update.blocks.nutritional.label_third",
            "default": "Per serving"
          },
          {
            "type": "richtext",
            "id": "content",
            "label": "t:local-march-update.blocks.nutritional.information.label",
            "info": "t:local-march-update.blocks.nutritional.information.info",
            "default": "<p>Energy, 205 kJ / 49 kcal<br/>Fat, 0g<br/>- of which: Saturates, 0g<br/>Carbohydrate, 10g<br/>- of which: Sugars, 7g<br/></p>"
          },
          {
            "type": "richtext",
            "id": "content_extra",
            "label": "t:local-march-update.blocks.nutritional.extra_information",
            "default": "<p><strong>Ingredients:</strong> Sugar, Glucose Syrup, Water, Beef Gelatine, Acid: Citric Acid, Flavouring</p>"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "checkbox",
            "label": "t:local-march-update.labels.dynamic_content.hide_block",
            "id": "hide_block_if",
            "default": false
          },
          {
            "type": "checkbox",
            "label": "Blend in with other accordion items",
            "id": "blend_in",
            "default": false
          }
        ]
      },
      {
        "type": "rating",
        "name": "t:settings_schema.product-grid.settings.header__1.content",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:settings_schema.product-grid.settings.header__1.info"
          }
        ]
      },
      {
        "type": "share",
        "name": "t:sections.main-product.blocks.share.name",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.main-product.blocks.share.settings.featured_image_info.content"
          },
          {
            "type": "paragraph",
            "content": "t:sections.main-product.blocks.share.settings.title_info.content"
          }
        ]
      },
      {
        "type": "collapsible_tab",
        "name": "t:sections.main-product.blocks.collapsible_tab.name",
        "settings": [
          {
            "id": "heading",
            "type": "text",
            "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label",
            "default": "Collapsible tab"
          },
          {
            "id": "content",
            "type": "richtext",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
          },
          {
            "id": "page",
            "type": "page",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.image.label"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "checkbox",
            "label": "t:local-march-update.labels.dynamic_content.hide_block",
            "id": "hide_block_if",
            "default": false
          }
        ]
      }, 
      {
        "type": "pickup_availability",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.name",
        "limit": 2,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.info"
          },
          {
            "type": "select",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.settings.style",
            "id": "style",
            "options": [
              {
                "value": "compact",
                "label": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.settings.option__1"
              },
              {
                "value": "extended",
                "label": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.settings.option__2"
              }
            ],
            "default": "extended"
          }
        ]
      },
      {
        "type": "related",
        "name": "t:sections.local-extra-words.settings_schema.product-card.products-list.name",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Products list"
          },
          {
            "type": "product_list",
            "id": "product_list",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.related.settings.products",
            "limit": 3
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "checkbox",
            "label": "t:local-march-update.labels.dynamic_content.hide_block",
            "id": "hide_block_if",
            "default": false
          }
        ]
      },
      {
        "type": "image",
        "name": "t:sections.local-extra-words.sections.image-section.name",
        "settings": [
          {
            "id": "image",
            "type": "image_picker",
            "label": "t:sections.gallery.blocks.image.settings.image.label"
          },
          {
            "id": "mobile_image",
            "type": "image_picker",
            "label": "t:sections.image.mobile_image"
          },
          {
            "type": "range",
            "id": "image_border_radius",
            "label": "t:sections.split-extra-words.settings_schema.typography.settings.border_radius",
            "min": 0,
            "max": 30,
            "step": 1,
            "unit": "px",
            "default": 0
          },
          {
            "type": "url",
            "id": "link",
            "label": "t:sections.local-extra-words.sections.announcement-bar.blocks.content.settings.link"
          },
          {
            "type": "checkbox",
            "id": "open_in_new_window",
            "label": "t:sections.local-extra-words.sections.announcement-bar.blocks.content.settings.target",
            "default": false
          }
        ]
      },
      {
        "type": "complementary",
        "name": "t:sections.complementary_products.name",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.complementary_products.settings.paragraph.content"
          },
          {
            "type": "text",
            "id": "heading",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Pairs well with"
          },
          {
            "type": "checkbox",
            "id": "show_price",
            "label": "t:settings_schema.search.settings.show_price.label",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_rating",
            "label": "t:settings_schema.product-grid.settings.show_reviews.label",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "show_quick_buy",
            "label": "t:sections.split-extra-words.settings_schema.product-grid.quick_buy.label",
            "default": true
          },
          {
            "type": "range",
            "id": "products_number",
            "label": "t:sections.featured-collection.settings.products_number.label",
            "min": 1,
            "max": 8,
            "step": 1,
            "default": 3
          }
        ]
      },
      {
        "type": "icons",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.icons.name",
        "limit": 2,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Icons list"
          },
          {
            "type": "paragraph",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.info"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_1"
          },
          {
            "type": "image_picker",
            "id": "icon_1_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_1_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_2"
          },
          {
            "type": "image_picker",
            "id": "icon_2_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_2_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_3"
          },
          {
            "type": "image_picker",
            "id": "icon_3_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_3_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_4"
          },
          {
            "type": "image_picker",
            "id": "icon_4_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_4_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_5"
          },
          {
            "type": "image_picker",
            "id": "icon_5_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_5_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_6"
          },
          {
            "type": "image_picker",
            "id": "icon_6_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_6_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "checkbox",
            "label": "t:local-march-update.labels.dynamic_content.hide_block",
            "id": "hide_block_if",
            "default": false
          }
        ]
      },
      {
        "type": "progress-dots",
        "name": "t:local-march-update.blocks.progress_dots.name",
        "settings": [
           {
            "type": "text",
            "id": "title",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Dots title"
          },
          {
            "type": "text",
            "id": "caption",
            "label": "t:sections.local-extra-words.sections.headings.caption",
            "default": "Dots caption"
          },
          {
            "type": "select",
            "id": "dot_icon",
            "label": "t:local-march-update.blocks.progress_dots.icon",
            "options": [
              {
                "value": "apple",
                "label": "Apple"
              },
              {
                "value": "bottle",
                "label": "Bottle"
              },
              {
                "value": "circle",
                "label": "Circle"
              },
              {
                "value": "circle-ghost",
                "label": "Circle empty"
              },
              {
                "value": "chili-pepper",
                "label": "Chili pepper"
              },
              {
                "value": "coffee-bean",
                "label": "Coffee bean"
              },
              {
                "value": "diamond",
                "label": "Diamond"
              },
              {
                "value": "drop",
                "label": "Drop"
              },
              {
                "value": "glass",
                "label": "Glass"
              },
              {
                "value": "heart",
                "label": "Heart"
              },
              {
                "value": "lemon",
                "label": "Lemon"
              },
              {
                "value": "star",
                "label": "Star"
              },
              {
                "value": "sun",
                "label": "Sun"
              }
            ],
            "default": "circle-ghost"
          },
          {
            "type": "range",
            "id": "dot_size",
            "label": "t:local-march-update.blocks.progress_dots.size",
            "min": 10,
            "max": 30,
            "step": 1,
            "default": 14
          },
          {
            "type": "range",
            "id": "total_dots",
            "label": "t:local-march-update.blocks.progress_dots.total",
            "min": 1,
            "max": 10,
            "step": 1,
            "default": 5
          },
          {
            "type": "range",
            "id": "active_dots",
            "label": "t:local-march-update.blocks.progress_dots.highlight",
            "min": 0,
            "max": 10,
            "step": 1,
            "default": 3
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "paragraph",
            "content": "t:local-march-update.blocks.progress_slider.dynamic_content.info"
          },
          {
            "type": "text",
            "id": "dynamic_content",
            "label": "t:local-march-update.blocks.progress_slider.dynamic_content.dots_label",
            "info": "custom.product_dots_chart_value"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.custom_colors"
          },
          {
            "type": "color",
            "id": "icon_color_default",
            "label": "t:local-march-update.blocks.progress_dots.inactive_color",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "color",
            "id": "icon_color_active",
            "label": "t:local-march-update.blocks.progress_dots.active_color",
            "default": "rgba(0,0,0,0)"
          }
        ]
      },
      {
        "type": "progress-slider",
        "name": "t:local-march-update.blocks.progress_slider.name",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Slider title"
          },
          {
            "type": "text",
            "id": "caption",
            "label": "t:sections.local-extra-words.sections.headings.caption",
            "default": "Slider caption"
          },
          {
            "type": "range",
            "id": "slider_height",
            "label": "t:local-march-update.blocks.progress_slider.height",
            "min": 4,
            "max": 20,
            "step": 1,
            "unit": "px",
            "default": 8
          },
          {
            "type": "range",
            "id": "slider_width",
            "label": "t:local-march-update.blocks.progress_slider.width",
            "min": 100,
            "max": 250,
            "step": 2,
            "unit": "px",
            "default": 250
          },
          {
            "type": "range",
            "id": "slider_value",
            "label": "t:local-march-update.blocks.progress_slider.value",
            "min": 0,
            "max": 100,
            "step": 1,
            "default": 50,
            "unit": "%"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "paragraph",
            "content": "t:local-march-update.blocks.progress_slider.dynamic_content.info"
          },
          {
            "type": "text",
            "id": "dynamic_content",
            "label": "t:local-march-update.blocks.progress_slider.dynamic_content.slider_label",
            "info": "custom.product_slider_chart_value"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.custom_colors"
          },
          {
            "type": "color",
            "id": "slider_color_default",
            "label": "t:local-march-update.blocks.progress_dots.inactive_color",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "color",
            "id": "slider_color_active",
            "label": "t:local-march-update.blocks.progress_dots.active_color",
            "default": "rgba(0,0,0,0)"
          }
        ]
      },
      {
        "type": "custom_liquid",
        "name": "t:sections.refactor_words.custom_code.name",
        "settings": [
          {
            "type": "liquid",
            "id": "custom_liquid",
            "label": "t:sections.custom-liquid.settings.custom_liquid.label"
          }
        ]
      },
      {
        "type": "button",
        "name": "t:sections.rich-text.blocks.button.name",
        "settings": [
          {
            "type": "text",
            "id": "text",
            "label": "t:sections.rich-text.blocks.button.settings.button_label.label",
            "default": "Learn more"
          },
          {
            "id": "style",
            "label": "t:sections.local-extra-words.sections.buttons.style.label",
            "type": "select",
            "options": [
              {
                "value": "outline",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__1"
              },
              {
                "value": "solid",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__2"
              }
            ],
            "default": "solid"
          },
          {
            "type": "url",
            "id": "link",
            "label": "t:sections.rich-text.blocks.button.settings.button_link.label"
          },
          {
            "type": "checkbox",
            "id": "open_in_new_window",
            "label": "t:sections.local-extra-words.sections.announcement-bar.blocks.content.settings.target",
            "default": false
          }
        ]
      },
      {
        "type": "popup",
        "name": "t:sections.popup.name",
        "settings": [
          {
            "type": "text",
            "id": "text",
            "label": "t:sections.rich-text.blocks.button.settings.button_label.label",
            "default": "t:settings_schema.labels.open_popup"
          },
          {
            "id": "style",
            "label": "t:sections.local-extra-words.sections.buttons.style.label",
            "type": "select",
            "options": [
              {
                "value": "outline",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__1"
              },
              {
                "value": "solid",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__2"
              }
            ],
            "default": "solid"
          },
          {
            "type": "header",
            "content": "t:sections.popup.settings.content.label"
          },
          {
            "type": "text",
            "id": "title",
            "label": "t:sections.popup.settings.title.label",
            "default": "t:settings_schema.labels.popup"
          },
          {
            "type": "richtext",
            "id": "content",
            "label": "t:sections.rich-text.blocks.text.name",
            "default": "<p>Stay up to date on the latest releases & special offers for this product.</p>"
          },
          {
            "type": "page",
            "id": "page",
            "label": "t:settings_schema.labels.page",
            "info": "t:settings_schema.labels.popup_page_info"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "checkbox",
            "label": "t:local-march-update.labels.dynamic_content.hide_block",
            "id": "hide_block_if",
            "default": false
          }
        ]
      },
      {
        "type": "variant_metafield",
        "name": "t:variant_metafields.name",
        "settings": [
          {
            "type": "paragraph",
            "content": "t:variant_metafields.info"
          },
          {
            "type": "text",
            "id": "key",
            "label": "t:variant_metafields.label",
            "info": "custom.variant_metafield"
          },
          {
            "type": "text",
            "label": "t:sections.local-extra-words.sections.main-header.settings.promotion_block.title.label",
            "id": "title"
          }
        ]
      },
      {
        "type": "space",
        "name": "t:local-march-update.blocks.space.name"
      }
    ],
    "settings": [
      {
        "type": "header",
        "content": "t:sections.main-product.settings.header.content",
        "info": "t:sections.main-product.settings.header.info"
      },
      {
        "type": "select",
        "id": "gallery_style",
        "label": "t:sections.main-product.settings.gallery_style.label",
        "options": [
          {
            "value": "scroll",
            "label": "t:sections.main-product.settings.gallery_style.options__1.label"
          },
          {
            "value": "slider",
            "label": "t:sections.main-product.settings.gallery_style.options__2.label"
          }
        ],
        "default": "scroll",
        "info": "t:sections.main-product.settings.gallery_style.info"
      },
			{
				"type": "select",
				"id": "gallery_ratio",
				"label": "t:sections.refactor_words.product-page.gallery_resize.label",
				"options": [
					{
						"value": "natural",
						"label": "t:sections.gallery.settings.aspect_ratio.options__5.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__5.group"
					},
					{
						"value": "1.33333",
						"label": "t:sections.gallery.settings.aspect_ratio.options__1.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					},
					{
						"value": "1",
						"label": "t:sections.gallery.settings.aspect_ratio.options__2.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					},
					{
						"value": "0.83333",
						"label": "t:sections.gallery.settings.aspect_ratio.options__3.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					},
					{
						"value": "0.666667",
						"label": "t:sections.gallery.settings.aspect_ratio.options__4.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					}
				],
				"default": "natural",
        "info": "t:sections.refactor_words.product-page.gallery_resize.info"
			},
			{
				"type": "checkbox",
				"id": "gallery_fit",
				"label": "t:sections.refactor_words.product-page.gallery_resize.option_1",
				"default": false
			},
      {
        "type": "checkbox",
        "id": "show_border",
        "label": "t:sections.local-extra-words.sections.main-product.settings.show_border",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "gallery_card_design",
        "label": "t:local-march-update.labels.inherit_card_design",
        "default": false
      },
			{
				"type": "range",
				"id": "gallery_padding",
				"label": "t:sections.refactor_words.product-page.gallery_padding.label",
				"min": 0,
				"max": 15,
				"step": 1,
				"default": 0,
				"unit": "%"
			},
      {
        "type": "radio",
        "id": "gallery_pagination",
        "label": "t:sections.local-extra-words.sections.main-product.settings.gallery_pagination",
        "options": [
          {
            "value": "dots",
            "label": "t:sections.main-product.settings.gallery_pagination.options__1.label"
          },
          {
            "value": "thumbnails",
            "label": "t:sections.main-product.settings.gallery_pagination.options__2.label"
          }
        ],
        "default": "dots"
      },
      {
        "type": "checkbox",
        "id": "enable_zoom",
        "label": "t:sections.main-product.settings.enable_zoom.label",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "enable_video_looping",
        "label": "t:sections.main-product.settings.enable_video_looping.label",
        "default": false
      },
      {
        "type": "header",
        "content": "t:sections.popup.settings.functionality.content"
      },
      {
        "type": "checkbox",
        "id": "default_to_first_variant",
        "label": "t:local-230.variant_default.label",
        "info": "t:local-230.variant_default.info",
        "default": true
      },
      {
        "type": "header",
        "content": "t:sticky_atc.label"
      },
      {
        "type": "checkbox",
        "id": "sticky_add_to_cart",
        "label": "t:sticky_atc.enable_sticky_atc",
        "default": false
      },
      { 
        "type": "select",
        "id": "sticky_atc_style",
        "label": "t:sections.local-extra-words.sections.header.blocks.info.style.label",
        "options": [
          {
            "value": "wide",
            "label": "t:sticky_atc.sticky_atc_style_wide"
          },
          {
            "value": "floating",
            "label": "t:sticky_atc.sticky_atc_style_floating"
          }
        ],
        "default": "wide"
      },
      {
        "type": "color",
        "id": "sticky_bar_bgcolor",
        "label": "t:sections.announcement-bar.settings.bar_bgcolor.label",
        "default": "#fff"
      },
      {
        "type": "color",
        "id": "sticky_bar_txtcolor",
        "label": "t:sections.announcement-bar.settings.bar_txtcolor.label",
        "default": "#111"
      }
    ]
  }
{% endschema %}