<div id="product-quick-view" data-js-product-page>

  {%- assign current_variant = product.selected_or_first_available_variant -%}
  {%- assign section_id = section.id | append: '-' | append: product.id -%}

  {%- liquid
    unless product.has_only_default_variant
      assign default_to_first_variant = section.settings.default_to_first_variant
    else 
      assign default_to_first_variant = true
    endunless
  -%}

  <product-page 
    id="product-{{ section_id }}" 
    class="grid grid--layout grid--gap-large grid-2 grid-lap-1 
    product product-quick-view__product product-component--default-to-first-variant-{{ default_to_first_variant }}" 
    data-collection="{{ collection.handle }}" data-id="{{ product.id }}" 
    data-availability="{{ product.available }}" 
    data-js-product-component
  >

    <div class="product-gallery product-gallery--slider" data-js-product-gallery>

      {% style %}
        #product-{{ section_id }} .product-gallery-item .lazy-image img {
          padding: {{ section.settings.gallery_padding }}% !important;
        }
      {% endstyle %}

      {%- if product.media.size > 1 -%}
        <css-slider data-options='{
          "selector": ".product-gallery-item",
          "autoHeight": true,
          {%- if section.settings.gallery_pagination == 'thumbnails' -%} 
            "thumbnails": false, 
            "navigation": false,
          {%- else -%}
            "indexNav": true,
            "navigationDOM": "<span class=\"css-slider-button css-slider-prev\" style=\"display:none\"><svg fill=\"none\" height=\"13\" viewBox=\"0 0 8 13\" width=\"8\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"#000\" ><path d=\"m7.91419 1.41431-6.48529 6.48528-1.4142102-1.41422 6.4852802-6.48527702z\"/><path d=\"m6.48528 12.9849-6.48528027-6.48532 1.41421027-1.41421 6.48528 6.48523z\"/></g></svg></span><span class=\"css-slider-button css-slider-next\" style=\"display:none\"><svg width=\"9\" height=\"13\" viewBox=\"0 0 9 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.914169 11.5857L7.39945 5.10041L8.81367 6.51463L2.32838 12.9999L0.914169 11.5857Z\" fill=\"black\"/><path d=\"M2.34307 0.0151367L8.82835 6.50042L7.41414 7.91463L0.928857 1.42935L2.34307 0.0151367Z\" fill=\"black\"/></svg></span>",
          {%- endif -%}
          "listenScroll": true
        }'
          class="css-slider--bottom-navigation"
          id="css-slider-{{ section_id }}"
        >
        {%- render 'custom-shadow', section_id: section.id -%} 
        {%- if settings.shadow_cards_x != 0 -%}
          {% style %}
            #css-slider-{{ section_id }} .css-slider-holder .css-slide {
              width: calc(100% - {{ settings.shadow_cards_x | abs }}px - {{ settings.shadow_cards_blur }}px);
            }
          {% endstyle %}
        {%- endif -%}
        {%- if settings.shadow_cards_y != 0 -%}
          {% style %}
            #css-slider-{{ section_id }} .css-slider-viewport  {
              padding-bottom: calc({{ settings.shadow_cards_y | abs }}px + {{ settings.shadow_cards_blur }}px);
              box-sizing: content-box;
            }
          {% endstyle %}
        {%- endif -%}
      {%- endif -%}

        <div class="grid grid--slider grid-1 grid--gap-small">

          {%- if product.media.size == 0 -%} 
            <div class="product-gallery-item" style="padding-top: 100%">
              {%- render 'lazy-svg', image: 'product-1', ratio: 1, class: 'svg-placeholder svg-placeholder--background' %}
            </div>
          {%- else -%}
            {%- liquid
              for media in product.media
                render 'product-media', media: media, aspect_ratio: section.settings.gallery_ratio, fit: section.settings.gallery_fit, enable_zoom: false, loop: section.settings.enable_video_looping, index: forloop.index0
                if media.media_type == "external_video" or media.media_type == "video"
                  assign video_script = true 
                endif
              endfor
            -%}
          {%- endif -%}

        </div>

      {%- if product.media.size > 1 -%} </css-slider> {%- endif -%}

      {%- if section.settings.gallery_pagination == 'thumbnails' and section.blocks.size > 1 -%}

        <div class="product-gallery__thumbnails" aria-hidden="true">
          <div class="product-gallery__thumbnails-holder">
            {%- for media in product.media -%}
              <button class="thumbnail element--border-radius" data-index="{{ forloop.index0 }}" tabindex="0">
                {%- render 'lazy-image-small', image: media.preview_image, aspect_ratio: settings.cart_image_ratio, fit: settings.cart_image_fit, class: 'thumbnail__image' -%}
                {%- if media.media_type == 'video' or media.media_type == 'external_video' -%}
                  <span class="thumbnail__badge">{% render 'theme-symbols', icon: 'badge-video-thumbnail' %}</span>
                {%- elsif media.media_type == 'model' -%}
                  <span class="thumbnail__badge">{% render 'theme-symbols', icon: 'badge-model-thumbnail' -%}</span>
                {%- endif -%}
              </button>
            {%- endfor -%}
          </div>
        </div>

      {%- endif -%}

      {%- assign first_3d_model = product.media | where: "media_type", "model" | first -%}

      {%- if first_3d_model -%}
        <div class="fullwidth text-align--center">
          <button
            title="{{ 'products.page.view_in_space_label' | t }}"
            class="button button--small button--icon button--outline product-gallery__view-in-space"
            data-shopify-xr
            data-shopify-first-model3d-id="{{ first_3d_model.id }}"
            data-shopify-model3d-id="{{ first_3d_model.id }}"
            data-shopify-title="{{ product.title | escape }}"
            data-shopify-xr-hidden
          >
            <span aria-hidden="true" class="button__icon">{%- render 'theme-symbols', icon: 'model-button' -%}</span>
            <span class='product-gallery__view-in-space-text'>{{ 'products.page.view_in_space' | t }}</span>
          </button>

          <link id="ModelViewerStyle" rel="stylesheet" href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css" media="print" onload="this.media='all'">
          <script src="{{ 'component-product-model.js' | asset_url }}" defer></script>

        </div>
        
      {%- endif -%}

    </div>
      
    {{ 'component-toggle.css' | asset_url | stylesheet_tag }}

    <div class="product-text remove-empty-space">

      {%- assign product_form_id = 'product-form-' | append: section_id -%}

      {%- for block in section.blocks -%}
        {%- case block.type -%}

          {%- when '@app' -%}
            {%- render block -%}

          {%- when 'variant_metafield' -%}
            <div class="product-variant-metafield show-block-if-variant-selected rte" 
              data-key="{{ block.settings.key }}" 
              data-js-variant-metafield
              {{ block.shopify_attributes }}
              data-update-block="variant-metafield"
            >
              {%- assign metafield_reference = block.settings.key -%}
              {%- if metafield_reference != blank -%}
                {%- assign metafield_keys = metafield_reference | split: '.' -%}
                {%- assign metafield_value = current_variant.metafields[metafield_keys[0]][metafield_keys[1]] -%}
                {%- if metafield_value != blank -%}
                  <div class="product-variant-metafield__container">
                    {%- if block.settings.title != blank %}
                      <span class="product-variant-metafield__title text-weight--medium">{{ block.settings.title | escape }}</span>
                    {%- endif -%}
                    {{ metafield_value | metafield_tag }}
                  </div>
                {%- endif -%}              
              {%- endif -%}
            </div>
          
            {%- when 'progress-slider' -%}
              {{ 'component-progress.css' | asset_url | stylesheet_tag }}
              
              <div id="progress-slider-{{ block.id }}" {{ block.shopify_attributes }}>
                <p class="product-related-title text-size--large text-weight--bold">{{ block.settings.title | escape }}</p>
                <div class="progress-slider__wrapper">
                  {%- if  block.settings.caption != blank -%}
                    <span class="progress-slider__caption">{{ block.settings.caption | escape }}</span>
                  {%- endif -%}
                  <div class="progress-slider">
                    <span class="progress-slider__active"></span>
                  </div>
                </div>
    
                {% liquid
                  assign color_active = settings.color_text_main 
                  if block.settings.slider_color_active != 'rgba(0,0,0,0)'
                    assign color_active = block.settings.slider_color_active 
                  endif
    
                  assign color_default = settings.color_text_main | color_modify: 'alpha', 0.07
                  if block.settings.slider_color_default != 'rgba(0,0,0,0)'
                    assign color_default = block.settings.slider_color_default
                  endif
                %}

                {%- liquid
                  assign slider_value = block.settings.slider_value 
                  if block.settings.dynamic_content != blank
                    assign metafield_keys = block.settings.dynamic_content | split: '.'
                    assign metafield_value = product.metafields[metafield_keys[0]][metafield_keys[1]]
                    if metafield_value != blank
                      assign slider_value = metafield_value
                    endif
                  endif
                -%}
    
                {% style %}
                  #progress-slider-{{ block.id }} .progress-slider {
                    background-color: {{ color_default }};
                    height: {{ block.settings.slider_height }}px;
                    width: {{ block.settings.slider_width }}px;
                  }
    
                  #progress-slider-{{ block.id }} .progress-slider__active {
                    background-color: {{ color_active }};
                    width: {{ slider_value }}%;
                    
                  }
                {% endstyle %}
              </div>
    
            {%- when 'progress-dots' -%}
              {{ 'component-progress.css' | asset_url | stylesheet_tag }}
              
              <div id="progress-dots-{{ block.id }}" {{ block.shopify_attributes }}>
                <p class="product-related-title text-size--large text-weight--bold">{{ block.settings.title | escape }}</p>
    
                <div class="progress-dots__wrapper">

                  {%- if block.settings.caption != blank -%}
                    <p class="progress-dots__caption">{{ block.settings.caption | escape }}</p>
                  {%- endif -%}

                  <div class="progress-dots">
    
                    {%- liquid

                      assign total_dots = block.settings.total_dots
                      assign active_dots = block.settings.active_dots | at_most: total_dots
                      assign inactive_dots = block.settings.total_dots | minus: active_dots

                      if block.settings.dynamic_content != blank
                        assign metafield_keys = block.settings.dynamic_content | split: '.'
                        assign metafield_value = product.metafields[metafield_keys[0]][metafield_keys[1]]
                        if metafield_value != blank
                          assign active_dots = metafield_value
                        endif
                      endif

                    -%}
                    
                    {%- for i in (1..active_dots) -%}
                      <span class="progress-dots__item progress-dots__item-active"></span>
                    {%- endfor -%}
                    
                    {%- for i in (1..inactive_dots) -%}
                      <span class="progress-dots__item"></span>
                    {%- endfor -%}

                  </div>
                </div>
    
                {% assign active_icon_name = block.settings.dot_icon | append: "-active" %}
                {% capture active_icon_content %}{% render 'progress-dots-icons', icon: active_icon_name %}{% endcapture %}
    
                {%- liquid
                  assign active_icon_size = active_icon_content | size
                  if active_icon_size == 0
                    assign active_icon_name = block.settings.dot_icon
                  else 
                    assign active_icon_name = block.settings.dot_icon | append: "-active"
                  endif
    
                  assign color_active = settings.color_text_main | url_encode 
                  if block.settings.icon_color_active != 'rgba(0,0,0,0)'
                    assign color_active = block.settings.icon_color_active | url_encode 
                  endif
    
                  assign color_default = settings.color_text_main | color_modify: 'alpha', 0.15
                  if block.settings.icon_color_default != 'rgba(0,0,0,0)'
                    assign color_default = block.settings.icon_color_default | url_encode 
                  endif
                -%}
                {% style %}
                  #progress-dots-{{ block.id }} .progress-dots__item {
                    background-image: url('data:image/svg+xml;utf8,{%- render 'progress-dots-icons', icon: block.settings.dot_icon color_1: color_default color_2: 'white' -%}');
                    height: {{ block.settings.dot_size }}px;
                    width: {{ block.settings.dot_size }}px;
                  }
                  #progress-dots-{{ block.id }} .progress-dots__item-active {
                    background-image: url('data:image/svg+xml;utf8,{%- render 'progress-dots-icons', icon: active_icon_name color_1: color_active color_2: 'white' -%}');
                    height: {{ block.settings.dot_size }}px;
                    width: {{ block.settings.dot_size }}px;
                  }
                {% endstyle %}
              </div>

          {%- when 'vendor' -%} 
            <span 
              class="product__subtitle {{ block.settings.text_size }}"
              {{ block.shopify_attributes }}
            >
              <a href="{{ product.vendor | url_for_vendor }}"><span class="text-animation--underline-thin">{{ product.vendor | escape }}</span></a>
            </span>

          {%- when 'space' -%}
            <div class="empty-space" {{ block.shopify_attributes }}>&nbsp;</div>

          {%- when 'text' -%} 
            <span 
              class="product__subtitle {{ block.settings.text_size }}"
              style="
                {% if block.settings.text_color != 'rgba(0,0,0,0)' %} color: {{ block.settings.text_color }}; {% endif %}
                {% if block.settings.text_transform %} text-transform: uppercase {% endif %}
              " 
              {{ block.shopify_attributes }}
            >
              {{ block.settings.text }}
            </span>

          {%- when 'title' -%}
            <h1 class="product__title h2" {{ block.shopify_attributes }}>{{ product.title | escape }}</h1>

          {%- when 'price' -%}

            <div class="text-size--xlarge" {{ block.shopify_attributes }} data-update-block="price-compact">
              <span class="show-block-if-variant-selected">
                {%- render 'product-price', variant: current_variant, target: current_variant, id: section_id -%}
              </span>
              {%- unless default_to_first_variant -%}
                <span class="show-block-if-variant-not-selected-yet">
                  {%- render 'product-price', variant: current_variant, target: product, product_price_varies: product.price_varies, id: section_id -%}
                </span>
              {%- endunless -%}
            </div>

          {%- when 'tax_info' -%}

            {%- if cart.taxes_included or shop.shipping_policy.body != blank -%}
              <div class="product-policies text-size--small" data-product-policies {{ block.shopify_attributes }}>
                {% if cart.taxes_included %}
                  {{ 'products.page.include_taxes' | t }}
                {%- endif -%}
                {%- if shop.shipping_policy.body != blank -%}
                  {{ 'products.page.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                {%- endif -%}
              </div>
            {%- endif -%}

          {%- when 'sku_barcode' -%}

            <div class="product__sku-barcode text-size--small show-block-if-variant-selected"  
              data-update-block="sku-barcode"
              {{ block.shopify_attributes }}
            >
              {%- if current_variant.sku != blank -%}
                <span class="product__sku">
                  {{ 'products.page.sku' | t }}{{ current_variant.sku | escape }}
                </span>
              {%- endif -%}
              {%- if current_variant.barcode != blank -%}
                <span class="product__barcode">
                    {{ 'products.page.barcode' | t }}{{ current_variant.barcode | escape }}
                </span>
              {%- endif -%}
            </div>

          {%- when 'description' -%}

            {%- if product.description != blank -%}
              <div class="product__description rte" {{ block.shopify_attributes }}>
                {{ product.description }}
              </div>
            {%- endif -%}

          {%- when 'variant_picker' -%}

            {%- render 'product-variant-picker', product: product, current_variant: current_variant, block: block, id: section_id, default_to_first_variant: default_to_first_variant, reload_product: 'page', no_history: true -%}

          {%- when 'buy_buttons' -%}

            <div class="product-actions" {{ block.shopify_attributes }}>

              {%- if block.settings.show_price -%}
                <div data-update-block="price-extended" class="product-actions__price show-block-if-variant-selected">
                  {%- render 'product-price', variant: current_variant, target: current_variant, text_size_class: 'text-size--xlarge', show_saving: true, id: section_id -%}
                </div>
              {%- endif -%}

              {%- if product.metafields.custom.info_article_url != blank -%}
                {%- liquid
                  assign raw_url = product.metafields.custom.info_article_url
                  assign cleaned_url = raw_url | remove_first: shop.url
                  assign cleaned_url = cleaned_url | remove_first: 'http://' | remove_first: 'https://'
                  assign cleaned_url = cleaned_url | remove_first: 'www.'
                  assign cleaned_url = cleaned_url | remove_first: 'dnatural.de'
                  assign cleaned_url = cleaned_url | remove_first: '/'
                  assign info_article_url = cleaned_url | prepend: '/'
                -%}
                
                <a class="button button--outline button--regular button--fullwidth" style="margin-top: 1em;" href="{{ info_article_url }}">
                  {%- comment -%}Untranslated label{% endcomment -%}
                  Mehr Infos
                </a>
              {%- else -%}
                <product-form 
                  id="add-to-cart-{{ section_id }}" class="product-form"
                  {% if settings.cart_action == 'overlay' %} data-ajax-cart data-js-product-form {% endif %}
                >
                
                {%- form 'product', product, id: product_form_id, class: 'form', novalidate: 'novalidate', data-type: 'add-to-cart-form' -%}

                  <span data-update-block="variant-id-main"><input type="hidden" name="id" value="{{ current_variant.id }}"></span>

                  {%- liquid
                    if product.gift_card? and block.settings.show_gift_card_recipient
                      render 'gift-card-recipent-form', product: product
                    endif
                  -%}

                  <div class="product__cart-functions">

                    <div class="flex-buttons shopify-buttons--not-{{ block.settings.style_buttons }}">

                      <style>
                        #add-to-cart-{{ section_id }} .flex-buttons {
                          {%- if block.settings.color_text_buttons != 'rgba(0,0,0,0)' -%}
                            --color-text-main: {{ block.settings.color_text_buttons }};
                            {%- liquid 
                              assign brightness_product_button = block.settings.color_text_buttons | color_brightness
                              if brightness_product_button > 150 
                                assign color_foreground_product_button = '#000' 
                              else 
                                assign color_foreground_product_button = '#fff' 
                              endif 
                            -%}
                            --color-foreground-main: {{ color_foreground_product_button }};
                          {%- endif -%}
                          {%- if block.settings.color_accent_buttons != 'rgba(0,0,0,0)' -%}
                            --color-accent-main: {{ block.settings.color_accent_buttons }};
                            {%- liquid
                              assign brightness_accent_main = block.settings.color_accent_buttons | color_brightness
                              if brightness_accent_main > 150 
                                assign color_foreground_accent_button = '#000' 
                              else 
                                assign color_foreground_accent_button = '#fff' 
                              endif 
                            -%}
                            --color-foreground-accent-main: {{ color_foreground_accent_button }};
                          {%- endif -%}
                        }
                      </style>

                      {%- liquid 
                        if block.settings.show_quantity_selector
                          render 'product-quantity', variant: current_variant, id: section_id, default_to_first_variant: default_to_first_variant
                        endif
                      -%}

                      <button type="submit" name="add" class="add-to-cart button button--{{ block.settings.style_buttons }} button--product button--loader 
                        {% unless current_variant.available %} disabled {% endunless %}
                        {% unless default_to_first_variant %} disabled {% endunless %}" 
                      data-js-product-add-to-cart>
                        <span class="button__text" data-js-product-add-to-cart-text>
                          {%- liquid
                            unless default_to_first_variant
                              if product.available
                                echo 'products.grid.choose_variant_first' | t
                              else
                                echo 'products.page.inventory.sold_out_variant' | t
                              endif
                            else
                              if current_variant.available
                                unless block.settings.show_preorder
                                  echo 'products.page.add_to_cart_button' | t
                                else
                                  echo 'products.page.preorder_button' | t
                                endunless
                              else
                                echo 'products.page.inventory.sold_out_variant' | t
                              endif
                            endunless
                          -%}
                        </span>
                        <span class="button__preloader">
                          <svg class="button__preloader-element" viewBox="25 25 50 50"><circle cx="50" cy="50" r="20" fill="none" stroke-width="4"/></svg>
                        </span>
                      </button>

                      {%- liquid
                        if block.settings.show_dynamic_checkout and block.settings.show_gift_card_recipient == false
                          echo form | payment_button
                        endif
                      -%}

                    </div>
                    
                  </div>

                {%- endform -%}

                </product-form>

                {%- assign product_form_installment_id = 'product-form-installment-' | append: product.id | append: '-' | append: section_id -%}
                {%- form 'product', product, id: product_form_installment_id -%}
                  <span data-update-block="variant-id-installment"><input type="hidden" name="id" value="{{ current_variant.id }}"></span>
                  {{ form | payment_terms }}
                {%- endform -%}
              {%- endif -%}

            </div>

          {%- when 'collapsible_tab' -%}

            <toggle-tab class="toggle" {{ block.shopify_attributes }}>
              <span class="toggle__title" data-js-title tabindex="0" aria-expanded="false" role="button" aria-controls="toggle-{{ block.id }}">{{ block.settings.heading | escape }}</span>
              <div id="toggle--{{ block.id }}" data-js-content class="toggle__content rte remove-empty-space" role="region">
                {%- liquid
                  echo block.settings.content
                  unless block.settings.page == blank
                    echo block.settings.page.content
                  endunless
                  unless block.settings.image == blank
                    assign sizes = 'sizes="(max-width: 948px) calc(100vw - 20px), (min-width: 949px) and (max-width: 1023px) calc(100vw - 200px), 25vw"'
                    render 'lazy-image', image: block.settings.image, type: 'image', alt: block.settings.title, sizes: sizes
                  endunless
                -%}
              </div>
            </toggle-tab>

          {%- when 'pickup_availability' -%} 

            {%- if block.settings.style == 'compact' -%}

              <div class="no-js-hidden" {{ block.shopify_attributes }} 
                data-update-block="pickup-availability-compact"
              >
                <pickup-availability-compact
                  class="no-js-hidden show-block-if-variant-selected"
                  data-base-url="{{ shop.url }}{{ routes.root_url }}"
                  data-variant-id="{{ current_variant.id }}"
                  data-has-only-default-variant="{{ product.has_only_default_variant }}"
                  data-id="{{ section_id }}"
                  data-static
                  {{ block.shopify_attributes }}
                >
                  <span class="alert alert--note alert--circle alert--circle-loading">
                    {{ 'store_availability.compact_widget.checking_availability' | t }}
                  </span>
                  <div style="display:none" data-js-pickup-availability-data>{%- render 'pickup-availability-data', current_variant: current_variant -%}</div>
                </pickup-availability-compact>
              </div>

            {%- else -%}

              <div 
                class="pickup-availability-widget element--border-radius no-js-hidden show-block-if-variant-selected"
                {{ block.shopify_attributes }} 
                data-update-block="pickup-availability"
              >
                {%- if current_variant != null -%}
                  <pickup-availability-widget data-id="{{ section_id }}">
                    {%- render 'pickup-availability-widget', product: product, current_variant: current_variant, include_sidebar: false %}
                  </pickup-availability-widget>
                {%- endif -%}
              </div>

            {%- endif -%}

          {%- when 'rating' -%}
            <div {{ block.shopify_attributes }}>
              {%- render 'rating', vendor: settings.reviews_app, product: product -%}
            </div>

          {%- when 'custom_liquid' -%}
            <div class="product-custom-liquid" {{ block.shopify_attributes }}>
              {{ block.settings.custom_liquid }}
            </div>

          {%- when 'related' -%}

            <div class="product-related" {{ block.shopify_attributes }}>

              {%- unless block.settings.title == blank -%}
                <span class="product-related-title text-size--large text-weight--bold">{{ block.settings.title | escape }}</span>
              {%- endunless -%}

              {%- for product in block.settings.product_list -%}

                <div class="product-related-item">

                  <a href="{{ product.url }}" class="product-related-item__image">
                    {%- render 'lazy-image-small', image: product.featured_media, aspect_ratio: settings.cart_image_ratio, fit: settings.cart_image_fit -%}
                  </a>

                  <div class="product-related-item__text">
                    <a class="product-related-item__title text-size--large text-weight--bold" href="{{ product.url }}">
                      <span class="text-animation--underline-thin">{{ product.title | escape }}</span>
                    </a>
                    <span class="product-related-item__price">
                      {%- render 'product-price', variant: product.selected_or_first_available_variant, product_price_varies: product.price_varies -%}
                    </span>
                  </div>

                  <div class="product-related-item__button">
                    {%- render 'quick-buy', product: product, button_classes: 'button--small' -%}
                  </div>

                </div>

              {%- endfor -%}

            </div>

          {%- when 'icons' -%}

            <div class="product-icons-list" {{ block.shopify_attributes }}>

              {%- unless block.settings.title == blank -%}
                <p class="text-size--large text-weight--bold">{{ block.settings.title | escape }}</p>
              {%- endunless -%}

              {%- capture product_icon_list -%}
                {%- liquid
                  render 'product-icon-label', icon: block.settings.icon_1_image, label: block.settings.icon_1_label, style: 'compact'
                  render 'product-icon-label', icon: block.settings.icon_2_image, label: block.settings.icon_2_label, style: 'compact'
                  render 'product-icon-label', icon: block.settings.icon_3_image, label: block.settings.icon_3_label, style: 'compact'
                  render 'product-icon-label', icon: block.settings.icon_4_image, label: block.settings.icon_4_label, style: 'compact'
                  render 'product-icon-label', icon: block.settings.icon_5_image, label: block.settings.icon_5_label, style: 'compact'
                  render 'product-icon-label', icon: block.settings.icon_6_image, label: block.settings.icon_6_label, style: 'compact'
                -%}
              {%- endcapture -%}

              {%- unless product_icon_list == blank -%}
                <div class="product-icons-list-container">{{ product_icon_list }}</div>
              {%- endunless -%}

            </div>

        {%- endcase -%}

      {%- endfor -%}

    </div>

    {%- if first_3d_model -%}
      <script type="application/json" id="ProductJSON-{{ product.id }}">
        {{ product.media | where: 'media_type', 'model' | json }}
      </script>
    {%- endif -%}

  </product-page>
  
  <div class="product-quick-view__header">
    <span class="hide lap-show product-quick-view__title text-size--large">{{ 'products.grid.quick_view' | t }}</span>
    <button class="product-quick-view__close" data-js-close>{%- render 'theme-symbols', icon: 'close' -%}</button>
  </div>

  <script src="{{ 'component-toggle.js' | asset_url }}" defer></script>
  
  {%- if section.settings.enable_zoom -%}
    <script src="{{ 'component-product-image-zoom.js' | asset_url }}" defer></script>
  {%- endif -%}

  <script src="{{ 'section-main-product.js' | asset_url }}" defer></script>
  
  {%- if video_script -%}
    {{ 'component-video.css' | asset_url | stylesheet_tag }}
    <script src="{{ 'component-video.js' | asset_url }}" defer></script>
  {%- endif -%}

</div>

{% schema %}
	{
    "name": "t:sections.local-extra-words.sections.product-quick-view.name",
    "class": "mount-css-slider mount-toggles",
    "tag": "section",
    "blocks": [
      {
        "type": "@app"
      },
      {
        "type": "text",
        "name": "t:sections.main-product.blocks.text.name",
        "settings": [
          {
            "id": "text",
            "type": "richtext",
            "default": "<p>Text block</p>",
            "label": "t:sections.main-product.blocks.text.settings.text.label"
          },
          {
            "type": "select",
            "id": "text_size",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.label",
            "options": [
              {
                "value": "text-size--xsmall",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__1"
              },
              {
                "value": "text-size--regular",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__2"
              },
              {
                "value": "text-size--large",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__3"
              }
            ],
            "default": "text-size--regular"
          },
          {
            "type": "color",
            "id": "text_color",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.color.label",
            "default": "#6A6A6A"
          },
          {
            "type": "checkbox",
            "id": "text_transform",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.transform.label",
            "default": false
          }
        ]
      },
      {
        "type": "vendor",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.vendor",
        "settings": [
          {
            "type": "select",
            "id": "text_size",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.label",
            "options": [
              {
                "value": "text-size--xsmall",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__1"
              },
              {
                "value": "text-size--regular",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__2"
              },
              {
                "value": "text-size--large",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__3"
              }
            ],
            "default": "text-size--regular"
          }
        ]
      },
      {
        "type": "title",
        "name": "t:sections.main-product.blocks.title.name",
        "limit": 1
      },
      {
        "type": "price",
        "name": "t:sections.main-product.blocks.price.name",
        "limit": 1
      },
      {
        "type": "tax_info",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.tax_info.name",
        "limit": 1
      },
      {
        "type": "sku_barcode",
        "name": "t:sections.main-product.blocks.sku_barcode.name",
        "limit": 1
      },
      {
        "type": "variant_picker",
        "name": "t:sections.main-product.blocks.variant_picker.name",
        "limit": 1,
        "settings": [ 
          {
            "type": "select",
            "id": "variants_style",
            "label": "t:sections.split-extra-words.sections.main-product.settings.variants.label",
            "options": [
              {
                "value": "radio",
                "label": "t:sections.split-extra-words.sections.main-product.settings.variants.options__1"
              },
              {
                "value": "select",
                "label": "t:sections.split-extra-words.sections.main-product.settings.variants.options__2"
              }
            ],
            "default": "radio"
          },
          {
            "type": "paragraph",
            "content": "t:new_color_swatches"
          },
          {
            "type": "select",
            "id": "show_quantities",
            "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.label",
            "info": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.info",
            "options": [
              {
                "value": "no",
                "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.options__1.label"
              },
              {
                "value": "below",
                "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.options__2.label"
              },
              {
                "value": "always",
                "label": "t:sections.main-product.blocks.variant_picker.settings.low_inventory_notification.options__3.label"
              }
            ],
            "default": "no"
          }
        ]
      },
      {
        "type": "buy_buttons",
        "name": "t:sections.main-product.blocks.buy_buttons.name",
        "limit": 1,
        "settings": [
          {
            "type": "checkbox",
            "id": "show_quantity_selector",
            "label": "t:sections.main-product.blocks.buy_buttons.settings.show_quantity_selector.label",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_price",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.buy_buttons.settings.show_price",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_dynamic_checkout",
            "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
            "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "show_gift_card_recipient",
            "default": false,
            "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
            "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.custom_colors"
          },
          {
            "type": "color",
            "id": "color_text_buttons",
            "label": "t:sections.split-extra-words.settings_schema.colors.settings.text",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "color",
            "id": "color_accent_buttons",
            "label": "t:sections.local-extra-words.settings_schema.colors.settings.accent",
            "default": "rgba(0,0,0,0)"
          },
          {
            "id": "style_buttons",
            "label": "t:sections.local-extra-words.sections.buttons.style.label",
            "type": "select",
            "options": [
              {
                "value": "outline",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__1"
              },
              {
                "value": "solid",
                "label": "t:sections.local-extra-words.sections.buttons.style.option__2"
              }
            ],
            "default": "solid"
          }
        ]
      },
      {
        "type": "description",
        "name": "t:sections.main-product.blocks.description.name",
        "limit": 1
      },
      {
        "type": "rating",
        "name": "t:settings_schema.product-grid.settings.header__1.content",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:settings_schema.product-grid.settings.header__1.info"
          }
        ]
      },
      {
        "type": "collapsible_tab",
        "name": "t:sections.main-product.blocks.collapsible_tab.name",
        "settings": [
          {
            "id": "heading",
            "type": "text",
            "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label",
            "default": "Collapsible tab"
          },
          {
            "id": "content",
            "type": "richtext",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
          },
          {
            "id": "page",
            "type": "page",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "t:sections.main-product.blocks.collapsible_tab.settings.image.label"
          }
        ]
      }, 
      {
        "type": "pickup_availability",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.name",
        "limit": 2,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.info"
          },
          {
            "type": "select",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.settings.style",
            "id": "style",
            "options": [
              {
                "value": "compact",
                "label": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.settings.option__1"
              },
              {
                "value": "extended",
                "label": "t:sections.local-extra-words.sections.main-product.blocks.pickup_availability.settings.option__2"
              }
            ],
            "default": "extended"
          }
        ]
      },
      {
        "type": "icons",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.icons.name",
        "limit": 2,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Icons list"
          },
          {
            "type": "paragraph",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.info"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_1"
          },
          {
            "type": "image_picker",
            "id": "icon_1_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_1_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_2"
          },
          {
            "type": "image_picker",
            "id": "icon_2_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_2_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_3"
          },
          {
            "type": "image_picker",
            "id": "icon_3_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_3_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_4"
          },
          {
            "type": "image_picker",
            "id": "icon_4_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_4_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_5"
          },
          {
            "type": "image_picker",
            "id": "icon_5_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_5_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          },
          {
            "type": "header",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.headers.icon_6"
          },
          {
            "type": "image_picker",
            "id": "icon_6_image",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon",
            "info": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.icon_info"
          },
          {
            "type": "text",
            "id": "icon_6_label",
            "label": "t:sections.local-extra-words.sections.main-product.blocks.icons.settings.label"
          }
        ]
      },

      {
        "type": "progress-dots",
        "name": "t:local-march-update.blocks.progress_dots.name",
        "settings": [
           {
            "type": "text",
            "id": "title",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Dots title"
          },
          {
            "type": "text",
            "id": "caption",
            "label": "t:sections.local-extra-words.sections.headings.caption",
            "default": "Dots caption"
          },
          {
            "type": "select",
            "id": "dot_icon",
            "label": "t:local-march-update.blocks.progress_dots.icon",
            "options": [
              {
                "value": "apple",
                "label": "Apple"
              },
              {
                "value": "bottle",
                "label": "Bottle"
              },
              {
                "value": "circle",
                "label": "Circle"
              },
              {
                "value": "circle-ghost",
                "label": "Circle empty"
              },
              {
                "value": "chili-pepper",
                "label": "Chili pepper"
              },
              {
                "value": "coffee-bean",
                "label": "Coffee bean"
              },
              {
                "value": "diamond",
                "label": "Diamond"
              },
              {
                "value": "drop",
                "label": "Drop"
              },
              {
                "value": "glass",
                "label": "Glass"
              },
              {
                "value": "heart",
                "label": "Heart"
              },
              {
                "value": "lemon",
                "label": "Lemon"
              },
              {
                "value": "star",
                "label": "Star"
              },
              {
                "value": "sun",
                "label": "Sun"
              }
            ],
            "default": "circle-ghost"
          },
          {
            "type": "range",
            "id": "dot_size",
            "label": "t:local-march-update.blocks.progress_dots.size",
            "min": 10,
            "max": 30,
            "step": 1,
            "default": 14
          },
          {
            "type": "range",
            "id": "total_dots",
            "label": "t:local-march-update.blocks.progress_dots.total",
            "min": 1,
            "max": 10,
            "step": 1,
            "default": 5
          },
          {
            "type": "range",
            "id": "active_dots",
            "label": "t:local-march-update.blocks.progress_dots.highlight",
            "min": 0,
            "max": 10,
            "step": 1,
            "default": 3
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "paragraph",
            "content": "t:local-march-update.blocks.progress_slider.dynamic_content.info"
          },
          {
            "type": "text",
            "id": "dynamic_content",
            "label": "t:local-march-update.blocks.progress_slider.dynamic_content.dots_label",
            "info": "custom.product_dots_chart_value"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.custom_colors"
          },
          {
            "type": "color",
            "id": "icon_color_default",
            "label": "t:local-march-update.blocks.progress_dots.inactive_color",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "color",
            "id": "icon_color_active",
            "label": "t:local-march-update.blocks.progress_dots.active_color",
            "default": "rgba(0,0,0,0)"
          }
        ]
      },
      {
        "type": "progress-slider",
        "name": "t:local-march-update.blocks.progress_slider.name",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "t:sections.local-extra-words.sections.headings.title",
            "default": "Slider title"
          },
          {
            "type": "text",
            "id": "caption",
            "label": "t:sections.local-extra-words.sections.headings.caption",
            "default": "Slider caption"
          },
          {
            "type": "range",
            "id": "slider_height",
            "label": "t:local-march-update.blocks.progress_slider.height",
            "min": 4,
            "max": 20,
            "step": 1,
            "unit": "px",
            "default": 8
          },
          {
            "type": "range",
            "id": "slider_width",
            "label": "t:local-march-update.blocks.progress_slider.width",
            "min": 100,
            "max": 250,
            "step": 2,
            "unit": "px",
            "default": 250
          },
          {
            "type": "range",
            "id": "slider_value",
            "label": "t:local-march-update.blocks.progress_slider.value",
            "min": 0,
            "max": 100,
            "step": 1,
            "default": 50,
            "unit": "%"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.dynamic_content.name"
          },
          {
            "type": "paragraph",
            "content": "t:local-march-update.blocks.progress_slider.dynamic_content.info"
          },
          {
            "type": "text",
            "id": "dynamic_content",
            "label": "t:local-march-update.blocks.progress_slider.dynamic_content.slider_label",
            "info": "custom.product_slider_chart_value"
          },
          {
            "type": "header",
            "content": "t:local-march-update.labels.custom_colors"
          },
          {
            "type": "color",
            "id": "slider_color_default",
            "label": "t:local-march-update.blocks.progress_dots.inactive_color",
            "default": "rgba(0,0,0,0)"
          },
          {
            "type": "color",
            "id": "slider_color_active",
            "label": "t:local-march-update.blocks.progress_dots.active_color",
            "default": "rgba(0,0,0,0)"
          }
        ]
      },
      {
        "type": "custom_liquid",
        "name": "t:sections.refactor_words.custom_code.name",
        "settings": [
          {
            "type": "liquid",
            "id": "custom_liquid",
            "label": "t:sections.custom-liquid.settings.custom_liquid.label"
          }
        ]
      },
      {
        "type": "variant_metafield",
        "name": "t:variant_metafields.name",
        "settings": [
          {
            "type": "paragraph",
            "content": "t:variant_metafields.info"
          },
          {
            "type": "text",
            "id": "key",
            "label": "t:variant_metafields.label",
            "info": "custom.variant_metafield"
          },
          {
            "type": "text",
            "label": "t:sections.local-extra-words.sections.main-header.settings.promotion_block.title.label",
            "id": "title"
          }
        ]
      },
      {
        "type": "space",
        "name": "t:local-march-update.blocks.space.name"
      }
    ],
    "settings": [
      {
        "type": "paragraph",
        "content": "t:sections.local-extra-words.sections.product-quick-view.info"
      },
      {
        "type": "header",
        "content": "t:sections.main-product.settings.header.content",
        "info": "t:sections.main-product.settings.header.info"
      },
			{
				"type": "select",
				"id": "gallery_ratio",
				"label": "t:sections.refactor_words.product-page.gallery_resize.label",
				"options": [
					{
						"value": "natural",
						"label": "t:sections.gallery.settings.aspect_ratio.options__5.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__5.group"
					},
					{
						"value": "1.33333",
						"label": "t:sections.gallery.settings.aspect_ratio.options__1.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					},
					{
						"value": "1",
						"label": "t:sections.gallery.settings.aspect_ratio.options__2.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					},
					{
						"value": "0.83333",
						"label": "t:sections.gallery.settings.aspect_ratio.options__3.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					},
					{
						"value": "0.666667",
						"label": "t:sections.gallery.settings.aspect_ratio.options__4.label",
						"group": "t:sections.gallery.settings.aspect_ratio.options__1.group"
					}
				],
				"default": "natural",
        "info": "t:sections.refactor_words.product-page.gallery_resize.info"
			},
			{
				"type": "checkbox",
				"id": "gallery_fit",
				"label": "t:sections.refactor_words.product-page.gallery_resize.option_1",
				"default": false
			},
      {
        "type": "checkbox",
        "id": "show_border",
        "label": "t:sections.local-extra-words.sections.main-product.settings.show_border",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "gallery_card_design",
        "label": "t:local-march-update.labels.inherit_card_design",
        "default": false
      },
			{
				"type": "range",
				"id": "gallery_padding",
				"label": "t:sections.refactor_words.product-page.gallery_padding.label",
				"min": 0,
				"max": 15,
				"step": 1,
				"default": 0,
				"unit": "%"
			},
      {
        "type": "radio",
        "id": "gallery_pagination",
        "label": "t:sections.local-extra-words.sections.main-product.settings.gallery_pagination",
        "options": [
          {
            "value": "dots",
            "label": "t:sections.main-product.settings.gallery_pagination.options__1.label"
          },
          {
            "value": "thumbnails",
            "label": "t:sections.main-product.settings.gallery_pagination.options__2.label"
          }
        ],
        "default": "dots"
      },
      {
        "type": "checkbox",
        "id": "enable_video_looping",
        "label": "t:sections.main-product.settings.enable_video_looping.label",
        "default": false
      },
      {
        "type": "header",
        "content": "t:sections.popup.settings.functionality.content"
      },
      {
        "type": "checkbox",
        "id": "default_to_first_variant",
        "label": "t:local-230.variant_default.label",
        "info": "t:local-230.variant_default.info",
        "default": true
      }
    ]
  }
{% endschema %}