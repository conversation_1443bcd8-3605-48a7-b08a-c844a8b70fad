<product-recommendations class="product-recommendations" data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ section.settings.products_number }}&intent=related">

  {%- if recommendations.performed and recommendations.products_count > 0 -%}

    <div class="container--large {% unless section.settings.remove_margin %} container--vertical-space {% endunless %} {% if section.settings.section_background_color != "rgba(0,0,0,0)" or section.settings.section_background_gradient != blank %} container--has-background {% endif %} remove-empty-space">

      {%- render 'section-heading', heading: section.settings.heading, subheading: section.settings.subheading, heading_tag: 'span', layout: section.settings.section_heading_layout-%}

      <css-slider data-options='{
          "selector": ".product-item",
          "groupCells": true,
          "indexNav": true,
          "navigationDOM": "<span class=\"css-slider-button css-slider-prev\" style=\"display:none\"><svg fill=\"none\" height=\"13\" viewBox=\"0 0 8 13\" width=\"8\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"#000\" ><path d=\"m7.91419 1.41431-6.48529 6.48528-1.4142102-1.41422 6.4852802-6.48527702z\"/><path d=\"m6.48528 12.9849-6.48528027-6.48532 1.41421027-1.41421 6.48528 6.48523z\"/></g></svg></span><span class=\"css-slider-button css-slider-next\" style=\"display:none\"><svg width=\"9\" height=\"13\" viewBox=\"0 0 9 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.914169 11.5857L7.39945 5.10041L8.81367 6.51463L2.32838 12.9999L0.914169 11.5857Z\" fill=\"black\"/><path d=\"M2.34307 0.0151367L8.82835 6.50042L7.41414 7.91463L0.928857 1.42935L2.34307 0.0151367Z\" fill=\"black\"/></svg></span>"
        }'
        class="css-slider css-slider--bottom-navigation"
        id="css-slider-{{ section.id }}"
      >
        {%- render 'custom-shadow', section_id: section.id -%}

        <div class="grid grid--slider {{ section.settings.layout }} {{ section.settings.layout_mobile }} kill-grid-tiny">
          {%- liquid
            for recommendation in recommendations.products
              render 'product-item', product: recommendation, section_blocks: section.blocks, layout: section.settings.layout, mobile_layout: section.settings.layout_mobile
            endfor
          -%}
        </div>
      </css-slider>

    </div>

    {{ 'component-product-item.css' | asset_url | stylesheet_tag }}

    {%- if section.settings.section_background_color != "rgba(0,0,0,0)" -%}
      {% style %}
        #shopify-section-{{ section.id }} {
          background-color: {{ section.settings.section_background_color }};
        }
      {% endstyle %}
    {%- endif -%}
    {%- if section.settings.section_background_gradient != blank -%}
      {% style %}
        #shopify-section-{{ section.id }} {
          background: {{ section.settings.section_background_gradient }};
        }
      {% endstyle %}
    {%- endif -%}
    {%- if section.settings.section_heading_color != "rgba(0,0,0,0)" -%}
      {% style %}
        #shopify-section-{{ section.id }} {
          --color-text-main: {{ section.settings.section_heading_color }};
        }
      {% endstyle %}
    {%- endif -%}

  {%- endif -%}
  
</product-recommendations>

{% schema %}
  {
    "name": "t:sections.product-recommendations.name",
    "class": "mount-css-slider",
    "blocks": [
      {
        "type": "price",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.price",
        "limit": 1
      },
      {
        "type": "title",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.title",
        "limit": 1
      },
      {
        "type": "quick_buy",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.quick_buy",
        "limit": 1
      },
      {
        "type": "vendor",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.vendor",
        "limit": 1
      },
      {
        "type": "rating",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.rating",
        "limit": 1
      },
      {
        "type": "icons",
        "name": "t:sections.local-extra-words.sections.main-product.blocks.icons.name",
        "limit": 1,
        "settings": [
          {
            "type": "paragraph",
            "content": "t:sections.local-extra-words.sections.main-product.blocks.icons.help"
          }
        ]
      },
      {
        "type": "text",
        "name": "t:sections.local-extra-words.sections.product-card.blocks.text.name",
        "settings": [
          {
            "type": "text",
            "id": "metafield",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.text.label",
            "info": "custom.product_text"
          },
          {
            "type": "select",
            "id": "text_size",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.label",
            "options": [
              {
                "value": "text-size--xsmall",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__1"
              },
              {
                "value": "text-size--regular",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__2"
              },
              {
                "value": "text-size--large",
                "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.size.option__3"
              }
            ],
            "default": "text-size--regular"
          },
          {
            "type": "color",
            "id": "text_color",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.color.label",
            "default": "#6A6A6A"
          },
          {
            "type": "checkbox",
            "id": "text_transform",
            "label": "t:sections.local-extra-words.sections.product-card.blocks.text.settings.transform.label",
            "default": false
          }
        ]
      },
      {
        "type": "product_link",
        "name": "t:sections.featured-product.blocks.product_link.name",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "button_label",
            "label": "t:sections.local-extra-words.sections.main-header.settings.promotion_block.button.label",
            "default": "View product"
          }
        ]
      }
    ],
    "settings": [
      {
        "type": "header",
        "content": "t:sections.product-recommendations.settings.header__1.content",
        "info": "t:sections.product-recommendations.settings.header__1.info"
      },
      {
        "type": "header",
        "content": "t:local-march-update.labels.heading"
      },
      {
        "type": "text",
        "id": "heading",
        "label": "t:sections.local-extra-words.sections.headings.heading",
        "default": "Recommended products"
      },
      {
        "type": "text",
        "id": "subheading",
        "label": "t:sections.local-extra-words.sections.headings.subheading"
      },
      {
        "type": "select",
        "id": "section_heading_layout",
        "label": "t:sections.rich-text.settings.text_alignment.label",
        "options": [
          {
            "value": "section-heading--left",
            "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
          },
          {
            "value": "section-heading--center",
            "label": "t:sections.rich-text.settings.text_alignment.options__2.label"
          }
        ],
        "default": "section-heading--left"
      },
      {
        "type": "header",
        "content": "t:sections.main-article.blocks.content.name"
      },
      {
        "type": "select",
        "id": "layout",
        "label": "t:sections.local-extra-words.sections.columns.name",
        "options": [
          {
            "value": "grid-3 grid-lap-2 grid-tiny-1",
            "label": "t:sections.local-extra-words.sections.columns.option__2"
          },
          {
            "value": "grid-4 grid-portable-3 grid-lap-2 grid-tiny-1",
            "label": "t:sections.local-extra-words.sections.columns.option__3"
          }
        ],
        "default": "grid-4 grid-portable-3 grid-lap-2 grid-tiny-1"
      },
      {
        "type": "select",
        "id": "layout_mobile",
        "label": "t:sections.local-extra-words.sections.columns.name_mobile",
        "options": [
          {
            "value": "grid-palm-1",
            "label": "t:sections.local-extra-words.sections.columns.option__0"
          },
          {
            "value": "grid-palm-2",
            "label": "t:sections.local-extra-words.sections.columns.option__1"
          }
        ],
        "default": "grid-palm-1"
      },
      {
        "type": "range",
        "id": "products_number",
        "label": "t:sections.featured-collection.settings.products_number.label",
        "min": 3,
        "max": 8,
        "step": 1,
        "default": 4
      },
      {
        "type": "header",
        "content": "t:local-march-update.labels.section_design"
      },
      {
        "type": "color",
        "id": "section_background_color",
        "label": "t:sections.local-extra-words.settings_schema.product-card.badges.custom_badges.color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color_background",
        "id": "section_background_gradient",
        "label": "t:local-230.background_gradient"
      },
      {
        "type": "color",
        "id": "section_heading_color",
        "label": "t:local-223.heading_text_color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "checkbox",
        "id": "remove_margin",
        "label": "t:sections.local-extra-words.sections.rich-text.settings.remove_margin.label",
        "default": false
      }
    ]
  }
{% endschema %}