<div class="container container--large container--vertical-space-small remove-empty-space">

  <div class="gutter-bottom--page">
    <h1 class="title h2">{{ 'collections.title' | t }}</h1>
  </div>

  {% # theme-check-disable VariableName %}
  {%- liquid
    assign collection_count = 0
    assign isEmpty = true
  
    if section.settings.display_type == 'all'
      case section.settings.sort
        when 'products_high' or 'products_low'
          assign list_collections = collections | sort: 'all_products_count'
        when 'date' or 'date_reversed'
          assign list_collections = collections | sort: 'published_at'
        else
          assign list_collections = collections
      endcase
      for collection in list_collections
        unless collection.handle == 'frontpage'
          assign collection_count = collection_count | plus: 1
        endunless
      endfor
    else
      assign collection_count = section.blocks.size
    endif
  
    if collection_count > 0
      assign isEmpty = false
    endif

    assign layout = section.settings.layout

    if layout contains 'grid-tiny-1'
      assign sizes = '(max-width: 359px) calc(100vw - 20px)'
    elsif layout contains 'grid-lap-1'
      assign sizes = '(max-width: 767px) calc(100vw - 40px)'
    endif

    if layout contains 'grid-lap-2'
      assign sizes = sizes | append: ', (max-width: 767px) calc((100vw - 50px) / 2)'
    endif

    if layout contains 'grid-4'
      assign sizes = sizes | append: ', (max-width: 1023px) calc((100vw - 80) / 3), (max-width: 1280px) calc((100vw - 100px) / 4), 300px'
    elsif layout contains 'grid-3'
      assign sizes = sizes | append: ', (max-width: 1280px) calc((100vw - 120px) / 3), 420px'
    elsif layout contains 'grid-2'
      assign sizes = sizes | append: ', (max-width: 1280px) calc((100vw - 100px) / 2), 620px'
    endif 

    assign sizes = sizes | prepend: 'sizes="' | append: '"'

  -%}
  {% # theme-check-enable VariableName %}

  <div class="grid grid--layout {% unless isEmpty %} {{ section.settings.layout }} {% endunless %}">

    {%- liquid

      if section.settings.display_type == 'all'
  
        if section.settings.sort == 'products_low' or section.settings.sort == 'date' or section.settings.sort == 'alphabetical'
  
          if isEmpty
            echo '<p class="no-content-message">'
              echo 'collections.no_collections' | t
            echo '</p>'
          else
            assign custom_index = -1
            for collection in list_collections
              if collection.handle == 'frontpage'
                continue
              else 
                assign custom_index = custom_index | plus: 1
              endif
              render 'collection-item', collection: collection, text_alignment: section.settings.text_alignment, index: custom_index, sizes: sizes, image_aspect_ratio: section.settings.image_aspect_ratio
            endfor
          endif
  
        else
  
          if isEmpty
            echo '<p class="no-content-message">'
              echo 'collections.no_collections' | t
            echo '</p>'
          else
            assign custom_index = -1
            for collection in list_collections reversed
              if collection.handle == 'frontpage'
                continue
              else 
                assign custom_index = custom_index | plus: 1
              endif
              render 'collection-item', collection: collection, text_alignment: section.settings.text_alignment, index: custom_index, sizes: sizes, image_aspect_ratio: section.settings.image_aspect_ratio
            endfor
          endif
  
        endif
  
      else
  
        if section.blocks == empty
  
        else
          for block in section.blocks
            render 'collection-item', collection: collections[block.settings.collection], text_alignment: section.settings.text_alignment, image: block.settings.image, index: forloop.index0, sizes: sizes, image_aspect_ratio: section.settings.image_aspect_ratio, shopify_attributes: block.shopify_attributes
          endfor
        endif
  
      endif
  
    -%}

  </div>

</div>

{% schema %}
	{	  
    "name": "t:sections.main-list-collections.name",
		"settings": [
      {
        "type": "select",
        "id": "layout",
        "label": "t:sections.local-extra-words.sections.columns.name",
        "info": "t:sections.local-extra-words.sections.columns.info",
        "options": [
          {
            "value": "grid-2 grid-lap-1",
            "label": "t:sections.local-extra-words.sections.columns.option__1"
          },
          {
            "value": "grid-3 grid-lap-2 grid-tiny-1",
            "label": "t:sections.local-extra-words.sections.columns.option__2"
          },
          {
            "value": "grid-4 grid-portable-3 grid-lap-2 grid-tiny-1",
            "label": "t:sections.local-extra-words.sections.columns.option__3"
          }
        ],
        "default": "grid-3 grid-lap-2 grid-tiny-1"
      },
      {
        "type": "select",
        "id": "image_aspect_ratio",
        "label": "t:settings_schema.product-grid.settings.aspect_ratio.label",
        "options": [
          {
            "value": "1.33333",
            "label": "t:sections.gallery.settings.aspect_ratio.options__1.label"
          },
          {
            "value": "1",
            "label": "t:sections.gallery.settings.aspect_ratio.options__2.label"
          },
          {
            "value": "0.83333",
            "label": "t:sections.gallery.settings.aspect_ratio.options__3.label"
          },
          {
            "value": "0.666667",
            "label": "t:sections.gallery.settings.aspect_ratio.options__4.label"
          }
        ],
        "default": "1"
      },
      {
				"type": "header",
				"content": "t:sections.main-list-collections.settings.header.content"
      },
		  {
		    "type": "paragraph",
	      "content": "t:sections.main-list-collections.settings.paragraph.content"
		  },
		  {
		    "type": "radio",
		    "id": "display_type",
	      "label": "t:sections.main-list-collections.settings.display_type.label",
		    "default": "all",
		    "options": [
		      {
		        "value": "all",
	          "label": "t:sections.main-list-collections.settings.display_type.options__1.label"
		      },
		      {
		        "value": "selected",
	          "label": "t:sections.main-list-collections.settings.display_type.options__2.label"
		      }
		    ]
		  },
		  {
	      "type": "select",
	      "id": "sort",
	      "label": "t:sections.main-list-collections.settings.sort.label",
	      "info": "t:sections.main-list-collections.settings.sort.info",
	      "default": "alphabetical",
	      "options": [
	        {
	          "value": "alphabetical",
	          "label": "t:sections.main-list-collections.settings.sort.options__1.label"
	        },
	        {
	          "value": "alphabetical_reversed",
	          "label": "t:sections.main-list-collections.settings.sort.options__2.label"
	        },
	        {
	          "value": "date_reversed",
	          "label": "t:sections.main-list-collections.settings.sort.options__3.label"
	        },
	        {
	          "value": "date",
	          "label": "t:sections.main-list-collections.settings.sort.options__4.label"
	        },
	        {
	          "value": "products_high",
	          "label": "t:sections.main-list-collections.settings.sort.options__5.label"
	        },
	        {
	          "value": "products_low",
	          "label": "t:sections.main-list-collections.settings.sort.options__6.label"
	        }
	      ]
	    }
		],
		"blocks": [
		  {
		    "type": "collection",
	      "name": "t:sections.main-list-collections.blocks.collection.name",
		    "settings": [
	        {
	          "type": "collection",
	          "id": "collection",
	          "label": "t:sections.main-list-collections.blocks.collection.settings.collection.label"
	        },
	        {
	          "type": "image_picker",
	          "id": "image",
	          "label": "t:sections.main-list-collections.blocks.collection.settings.image.label",
	          "info": "t:sections.main-list-collections.blocks.collection.settings.image.info"
	        }
		    ]
		  }
		]
	}
{% endschema %}