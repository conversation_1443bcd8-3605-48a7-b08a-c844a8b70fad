{{ 'component-product-item.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .review-item {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  .review-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  .review-header--author-and-rating {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .review-item--verified {
    position: relative;
    padding-left: 1.5rem;
  }

  .review-item--verified::before {
    content: '';
    display: inline-block;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    background-image: url('{{ 'verified-badge.svg' | asset_url }}');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .review-product {
    padding-bottom: 1.5rem;
  }

  .review-location {
    padding-top: 1.5rem;
  }

  .review-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 1rem;
    margin-top: auto;
  }
{%- endstyle -%}

<div class="container--large {% unless section.settings.remove_margin %} container--vertical-space {% endunless %} {% if section.settings.section_background_color != "rgba(0,0,0,0)" or section.settings.section_background_gradient != blank %} container--has-background {% endif %} remove-empty-space">
  {%- render 'section-heading', heading: section.settings.heading, subheading: section.settings.subheading, heading_tag: section.settings.seo_h_tag, layout: section.settings.section_heading_layout -%}

  {%- if section.settings.style == 'slider' -%}
    <css-slider
      data-options='
        {
          "selector": ".review-item",
          "indexNav": true,
          "groupCells": true,
          "navigationDOM": "<span class=\"css-slider-button css-slider-prev\" style=\"display:none\"><svg fill=\"none\" height=\"13\" viewBox=\"0 0 8 13\" width=\"8\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"#000\" ><path d=\"m7.91419 1.41431-6.48529 6.48528-1.4142102-1.41422 6.4852802-6.48527702z\"/><path d=\"m6.48528 12.9849-6.48528027-6.48532 1.41421027-1.41421 6.48528 6.48523z\"/></g></svg></span><span class=\"css-slider-button css-slider-next\" style=\"display:none\"><svg width=\"9\" height=\"13\" viewBox=\"0 0 9 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.914169 11.5857L7.39945 5.10041L8.81367 6.51463L2.32838 12.9999L0.914169 11.5857Z\" fill=\"black\"/><path d=\"M2.34307 0.0151367L8.82835 6.50042L7.41414 7.91463L0.928857 1.42935L2.34307 0.0151367Z\" fill=\"black\"/></svg></span>"
        }
      '
      class="css-slider css-slider--bottom-navigation"
      id="css-slider-{{ section.id }}"
    >
      {%- render 'custom-shadow', section.id: section.id -%}
  {%- endif -%}

  {%- liquid
    assign grid_class = 'grid-3 grid-lap-2 grid-tiny-1 grid-palm-1'
  -%}

  <div class="grid {% if section.settings.style == 'slider' %} grid--slider {% else %} grid--layout {% endif %} {{ grid_class }} {{ grid_class }}">
    {%- for block in section.blocks -%}
      {%- if block.type == 'review' -%}
        <div id="review-item-{{ block.id }}" class="review-item card">
          <div class="review-header">
            <div class="review-header--author-and-rating">
              <span class="review-header__author text-size--large">
                {{ block.settings.author }}
              </span>
              <span class="review-header__rating">
                {%- render 'rating', rating: block.settings.rating -%}
              </span>
            </div>
            {%- if block.settings.is_verified_customer -%}
              <span class="review-item--verified">
                Verifizierter Kunde
              </span>
            {%- endif -%}
          </div>
          <div class="review-body">
            {{ block.settings.body }}
          </div>
          <div class="review-footer">
            {%- unless block.settings.product == blank -%}
              <div class="review-product">
                {{ block.settings.product.title }}
              </div>
            {%- endunless -%}
            {%- unless block.settings.location == blank -%}
              <div class="review-location">
                {{ block.settings.location }}
              </div>
            {%- endunless -%}
          </div>
        </div>
      {%- endif -%}
    {%- endfor -%}
  </div>

  {%- if section.settings.style == 'slider' -%}
    </css-slider>
  {%- endif -%}
</div>

{% schema %}
  {
    "name": "Reviews",
    "class": "mount-css-slider",
    "blocks": [
      {
        "type": "review",
        "name": "Review",
        "settings": [
          {
            "type": "text",
            "id": "author",
            "label": "Review author",
          },
          {
            "type": "number",
            "id": "rating",
            "label": "Review rating",
            "default": 0
          },
          {
            "type": "checkbox",
            "id": "is_verified_customer",
            "label": "Is verified customer"
          },
          {
            "type": "text",
            "id": "body",
            "label": "Review body"
          },
          {
            "type": "product",
            "id": "product",
            "label": "Reviewed product"
          },
          {
            "type": "text",
            "id": "location",
            "label": "Location"
          }
        ]
      }
    ],
    "settings": [
      {
        "type": "header",
        "content": "t:local-march-update.labels.heading"
      },
      {
        "type": "text",
        "id": "heading",
        "label": "t:sections.local-extra-words.sections.headings.heading",
        "default": "Das sagen unsere Kunden"
      },
      {
        "type": "text",
        "id": "subheading",
        "label": "t:sections.local-extra-words.sections.headings.subheading",
      },
      {
        "type": "select",
        "id": "section_heading_layout",
        "label": "t:sections.rich-text.settings.text_alignment.label",
        "options": [
          {
            "value": "section-heading--left",
            "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
          },
          {
            "value": "section-heading--center",
            "label": "t:sections.rich-text.settings.text_alignment.options__2.label"
          }
        ],
        "default": "section-heading--left"
      },
      {
        "type": "header",
        "content": "t:sections.main-article.blocks.content.name"
      },
      {
        "type": "select",
        "id": "style",
        "label": "t:sections.featured-collection.settings.layout.label",
        "options": [
          {
            "label": "t:sections.featured-collection.settings.layout.options__1.label",
            "value": "slider"
          },
          {
            "label": "t:sections.featured-collection.settings.layout.options__2.label",
            "value": "grid"
          }
        ],
        "default": "grid"
      },
      {
        "type": "header",
        "content": "t:local-march-update.labels.section_design"
      },
      {
        "type": "color",
        "id": "section_background_color",
        "label": "t:sections.local-extra-words.settings_schema.product-card.badges.custom_badges.color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "color_background",
        "id": "section_background_gradient",
        "label": "t:local-230.background_gradient"
      },
      {
        "type": "color",
        "id": "section_heading_color",
        "label": "t:local-223.heading_text_color",
        "default": "rgba(0,0,0,0)"
      },
      {
        "type": "checkbox",
        "id": "remove_margin",
        "label": "t:sections.local-extra-words.sections.rich-text.settings.remove_margin.label",
        "default": false
      },
      {
        "type": "header",
        "content": "t:sections.refactor_words.seo.name"
      },
      {
        "type": "select",
        "id": "seo_h_tag",
        "label": "t:sections.refactor_words.seo.label",
        "info": "t:sections.refactor_words.seo.info",
        "options": [
          {
            "value": "h1",
            "label": "H1"
          },
          {
            "value": "h2",
            "label": "H2"
          },
          {
            "value": "h3",
            "label": "H3"
          },
          {
            "value": "h4",
            "label": "H4"
          },
          {
            "value": "span",
            "label": "span"
          }
        ],
        "default": "h2"
      }
    ],
    "presets": [
      {
        "name": "Reviews",
        "blocks": [
          { "type": "review" }
        ]
      }
    ],
    "disabled_on": {
      "groups": ["header", "footer"]
    }
  }
{% endschema %}