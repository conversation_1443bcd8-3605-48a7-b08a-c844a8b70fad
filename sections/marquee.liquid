{%- if section.blocks.size > 0 -%}

  <div class="{% unless section.settings.remove_top_margin %} container--vertical-space {% endunless %} {% if section.settings.remove_bottom_margin %} container--negative-margin {% endif %}">

    <style type="text/css">
      #element-{{ section.id }} {
        {% if section.settings.color_background_main != 'rgba(0,0,0,0)' %}
          background: {{ section.settings.color_background_main }};
        {% endif %}
        {% if section.settings.section_background_gradient != blank %}
          background: {{ section.settings.section_background_gradient }};
        {% endif %}
      }
      {% if section.settings.color_text_main != 'rgba(0,0,0,0)' %}
        #element-{{ section.id }}, #element-{{ section.id }} a {
          color: {{ section.settings.color_text_main }};
        }
      {% endif %}
    </style>
    
    {{ 'section-marquee.css' | asset_url | stylesheet_tag }}
    <script src="{{ 'component-scrolling-text.js' | asset_url }}" defer></script>

    {%- liquid
      assign scroll_direction = 'ltr'
      if section.settings.scroll_direction == 'right'
        assign scroll_direction = 'rtl'
      endif
    -%}

    <scrolling-text id="element-{{ section.id }}" class="scrolling-text text-size--xlarge" data-scrolling-speed="{{ section.settings.speed }}" data-scrolling-direction="{{ scroll_direction }}" data-pause-on-hover="{{ section.settings.pause_on_hover }}" dir="{{ scroll_direction }}">
      <div class="scrolling-text__container">  
        {%- liquid
          for block in section.blocks
            echo '<span>' | append: block.settings.marquee_text | append: '</span>'
          endfor
        -%}
      </div>
    </scrolling-text>

  </div>

{%- endif -%}

{% schema %}
{
  "name": "t:sections.local-extra-words.sections.marquee.name",
  "class": "mount-scrolling-text",
  "settings": [
    {
      "id": "scroll_direction",
      "type": "select",
      "options": [
        {
          "value": "right",
          "label": "t:sections.rich-text.settings.text_alignment.options__3.label"
        },
        {
          "value": "left",
          "label": "t:sections.rich-text.settings.text_alignment.options__1.label"
        }
      ],
      "default": "left",
      "label": "t:sections.local-extra-words.sections.marquee.settings.scroll_direction"
    },
    {
      "type": "range",
      "id": "speed",
      "label": "t:sections.local-extra-words.sections.marquee.settings.scroll_speed",
      "min": 20,
      "max": 300,
      "step": 10,
      "default": 100
    },
    {
      "type": "checkbox",
      "id": "pause_on_hover",
      "label": "t:sections.local-extra-words.sections.marquee.settings.pause_on_mouseover",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "remove_top_margin",
      "label": "t:sections.local-extra-words.settings_schema.layout.sections.remove_vertical_space",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "remove_bottom_margin",
      "label": "t:local-march-update.labels.bottom_margin",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.local-extra-words.sections.headings.custom_colors"
    },
    {
      "type": "color",
      "id": "color_background_main",
      "label": "t:sections.split-extra-words.settings_schema.colors.settings.background",
      "default": "#e9e9e9"
    },
    {
      "type": "color_background",
      "id": "section_background_gradient",
      "label": "t:local-230.background_gradient"
    },
    {
      "type": "color",
      "id": "color_text_main",
      "label": "t:sections.split-extra-words.settings_schema.colors.settings.text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "t:sections.local-extra-words.sections.marquee.settings.scroll_item",
      "settings": [
        {
          "type": "header",
          "content": "t:sections.local-extra-words.sections.headings.text_content"
        },
        {
          "type": "inline_richtext",
          "id": "marquee_text",
          "label": "t:sections.local-extra-words.sections.marquee.settings.scroll_item_text",
          "default": "Default scrolling text. Lorem ipsum texturum marquee tamet"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.local-extra-words.sections.marquee.name",
      "blocks": [
        { "type": "text" }
      ]
    }
  ]
}
{% endschema %}