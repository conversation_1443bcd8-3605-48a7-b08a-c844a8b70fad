.contact-form {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: var(--gutter-large) 0;
  gap: var(--gutter-regular);
}
@media screen and (max-width: 1023px) {
  .contact-form {
    grid-template-columns: 100%;
    gap: var(--gutter-xlarge);
  }
}
.contact-form label {
  padding-bottom: 0.25rem;
  display: inline-block;
}
.contact-form input:not([type=checkbox]) {
  width: 100%;
}
.contact-form textarea {
  width: 100%;
  height: 14.25rem;
}
.contact-form__row {
  padding-bottom: var(--gutter-small);
}
.contact-form__row--flex {
  display: flex;
  align-items: center;
  gap: var(--gutter-small);
}
.contact-form__row--flex input {
  width: auto;
}
.contact-form__row--flex > * {
  flex: auto;
  width: auto;
}
.contact-form__info a {
  text-decoration: underline;
}