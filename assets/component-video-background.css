.video-background {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--color-third-background-main);
}
.video-background video {
  position: relative;
  z-index: 0;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover;
  opacity: 0;
  transition: all 0.3s linear;
  border-radius: var(--border-radius-cards);
}
.video-background .video-text__background.loaded video {
  opacity: 1;
}
.video-background .video-text__image figure {
  border-radius: var(--border-radius-cards);
}

.video-text__background,
.video-text__container {
  height: 100%;
}