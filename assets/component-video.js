if (typeof VideoPopup !== 'function') {

  class VideoPopup extends HTMLElement {
    constructor() {
      super();
      this.playButtonSvg = '<svg class="svg symbol symbol--play" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 48 48" style="enable-background:new 0 0 48 48; width: 48px; height: 48px;" xml:space="preserve"><g><path d="M32.5,22l-12.6-7.8c-1.1-0.7-2.6,0.1-2.6,1.5v15.7c0,1.3,1.5,2.2,2.6,1.5L32.5,25C33.6,24.3,33.6,22.7,32.5,22z"/></g></svg>';
      this.playButtonDisabledSvg = '<svg class="svg symbol symbol--play-disabled" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" viewBox="0 0 48 48" style="enable-background:new 0 0 48 48;width:48px;height:48px" xml:space="preserve"><g><path d="M32.5,22l-12.6-7.8c-1.1-0.7-2.6,0.1-2.6,1.5v15.7c0,1.3,1.5,2.2,2.6,1.5L32.5,25c1.1-0.7,1.1-2.3,0-3z"/><line x1="8" y1="8" x2="40" y2="40" stroke="black" stroke-width="2"/></g></svg>';
      
      this.bindEvents();
      this.bindCookieEvents();
      this.handleCookieConsent();
    }

    bindEvents() {
      const link = this.querySelector('[data-js-video-popup-link]');
      if (link) {
        link.addEventListener('click', this.handleVideoOpen.bind(this));
      }
    }

    bindCookieEvents() {
      window.addEventListener('CookiebotOnLoad', () => this.handleCookieConsent());
      window.addEventListener('CookiebotOnAccept', () => this.handleCookieConsent());
      window.addEventListener('CookiebotOnDecline', () => this.handleCookieConsent());
    }

    handleCookieConsent() {
      const link = this.querySelector('[data-js-video-popup-link]');
      const playButton = this.querySelector('.video-popup__play');
      const disclaimer = this.querySelector('.video-popup__disclaimer');

      if (disclaimer) {
        disclaimer.style.display = 'block';
      }

      if (!window.Cookiebot.consent.marketing || !window.Cookiebot.consent.statistics || !window.Cookiebot.consent.preferences) {
        if (link) link.style.pointerEvents = 'none';
        if (playButton) {
          playButton.innerHTML = this.playButtonDisabledSvg;
          playButton.style.display = "block";
        }
      } else {
        if (link) link.style.pointerEvents = 'auto';
        if (playButton) {
          playButton.innerHTML = this.playButtonSvg;
          playButton.style.display = "block";
        }
      }
    }

    handleVideoOpen(e) {
      e.preventDefault();

      if (!Cookiebot.consent.marketing || !Cookiebot.consent.statistics || !Cookiebot.consent.preferences) {
        return;
      }

      this.createBlackout();
      this.classList.add('video-opened');
      this.bindCloseButton();
      this.injectVideo();
    }

    createBlackout() {
      const blackout = document.createElement('div');
      blackout.classList.add('video-popup__blackout');
      this.append(blackout);
      // Force reflow to enable transition
      setTimeout(() => {
        blackout.style.opacity = '1';
      }, 10);
    }

    bindCloseButton() {
      const closeButton = this.querySelector('[data-js-video-popup-close]');
      if (closeButton) {
        closeButton.addEventListener('click', () => {
          this.handleClose();
        }, { once: true });
      }
    }

    handleClose() {
      this.querySelectorAll('iframe, video').forEach(elm => elm.remove());
      this.querySelector('.video-popup__blackout')?.remove();
      this.classList.remove('video-opened');
    }

    injectVideo() {
      setTimeout(() => {
        const container = this.querySelector('[data-js-video-popup-container]');
        const template = this.querySelector('template');
        if (container && template) {
          container.appendChild(template.content.firstElementChild.cloneNode(true));
          setTimeout(() => {
            this._playMedia(this.closest('[data-video]'));
          }, 500);
        }
      }, 50);
    }

    _playMedia(media) {
      if (!media) return;

      const mediaType = media.dataset.productMediaType;
      switch (mediaType) {
        case 'video':
          const video = media.querySelector('video');
          if (video) {
            video.play();
          }
          break;
        case 'external_video-youtube':
          const youtubeFrame = media.querySelector('.js-youtube');
          if (youtubeFrame) {
            youtubeFrame.contentWindow.postMessage('{"event":"command","func":"playVideo","args":""}', '*');
          }
          break;
        case 'external_video-vimeo':
          const vimeoFrame = media.querySelector('.js-vimeo');
          if (vimeoFrame) {
            vimeoFrame.contentWindow.postMessage('{"method":"play"}', '*');
          }
          break;
      }
    }
  }

  if (typeof customElements.get('video-popup') === 'undefined') {
    customElements.define('video-popup', VideoPopup);
  }

}