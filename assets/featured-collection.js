if (typeof window.profileResizeObserver === 'undefined') {
  // Create a ResizeObserver to watch for size changes if it doesn't exist yet
  window.profileResizeObserver = new ResizeObserver(() => {
    requestAnimationFrame(() => {
      adjustProfileImageHeight();
    });
  });
  
  document.addEventListener('DOMContentLoaded', () => {
    adjustProfileImageHeight();
  });

  window.addEventListener('unload', () => {
    cleanup();
  });
}

// Cleanup function to disconnect observer and unobserve elements
function cleanup() {
  const products = document.querySelectorAll('.product-item');
  products.forEach(product => {
    window.profileResizeObserver.unobserve(product);
  });
  window.profileResizeObserver.disconnect();
}

function adjustProfileImageHeight() {
  // Cleanup previous observations before starting new ones
  cleanup();
  
  const profileCollections = document.querySelectorAll('.profile-collection');
  
  profileCollections.forEach(profileCollection => {
    const firstProduct = profileCollection.querySelector('.product-item');
    const imageHolder = profileCollection.querySelector('.profile-collection-image-holder');
    const profileDetails = profileCollection.querySelector('.profile-details');
    const addButton = firstProduct?.querySelector('button[name="add"]');
    
    if (firstProduct && imageHolder) {
      // Check and update height if needed
      const productHeight = firstProduct.offsetHeight;
      const productWidth = firstProduct.offsetWidth;
      const currentHeight = imageHolder.offsetHeight;
      const currentWidth = imageHolder.offsetWidth;
      
      if (Math.abs(currentHeight - productHeight) > 0) {
        imageHolder.style.height = `${productHeight}px`;
      }

      if (Math.abs(currentWidth - productWidth) > 0) {
        imageHolder.style.width = `${productWidth}px`;
      }

      // Check and update position if needed
      if (profileDetails && addButton) {
        const addButtonRect = addButton.getBoundingClientRect();
        const firstProductRect = firstProduct.getBoundingClientRect();
        const bottomOffset = firstProductRect.bottom - addButtonRect.bottom;
        const currentBottom = profileDetails.style.bottom
          ? parseFloat(profileDetails.style.bottom)
          : 0;
        
        if (Math.abs(currentBottom - bottomOffset) > 0) {
          profileDetails.style.bottom = `${bottomOffset}px`;
        }
      }

      // Observe the product element for future size changes
      window.profileResizeObserver.observe(firstProduct);
    }
  });
}