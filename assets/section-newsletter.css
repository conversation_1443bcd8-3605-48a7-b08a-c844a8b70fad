@media screen and (max-width: 1023px) {
  .newsletter .button, .newsletter input[type=email] {
    font-size: calc( 					16px / 16 * var(--base-body-size) + 0px 				);
  }
}
@media screen and (min-width: 1024px) {
  .newsletter .button, .newsletter input[type=email] {
    font-size: calc( 				18px / 16 * var(--base-body-size) + 0px 			);
  }
}

.newsletter__container {
  max-width: 37.5rem;
  margin: 0 auto;
  padding: 0 1.25rem;
}
.newsletter__info {
  color: var(--color-secondary-text-cards);
}
.newsletter__icon svg path {
  stroke: var(--color-text-cards);
}
.newsletter fieldset {
  display: grid;
  grid-template-columns: 1fr min-content;
  align-items: center;
  margin-inline-start: auto;
  margin-inline-end: auto;
  row-gap: 1.25rem;
}
@media screen and (max-width: 474px) {
  .newsletter fieldset {
    grid-template-columns: 100%;
  }
}
.newsletter input[type=email] {
  border-width: var(--border-width-buttons);
  border-color: var(--color-secondary-text-cards);
  background-color: transparent;
  color: var(--color-secondary-text-cards);
}
@media screen and (min-width: 475px) {
  html[dir=rtl] .newsletter input[type=email] {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left-width: 0;
  }
  html[dir=ltr] .newsletter input[type=email] {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right-width: 0;
  }
}
.newsletter input[type=email]::placeholder {
  color: var(--color-secondary-text-cards);
  opacity: 1;
}
.newsletter .button {
  border-color: var(--color-secondary-text-cards);
  color: var(--color-text-cards);
  padding: var(--input-padding) 2.1875rem;
  line-height: var(--base-body-line);
}
@media screen and (min-width: 475px) {
  html[dir=rtl] .newsletter .button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  html[dir=ltr] .newsletter .button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}