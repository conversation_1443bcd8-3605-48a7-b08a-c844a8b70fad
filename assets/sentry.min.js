/*! @sentry/browser (Performance Monitoring and Replay) 8.52.0 (bbafacb) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){t=window.Sentry||{};const e="8.52.0",n=globalThis;function r(t,r,s){const i=n,o=i.__SENTRY__=i.__SENTRY__||{},c=o[e]=o[e]||{};return c[t]||(c[t]=r())}const s=["debug","info","warn","error","log","assert","trace"],i={};function o(t){if(!("console"in n))return t();const e=n.console,r={},s=Object.keys(i);s.forEach((t=>{const n=i[t];r[t]=e[t],e[t]=n}));try{return t()}finally{s.forEach((t=>{e[t]=r[t]}))}}const c=r("logger",(function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return s.forEach((t=>{e[t]=()=>{}})),e})),a=50,u="?",l=/\(error: (.*)\)/,f=/captureMessage|captureException/;function h(...t){const e=t.sort(((t,e)=>t[0]-e[0])).map((t=>t[1]));return(t,n=0,r=0)=>{const s=[],i=t.split("\n");for(let t=n;t<i.length;t++){const n=i[t];if(n.length>1024)continue;const o=l.test(n)?n.replace(l,"$1"):n;if(!o.match(/\S*Error: /)){for(const t of e){const e=t(o);if(e){s.push(e);break}}if(s.length>=a+r)break}}return function(t){if(!t.length)return[];const e=Array.from(t);/sentryWrapped/.test(d(e).function||"")&&e.pop();e.reverse(),f.test(d(e).function||"")&&(e.pop(),f.test(d(e).function||"")&&e.pop());return e.slice(0,a).map((t=>({...t,filename:t.filename||d(e).filename,function:t.function||u})))}(s.slice(r))}}function d(t){return t[t.length-1]||{}}const p="<anonymous>";function m(t){try{return t&&"function"==typeof t&&t.name||p}catch(t){return p}}function y(t){const e=t.exception;if(e){const t=[];try{return e.values.forEach((e=>{e.stacktrace.frames&&t.push(...e.stacktrace.frames)})),t}catch(t){return}}}const g={},v={};function b(t,e){g[t]=g[t]||[],g[t].push(e)}function w(t,e){if(!v[t]){v[t]=!0;try{e()}catch(t){}}}function k(t,e){const n=t&&g[t];if(n)for(const t of n)try{t(e)}catch(t){}}let S=null;function _(t){const e="error";b(e,t),w(e,T)}function T(){S=n.onerror,n.onerror=function(t,e,n,r,s){return k("error",{column:r,error:s,line:n,msg:t,url:e}),!!S&&S.apply(this,arguments)},n.onerror.__SENTRY_INSTRUMENTED__=!0}let I=null;function E(t){const e="unhandledrejection";b(e,t),w(e,x)}function x(){I=n.onunhandledrejection,n.onunhandledrejection=function(t){return k("unhandledrejection",t),!I||I.apply(this,arguments)},n.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function C(){return A(n),n}function A(t){const n=t.__SENTRY__=t.__SENTRY__||{};return n.version=n.version||e,n[e]=n[e]||{}}const R=Object.prototype.toString;function O(t){switch(R.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return U(t,Error)}}function M(t,e){return R.call(t)===`[object ${e}]`}function $(t){return M(t,"ErrorEvent")}function D(t){return M(t,"DOMError")}function L(t){return M(t,"String")}function N(t){return"object"==typeof t&&null!==t&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function F(t){return null===t||N(t)||"object"!=typeof t&&"function"!=typeof t}function P(t){return M(t,"Object")}function j(t){return"undefined"!=typeof Event&&U(t,Event)}function B(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function U(t,e){try{return t instanceof e}catch(t){return!1}}function z(t){return!("object"!=typeof t||null===t||!t.__isVue&&!t.t)}const q=n,H=80;function W(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,s=[];let i=0,o=0;const c=" > ",a=c.length;let u;const l=Array.isArray(e)?e:e.keyAttrs,f=!Array.isArray(e)&&e.maxStringLength||H;for(;n&&i++<r&&(u=J(n,l),!("html"===u||i>1&&o+s.length*a+u.length>=f));)s.push(u),o+=u.length,n=n.parentNode;return s.reverse().join(c)}catch(t){return"<unknown>"}}function J(t,e){const n=t,r=[];if(!n||!n.tagName)return"";if(q.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=e&&e.length?e.filter((t=>n.getAttribute(t))).map((t=>[t,n.getAttribute(t)])):null;if(s&&s.length)s.forEach((t=>{r.push(`[${t[0]}="${t[1]}"]`)}));else{n.id&&r.push(`#${n.id}`);const t=n.className;if(t&&L(t)){const e=t.split(/\s+/);for(const t of e)r.push(`.${t}`)}}const i=["aria-label","type","name","title","alt"];for(const t of i){const e=n.getAttribute(t);e&&r.push(`[${t}="${e}"]`)}return r.join("")}function K(){try{return q.document.location.href}catch(t){return""}}function Y(t){if(!q.HTMLElement)return null;let e=t;for(let t=0;t<5;t++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}function G(t,e=0){return"string"!=typeof t||0===e||t.length<=e?t:`${t.slice(0,e)}...`}function X(t,e){if(!Array.isArray(t))return"";const n=[];for(let e=0;e<t.length;e++){const r=t[e];try{z(r)?n.push("[VueViewModel]"):n.push(String(r))}catch(t){n.push("[value cannot be serialized]")}}return n.join(e)}function V(t,e,n=!1){return!!L(t)&&(M(e,"RegExp")?e.test(t):!!L(e)&&(n?t===e:t.includes(e)))}function Q(t,e=[],n=!1){return e.some((e=>V(t,e,n)))}function Z(t,e,n){if(!(e in t))return;const r=t[e],s=n(r);"function"==typeof s&&et(s,r);try{t[e]=s}catch(t){}}function tt(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch(t){}}function et(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,tt(t,"__sentry_original__",e)}catch(t){}}function nt(t){return t.__sentry_original__}function rt(t){if(O(t))return{message:t.message,name:t.name,stack:t.stack,...it(t)};if(j(t)){const e={type:t.type,target:st(t.target),currentTarget:st(t.currentTarget),...it(t)};return"undefined"!=typeof CustomEvent&&U(t,CustomEvent)&&(e.detail=t.detail),e}return t}function st(t){try{return e=t,"undefined"!=typeof Element&&U(e,Element)?W(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}var e}function it(t){if("object"==typeof t&&null!==t){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function ot(t){return ct(t,new Map)}function ct(t,e){if(function(t){if(!P(t))return!1;try{const e=Object.getPrototypeOf(t).constructor.name;return!e||"Object"===e}catch(t){return!0}}(t)){const n=e.get(t);if(void 0!==n)return n;const r={};e.set(t,r);for(const n of Object.getOwnPropertyNames(t))void 0!==t[n]&&(r[n]=ct(t[n],e));return r}if(Array.isArray(t)){const n=e.get(t);if(void 0!==n)return n;const r=[];return e.set(t,r),t.forEach((t=>{r.push(ct(t,e))})),r}return t}const at=1e3;function ut(){return Date.now()/at}const lt=function(){const{performance:t}=n;if(!t||!t.now)return ut;const e=Date.now()-t.now(),r=null==t.timeOrigin?e:t.timeOrigin;return()=>(r+t.now())/at}(),ft=(()=>{const{performance:t}=n;if(!t||!t.now)return;const e=36e5,r=t.now(),s=Date.now(),i=t.timeOrigin?Math.abs(t.timeOrigin+r-s):e,o=i<e,c=t.timing&&t.timing.navigationStart,a="number"==typeof c?Math.abs(c+r-s):e;return o||a<e?i<=a?t.timeOrigin:c:s})();function ht(){const t=n,e=t.crypto||t.msCrypto;let r=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(r=()=>{const t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(t){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&r())>>t/4).toString(16)))}function dt(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function pt(t){const{message:e,event_id:n}=t;if(e)return e;const r=dt(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function mt(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],i=s[0]=s[0]||{};i.value||(i.value=e||""),i.type||(i.type="Error")}function yt(t,e){const n=dt(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const t={...r&&r.data,...e.data};n.mechanism.data=t}}function gt(t){if(function(t){try{return t.__sentry_captured__}catch(t){}}(t))return!0;try{tt(t,"__sentry_captured__",!0)}catch(t){}return!1}var vt;function bt(t){return new kt((e=>{e(t)}))}function wt(t){return new kt(((e,n)=>{n(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(vt||(vt={}));class kt{constructor(t){kt.prototype.__init.call(this),kt.prototype.__init2.call(this),kt.prototype.__init3.call(this),kt.prototype.__init4.call(this),this.i=vt.PENDING,this.o=[];try{t(this.u,this.l)}catch(t){this.l(t)}}then(t,e){return new kt(((n,r)=>{this.o.push([!1,e=>{if(t)try{n(t(e))}catch(t){r(t)}else n(e)},t=>{if(e)try{n(e(t))}catch(t){r(t)}else r(t)}]),this.h()}))}catch(t){return this.then((t=>t),t)}finally(t){return new kt(((e,n)=>{let r,s;return this.then((e=>{s=!1,r=e,t&&t()}),(e=>{s=!0,r=e,t&&t()})).then((()=>{s?n(r):e(r)}))}))}__init(){this.u=t=>{this.p(vt.RESOLVED,t)}}__init2(){this.l=t=>{this.p(vt.REJECTED,t)}}__init3(){this.p=(t,e)=>{this.i===vt.PENDING&&(B(e)?e.then(this.u,this.l):(this.i=t,this.m=e,this.h()))}}__init4(){this.h=()=>{if(this.i===vt.PENDING)return;const t=this.o.slice();this.o=[],t.forEach((t=>{t[0]||(this.i===vt.RESOLVED&&t[1](this.m),this.i===vt.REJECTED&&t[2](this.m),t[0]=!0)}))}}}function St(t){const e=lt(),n={sid:ht(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return ot({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(n)};return t&&_t(n,t),n}function _t(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||lt(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:ht()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"==typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof e.duration)t.duration=e.duration;else{const e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"==typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}function Tt(){return ht()}function It(){return ht().substring(16)}function Et(t,e,n=2){if(!e||"object"!=typeof e||n<=0)return e;if(t&&e&&0===Object.keys(e).length)return t;const r={...t};for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=Et(r[t],e[t],n-1));return r}const xt="_sentrySpan";function Ct(t,e){e?tt(t,xt,e):delete t[xt]}function At(t){return t[xt]}class Rt{constructor(){this.v=!1,this.k=[],this.S=[],this._=[],this.T=[],this.I={},this.C={},this.A={},this.R={},this.O={},this.M={traceId:Tt(),spanId:It()}}clone(){const t=new Rt;return t._=[...this._],t.C={...this.C},t.A={...this.A},t.R={...this.R},this.R.flags&&(t.R.flags={values:[...this.R.flags.values]}),t.I=this.I,t.D=this.D,t.L=this.L,t.N=this.N,t.F=this.F,t.S=[...this.S],t.P=this.P,t.T=[...this.T],t.O={...this.O},t.M={...this.M},t.j=this.j,t.B=this.B,Ct(t,At(this)),t}setClient(t){this.j=t}setLastEventId(t){this.B=t}getClient(){return this.j}lastEventId(){return this.B}addScopeListener(t){this.k.push(t)}addEventProcessor(t){return this.S.push(t),this}setUser(t){return this.I=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this.L&&_t(this.L,{user:t}),this.U(),this}getUser(){return this.I}getRequestSession(){return this.P}setRequestSession(t){return this.P=t,this}setTags(t){return this.C={...this.C,...t},this.U(),this}setTag(t,e){return this.C={...this.C,[t]:e},this.U(),this}setExtras(t){return this.A={...this.A,...t},this.U(),this}setExtra(t,e){return this.A={...this.A,[t]:e},this.U(),this}setFingerprint(t){return this.F=t,this.U(),this}setLevel(t){return this.D=t,this.U(),this}setTransactionName(t){return this.N=t,this.U(),this}setContext(t,e){return null===e?delete this.R[t]:this.R[t]=e,this.U(),this}setSession(t){return t?this.L=t:delete this.L,this.U(),this}getSession(){return this.L}update(t){if(!t)return this;const e="function"==typeof t?t(this):t,[n,r]=e instanceof Ot?[e.getScopeData(),e.getRequestSession()]:P(e)?[t,t.requestSession]:[],{tags:s,extra:i,user:o,contexts:c,level:a,fingerprint:u=[],propagationContext:l}=n||{};return this.C={...this.C,...s},this.A={...this.A,...i},this.R={...this.R,...c},o&&Object.keys(o).length&&(this.I=o),a&&(this.D=a),u.length&&(this.F=u),l&&(this.M=l),r&&(this.P=r),this}clear(){return this._=[],this.C={},this.A={},this.I={},this.R={},this.D=void 0,this.N=void 0,this.F=void 0,this.P=void 0,this.L=void 0,Ct(this,void 0),this.T=[],this.setPropagationContext({traceId:Tt()}),this.U(),this}addBreadcrumb(t,e){const n="number"==typeof e?e:100;if(n<=0)return this;const r={timestamp:ut(),...t};return this._.push(r),this._.length>n&&(this._=this._.slice(-n),this.j&&this.j.recordDroppedEvent("buffer_overflow","log_item")),this.U(),this}getLastBreadcrumb(){return this._[this._.length-1]}clearBreadcrumbs(){return this._=[],this.U(),this}addAttachment(t){return this.T.push(t),this}clearAttachments(){return this.T=[],this}getScopeData(){return{breadcrumbs:this._,attachments:this.T,contexts:this.R,tags:this.C,extra:this.A,user:this.I,level:this.D,fingerprint:this.F||[],eventProcessors:this.S,propagationContext:this.M,sdkProcessingMetadata:this.O,transactionName:this.N,span:At(this)}}setSDKProcessingMetadata(t){return this.O=Et(this.O,t,2),this}setPropagationContext(t){return this.M={spanId:It(),...t},this}getPropagationContext(){return this.M}captureException(t,e){const n=e&&e.event_id?e.event_id:ht();if(!this.j)return c.warn("No client configured on scope - will not capture exception!"),n;const r=new Error("Sentry syntheticException");return this.j.captureException(t,{originalException:t,syntheticException:r,...e,event_id:n},this),n}captureMessage(t,e,n){const r=n&&n.event_id?n.event_id:ht();if(!this.j)return c.warn("No client configured on scope - will not capture message!"),r;const s=new Error(t);return this.j.captureMessage(t,e,{originalException:t,syntheticException:s,...n,event_id:r},this),r}captureEvent(t,e){const n=e&&e.event_id?e.event_id:ht();return this.j?(this.j.captureEvent(t,{...e,event_id:n},this),n):(c.warn("No client configured on scope - will not capture event!"),n)}U(){this.v||(this.v=!0,this.k.forEach((t=>{t(this)})),this.v=!1)}}const Ot=Rt;class Mt{constructor(t,e){let n,r;n=t||new Ot,r=e||new Ot,this.q=[{scope:n}],this.H=r}withScope(t){const e=this.W();let n;try{n=t(e)}catch(t){throw this.J(),t}return B(n)?n.then((t=>(this.J(),t)),(t=>{throw this.J(),t})):(this.J(),n)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this.H}getStackTop(){return this.q[this.q.length-1]}W(){const t=this.getScope().clone();return this.q.push({client:this.getClient(),scope:t}),t}J(){return!(this.q.length<=1)&&!!this.q.pop()}}function $t(){const t=A(C());return t.stack=t.stack||new Mt(r("defaultCurrentScope",(()=>new Ot)),r("defaultIsolationScope",(()=>new Ot)))}function Dt(t){return $t().withScope(t)}function Lt(t,e){const n=$t();return n.withScope((()=>(n.getStackTop().scope=t,e(t))))}function Nt(t){return $t().withScope((()=>t($t().getIsolationScope())))}function Ft(t){const e=A(t);return e.acs?e.acs:{withIsolationScope:Nt,withScope:Dt,withSetScope:Lt,withSetIsolationScope:(t,e)=>Nt(e),getCurrentScope:()=>$t().getScope(),getIsolationScope:()=>$t().getIsolationScope()}}function Pt(){return Ft(C()).getCurrentScope()}function jt(){return Ft(C()).getIsolationScope()}function Bt(){return r("globalScope",(()=>new Ot))}function Ut(...t){const e=Ft(C());if(2===t.length){const[n,r]=t;return n?e.withSetScope(n,r):e.withScope(r)}return e.withScope(t[0])}function zt(){return Pt().getClient()}function qt(t){const e=t.getPropagationContext(),{traceId:n,spanId:r,parentSpanId:s}=e;return ot({trace_id:n,span_id:r,parent_span_id:s})}const Ht="_sentryMetrics";function Wt(t){const e=t[Ht];if(!e)return;const n={};for(const[,[t,r]]of e){(n[t]||(n[t]=[])).push(ot(r))}return n}const Jt="sentry.source",Kt="sentry.sample_rate",Yt="sentry.op",Gt="sentry.origin",Xt="sentry.idle_span_finish_reason",Vt="sentry.measurement_unit",Qt="sentry.measurement_value",Zt="sentry.custom_span_name",te="sentry.exclusive_time",ee=0,ne=1,re=2;function se(t,e){t.setAttribute("http.response.status_code",e);const n=function(t){if(t<400&&t>=100)return{code:ne};if(t>=400&&t<500)switch(t){case 401:return{code:re,message:"unauthenticated"};case 403:return{code:re,message:"permission_denied"};case 404:return{code:re,message:"not_found"};case 409:return{code:re,message:"already_exists"};case 413:return{code:re,message:"failed_precondition"};case 429:return{code:re,message:"resource_exhausted"};case 499:return{code:re,message:"cancelled"};default:return{code:re,message:"invalid_argument"}}if(t>=500&&t<600)switch(t){case 501:return{code:re,message:"unimplemented"};case 503:return{code:re,message:"unavailable"};case 504:return{code:re,message:"deadline_exceeded"};default:return{code:re,message:"internal_error"}}return{code:re,message:"unknown_error"}}(e);"unknown_error"!==n.message&&t.setStatus(n)}const ie="sentry-",oe=/^sentry-/,ce=8192;function ae(t){const e=function(t){if(!t||!L(t)&&!Array.isArray(t))return;if(Array.isArray(t))return t.reduce(((t,e)=>{const n=le(e);return Object.entries(n).forEach((([e,n])=>{t[e]=n})),t}),{});return le(t)}(t);if(!e)return;const n=Object.entries(e).reduce(((t,[e,n])=>{if(e.match(oe)){t[e.slice(ie.length)]=n}return t}),{});return Object.keys(n).length>0?n:void 0}function ue(t){if(!t)return;return function(t){if(0===Object.keys(t).length)return;return Object.entries(t).reduce(((t,[e,n],r)=>{const s=`${encodeURIComponent(e)}=${encodeURIComponent(n)}`,i=0===r?s:`${t},${s}`;return i.length>ce?t:i}),"")}(Object.entries(t).reduce(((t,[e,n])=>(n&&(t[`${ie}${e}`]=n),t)),{}))}function le(t){return t.split(",").map((t=>t.split("=").map((t=>decodeURIComponent(t.trim()))))).reduce(((t,[e,n])=>(e&&n&&(t[e]=n),t)),{})}const fe=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function he(t,e){const n=function(t){if(!t)return;const e=t.match(fe);if(!e)return;let n;return"1"===e[3]?n=!0:"0"===e[3]&&(n=!1),{traceId:e[1],parentSampled:n,parentSpanId:e[2]}}(t),r=ae(e);if(!n||!n.traceId)return{traceId:Tt(),spanId:It()};const{traceId:s,parentSpanId:i,parentSampled:o}=n;return{traceId:s,parentSpanId:i,spanId:It(),sampled:o,dsc:r||{}}}function de(t=Tt(),e=It(),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${t}-${e}${r}`}const pe=1;let me=!1;function ye(t){const{spanId:e,traceId:n}=t.spanContext(),{data:r,op:s,parent_span_id:i,status:o,origin:c}=ke(t);return ot({parent_span_id:i,span_id:e,trace_id:n,data:r,op:s,status:o,origin:c})}function ge(t){const{spanId:e,traceId:n,isRemote:r}=t.spanContext();return ot({parent_span_id:r?e:ke(t).parent_span_id,span_id:r?It():e,trace_id:n})}function ve(t){const{traceId:e,spanId:n}=t.spanContext();return de(e,n,Se(t))}function be(t){return"number"==typeof t?we(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?we(t.getTime()):lt()}function we(t){return t>9999999999?t/1e3:t}function ke(t){if(function(t){return"function"==typeof t.getSpanJSON}(t))return t.getSpanJSON();try{const{spanId:e,traceId:n}=t.spanContext();if(function(t){const e=t;return!!(e.attributes&&e.startTime&&e.name&&e.endTime&&e.status)}(t)){const{attributes:r,startTime:s,name:i,endTime:o,parentSpanId:c,status:a}=t;return ot({span_id:e,trace_id:n,data:r,description:i,parent_span_id:c,start_timestamp:be(s),timestamp:be(o)||void 0,status:_e(a),op:r[Yt],origin:r[Gt],_metrics_summary:Wt(t)})}return{span_id:e,trace_id:n}}catch(t){return{}}}function Se(t){const{traceFlags:e}=t.spanContext();return e===pe}function _e(t){if(t&&t.code!==ee)return t.code===ne?"ok":t.message||"unknown_error"}const Te="_sentryChildSpans",Ie="_sentryRootSpan";function Ee(t,e){const n=t[Ie]||t;tt(e,Ie,n),t[Te]?t[Te].add(e):tt(t,Te,new Set([e]))}function xe(t){const e=new Set;return function t(n){if(!e.has(n)&&Se(n)){e.add(n);const r=n[Te]?Array.from(n[Te]):[];for(const e of r)t(e)}}(t),Array.from(e)}function Ce(t){return t[Ie]||t}function Ae(){const t=Ft(C());return t.getActiveSpan?t.getActiveSpan():At(Pt())}function Re(t,e,n,r,s,i){const o=Ae();o&&function(t,e,n,r,s,i,o){const c=t[Ht]||(t[Ht]=new Map),a=`${e}:${n}@${s}`,u=c.get(o);if(u){const[,t]=u;c.set(o,[a,{min:Math.min(t.min,r),max:Math.max(t.max,r),count:t.count+=1,sum:t.sum+=r,tags:t.tags}])}else c.set(o,[a,{min:r,max:r,count:1,sum:r,tags:i}])}(o,t,e,n,r,s,i)}function Oe(){me||(o((()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")})),me=!0)}let Me=!1;function $e(){Me||(Me=!0,_(De),E(De))}function De(){const t=Ae(),e=t&&Ce(t);if(e){const t="internal_error";e.setStatus({code:re,message:t})}}De.tag="sentry_tracingErrorCallback";const Le="_sentryScope",Ne="_sentryIsolationScope";function Fe(t){return{scope:t[Le],isolationScope:t[Ne]}}function Pe(t){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const e=zt(),n=t||e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}class je{constructor(t={}){this.K=t.traceId||Tt(),this.Y=t.spanId||It()}spanContext(){return{spanId:this.Y,traceId:this.K,traceFlags:0}}end(t){}setAttribute(t,e){return this}setAttributes(t){return this}setStatus(t){return this}updateName(t){return this}isRecording(){return!1}addEvent(t,e,n){return this}addLink(t){return this}addLinks(t){return this}recordException(t,e){}}function Be(t,e,n=(()=>{})){let r;try{r=t()}catch(t){throw e(t),n(),t}return function(t,e,n){if(B(t))return t.then((t=>(n(),t)),(t=>{throw e(t),n(),t}));return n(),t}(r,e,n)}const Ue="production",ze="_frozenDsc";function qe(t,e){tt(t,ze,e)}function He(t,e){const n=e.getOptions(),{publicKey:r}=e.getDsn()||{},s=ot({environment:n.environment||Ue,release:n.release,public_key:r,trace_id:t});return e.emit("createDsc",s),s}function We(t,e){const n=e.getPropagationContext();return n.dsc||He(n.traceId,t)}function Je(t){const e=zt();if(!e)return{};const n=Ce(t),r=n[ze];if(r)return r;const s=n.spanContext().traceState,i=s&&s.get("sentry.dsc"),o=i&&ae(i);if(o)return o;const c=He(t.spanContext().traceId,e),a=ke(n),u=a.data||{},l=u[Kt];null!=l&&(c.sample_rate=`${l}`);const f=u[Jt],h=a.description;return"url"!==f&&h&&(c.transaction=h),Pe()&&(c.sampled=String(Se(n))),e.emit("createDsc",c,n),c}function Ke(t){if("boolean"==typeof t)return Number(t);const e="string"==typeof t?parseFloat(t):t;return"number"!=typeof e||isNaN(e)||e<0||e>1?void 0:e}const Ye=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Ge(t,e=!1){const{host:n,path:r,pass:s,port:i,projectId:o,protocol:c,publicKey:a}=t;return`${c}://${a}${e&&s?`:${s}`:""}@${n}${i?`:${i}`:""}/${r?`${r}/`:r}${o}`}function Xe(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function Ve(t){const e="string"==typeof t?function(t){const e=Ye.exec(t);if(!e)return void o((()=>{console.error(`Invalid Sentry Dsn: ${t}`)}));const[n,r,s="",i="",c="",a=""]=e.slice(1);let u="",l=a;const f=l.split("/");if(f.length>1&&(u=f.slice(0,-1).join("/"),l=f.pop()),l){const t=l.match(/^\d+/);t&&(l=t[0])}return Xe({host:i,pass:s,path:u,projectId:l,port:c,protocol:n,publicKey:r})}(t):Xe(t);if(e)return e}function Qe(t,e=100,n=1/0){try{return tn("",t,e,n)}catch(t){return{ERROR:`**non-serializable** (${t})`}}}function Ze(t,e=3,n=102400){const r=Qe(t,e);return s=r,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(s))>n?Ze(t,e-1,n):r;var s}function tn(t,e,n=1/0,r=1/0,s=function(){const t="function"==typeof WeakSet,e=t?new WeakSet:[];return[function(n){if(t)return!!e.has(n)||(e.add(n),!1);for(let t=0;t<e.length;t++)if(e[t]===n)return!0;return e.push(n),!1},function(n){if(t)e.delete(n);else for(let t=0;t<e.length;t++)if(e[t]===n){e.splice(t,1);break}}]}()){const[i,o]=s;if(null==e||["boolean","string"].includes(typeof e)||"number"==typeof e&&Number.isFinite(e))return e;const c=function(t,e){try{if("domain"===t&&e&&"object"==typeof e&&e.G)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!=typeof global&&e===global)return"[Global]";if("undefined"!=typeof window&&e===window)return"[Window]";if("undefined"!=typeof document&&e===document)return"[Document]";if(z(e))return"[VueViewModel]";if(P(n=e)&&"nativeEvent"in n&&"preventDefault"in n&&"stopPropagation"in n)return"[SyntheticEvent]";if("number"==typeof e&&!Number.isFinite(e))return`[${e}]`;if("function"==typeof e)return`[Function: ${m(e)}]`;if("symbol"==typeof e)return`[${String(e)}]`;if("bigint"==typeof e)return`[BigInt: ${String(e)}]`;const r=function(t){const e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}(e);return/^HTML(\w*)Element$/.test(r)?`[HTMLElement: ${r}]`:`[object ${r}]`}catch(t){return`**non-serializable** (${t})`}var n}(t,e);if(!c.startsWith("[object "))return c;if(e.__sentry_skip_normalization__)return e;const a="number"==typeof e.__sentry_override_normalization_depth__?e.__sentry_override_normalization_depth__:n;if(0===a)return c.replace("object ","");if(i(e))return"[Circular ~]";const u=e;if(u&&"function"==typeof u.toJSON)try{return tn("",u.toJSON(),a-1,r,s)}catch(t){}const l=Array.isArray(e)?[]:{};let f=0;const h=rt(e);for(const t in h){if(!Object.prototype.hasOwnProperty.call(h,t))continue;if(f>=r){l[t]="[MaxProperties ~]";break}const e=h[t];l[t]=tn(t,e,a-1,r,s),f++}return o(e),l}function en(t,e=[]){return[t,e]}function nn(t,e){const[n,r]=t;return[n,[...r,e]]}function rn(t,e){const n=t[1];for(const t of n){if(e(t,t[0].type))return!0}return!1}function sn(t){return n.__SENTRY__&&n.__SENTRY__.encodePolyfill?n.__SENTRY__.encodePolyfill(t):(new TextEncoder).encode(t)}function on(t){const[e,n]=t;let r=JSON.stringify(e);function s(t){"string"==typeof r?r="string"==typeof t?r+t:[sn(r),t]:r.push("string"==typeof t?sn(t):t)}for(const t of n){const[e,n]=t;if(s(`\n${JSON.stringify(e)}\n`),"string"==typeof n||n instanceof Uint8Array)s(n);else{let t;try{t=JSON.stringify(n)}catch(e){t=JSON.stringify(Qe(n))}s(t)}}return"string"==typeof r?r:function(t){const e=t.reduce(((t,e)=>t+e.length),0),n=new Uint8Array(e);let r=0;for(const e of t)n.set(e,r),r+=e.length;return n}(r)}function cn(t){return[{type:"span"},t]}function an(t){const e="string"==typeof t.data?sn(t.data):t.data;return[ot({type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),e]}const un={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function ln(t){return un[t]}function fn(t){if(!t||!t.sdk)return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function hn(t,e,n,r){const s=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:Ge(r)},...s&&{trace:ot({...s})}}}function dn(t,e,n,r=Ae()){const s=r&&Ce(r);s&&s.addEvent(t,{[Qt]:e,[Vt]:n})}function pn(t){if(!t||0===t.length)return;const e={};return t.forEach((t=>{const n=t.attributes||{},r=n[Vt],s=n[Qt];"string"==typeof r&&"number"==typeof s&&(e[t.name]={value:s,unit:r})})),e}class mn{constructor(t={}){this.K=t.traceId||Tt(),this.Y=t.spanId||It(),this.X=t.startTimestamp||lt(),this.V={},this.setAttributes({[Gt]:"manual",[Yt]:t.op,...t.attributes}),this.Z=t.name,t.parentSpanId&&(this.tt=t.parentSpanId),"sampled"in t&&(this.et=t.sampled),t.endTimestamp&&(this.nt=t.endTimestamp),this.G=[],this.rt=t.isStandalone,this.nt&&this.st()}addLink(t){return this}addLinks(t){return this}recordException(t,e){}spanContext(){const{Y:t,K:e,et:n}=this;return{spanId:t,traceId:e,traceFlags:n?pe:0}}setAttribute(t,e){return void 0===e?delete this.V[t]:this.V[t]=e,this}setAttributes(t){return Object.keys(t).forEach((e=>this.setAttribute(e,t[e]))),this}updateStartTime(t){this.X=be(t)}setStatus(t){return this.it=t,this}updateName(t){return this.Z=t,this.setAttribute(Jt,"custom"),this}end(t){this.nt||(this.nt=be(t),this.st())}getSpanJSON(){return ot({data:this.V,description:this.Z,op:this.V[Yt],parent_span_id:this.tt,span_id:this.Y,start_timestamp:this.X,status:_e(this.it),timestamp:this.nt,trace_id:this.K,origin:this.V[Gt],_metrics_summary:Wt(this),profile_id:this.V["sentry.profile_id"],exclusive_time:this.V[te],measurements:pn(this.G),is_segment:this.rt&&Ce(this)===this||void 0,segment_id:this.rt?Ce(this).spanContext().spanId:void 0})}isRecording(){return!this.nt&&!!this.et}addEvent(t,e,n){const r=yn(e)?e:n||lt(),s=yn(e)?{}:e||{},i={name:t,time:be(r),attributes:s};return this.G.push(i),this}isStandaloneSpan(){return!!this.rt}st(){const t=zt();t&&t.emit("spanEnd",this);if(!(this.rt||this===Ce(this)))return;if(this.rt)return void(this.et?function(t){const e=zt();if(!e)return;const n=t[1];if(!n||0===n.length)return void e.recordDroppedEvent("before_send","span");e.sendEnvelope(t)}(function(t,e){const n=Je(t[0]),r=e&&e.getDsn(),s=e&&e.getOptions().tunnel,i={sent_at:(new Date).toISOString(),...function(t){return!!t.trace_id&&!!t.public_key}(n)&&{trace:n},...!!s&&r&&{dsn:Ge(r)}},o=e&&e.getOptions().beforeSendSpan,c=o?t=>{const e=o(ke(t));return e||Oe(),e}:t=>ke(t),a=[];for(const e of t){const t=c(e);t&&a.push(cn(t))}return en(i,a)}([this],t)):t&&t.recordDroppedEvent("sample_rate","span"));const e=this.ot();if(e){(Fe(this).scope||Pt()).captureEvent(e)}}ot(){if(!gn(ke(this)))return;this.Z||(this.Z="<unlabeled transaction>");const{scope:t,isolationScope:e}=Fe(this),n=(t||Pt()).getClient()||zt();if(!0!==this.et)return void(n&&n.recordDroppedEvent("sample_rate","transaction"));const r=xe(this).filter((t=>t!==this&&!function(t){return t instanceof mn&&t.isStandaloneSpan()}(t))).map((t=>ke(t))).filter(gn),s=this.V[Jt];delete this.V[Zt],r.forEach((t=>{t.data&&delete t.data[Zt]}));const i={contexts:{trace:ye(this)},spans:r.length>1e3?r.sort(((t,e)=>t.start_timestamp-e.start_timestamp)).slice(0,1e3):r,start_timestamp:this.X,timestamp:this.nt,transaction:this.Z,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:t,capturedSpanIsolationScope:e,...ot({dynamicSamplingContext:Je(this)})},_metrics_summary:Wt(this),...s&&{transaction_info:{source:s}}},o=pn(this.G);return o&&Object.keys(o).length&&(i.measurements=o),i}}function yn(t){return t&&"number"==typeof t||t instanceof Date||Array.isArray(t)}function gn(t){return!!(t.start_timestamp&&t.timestamp&&t.span_id&&t.trace_id)}const vn="__SENTRY_SUPPRESS_TRACING__";function bn(t,e){const n=Tn();if(n.startSpanManual)return n.startSpanManual(t,e);const r=_n(t),{forceTransaction:s,parentSpan:i}=t;return Ut(t.scope,(()=>xn(i)((()=>{const n=Pt(),i=En(n),o=t.onlyIfParent&&!i?new je:Sn({parentSpan:i,spanArguments:r,forceTransaction:s,scope:n});function c(){o.end()}return Ct(n,o),Be((()=>e(o,c)),(()=>{const{status:t}=ke(o);!o.isRecording()||t&&"ok"!==t||o.setStatus({code:re,message:"internal_error"})}))}))))}function wn(t){const e=Tn();if(e.startInactiveSpan)return e.startInactiveSpan(t);const n=_n(t),{forceTransaction:r,parentSpan:s}=t;return(t.scope?e=>Ut(t.scope,e):void 0!==s?t=>kn(s,t):t=>t())((()=>{const e=Pt(),s=En(e);return t.onlyIfParent&&!s?new je:Sn({parentSpan:s,spanArguments:n,forceTransaction:r,scope:e})}))}function kn(t,e){const n=Tn();return n.withActiveSpan?n.withActiveSpan(t,e):Ut((n=>(Ct(n,t||void 0),e(n))))}function Sn({parentSpan:t,spanArguments:e,forceTransaction:n,scope:r}){if(!Pe())return new je;const s=jt();let i;if(t&&!n)i=function(t,e,n){const{spanId:r,traceId:s}=t.spanContext(),i=!e.getScopeData().sdkProcessingMetadata[vn]&&Se(t),o=i?new mn({...n,parentSpanId:r,traceId:s,sampled:i}):new je({traceId:s});Ee(t,o);const c=zt();c&&(c.emit("spanStart",o),n.endTimestamp&&c.emit("spanEnd",o));return o}(t,r,e),Ee(t,i);else if(t){const n=Je(t),{traceId:s,spanId:o}=t.spanContext(),c=Se(t);i=In({traceId:s,parentSpanId:o,...e},r,c),qe(i,n)}else{const{traceId:t,dsc:n,parentSpanId:o,sampled:c}={...s.getPropagationContext(),...r.getPropagationContext()};i=In({traceId:t,parentSpanId:o,...e},r,c),n&&qe(i,n)}return function(t,e,n){t&&(tt(t,Ne,n),tt(t,Le,e))}(i,r,s),i}function _n(t){const e={isStandalone:(t.experimental||{}).standalone,...t};if(t.startTime){const n={...e};return n.startTimestamp=be(t.startTime),delete n.startTime,n}return e}function Tn(){return Ft(C())}function In(t,e,n){const r=zt(),s=r&&r.getOptions()||{},{name:i="",attributes:o}=t,[c,a]=e.getScopeData().sdkProcessingMetadata[vn]?[!1]:function(t,e){if(!Pe(t))return[!1];const n=jt().getScopeData().sdkProcessingMetadata.normalizedRequest,r={...e,normalizedRequest:e.normalizedRequest||n};let s;s="function"==typeof t.tracesSampler?t.tracesSampler(r):void 0!==r.parentSampled?r.parentSampled:void 0!==t.tracesSampleRate?t.tracesSampleRate:1;const i=Ke(s);return void 0===i?[!1]:i&&Math.random()<i?[!0,i]:[!1,i]}(s,{name:i,parentSampled:n,attributes:o,transactionContext:{name:i,parentSampled:n}}),u=new mn({...t,attributes:{[Jt]:"custom",...t.attributes},sampled:c});return void 0!==a&&u.setAttribute(Kt,a),r&&r.emit("spanStart",u),u}function En(t){const e=At(t);if(!e)return;const n=zt();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?Ce(e):e}function xn(t){return void 0!==t?e=>kn(t,e):t=>t()}const Cn={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},An="heartbeatFailed",Rn="idleTimeout",On="finalTimeout",Mn="externalFinish";function $n(t,e={}){const n=new Map;let r,s=!1,i=Mn,o=!e.disableAutoFinish;const a=[],{idleTimeout:u=Cn.idleTimeout,finalTimeout:l=Cn.finalTimeout,childSpanTimeout:f=Cn.childSpanTimeout,beforeSpanEnd:h}=e,d=zt();if(!d||!Pe())return new je;const p=Pt(),m=Ae(),y=function(t){const e=wn(t);return Ct(Pt(),e),e}(t);function g(){r&&(clearTimeout(r),r=void 0)}function v(t){g(),r=setTimeout((()=>{!s&&0===n.size&&o&&(i=Rn,y.end(t))}),u)}function b(t){r=setTimeout((()=>{!s&&o&&(i=An,y.end(t))}),f)}function w(t){s=!0,n.clear(),a.forEach((t=>t())),Ct(p,m);const e=ke(y),{start_timestamp:r}=e;if(!r)return;(e.data||{})[Xt]||y.setAttribute(Xt,i),c.log(`[Tracing] Idle span "${e.op}" finished`);const o=xe(y).filter((t=>t!==y));let f=0;o.forEach((e=>{e.isRecording()&&(e.setStatus({code:re,message:"cancelled"}),e.end(t));const n=ke(e),{timestamp:r=0,start_timestamp:s=0}=n;r-s<=(l+u)/1e3&&s<=t||(!function(t,e){t[Te]&&t[Te].delete(e)}(y,e),f++)})),f>0&&y.setAttribute("sentry.idle_span_discarded_spans",f)}return y.end=new Proxy(y.end,{apply(t,e,n){h&&h(y);const[r,...s]=n,i=be(r||lt()),o=xe(y).filter((t=>t!==y));if(!o.length)return w(i),Reflect.apply(t,e,[i,...s]);const c=o.map((t=>ke(t).timestamp)).filter((t=>!!t)),a=c.length?Math.max(...c):void 0,u=ke(y).start_timestamp,f=Math.min(u?u+l/1e3:1/0,Math.max(u||-1/0,Math.min(i,a||1/0)));return w(f),Reflect.apply(t,e,[f,...s])}}),a.push(d.on("spanStart",(t=>{if(s||t===y||ke(t).timestamp)return;var e;xe(y).includes(t)&&(e=t.spanContext().spanId,g(),n.set(e,!0),b(lt()+f/1e3))}))),a.push(d.on("spanEnd",(t=>{var e;s||(e=t.spanContext().spanId,n.has(e)&&n.delete(e),0===n.size&&v(lt()+u/1e3))}))),a.push(d.on("idleSpanEnableAutoFinish",(t=>{t===y&&(o=!0,v(),n.size&&b())}))),e.disableAutoFinish||v(),setTimeout((()=>{s||(y.setStatus({code:re,message:"deadline_exceeded"}),i=On,y.end())}),l),y}function Dn(t,e,n,r=0){return new kt(((s,i)=>{const o=t[r];if(null===e||"function"!=typeof o)s(e);else{const c=o({...e},n);B(c)?c.then((e=>Dn(t,e,n,r+1).then(s))).then(null,i):Dn(t,c,n,r+1).then(s).then(null,i)}}))}let Ln,Nn,Fn;function Pn(t,e){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:i}=e;!function(t,e){const{extra:n,tags:r,user:s,contexts:i,level:o,transactionName:c}=e,a=ot(n);a&&Object.keys(a).length&&(t.extra={...a,...t.extra});const u=ot(r);u&&Object.keys(u).length&&(t.tags={...u,...t.tags});const l=ot(s);l&&Object.keys(l).length&&(t.user={...l,...t.user});const f=ot(i);f&&Object.keys(f).length&&(t.contexts={...f,...t.contexts});o&&(t.level=o);c&&"transaction"!==t.type&&(t.transaction=c)}(t,e),r&&function(t,e){t.contexts={trace:ge(e),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:Je(e),...t.sdkProcessingMetadata};const n=Ce(e),r=ke(n).description;r&&!t.transaction&&"transaction"===t.type&&(t.transaction=r)}(t,r),function(t,e){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],e&&(t.fingerprint=t.fingerprint.concat(e));t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}(t,n),function(t,e){const n=[...t.breadcrumbs||[],...e];t.breadcrumbs=n.length?n:void 0}(t,s),function(t,e){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...e}}(t,i)}function jn(t,e){const{extra:n,tags:r,user:s,contexts:i,level:o,sdkProcessingMetadata:c,breadcrumbs:a,fingerprint:u,eventProcessors:l,attachments:f,propagationContext:h,transactionName:d,span:p}=e;Bn(t,"extra",n),Bn(t,"tags",r),Bn(t,"user",s),Bn(t,"contexts",i),t.sdkProcessingMetadata=Et(t.sdkProcessingMetadata,c,2),o&&(t.level=o),d&&(t.transactionName=d),p&&(t.span=p),a.length&&(t.breadcrumbs=[...t.breadcrumbs,...a]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),l.length&&(t.eventProcessors=[...t.eventProcessors,...l]),f.length&&(t.attachments=[...t.attachments,...f]),t.propagationContext={...t.propagationContext,...h}}function Bn(t,e,n){t[e]=Et(t[e],n,1)}function Un(t,e,r,s,i,o){const{normalizeDepth:c=3,normalizeMaxBreadth:a=1e3}=t,u={...e,event_id:e.event_id||r.event_id||ht(),timestamp:e.timestamp||ut()},l=r.integrations||t.integrations.map((t=>t.name));!function(t,e){const{environment:n,release:r,dist:s,maxValueLength:i=250}=e;t.environment=t.environment||n||Ue,!t.release&&r&&(t.release=r);!t.dist&&s&&(t.dist=s);t.message&&(t.message=G(t.message,i));const o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=G(o.value,i));const c=t.request;c&&c.url&&(c.url=G(c.url,i))}(u,t),function(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}(u,l),i&&i.emit("applyFrameMetadata",e),void 0===e.type&&function(t,e){const r=function(t){const e=n._sentryDebugIds;if(!e)return{};const r=Object.keys(e);return Fn&&r.length===Nn||(Nn=r.length,Fn=r.reduce(((n,r)=>{Ln||(Ln={});const s=Ln[r];if(s)n[s[0]]=s[1];else{const s=t(r);for(let t=s.length-1;t>=0;t--){const i=s[t],o=i&&i.filename,c=e[r];if(o&&c){n[o]=c,Ln[r]=[o,c];break}}}return n}),{})),Fn}(e);try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{r&&t.filename&&(t.debug_id=r[t.filename])}))}))}catch(t){}}(u,t.stackParser);const f=function(t,e){if(!e)return t;const n=t?t.clone():new Ot;return n.update(e),n}(s,r.captureContext);r.mechanism&&yt(u,r.mechanism);const h=i?i.getEventProcessors():[],d=Bt().getScopeData();if(o){jn(d,o.getScopeData())}if(f){jn(d,f.getScopeData())}const p=[...r.attachments||[],...d.attachments];p.length&&(r.attachments=p),Pn(u,d);return Dn([...h,...d.eventProcessors],u,r).then((t=>(t&&function(t){const e={};try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.debug_id&&(t.abs_path?e[t.abs_path]=t.debug_id:t.filename&&(e[t.filename]=t.debug_id),delete t.debug_id)}))}))}catch(t){}if(0===Object.keys(e).length)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const n=t.debug_meta.images;Object.entries(e).forEach((([t,e])=>{n.push({type:"sourcemap",code_file:t,debug_id:e})}))}(t),"number"==typeof c&&c>0?function(t,e,n){if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:Qe(t.data,e,n)}})))},...t.user&&{user:Qe(t.user,e,n)},...t.contexts&&{contexts:Qe(t.contexts,e,n)},...t.extra&&{extra:Qe(t.extra,e,n)}};t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=Qe(t.contexts.trace.data,e,n)));t.spans&&(r.spans=t.spans.map((t=>({...t,...t.data&&{data:Qe(t.data,e,n)}}))));t.contexts&&t.contexts.flags&&r.contexts&&(r.contexts.flags=Qe(t.contexts.flags,3,n));return r}(t,c,a):t)))}function zn(t){if(t)return function(t){return t instanceof Ot||"function"==typeof t}(t)||function(t){return Object.keys(t).some((t=>qn.includes(t)))}(t)?{captureContext:t}:t}const qn=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function captureException(t,e){return Pt().captureException(t,zn(e))}function Hn(t,e){return Pt().captureEvent(t,e)}function Wn(t,e){jt().setContext(t,e)}function Jn(t){jt().setExtras(t)}function Kn(t,e){jt().setExtra(t,e)}function Yn(t){jt().setTags(t)}function Gn(t,e){jt().setTag(t,e)}function Xn(t){jt().setUser(t)}function Vn(){return jt().lastEventId()}function Qn(t){jt().addEventProcessor(t)}function Zn(t){const e=zt(),r=jt(),s=Pt(),{release:i,environment:o=Ue}=e&&e.getOptions()||{},{userAgent:c}=n.navigator||{},a=St({release:i,environment:o,user:s.getUser()||r.getUser(),...c&&{userAgent:c},...t}),u=r.getSession();return u&&"ok"===u.status&&_t(u,{status:"exited"}),tr(),r.setSession(a),s.setSession(a),a}function tr(){const t=jt(),e=Pt(),n=e.getSession()||t.getSession();n&&function(t,e){let n={};"ok"===t.status&&(n={status:"exited"}),_t(t,n)}(n),er(),t.setSession(),e.setSession()}function er(){const t=jt(),e=Pt(),n=zt(),r=e.getSession()||t.getSession();r&&n&&n.captureSession(r)}function nr(t=!1){t?tr():er()}const rr="7";function sr(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function ir(t,e,n){return e||`${function(t){return`${sr(t)}${t.projectId}/envelope/`}(t)}?${function(t,e){const n={sentry_version:rr};return t.publicKey&&(n.sentry_key=t.publicKey),e&&(n.sentry_client=`${e.name}/${e.version}`),new URLSearchParams(n).toString()}(t,n)}`}const or=[];function cr(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;if(e.forEach((t=>{t.isDefaultInstance=!0})),Array.isArray(n))r=[...e,...n];else if("function"==typeof n){const t=n(e);r=Array.isArray(t)?t:[t]}else r=e;const s=function(t){const e={};return t.forEach((t=>{const{name:n}=t,r=e[n];r&&!r.isDefaultInstance&&t.isDefaultInstance||(e[n]=t)})),Object.values(e)}(r),i=s.findIndex((t=>"Debug"===t.name));if(i>-1){const[t]=s.splice(i,1);s.push(t)}return s}function ar(t,e){for(const n of e)n&&n.afterAllSetup&&n.afterAllSetup(t)}function ur(t,e,n){if(!n[e.name]){if(n[e.name]=e,-1===or.indexOf(e.name)&&"function"==typeof e.setupOnce&&(e.setupOnce(),or.push(e.name)),e.setup&&"function"==typeof e.setup&&e.setup(t),"function"==typeof e.preprocessEvent){const n=e.preprocessEvent.bind(e);t.on("preprocessEvent",((e,r)=>n(e,r,t)))}if("function"==typeof e.processEvent){const n=e.processEvent.bind(e),r=Object.assign(((e,r)=>n(e,r,t)),{id:e.name});t.addEventProcessor(r)}}}class lr extends Error{constructor(t,e="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=e}}class fr{constructor(t){if(this.ct=t,this._integrations={},this.ut=0,this.lt={},this.ft={},this.S=[],t.dsn&&(this.ht=Ve(t.dsn)),this.ht){const e=ir(this.ht,t.tunnel,t._metadata?t._metadata.sdk:void 0);this.dt=t.transport({tunnel:this.ct.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:e})}const e=["enableTracing","tracesSampleRate","tracesSampler"].find((e=>e in t&&null==t[e]));e&&o((()=>{console.warn(`[Sentry] Deprecation warning: \`${e}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)}))}captureException(t,e,n){const r=ht();if(gt(t))return r;const s={event_id:r,...e};return this.yt(this.eventFromException(t,s).then((t=>this.gt(t,s,n)))),s.event_id}captureMessage(t,e,n,r){const s={event_id:ht(),...n},i=N(t)?t:String(t),o=F(t)?this.eventFromMessage(i,e,s):this.eventFromException(t,s);return this.yt(o.then((t=>this.gt(t,s,r)))),s.event_id}captureEvent(t,e,n){const r=ht();if(e&&e.originalException&&gt(e.originalException))return r;const s={event_id:r,...e},i=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this.yt(this.gt(t,s,i||n)),s.event_id}captureSession(t){"string"!=typeof t.release||(this.sendSession(t),_t(t,{init:!1}))}getDsn(){return this.ht}getOptions(){return this.ct}getSdkMetadata(){return this.ct._metadata}getTransport(){return this.dt}flush(t){const e=this.dt;return e?(this.emit("flush"),this.vt(t).then((n=>e.flush(t).then((t=>n&&t))))):bt(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,this.emit("close"),t)))}getEventProcessors(){return this.S}addEventProcessor(t){this.S.push(t)}init(){(this.bt()||this.ct.integrations.some((({name:t})=>t.startsWith("Spotlight"))))&&this.wt()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const e=this._integrations[t.name];ur(this,t,this._integrations),e||ar(this,[t])}sendEvent(t,e={}){this.emit("beforeSendEvent",t,e);let n=function(t,e,n,r){const s=fn(n),i=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,e){e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]])}(t,n&&n.sdk);const o=hn(t,s,r,e);return delete t.sdkProcessingMetadata,en(o,[[{type:i},t]])}(t,this.ht,this.ct._metadata,this.ct.tunnel);for(const t of e.attachments||[])n=nn(n,an(t));const r=this.sendEnvelope(n);r&&r.then((e=>this.emit("afterSendEvent",t,e)),null)}sendSession(t){const e=function(t,e,n,r){const s=fn(n);return en({sent_at:(new Date).toISOString(),...s&&{sdk:s},...!!r&&e&&{dsn:Ge(e)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()]])}(t,this.ht,this.ct._metadata,this.ct.tunnel);this.sendEnvelope(e)}recordDroppedEvent(t,e,n){if(this.ct.sendClientReports){const r="number"==typeof n?n:1,s=`${t}:${e}`;this.lt[s]=(this.lt[s]||0)+r}}on(t,e){const n=this.ft[t]=this.ft[t]||[];return n.push(e),()=>{const t=n.indexOf(e);t>-1&&n.splice(t,1)}}emit(t,...e){const n=this.ft[t];n&&n.forEach((t=>t(...e)))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this.bt()&&this.dt?this.dt.send(t).then(null,(t=>t)):bt({})}wt(){const{integrations:t}=this.ct;this._integrations=function(t,e){const n={};return e.forEach((e=>{e&&ur(t,e,n)})),n}(this,t),ar(this,t)}kt(t,e){let n="fatal"===e.level,r=!1;const s=e.exception&&e.exception.values;if(s){r=!0;for(const t of s){const e=t.mechanism;if(e&&!1===e.handled){n=!0;break}}}const i="ok"===t.status;(i&&0===t.errors||i&&n)&&(_t(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}vt(t){return new kt((e=>{let n=0;const r=setInterval((()=>{0==this.ut?(clearInterval(r),e(!0)):(n+=1,t&&n>=t&&(clearInterval(r),e(!1)))}),1)}))}bt(){return!1!==this.getOptions().enabled&&void 0!==this.dt}St(t,e,n=Pt(),r=jt()){const s=this.getOptions(),i=Object.keys(this._integrations);return!e.integrations&&i.length>0&&(e.integrations=i),this.emit("preprocessEvent",t,e),t.type||r.setLastEventId(t.event_id||e.event_id),Un(s,t,e,n,this,r).then((t=>{if(null===t)return t;t.contexts={trace:qt(n),...t.contexts};const e=We(this,n);return t.sdkProcessingMetadata={dynamicSamplingContext:e,...t.sdkProcessingMetadata},t}))}gt(t,e={},n){return this._t(t,e,n).then((t=>t.event_id),(t=>{}))}_t(t,e,n){const r=this.getOptions(),{sampleRate:s}=r,i=dr(t),o=hr(t),c=t.type||"error",a=`before send for type \`${c}\``,u=void 0===s?void 0:Ke(s);if(o&&"number"==typeof u&&Math.random()>u)return this.recordDroppedEvent("sample_rate","error",t),wt(new lr(`Discarding event because it's not included in the random sample (sampling rate = ${s})`,"log"));const l="replay_event"===c?"replay":c,f=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this.St(t,e,n,f).then((n=>{if(null===n)throw this.recordDroppedEvent("event_processor",l,t),new lr("An event processor returned `null`, will not send event.","log");if(e.data&&!0===e.data.__sentry__)return n;const s=function(t,e,n,r){const{beforeSend:s,beforeSendTransaction:i,beforeSendSpan:o}=e;if(hr(n)&&s)return s(n,r);if(dr(n)){if(n.spans&&o){const e=[];for(const r of n.spans){const n=o(r);n?e.push(n):(Oe(),t.recordDroppedEvent("before_send","span"))}n.spans=e}if(i){if(n.spans){const t=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:t}}return i(n,r)}}return n}(this,r,n,e);return function(t,e){const n=`${e} must return \`null\` or a valid event.`;if(B(t))return t.then((t=>{if(!P(t)&&null!==t)throw new lr(n);return t}),(t=>{throw new lr(`${e} rejected with ${t}`)}));if(!P(t)&&null!==t)throw new lr(n);return t}(s,a)})).then((r=>{if(null===r){if(this.recordDroppedEvent("before_send",l,t),i){const e=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",e)}throw new lr(`${a} returned \`null\`, will not send event.`,"log")}const s=n&&n.getSession();if(!i&&s&&this.kt(s,r),i){const t=(r.sdkProcessingMetadata&&r.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(r.spans?r.spans.length:0);t>0&&this.recordDroppedEvent("before_send","span",t)}const o=r.transaction_info;if(i&&o&&r.transaction!==t.transaction){const t="custom";r.transaction_info={...o,source:t}}return this.sendEvent(r,e),r})).then(null,(t=>{if(t instanceof lr)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new lr(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}yt(t){this.ut++,t.then((t=>(this.ut--,t)),(t=>(this.ut--,t)))}Tt(){const t=this.lt;return this.lt={},Object.entries(t).map((([t,e])=>{const[n,r]=t.split(":");return{reason:n,category:r,quantity:e}}))}It(){const t=this.Tt();if(0===t.length)return;if(!this.ht)return;const e=(n=t,en((r=this.ct.tunnel&&Ge(this.ht))?{dsn:r}:{},[[{type:"client_report"},{timestamp:ut(),discarded_events:n}]]));var n,r;this.sendEnvelope(e)}}function hr(t){return void 0===t.type}function dr(t){return"transaction"===t.type}function pr(t){Pt().setClient(t)}function mr(t){const e=[];function n(t){return e.splice(e.indexOf(t),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(r){if(!(void 0===t||e.length<t))return wt(new lr("Not adding Promise because buffer limit was reached."));const s=r();return-1===e.indexOf(s)&&e.push(s),s.then((()=>n(s))).then(null,(()=>n(s).then(null,(()=>{})))),s},drain:function(t){return new kt(((n,r)=>{let s=e.length;if(!s)return n(!0);const i=setTimeout((()=>{t&&t>0&&n(!1)}),t);e.forEach((t=>{bt(t).then((()=>{--s||(clearTimeout(i),n(!0))}),r)}))}))}}}const yr=6e4;function gr(t,e,n=Date.now()){return function(t,e){return t[e]||t.all||0}(t,e)>n}function vr(t,{statusCode:e,headers:n},r=Date.now()){const s={...t},i=n&&n["x-sentry-rate-limits"],o=n&&n["retry-after"];if(i)for(const t of i.trim().split(",")){const[e,n,,,i]=t.split(":",5),o=parseInt(e,10),c=1e3*(isNaN(o)?60:o);if(n)for(const t of n.split(";"))"metric_bucket"===t&&i&&!i.split(";").includes("custom")||(s[t]=r+c);else s.all=r+c}else o?s.all=r+function(t,e=Date.now()){const n=parseInt(`${t}`,10);if(!isNaN(n))return 1e3*n;const r=Date.parse(`${t}`);return isNaN(r)?yr:r-e}(o,r):429===e&&(s.all=r+6e4);return s}const br=64;function wr(t,e,n=mr(t.bufferSize||br)){let r={};return{send:function(s){const i=[];if(rn(s,((e,n)=>{const s=ln(n);if(gr(r,s)){const r=kr(e,n);t.recordDroppedEvent("ratelimit_backoff",s,r)}else i.push(e)})),0===i.length)return bt({});const o=en(s[0],i),c=e=>{rn(o,((n,r)=>{const s=kr(n,r);t.recordDroppedEvent(e,ln(r),s)}))};return n.add((()=>e({body:on(o)}).then((t=>(r=vr(r,t),t)),(t=>{throw c("network_error"),t})))).then((t=>t),(t=>{if(t instanceof lr)return c("queue_overflow"),bt({});throw t}))},flush:t=>n.drain(t)}}function kr(t,e){if("event"===e||"transaction"===e)return Array.isArray(t)?t[1]:void 0}function Sr(t,e){const n=e&&e.getDsn(),r=e&&e.getOptions().tunnel;return function(t,e){return!!e&&t.includes(e.host)}(t,n)||function(t,e){if(!e)return!1;return _r(t)===_r(e)}(t,r)}function _r(t){return"/"===t[t.length-1]?t.slice(0,-1):t}function Tr(t={}){const e=zt();if(!function(){const t=zt();return!!t&&!1!==t.getOptions().enabled&&!!t.getTransport()}()||!e)return{};const n=Ft(C());if(n.getTraceData)return n.getTraceData(t);const r=Pt(),s=t.span||Ae(),i=s?ve(s):function(t){const{traceId:e,sampled:n,spanId:r}=t.getPropagationContext();return de(e,r,n)}(r),o=ue(s?Je(s):We(e,r));return fe.test(i)?{"sentry-trace":i,baggage:o}:(c.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const Ir=100;function Er(t,e){const n=zt(),r=jt();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:i=Ir}=n.getOptions();if(i<=0)return;const c={timestamp:ut(),...t},a=s?o((()=>s(c,e))):c;null!==a&&(n.emit&&n.emit("beforeAddBreadcrumb",a,e),r.addBreadcrumb(a,i))}let xr;const Cr=new WeakMap,Ar=()=>({name:"FunctionToString",setupOnce(){xr=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=nt(this),n=Cr.has(zt())&&void 0!==e?e:this;return xr.apply(n,t)}}catch(t){}},setup(t){Cr.set(t,!0)}}),Rr=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/],Or=(t={})=>({name:"InboundFilters",processEvent(e,n,r){const s=r.getOptions(),i=function(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:Rr],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(t,s);return function(t,e){if(e.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(t){}return!1}(t))return!0;if(function(t,e){if(t.type||!e||!e.length)return!1;return function(t){const e=[];t.message&&e.push(t.message);let n;try{n=t.exception.values[t.exception.values.length-1]}catch(t){}n&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`));return e}(t).some((t=>Q(t,e)))}(t,e.ignoreErrors))return!0;if(function(t){if(t.type)return!1;if(!t.exception||!t.exception.values||0===t.exception.values.length)return!1;return!t.message&&!t.exception.values.some((t=>t.stacktrace||t.type&&"Error"!==t.type||t.value))}(t))return!0;if(function(t,e){if("transaction"!==t.type||!e||!e.length)return!1;const n=t.transaction;return!!n&&Q(n,e)}(t,e.ignoreTransactions))return!0;if(function(t,e){if(!e||!e.length)return!1;const n=Mr(t);return!!n&&Q(n,e)}(t,e.denyUrls))return!0;if(!function(t,e){if(!e||!e.length)return!0;const n=Mr(t);return!n||Q(n,e)}(t,e.allowUrls))return!0;return!1}(e,i)?null:e}});function Mr(t){try{let e;try{e=t.exception.values[0].stacktrace.frames}catch(t){}return e?function(t=[]){for(let e=t.length-1;e>=0;e--){const n=t[e];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(e):null}catch(t){return null}}function $r(t,e,n=250,r,s,i,o){if(!(i.exception&&i.exception.values&&o&&U(o.originalException,Error)))return;const c=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;var a,u;c&&(i.exception.values=(a=Dr(t,e,s,o.originalException,r,i.exception.values,c,0),u=n,a.map((t=>(t.value&&(t.value=G(t.value,u)),t)))))}function Dr(t,e,n,r,s,i,o,c){if(i.length>=n+1)return i;let a=[...i];if(U(r[s],Error)){Lr(o,c);const i=t(e,r[s]),u=a.length;Nr(i,s,u,c),a=Dr(t,e,n,r[s],s,[i,...a],i,u)}return Array.isArray(r.errors)&&r.errors.forEach(((r,i)=>{if(U(r,Error)){Lr(o,c);const u=t(e,r),l=a.length;Nr(u,`errors[${i}]`,l,c),a=Dr(t,e,n,r,s,[u,...a],u,l)}})),a}function Lr(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,..."AggregateError"===t.type&&{is_exception_group:!0},exception_id:e}}function Nr(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function Fr(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function Pr(){"console"in n&&s.forEach((function(t){t in n.console&&Z(n.console,t,(function(e){return i[t]=e,function(...e){k("console",{args:e,level:t});const r=i[t];r&&r.apply(n.console,e)}}))}))}function jr(t){return"warn"===t?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log"}const Br=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(t,e){if(!e)return!1;if(function(t,e){const n=t.message,r=e.message;if(!n&&!r)return!1;if(n&&!r||!n&&r)return!1;if(n!==r)return!1;if(!zr(t,e))return!1;if(!Ur(t,e))return!1;return!0}(t,e))return!0;if(function(t,e){const n=qr(e),r=qr(t);if(!n||!r)return!1;if(n.type!==r.type||n.value!==r.value)return!1;if(!zr(t,e))return!1;if(!Ur(t,e))return!1;return!0}(t,e))return!0;return!1}(e,t))return null}catch(t){}return t=e}}};function Ur(t,e){let n=y(t),r=y(e);if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;if(r.length!==n.length)return!1;for(let t=0;t<r.length;t++){const e=r[t],s=n[t];if(e.filename!==s.filename||e.lineno!==s.lineno||e.colno!==s.colno||e.function!==s.function)return!1}return!0}function zr(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return!(n.join("")!==r.join(""))}catch(t){return!1}}function qr(t){return t.exception&&t.exception.values&&t.exception.values[0]}const Hr="d";function Wr(t,e){const n=r("globalMetricsAggregators",(()=>new WeakMap)),s=n.get(t);if(s)return s;const i=new e(t);return t.on("flush",(()=>i.flush())),t.on("close",(()=>i.close())),n.set(t,i),i}function Jr(t,e,n,r,s={}){const i=s.client||zt();if(!i)return;const o=Ae(),c=o?Ce(o):void 0,a=c&&ke(c).description,{unit:u,tags:l,timestamp:f}=s,{release:h,environment:d}=i.getOptions(),p={};h&&(p.release=h),d&&(p.environment=d),a&&(p.transaction=a);Wr(i,t).add(e,n,r,u,{...p,...l},f)}function Kr(t,e,n,r){Jr(t,Hr,e,Gr(n),r)}const Yr={increment:function(t,e,n=1,r){Jr(t,"c",e,Gr(n),r)},distribution:Kr,set:function(t,e,n,r){Jr(t,"s",e,n,r)},gauge:function(t,e,n,r){Jr(t,"g",e,Gr(n),r)},timing:function(t,e,n,r="second",s){if("function"==typeof n){const r=lt();return bn({op:"metrics.timing",name:e,startTime:r,onlyIfParent:!0},(i=>Be((()=>n()),(()=>{}),(()=>{const n=lt();Kr(t,e,n-r,{...s,unit:"second"}),i.end(n)}))))}Kr(t,e,n,{...s,unit:r})},getMetricsAggregatorForClient:Wr};function Gr(t){return"string"==typeof t?parseInt(t):t}function Xr(t){return t.replace(/[^\w\-./]+/gi,"")}const Vr=[["\n","\\n"],["\r","\\r"],["\t","\\t"],["\\","\\\\"],["|","\\u{7c}"],[",","\\u{2c}"]];function Qr(t){return[...t].reduce(((t,e)=>t+function(t){for(const[e,n]of Vr)if(t===e)return n;return t}(e)),"")}function Zr(t,e){c.log(`Flushing aggregated metrics, number of metrics: ${e.length}`);const n=function(t,e,n,r){const s={sent_at:(new Date).toISOString()};n&&n.sdk&&(s.sdk={name:n.sdk.name,version:n.sdk.version});r&&e&&(s.dsn=Ge(e));const i=function(t){const e=function(t){let e="";for(const n of t){const t=Object.entries(n.tags),r=t.length>0?`|#${t.map((([t,e])=>`${t}:${e}`)).join(",")}`:"";e+=`${n.name}@${n.unit}:${n.metric}|${n.metricType}${r}|T${n.timestamp}\n`}return e}(t);return[{type:"statsd",length:e.length},e]}(t);return en(s,[i])}(e,t.getDsn(),t.getSdkMetadata(),t.getOptions().tunnel);t.sendEnvelope(n)}const ts={c:class{constructor(t){this.m=t}get weight(){return 1}add(t){this.m+=t}toString(){return`${this.m}`}},g:class{constructor(t){this.Et=t,this.xt=t,this.Ct=t,this.At=t,this.Rt=1}get weight(){return 5}add(t){this.Et=t,t<this.xt&&(this.xt=t),t>this.Ct&&(this.Ct=t),this.At+=t,this.Rt++}toString(){return`${this.Et}:${this.xt}:${this.Ct}:${this.At}:${this.Rt}`}},[Hr]:class{constructor(t){this.m=[t]}get weight(){return this.m.length}add(t){this.m.push(t)}toString(){return this.m.join(":")}},s:class{constructor(t){this.first=t,this.m=new Set([t])}get weight(){return this.m.size}add(t){this.m.add(t)}toString(){return Array.from(this.m).map((t=>"string"==typeof t?function(t){let e=0;for(let n=0;n<t.length;n++)e=(e<<5)-e+t.charCodeAt(n),e&=e;return e>>>0}(t):t)).join(":")}}};class es{constructor(t){this.j=t,this.Ot=new Map,this.Mt=setInterval((()=>this.flush()),5e3)}add(t,e,n,r="none",s={},i=lt()){const o=Math.floor(i),c=e.replace(/[^\w\-.]+/gi,"_");const a=function(t){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[Xr(n)]=Qr(String(t[n])));return e}(s),u=function(t){return t.replace(/[^\w]+/gi,"_")}(r),l=function(t,e,n,r){return`${t}${e}${n}${Object.entries(ot(r)).sort(((t,e)=>t[0].localeCompare(e[0])))}`}(t,c,u,a);let f=this.Ot.get(l);const h=f&&"s"===t?f.metric.weight:0;f?(f.metric.add(n),f.timestamp<o&&(f.timestamp=o)):(f={metric:new ts[t](n),timestamp:o,metricType:t,name:c,unit:u,tags:a},this.Ot.set(l,f));Re(t,c,"string"==typeof n?f.metric.weight-h:n,u,s,l)}flush(){if(0===this.Ot.size)return;const t=Array.from(this.Ot.values());Zr(this.j,t),this.Ot.clear()}close(){clearInterval(this.Mt),this.flush()}}function ns(t,e,n,r,s="auto.http.browser"){if(!t.fetchData)return;const i=Pe()&&e(t.fetchData.url);if(t.endTimestamp&&i){const e=t.fetchData.__span;if(!e)return;const n=r[e];return void(n&&(!function(t,e){if(e.response){se(t,e.response.status);const n=e.response&&e.response.headers&&e.response.headers.get("content-length");if(n){const e=parseInt(n);e>0&&t.setAttribute("http.response_content_length",e)}}else e.error&&t.setStatus({code:re,message:"internal_error"});t.end()}(n,t),delete r[e]))}const{method:o,url:c}=t.fetchData,a=function(t){try{return new URL(t).href}catch(t){return}}(c),u=a?Fr(a).host:void 0,l=!!Ae(),f=i&&l?wn({name:`${o} ${c}`,attributes:{url:c,type:"fetch","http.method":o,"http.url":a,"server.address":u,[Gt]:s,[Yt]:"http.client"}}):new je;if(t.fetchData.__span=f.spanContext().spanId,r[f.spanContext().spanId]=f,n(t.fetchData.url)){const e=t.args[0],n=t.args[1]||{},r=function(t,e,n){const r=Tr({span:n}),s=r["sentry-trace"],i=r.baggage;if(!s)return;const o=e.headers||(function(t){return"undefined"!=typeof Request&&U(t,Request)}(t)?t.headers:void 0);if(o){if(function(t){return"undefined"!=typeof Headers&&U(t,Headers)}(o)){const t=new Headers(o);if(t.set("sentry-trace",s),i){const e=t.get("baggage");if(e){const n=rs(e);t.set("baggage",n?`${n},${i}`:i)}else t.set("baggage",i)}return t}if(Array.isArray(o)){const t=[...o.filter((t=>!(Array.isArray(t)&&"sentry-trace"===t[0]))).map((t=>{if(Array.isArray(t)&&"baggage"===t[0]&&"string"==typeof t[1]){const[e,n,...r]=t;return[e,rs(n),...r]}return t})),["sentry-trace",s]];return i&&t.push(["baggage",i]),t}{const t="baggage"in o?o.baggage:void 0;let e=[];return Array.isArray(t)?e=t.map((t=>"string"==typeof t?rs(t):t)).filter((t=>""===t)):t&&e.push(rs(t)),i&&e.push(i),{...o,"sentry-trace":s,baggage:e.length>0?e.join(","):void 0}}}return{...r}}(e,n,Pe()&&l?f:void 0);r&&(t.args[1]=n,n.headers=r)}return f}function rs(t){return t.split(",").filter((t=>!t.split("=")[0].startsWith(ie))).join(",")}const ss=function(){return{bindClient(t){Pt().setClient(t)},withScope:Ut,getClient:()=>zt(),getScope:Pt,getIsolationScope:jt,captureException:(t,e)=>Pt().captureException(t,e),captureMessage:(t,e,n)=>Pt().captureMessage(t,e,n),captureEvent:Hn,addBreadcrumb:Er,setUser:Xn,setTags:Yn,setTag:Gn,setExtra:Kn,setExtras:Jn,setContext:Wn,getIntegration(t){const e=zt();return e&&e.getIntegrationByName(t.id)||null},startSession:Zn,endSession:tr,captureSession(t){if(t)return tr();!function(){const t=Pt(),e=zt(),n=t.getSession();e&&n&&e.captureSession(n)}()}}};function is(t){return void 0===t?void 0:t>=400&&t<500?"warning":t>=500?"error":void 0}const os=n;function cs(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function as(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in os))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}())return!1;if(cs(os.fetch))return!0;let t=!1;const e=os.document;if(e&&"function"==typeof e.createElement)try{const n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=cs(n.contentWindow.fetch)),e.head.removeChild(n)}catch(t){}return t}function us(t,e){const n="fetch";b(n,t),w(n,(()=>ls(void 0,e)))}function ls(t,e=!1){e&&!as()||Z(n,"fetch",(function(e){return function(...r){const s=new Error,{method:i,url:o}=function(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){const[e,n]=t;return{url:ds(e),method:hs(n,"method")?String(n.method).toUpperCase():"GET"}}const e=t[0];return{url:ds(e),method:hs(e,"method")?String(e.method).toUpperCase():"GET"}}(r),c={args:r,fetchData:{method:i,url:o},startTimestamp:1e3*lt(),virtualError:s};return t||k("fetch",{...c}),e.apply(n,r).then((async e=>(t?t(e):k("fetch",{...c,endTimestamp:1e3*lt(),response:e}),e)),(t=>{throw k("fetch",{...c,endTimestamp:1e3*lt(),error:t}),O(t)&&void 0===t.stack&&(t.stack=s.stack,tt(t,"framesToPop",1)),t}))}}))}function fs(t){let e;try{e=t.clone()}catch(t){return}!async function(t,e){if(t&&t.body){const n=t.body,r=n.getReader(),s=setTimeout((()=>{n.cancel().then(null,(()=>{}))}),9e4);let i=!0;for(;i;){let t;try{t=setTimeout((()=>{n.cancel().then(null,(()=>{}))}),5e3);const{done:s}=await r.read();clearTimeout(t),s&&(e(),i=!1)}catch(t){i=!1}finally{clearTimeout(t)}}clearTimeout(s),r.releaseLock(),n.cancel().then(null,(()=>{}))}}(e,(()=>{k("fetch-body-resolved",{endTimestamp:1e3*lt(),response:t})}))}function hs(t,e){return!!t&&"object"==typeof t&&!!t[e]}function ds(t){return"string"==typeof t?t:t?hs(t,"url")?t.url:t.toString?t.toString():"":""}function ps(){return"undefined"!=typeof window&&!0}const ms=n;function ys(t){let e,n=t[0],r=1;for(;r<t.length;){const s=t[r],i=t[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(e=n,n=i(n)):"call"!==s&&"optionalCall"!==s||(n=i(((...t)=>n.call(e,...t))),e=void 0)}return n}const gs=n;let vs=0;function bs(){return vs>0}function ws(t,e={}){if(!function(t){return"function"==typeof t}(t))return t;try{const e=t.__sentry_wrapped__;if(e)return"function"==typeof e?e:t;if(nt(t))return t}catch(e){return t}const sentryWrapped=function(...n){try{const r=n.map((t=>ws(t,e)));return t.apply(this,r)}catch(t){throw vs++,setTimeout((()=>{vs--})),Ut((r=>{r.addEventProcessor((t=>(e.mechanism&&(mt(t,void 0),yt(t,e.mechanism)),t.extra={...t.extra,arguments:n},t))),captureException(t)})),t}};try{for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(sentryWrapped[e]=t[e])}catch(t){}et(sentryWrapped,t),tt(t,"__sentry_wrapped__",sentryWrapped);try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:()=>t.name})}catch(t){}return sentryWrapped}function ks(t,e){const n=Ts(t,e),r={type:xs(e),value:Cs(e)};return n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function Ss(t,e,n,r){const s=zt(),i=s&&s.getOptions().normalizeDepth,o=function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const n=t[e];if(n instanceof Error)return n}return}(e),c={__serialized__:Ze(e,i)};if(o)return{exception:{values:[ks(t,o)]},extra:c};const a={exception:{values:[{type:j(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:$s(e,{isUnhandledRejection:r})}]},extra:c};if(n){const e=Ts(t,n);e.length&&(a.exception.values[0].stacktrace={frames:e})}return a}function _s(t,e){return{exception:{values:[ks(t,e)]}}}function Ts(t,e){const n=e.stacktrace||e.stack||"",r=function(t){if(t&&Is.test(t.message))return 1;return 0}(e),s=function(t){if("number"==typeof t.framesToPop)return t.framesToPop;return 0}(e);try{return t(n,r,s)}catch(t){}return[]}const Is=/Minified React error #\d+;/i;function Es(t){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&t instanceof WebAssembly.Exception}function xs(t){const e=t&&t.name;if(!e&&Es(t)){return t.message&&Array.isArray(t.message)&&2==t.message.length?t.message[0]:"WebAssembly.Exception"}return e}function Cs(t){const e=t&&t.message;return e?e.error&&"string"==typeof e.error.message?e.error.message:Es(t)&&Array.isArray(t.message)&&2==t.message.length?t.message[1]:e:"No error message"}function As(t,e,n,r){const s=Os(t,e,n&&n.syntheticException||void 0,r);return yt(s),s.level="error",n&&n.event_id&&(s.event_id=n.event_id),bt(s)}function Rs(t,e,n="info",r,s){const i=Ms(t,e,r&&r.syntheticException||void 0,s);return i.level=n,r&&r.event_id&&(i.event_id=r.event_id),bt(i)}function Os(t,e,n,r,s){let i;if($(e)&&e.error){return _s(t,e.error)}if(D(e)||M(e,"DOMException")){const s=e;if("stack"in e)i=_s(t,e);else{const e=s.name||(D(s)?"DOMError":"DOMException"),o=s.message?`${e}: ${s.message}`:e;i=Ms(t,o,n,r),mt(i,o)}return"code"in s&&(i.tags={...i.tags,"DOMException.code":`${s.code}`}),i}if(O(e))return _s(t,e);if(P(e)||j(e)){return i=Ss(t,e,n,s),yt(i,{synthetic:!0}),i}return i=Ms(t,e,n,r),mt(i,`${e}`),yt(i,{synthetic:!0}),i}function Ms(t,e,n,r){const s={};if(r&&n){const r=Ts(t,n);r.length&&(s.exception={values:[{value:e,stacktrace:{frames:r}}]}),yt(s,{synthetic:!0})}if(N(e)){const{__sentry_template_string__:t,__sentry_template_values__:n}=e;return s.logentry={message:t,params:n},s}return s.message=e,s}function $s(t,{isUnhandledRejection:e}){const n=function(t,e=40){const n=Object.keys(rt(t));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=e)return G(r,e);for(let t=n.length;t>0;t--){const r=n.slice(0,t).join(", ");if(!(r.length>e))return t===n.length?r:G(r,e)}return""}(t),r=e?"promise rejection":"exception";if($(t))return`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``;if(j(t)){return`Event \`${function(t){try{const e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch(t){}}(t)}\` (type=${t.type}) captured as ${r}`}return`Object captured as ${r} with keys: ${n}`}function Ds(t,{metadata:e,tunnel:n,dsn:r}){const s={event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&e.sdk&&{sdk:{name:e.sdk.name,version:e.sdk.version}},...!!n&&!!r&&{dsn:Ge(r)}},i=function(t){return[{type:"user_report"},t]}(t);return en(s,[i])}class Ls extends fr{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t};!function(t,n,r=[n],s="npm"){const i=t._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${n}`,packages:r.map((t=>({name:`${s}:@sentry/${t}`,version:e}))),version:e}),t._metadata=i}(n,"browser",["browser"],gs.SENTRY_SDK_SOURCE||"cdn"),super(n),n.sendClientReports&&gs.document&&gs.document.addEventListener("visibilitychange",(()=>{"hidden"===gs.document.visibilityState&&this.It()}))}eventFromException(t,e){return As(this.ct.stackParser,t,e,this.ct.attachStacktrace)}eventFromMessage(t,e="info",n){return Rs(this.ct.stackParser,t,e,n,this.ct.attachStacktrace)}captureUserFeedback(t){if(!this.bt())return;const e=Ds(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(e)}St(t,e,n){return t.platform=t.platform||"javascript",super.St(t,e,n)}}const Ns=(t,e,n,r)=>{let s,i;return o=>{e.value>=0&&(o||r)&&(i=e.value-(s||0),(i||void 0===s)&&(s=e.value,e.delta=i,e.rating=((t,e)=>t>e[1]?"poor":t>e[0]?"needs-improvement":"good")(e.value,n),t(e)))}},Fs=n,Ps=(t=!0)=>{const e=Fs.performance&&Fs.performance.getEntriesByType&&Fs.performance.getEntriesByType("navigation")[0];if(!t||e&&e.responseStart>0&&e.responseStart<performance.now())return e},js=()=>{const t=Ps();return t&&t.activationStart||0},Bs=(t,e)=>{const n=Ps();let r="navigate";n&&(Fs.document&&Fs.document.prerendering||js()>0?r="prerender":Fs.document&&Fs.document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-")));return{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},Us=(t,e,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver((t=>{Promise.resolve().then((()=>{e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},zs=t=>{const e=e=>{("pagehide"===e.type||Fs.document&&"hidden"===Fs.document.visibilityState)&&t(e)};Fs.document&&(addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0))},qs=t=>{let e=!1;return()=>{e||(t(),e=!0)}};let Hs=-1;const Ws=t=>{"hidden"===Fs.document.visibilityState&&Hs>-1&&(Hs="visibilitychange"===t.type?t.timeStamp:0,Js())},Js=()=>{removeEventListener("visibilitychange",Ws,!0),removeEventListener("prerenderingchange",Ws,!0)},Ks=()=>(Fs.document&&Hs<0&&(Hs="hidden"!==Fs.document.visibilityState||Fs.document.prerendering?1/0:0,addEventListener("visibilitychange",Ws,!0),addEventListener("prerenderingchange",Ws,!0)),{get firstHiddenTime(){return Hs}}),Ys=t=>{Fs.document&&Fs.document.prerendering?addEventListener("prerenderingchange",(()=>t()),!0):t()},Gs=[1800,3e3],Xs=[.1,.25],Vs=(t,e={})=>{((t,e={})=>{Ys((()=>{const n=Ks(),r=Bs("FCP");let s;const i=Us("paint",(t=>{t.forEach((t=>{"first-contentful-paint"===t.name&&(i.disconnect(),t.startTime<n.firstHiddenTime&&(r.value=Math.max(t.startTime-js(),0),r.entries.push(t),s(!0)))}))}));i&&(s=Ns(t,r,Gs,e.reportAllChanges))}))})(qs((()=>{const n=Bs("CLS",0);let r,s=0,i=[];const o=t=>{t.forEach((t=>{if(!t.hadRecentInput){const e=i[0],n=i[i.length-1];s&&e&&n&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(s+=t.value,i.push(t)):(s=t.value,i=[t])}})),s>n.value&&(n.value=s,n.entries=i,r())},c=Us("layout-shift",o);c&&(r=Ns(t,n,Xs,e.reportAllChanges),zs((()=>{o(c.takeRecords()),r(!0)})),setTimeout(r,0))})))},Qs=[100,300],Zs=(t,e={})=>{Ys((()=>{const n=Ks(),r=Bs("FID");let s;const i=t=>{t.startTime<n.firstHiddenTime&&(r.value=t.processingStart-t.startTime,r.entries.push(t),s(!0))},o=t=>{t.forEach(i)},c=Us("first-input",o);s=Ns(t,r,Qs,e.reportAllChanges),c&&zs(qs((()=>{o(c.takeRecords()),c.disconnect()})))}))};let ti=0,ei=1/0,ni=0;const ri=t=>{t.forEach((t=>{t.interactionId&&(ei=Math.min(ei,t.interactionId),ni=Math.max(ni,t.interactionId),ti=ni?(ni-ei)/7+1:0)}))};let si;const ii=()=>{"interactionCount"in performance||si||(si=Us("event",ri,{type:"event",buffered:!0,durationThreshold:0}))},oi=[],ci=new Map;const ai=()=>(si?ti:performance.interactionCount||0)-0,ui=[],li=t=>{if(ui.forEach((e=>e(t))),!t.interactionId&&"first-input"!==t.entryType)return;const e=oi[oi.length-1],n=ci.get(t.interactionId);if(n||oi.length<10||e&&t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===(n.entries[0]&&n.entries[0].startTime)&&n.entries.push(t);else{const e={id:t.interactionId,latency:t.duration,entries:[t]};ci.set(e.id,e),oi.push(e)}oi.sort(((t,e)=>e.latency-t.latency)),oi.length>10&&oi.splice(10).forEach((t=>ci.delete(t.id)))}},fi=t=>{const e=Fs.requestIdleCallback||Fs.setTimeout;let n=-1;return t=qs(t),Fs.document&&"hidden"===Fs.document.visibilityState?t():(n=e(t),zs(t)),n},hi=[200,500],di=(t,e={})=>{"PerformanceEventTiming"in Fs&&"interactionId"in PerformanceEventTiming.prototype&&Ys((()=>{ii();const n=Bs("INP");let r;const s=t=>{fi((()=>{t.forEach(li);const e=(()=>{const t=Math.min(oi.length-1,Math.floor(ai()/50));return oi[t]})();e&&e.latency!==n.value&&(n.value=e.latency,n.entries=e.entries,r())}))},i=Us("event",s,{durationThreshold:null!=e.durationThreshold?e.durationThreshold:40});r=Ns(t,n,hi,e.reportAllChanges),i&&(i.observe({type:"first-input",buffered:!0}),zs((()=>{s(i.takeRecords()),r(!0)})))}))},pi=[2500,4e3],mi={},yi=(t,e={})=>{Ys((()=>{const n=Ks(),r=Bs("LCP");let s;const i=t=>{e.reportAllChanges||(t=t.slice(-1)),t.forEach((t=>{t.startTime<n.firstHiddenTime&&(r.value=Math.max(t.startTime-js(),0),r.entries=[t],s())}))},o=Us("largest-contentful-paint",i);if(o){s=Ns(t,r,pi,e.reportAllChanges);const n=qs((()=>{mi[r.id]||(i(o.takeRecords()),o.disconnect(),mi[r.id]=!0,s(!0))}));["keydown","click"].forEach((t=>{Fs.document&&addEventListener(t,(()=>fi(n)),{once:!0,capture:!0})})),zs(n)}}))},gi=[800,1800],vi=t=>{Fs.document&&Fs.document.prerendering?Ys((()=>vi(t))):Fs.document&&"complete"!==Fs.document.readyState?addEventListener("load",(()=>vi(t)),!0):setTimeout(t,0)},bi=(t,e={})=>{const n=Bs("TTFB"),r=Ns(t,n,gi,e.reportAllChanges);vi((()=>{const t=Ps();t&&(n.value=Math.max(t.responseStart-js(),0),n.entries=[t],r(!0))}))},wi={},ki={};let Si,_i,Ti,Ii,Ei;function xi(t,e=!1){return Pi("cls",t,$i,Si,e)}function Ci(t,e=!1){return Pi("lcp",t,Li,Ti,e)}function Ai(t){return Pi("fid",t,Di,_i)}function Ri(t){return Pi("inp",t,Fi,Ei)}function Oi(t,e){return ji(t,e),ki[t]||(!function(t){const e={};"event"===t&&(e.durationThreshold=0);Us(t,(e=>{Mi(t,{entries:e})}),e)}(t),ki[t]=!0),Bi(t,e)}function Mi(t,e){const n=wi[t];if(n&&n.length)for(const t of n)try{t(e)}catch(t){}}function $i(){return Vs((t=>{Mi("cls",{metric:t}),Si=t}),{reportAllChanges:!0})}function Di(){return Zs((t=>{Mi("fid",{metric:t}),_i=t}))}function Li(){return yi((t=>{Mi("lcp",{metric:t}),Ti=t}),{reportAllChanges:!0})}function Ni(){return bi((t=>{Mi("ttfb",{metric:t}),Ii=t}))}function Fi(){return di((t=>{Mi("inp",{metric:t}),Ei=t}))}function Pi(t,e,n,r,s=!1){let i;return ji(t,e),ki[t]||(i=n(),ki[t]=!0),r&&e({metric:r}),Bi(t,e,s?i:void 0)}function ji(t,e){wi[t]=wi[t]||[],wi[t].push(e)}function Bi(t,e,n){return()=>{n&&n();const r=wi[t];if(!r)return;const s=r.indexOf(e);-1!==s&&r.splice(s,1)}}function Ui(t){return"number"==typeof t&&isFinite(t)}function zi(t,e,n,{...r}){const s=ke(t).start_timestamp;return s&&s>e&&"function"==typeof t.updateStartTime&&t.updateStartTime(e),kn(t,(()=>{const t=wn({startTime:e,...r});return t&&t.end(n),t}))}function qi(t){const e=zt();if(!e)return;const{name:n,transaction:r,attributes:s,startTime:i}=t,{release:o,environment:c}=e.getOptions(),a=e.getIntegrationByName("Replay"),u=a&&a.getReplayId(),l=Pt(),f=l.getUser(),h=void 0!==f?f.email||f.id||f.ip_address:void 0;let d;try{d=l.getScopeData().contexts.profile.profile_id}catch(t){}return wn({name:n,attributes:{release:o,environment:c,user:h||void 0,profile_id:d||void 0,replay_id:u||void 0,transaction:r,"user_agent.original":Fs.navigator&&Fs.navigator.userAgent,...s},startTime:i,experimental:{standalone:!0}})}function Hi(){return Fs&&Fs.addEventListener&&Fs.performance}function Wi(t){return t/1e3}function Ji(){let t,e,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch(t){return!1}}())return;let r=!1;function s(){r||(r=!0,e&&function(t,e,n){const r=Wi((ft||0)+(e&&e.startTime||0)),s=Pt().getScopeData().transactionName,i=e?W(e.sources[0]&&e.sources[0].node):"Layout shift",o=ot({[Gt]:"auto.http.browser.cls",[Yt]:"ui.webvital.cls",[te]:e&&e.duration||0,"sentry.pageload.span_id":n}),c=qi({name:i,transaction:s,attributes:o,startTime:r});c&&(c.addEvent("cls",{[Vt]:"",[Qt]:t}),c.end(r))}(n,t,e),i())}const i=xi((({metric:e})=>{const r=e.entries[e.entries.length-1];r&&(n=e.value,t=r)}),!0);zs((()=>{s()})),setTimeout((()=>{const t=zt();if(!t)return;const n=t.on("startNavigationSpan",(()=>{s(),n&&n()})),r=Ae(),i=r&&Ce(r),o=i&&ke(i);o&&"pageload"===o.op&&(e=i.spanContext().spanId)}),0)}const Ki=2147483647;let Yi,Gi,Xi=0,Vi={};function Qi({recordClsStandaloneSpans:t}){const e=Hi();if(e&&ft){e.mark&&Fs.performance.mark("sentry-tracing-init");const n=Ai((({metric:t})=>{const e=t.entries[t.entries.length-1];if(!e)return;const n=Wi(ft),r=Wi(e.startTime);Vi.fid={value:t.value,unit:"millisecond"},Vi["mark.fid"]={value:n+r,unit:"second"}})),r=Ci((({metric:t})=>{const e=t.entries[t.entries.length-1];e&&(Vi.lcp={value:t.value,unit:"millisecond"},Yi=e)}),!0),s=Pi("ttfb",(({metric:t})=>{t.entries[t.entries.length-1]&&(Vi.ttfb={value:t.value,unit:"millisecond"})}),Ni,Ii),i=t?Ji():xi((({metric:t})=>{const e=t.entries[t.entries.length-1];e&&(Vi.cls={value:t.value,unit:""},Gi=e)}),!0);return()=>{n(),r(),s(),i&&i()}}return()=>{}}function Zi(t,e){const n=Hi();if(!n||!n.getEntries||!ft)return;const r=Wi(ft),s=n.getEntries(),{op:i,start_timestamp:o}=ke(t);if(s.slice(Xi).forEach((e=>{const n=Wi(e.startTime),s=Wi(Math.max(0,e.duration));if(!("navigation"===i&&o&&r+n<o))switch(e.entryType){case"navigation":!function(t,e,n){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((r=>{to(t,e,r,n)})),to(t,e,"secureConnection",n,"TLS/SSL"),to(t,e,"fetch",n,"cache"),to(t,e,"domainLookup",n,"DNS"),function(t,e,n){const r=n+Wi(e.requestStart),s=n+Wi(e.responseEnd),i=n+Wi(e.responseStart);e.responseEnd&&(zi(t,r,s,{op:"browser.request",name:e.name,attributes:{[Gt]:"auto.ui.browser.metrics"}}),zi(t,i,s,{op:"browser.response",name:e.name,attributes:{[Gt]:"auto.ui.browser.metrics"}}))}(t,e,n)}(t,e,r);break;case"mark":case"paint":case"measure":{!function(t,e,n,r,s){const i=Ps(!1),o=Wi(i?i.requestStart:0),c=s+Math.max(n,o),a=s+n,u=a+r,l={[Gt]:"auto.resource.browser.metrics"};c!==a&&(l["sentry.browser.measure_happened_before_request"]=!0,l["sentry.browser.measure_start_time"]=c);zi(t,c,u,{name:e.name,op:e.entryType,attributes:l})}(t,e,n,s,r);const i=Ks(),o=e.startTime<i.firstHiddenTime;"first-paint"===e.name&&o&&(Vi.fp={value:e.startTime,unit:"millisecond"}),"first-contentful-paint"===e.name&&o&&(Vi.fcp={value:e.startTime,unit:"millisecond"});break}case"resource":!function(t,e,n,r,s,i){if("xmlhttprequest"===e.initiatorType||"fetch"===e.initiatorType)return;const o=Fr(n),c={[Gt]:"auto.resource.browser.metrics"};eo(c,e,"transferSize","http.response_transfer_size"),eo(c,e,"encodedBodySize","http.response_content_length"),eo(c,e,"decodedBodySize","http.decoded_response_content_length");const a=e.deliveryType;null!=a&&(c["http.response_delivery_type"]=a);const u=e.renderBlockingStatus;u&&(c["resource.render_blocking_status"]=u);o.protocol&&(c["url.scheme"]=o.protocol.split(":").pop());o.host&&(c["server.address"]=o.host);c["url.same_origin"]=n.includes(Fs.location.origin);const l=i+r,f=l+s;zi(t,l,f,{name:n.replace(Fs.location.origin,""),op:e.initiatorType?`resource.${e.initiatorType}`:"resource.other",attributes:c})}(t,e,e.name,n,s,r)}})),Xi=Math.max(s.length-1,0),function(t){const e=Fs.navigator;if(!e)return;const n=e.connection;n&&(n.effectiveType&&t.setAttribute("effectiveConnectionType",n.effectiveType),n.type&&t.setAttribute("connectionType",n.type),Ui(n.rtt)&&(Vi["connection.rtt"]={value:n.rtt,unit:"millisecond"}));Ui(e.deviceMemory)&&t.setAttribute("deviceMemory",`${e.deviceMemory} GB`);Ui(e.hardwareConcurrency)&&t.setAttribute("hardwareConcurrency",String(e.hardwareConcurrency))}(t),"pageload"===i){!function(t){const e=Ps(!1);if(!e)return;const{responseStart:n,requestStart:r}=e;r<=n&&(t["ttfb.requestTime"]={value:n-r,unit:"millisecond"})}(Vi);const n=Vi["mark.fid"];n&&Vi.fid&&(zi(t,n.value,n.value+Wi(Vi.fid.value),{name:"first input delay",op:"ui.action",attributes:{[Gt]:"auto.ui.browser.metrics"}}),delete Vi["mark.fid"]),"fcp"in Vi&&e.recordClsOnPageloadSpan||delete Vi.cls,Object.entries(Vi).forEach((([t,e])=>{dn(t,e.value,e.unit)})),t.setAttribute("performance.timeOrigin",r),t.setAttribute("performance.activationStart",js()),function(t){Yi&&(Yi.element&&t.setAttribute("lcp.element",W(Yi.element)),Yi.id&&t.setAttribute("lcp.id",Yi.id),Yi.url&&t.setAttribute("lcp.url",Yi.url.trim().slice(0,200)),null!=Yi.loadTime&&t.setAttribute("lcp.loadTime",Yi.loadTime),null!=Yi.renderTime&&t.setAttribute("lcp.renderTime",Yi.renderTime),t.setAttribute("lcp.size",Yi.size));Gi&&Gi.sources&&Gi.sources.forEach(((e,n)=>t.setAttribute(`cls.source.${n+1}`,W(e.node))))}(t)}Yi=void 0,Gi=void 0,Vi={}}function to(t,e,n,r,s=n){const i=function(t){if("secureConnection"===t)return"connectEnd";if("fetch"===t)return"domainLookupStart";return`${t}End`}(n),o=e[i],c=e[`${n}Start`];c&&o&&zi(t,r+Wi(c),r+Wi(o),{op:`browser.${s}`,name:e.name,attributes:{[Gt]:"auto.ui.browser.metrics"}})}function eo(t,e,n,r){const s=e[n];null!=s&&s<Ki&&(t[r]=s)}const no=1e3;let ro,so,io,oo;function co(t){b("dom",t),w("dom",ao)}function ao(){if(!Fs.document)return;const t=k.bind(null,"dom"),e=uo(t,!0);Fs.document.addEventListener("click",e,!1),Fs.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach((e=>{const n=Fs[e],r=n&&n.prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(Z(r,"addEventListener",(function(e){return function(n,r,s){if("click"===n||"keypress"==n)try{const r=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},i=r[n]=r[n]||{refCount:0};if(!i.handler){const r=uo(t);i.handler=r,e.call(this,n,r,s)}i.refCount++}catch(t){}return e.call(this,n,r,s)}})),Z(r,"removeEventListener",(function(t){return function(e,n,r){if("click"===e||"keypress"==e)try{const n=this.__sentry_instrumentation_handlers__||{},s=n[e];s&&(s.refCount--,s.refCount<=0&&(t.call(this,e,s.handler,r),s.handler=void 0,delete n[e]),0===Object.keys(n).length&&delete this.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,e,n,r)}})))}))}function uo(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(t){try{return t.target}catch(t){return null}}(n);if(function(t,e){return"keypress"===t&&(!e||!e.tagName||"INPUT"!==e.tagName&&"TEXTAREA"!==e.tagName&&!e.isContentEditable)}(n.type,r))return;tt(n,"_sentryCaptured",!0),r&&!r._sentryId&&tt(r,"_sentryId",ht());const s="keypress"===n.type?"input":n.type;if(!function(t){if(t.type!==so)return!1;try{if(!t.target||t.target._sentryId!==io)return!1}catch(t){}return!0}(n)){t({event:n,name:s,global:e}),so=n.type,io=r?r._sentryId:void 0}clearTimeout(ro),ro=Fs.setTimeout((()=>{io=void 0,so=void 0}),no)}}function lo(t){const e="history";b(e,t),w(e,fo)}function fo(){if(!function(){const t=ms.chrome,e=t&&t.app&&t.app.runtime,n="history"in ms&&!!ms.history.pushState&&!!ms.history.replaceState;return!e&&n}())return;const t=Fs.onpopstate;function e(t){return function(...e){const n=e.length>2?e[2]:void 0;if(n){const t=oo,e=String(n);oo=e;k("history",{from:t,to:e})}return t.apply(this,e)}}Fs.onpopstate=function(...e){const n=Fs.location.href,r=oo;oo=n;if(k("history",{from:r,to:n}),t)try{return t.apply(this,e)}catch(t){}},Z(Fs.history,"pushState",e),Z(Fs.history,"replaceState",e)}const ho={};function po(t){const e=ho[t];if(e)return e;let n=Fs[t];if(cs(n))return ho[t]=n.bind(Fs);const r=Fs.document;if(r&&"function"==typeof r.createElement)try{const e=r.createElement("iframe");e.hidden=!0,r.head.appendChild(e);const s=e.contentWindow;s&&s[t]&&(n=s[t]),r.head.removeChild(e)}catch(t){}return n?ho[t]=n.bind(Fs):n}function mo(t){ho[t]=void 0}function yo(...t){return po("setTimeout")(...t)}const go="__sentry_xhr_v3__";function vo(t){b("xhr",t),w("xhr",bo)}function bo(){if(!Fs.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(t,e,n){const r=new Error,s=1e3*lt(),i=L(n[0])?n[0].toUpperCase():void 0,o=function(t){if(L(t))return t;try{return t.toString()}catch(t){}return}(n[1]);if(!i||!o)return t.apply(e,n);e[go]={method:i,url:o,request_headers:{}},"POST"===i&&o.match(/sentry_key/)&&(e.__sentry_own_request__=!0);const c=()=>{const t=e[go];if(t&&4===e.readyState){try{t.status_code=e.status}catch(t){}k("xhr",{endTimestamp:1e3*lt(),startTimestamp:s,xhr:e,virtualError:r})}};return"onreadystatechange"in e&&"function"==typeof e.onreadystatechange?e.onreadystatechange=new Proxy(e.onreadystatechange,{apply:(t,e,n)=>(c(),t.apply(e,n))}):e.addEventListener("readystatechange",c),e.setRequestHeader=new Proxy(e.setRequestHeader,{apply(t,e,n){const[r,s]=n,i=e[go];return i&&L(r)&&L(s)&&(i.request_headers[r.toLowerCase()]=s),t.apply(e,n)}}),t.apply(e,n)}}),t.send=new Proxy(t.send,{apply(t,e,n){const r=e[go];if(!r)return t.apply(e,n);void 0!==n[0]&&(r.body=n[0]);return k("xhr",{startTimestamp:1e3*lt(),xhr:e}),t.apply(e,n)}})}const wo=[],ko=new Map;function So(){if(Hi()&&ft){const t=Ri((({metric:t})=>{if(null==t.value)return;const e=t.entries.find((e=>e.duration===t.value&&_o[e.name]));if(!e)return;const{interactionId:n}=e,r=_o[e.name],s=Wi(ft+e.startTime),i=Wi(t.value),o=Ae(),c=o?Ce(o):void 0,a=(null!=n?ko.get(n):void 0)||c,u=a?ke(a).description:Pt().getScopeData().transactionName,l=qi({name:W(e.target),transaction:u,attributes:ot({[Gt]:"auto.http.browser.inp",[Yt]:`ui.interaction.${r}`,[te]:e.duration}),startTime:s});l&&(l.addEvent("inp",{[Vt]:"millisecond",[Qt]:t.value}),l.end(s+i))}));return()=>{t()}}return()=>{}}const _o={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function To(t,e=po("fetch")){let n=0,r=0;return wr(t,(function(s){const i=s.body.length;n+=i,r++;const o={body:s.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return mo("fetch"),wt("No fetch implementation available");try{return e(t.url,o).then((t=>(n-=i,r--,{statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}catch(t){return mo("fetch"),n-=i,r--,wt(t)}}))}function Io(t,e,n,r){const s={filename:t,function:"<anonymous>"===e?u:e,in_app:!0};return void 0!==n&&(s.lineno=n),void 0!==r&&(s.colno=r),s}const Eo=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,xo=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Co=/\((\S*)(?::(\d+))(?::(\d+))\)/,Ao=[30,t=>{const e=Eo.exec(t);if(e){const[,t,n,r]=e;return Io(t,u,+n,+r)}const n=xo.exec(t);if(n){if(n[2]&&0===n[2].indexOf("eval")){const t=Co.exec(n[2]);t&&(n[2]=t[1],n[3]=t[2],n[4]=t[3])}const[t,e]=Uo(n[1]||u,n[2]);return Io(e,t,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],Ro=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Oo=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Mo=[50,t=>{const e=Ro.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const t=Oo.exec(e[3]);t&&(e[1]=e[1]||"eval",e[3]=t[1],e[4]=t[2],e[5]="")}let t=e[3],n=e[1]||u;return[n,t]=Uo(n,t),Io(t,n,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}],$o=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,Do=[40,t=>{const e=$o.exec(t);return e?Io(e[2],e[1]||u,+e[3],e[4]?+e[4]:void 0):void 0}],Lo=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,No=[10,t=>{const e=Lo.exec(t);return e?Io(e[2],e[3]||u,+e[1]):void 0}],Fo=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,Po=[20,t=>{const e=Fo.exec(t);return e?Io(e[5],e[3]||e[4]||u,+e[1],+e[2]):void 0}],jo=[Ao,Mo],Bo=h(...jo),Uo=(t,e)=>{const n=-1!==t.indexOf("safari-extension"),r=-1!==t.indexOf("safari-web-extension");return n||r?[-1!==t.indexOf("@")?t.split("@")[0]:u,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},zo=1024,qo=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(t){e.console&&function(t){const e="console";b(e,t),w(e,Pr)}(function(t){return function(e){if(zt()!==t)return;const n={category:"console",data:{arguments:e.args,logger:"console"},level:jr(e.level),message:X(e.args," ")};if("assert"===e.level){if(!1!==e.args[0])return;n.message=`Assertion failed: ${X(e.args.slice(1)," ")||"console.assert"}`,n.data.arguments=e.args.slice(1)}Er(n,{input:e.args,level:e.level})}}(t)),e.dom&&co(function(t,e){return function(n){if(zt()!==t)return;let r,s,i="object"==typeof e?e.serializeAttribute:void 0,o="object"==typeof e&&"number"==typeof e.maxStringLength?e.maxStringLength:void 0;o&&o>zo&&(o=zo),"string"==typeof i&&(i=[i]);try{const t=n.event,e=function(t){return!!t&&!!t.target}(t)?t.target:t;r=W(e,{keyAttrs:i,maxStringLength:o}),s=Y(e)}catch(t){r="<unknown>"}if(0===r.length)return;const c={category:`ui.${n.name}`,message:r};s&&(c.data={"ui.component_name":s}),Er(c,{event:n.event,name:n.name,global:n.global})}}(t,e.dom)),e.xhr&&vo(function(t){return function(e){if(zt()!==t)return;const{startTimestamp:n,endTimestamp:r}=e,s=e.xhr[go];if(!n||!r||!s)return;const{method:i,url:o,status_code:c,body:a}=s,u={method:i,url:o,status_code:c},l={xhr:e.xhr,input:a,startTimestamp:n,endTimestamp:r};Er({category:"xhr",data:u,type:"http",level:is(c)},l)}}(t)),e.fetch&&us(function(t){return function(e){if(zt()!==t)return;const{startTimestamp:n,endTimestamp:r}=e;if(r&&(!e.fetchData.url.match(/sentry_key/)||"POST"!==e.fetchData.method))if(e.error){Er({category:"fetch",data:e.fetchData,level:"error",type:"http"},{data:e.error,input:e.args,startTimestamp:n,endTimestamp:r})}else{const t=e.response,s={...e.fetchData,status_code:t&&t.status},i={input:e.args,response:t,startTimestamp:n,endTimestamp:r};Er({category:"fetch",data:s,type:"http",level:is(s.status_code)},i)}}}(t)),e.history&&lo(function(t){return function(e){if(zt()!==t)return;let n=e.from,r=e.to;const s=Fr(gs.location.href);let i=n?Fr(n):void 0;const o=Fr(r);i&&i.path||(i=s),s.protocol===o.protocol&&s.host===o.host&&(r=o.relative),s.protocol===i.protocol&&s.host===i.host&&(n=i.relative),Er({category:"navigation",data:{from:n,to:r}})}}(t)),e.sentry&&t.on("beforeSendEvent",function(t){return function(e){zt()===t&&Er({category:"sentry."+("transaction"===e.type?"transaction":"event"),event_id:e.event_id,level:e.level,message:pt(e)},{event:e})}}(t))}}};const Ho=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Wo=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&Z(gs,"setTimeout",Jo),e.setInterval&&Z(gs,"setInterval",Jo),e.requestAnimationFrame&&Z(gs,"requestAnimationFrame",Ko),e.XMLHttpRequest&&"XMLHttpRequest"in gs&&Z(XMLHttpRequest.prototype,"send",Yo);const t=e.eventTarget;if(t){(Array.isArray(t)?t:Ho).forEach(Go)}}}};function Jo(t){return function(...e){const n=e[0];return e[0]=ws(n,{mechanism:{data:{function:m(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function Ko(t){return function(e){return t.apply(this,[ws(e,{mechanism:{data:{function:"requestAnimationFrame",handler:m(t)},handled:!1,type:"instrument"}})])}}function Yo(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in n&&"function"==typeof n[t]&&Z(n,t,(function(e){const n={mechanism:{data:{function:t,handler:m(e)},handled:!1,type:"instrument"}},r=nt(e);return r&&(n.mechanism.data.handler=m(r)),ws(e,n)}))})),t.apply(this,e)}}function Go(t){const e=gs[t],n=e&&e.prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(Z(n,"addEventListener",(function(e){return function(n,r,s){try{"function"==typeof r.handleEvent&&(r.handleEvent=ws(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:m(r),target:t},handled:!1,type:"instrument"}}))}catch(t){}return e.apply(this,[n,ws(r,{mechanism:{data:{function:"addEventListener",handler:m(r),target:t},handled:!1,type:"instrument"}}),s])}})),Z(n,"removeEventListener",(function(t){return function(e,n,r){try{const s=n.__sentry_wrapped__;s&&t.call(this,e,s,r)}catch(t){}return t.call(this,e,n,r)}})))}const Xo=()=>({name:"BrowserSession",setupOnce(){void 0!==gs.document&&(Zn({ignoreDuration:!0}),nr(),lo((({from:t,to:e})=>{void 0!==t&&t!==e&&(Zn({ignoreDuration:!0}),nr())})))}}),Vo=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(t){e.onerror&&function(t){_((e=>{const{stackParser:n,attachStacktrace:r}=Qo();if(zt()!==t||bs())return;const{msg:s,url:i,line:o,column:c,error:a}=e,l=function(t,e,n,r){const s=t.exception=t.exception||{},i=s.values=s.values||[],o=i[0]=i[0]||{},c=o.stacktrace=o.stacktrace||{},a=c.frames=c.frames||[],l=r,f=n,h=L(e)&&e.length>0?e:K();0===a.length&&a.push({colno:l,filename:h,function:u,in_app:!0,lineno:f});return t}(Os(n,a||s,void 0,r,!1),i,o,c);l.level="error",Hn(l,{originalException:a,mechanism:{handled:!1,type:"onerror"}})}))}(t),e.onunhandledrejection&&function(t){E((e=>{const{stackParser:n,attachStacktrace:r}=Qo();if(zt()!==t||bs())return;const s=function(t){if(F(t))return t;try{if("reason"in t)return t.reason;if("detail"in t&&"reason"in t.detail)return t.detail.reason}catch(t){}return t}(e),i=F(s)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(s)}`}]}}:Os(n,s,void 0,r,!0);i.level="error",Hn(i,{originalException:s,mechanism:{handled:!1,type:"onunhandledrejection"}})}))}(t)}}};function Qo(){const t=zt();return t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const Zo=()=>({name:"HttpContext",preprocessEvent(t){if(!gs.navigator&&!gs.location&&!gs.document)return;const e=t.request&&t.request.url||gs.location&&gs.location.href,{referrer:n}=gs.document||{},{userAgent:r}=gs.navigator||{},s={...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},i={...t.request,...e&&{url:e},headers:s};t.request=i}}),tc=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(t,r,s){const i=s.getOptions();$r(ks,i.stackParser,i.maxValueLength,n,e,t,r)}}};function ec(t){const e=[Or(),Ar(),Wo(),qo(),Vo(),tc(),Br(),Zo()];return!1!==t.autoSessionTracking&&e.push(Xo()),e}const nc={replayIntegration:"replay",replayCanvasIntegration:"replay-canvas",feedbackIntegration:"feedback",feedbackModalIntegration:"feedback-modal",feedbackScreenshotIntegration:"feedback-screenshot",captureConsoleIntegration:"captureconsole",contextLinesIntegration:"contextlines",linkedErrorsIntegration:"linkederrors",debugIntegration:"debug",dedupeIntegration:"dedupe",extraErrorDataIntegration:"extraerrordata",httpClientIntegration:"httpclient",reportingObserverIntegration:"reportingobserver",rewriteFramesIntegration:"rewriteframes",sessionTimingIntegration:"sessiontiming",browserProfilingIntegration:"browserprofiling",moduleMetadataIntegration:"modulemetadata"},rc=gs;const sc={increment:function(t,e=1,n){Yr.increment(es,t,e,n)},distribution:function(t,e,n){Yr.distribution(es,t,e,n)},set:function(t,e,n){Yr.set(es,t,e,n)},gauge:function(t,e,n){Yr.gauge(es,t,e,n)},timing:function(t,e,n="second",r){return Yr.timing(es,t,e,n,r)}};const ic=new WeakMap,oc=new Map,cc={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function ac(t,e){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:i,enableHTTPTimings:o,tracePropagationTargets:c}={traceFetch:cc.traceFetch,traceXHR:cc.traceXHR,trackFetchStreamPerformance:cc.trackFetchStreamPerformance,...e},a="function"==typeof i?i:t=>!0,u=t=>function(t,e){const n=gs.location&&gs.location.href;if(n){let r,s;try{r=new URL(t,n),s=new URL(n).origin}catch(t){return!1}const i=r.origin===s;return e?Q(r.toString(),e)||i&&Q(r.pathname,e):i}{const n=!!t.match(/^\/(?!\/)/);return e?Q(t,e):n}}(t,c),l={};n&&(t.addEventProcessor((t=>("transaction"===t.type&&t.spans&&t.spans.forEach((t=>{if("http.client"===t.op){const e=oc.get(t.span_id);e&&(t.timestamp=e/1e3,oc.delete(t.span_id))}})),t))),s&&function(t){const e="fetch-body-resolved";b(e,t),w(e,(()=>ls(fs)))}((t=>{if(t.response){const e=ic.get(t.response);e&&t.endTimestamp&&oc.set(e,t.endTimestamp)}})),us((t=>{const e=ns(t,a,u,l);if(t.response&&t.fetchData.__span&&ic.set(t.response,t.fetchData.__span),e){const n=fc(t.fetchData.url),r=n?Fr(n).host:void 0;e.setAttributes({"http.url":n,"server.address":r})}o&&e&&uc(e)}))),r&&vo((t=>{const e=function(t,e,n,r){const s=t.xhr,i=s&&s[go];if(!s||s.__sentry_own_request__||!i)return;const o=Pe()&&e(i.url);if(t.endTimestamp&&o){const t=s.__sentry_xhr_span_id__;if(!t)return;const e=r[t];return void(e&&void 0!==i.status_code&&(se(e,i.status_code),e.end(),delete r[t]))}const c=fc(i.url),a=c?Fr(c).host:void 0,u=!!Ae(),l=o&&u?wn({name:`${i.method} ${i.url}`,attributes:{type:"xhr","http.method":i.method,"http.url":c,url:i.url,"server.address":a,[Gt]:"auto.http.browser",[Yt]:"http.client"}}):new je;s.__sentry_xhr_span_id__=l.spanContext().spanId,r[s.__sentry_xhr_span_id__]=l,n(i.url)&&function(t,e){const{"sentry-trace":n,baggage:r}=Tr({span:e});n&&function(t,e,n){try{t.setRequestHeader("sentry-trace",e),n&&t.setRequestHeader("baggage",n)}catch(t){}}(t,n,r)}(s,Pe()&&u?l:void 0);return l}(t,a,u,l);o&&e&&uc(e)}))}function uc(t){const{url:e}=ke(t).data||{};if(!e||"string"!=typeof e)return;const n=Oi("resource",(({entries:r})=>{r.forEach((r=>{if(function(t){return"resource"===t.entryType&&"initiatorType"in t&&"string"==typeof t.nextHopProtocol&&("fetch"===t.initiatorType||"xmlhttprequest"===t.initiatorType)}(r)&&r.name.endsWith(e)){(function(t){const{name:e,version:n}=function(t){let e="unknown",n="unknown",r="";for(const s of t){if("/"===s){[e,n]=t.split("/");break}if(!isNaN(Number(s))){e="h"===r?"http":r,n=t.split(r)[1];break}r+=s}r===t&&(e=r);return{name:e,version:n}}(t.nextHopProtocol),r=[];if(r.push(["network.protocol.version",n],["network.protocol.name",e]),!ft)return r;return[...r,["http.request.redirect_start",lc(t.redirectStart)],["http.request.fetch_start",lc(t.fetchStart)],["http.request.domain_lookup_start",lc(t.domainLookupStart)],["http.request.domain_lookup_end",lc(t.domainLookupEnd)],["http.request.connect_start",lc(t.connectStart)],["http.request.secure_connection_start",lc(t.secureConnectionStart)],["http.request.connection_end",lc(t.connectEnd)],["http.request.request_start",lc(t.requestStart)],["http.request.response_start",lc(t.responseStart)],["http.request.response_end",lc(t.responseEnd)]]})(r).forEach((e=>t.setAttribute(...e))),setTimeout(n)}}))}))}function lc(t=0){return((ft||performance.timeOrigin)+t)/1e3}function fc(t){try{return new URL(t,gs.location.origin).href}catch(t){return}}const hc={...Cn,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,_experiments:{},...cc};function dc(t,e,n){t.emit("startPageLoadSpan",e,n),Pt().setTransactionName(e.name);const r=Ae();return"pageload"===(r&&ke(r).op)?r:void 0}function pc(t,e){jt().setPropagationContext({traceId:Tt()}),Pt().setPropagationContext({traceId:Tt()}),t.emit("startNavigationSpan",e),Pt().setTransactionName(e.name);const n=Ae();return"navigation"===(n&&ke(n).op)?n:void 0}function mc(t){const e=(n=`meta[name=${t}]`,q.document&&q.document.querySelector?q.document.querySelector(n):null);var n;return e?e.getAttribute("content"):void 0}const yc=()=>{},gc=["attachTo","createForm","createWidget","remove"],vc=Object.assign((t=>(o((()=>{console.warn("You are using feedbackIntegration() even though this bundle does not include feedback.")})),{name:"Feedback",...gc.reduce(((t,e)=>(t[e]=yc,t)),{})})),{_isShim:!0}),bc=n,wc="sentryReplaySession",kc="replay_event",Sc="Unable to send Replay",_c=15e4,Tc=5e3,Ic=2e7,Ec=36e5;function xc(t,e){return null!=t?t:e()}function Cc(t){let e,n=t[0],r=1;for(;r<t.length;){const s=t[r],i=t[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(e=n,n=i(n)):"call"!==s&&"optionalCall"!==s||(n=i(((...t)=>n.call(e,...t))),e=void 0)}return n}var Ac;function Rc(t){const e=Cc([t,"optionalAccess",t=>t.host]);return Boolean(Cc([e,"optionalAccess",t=>t.shadowRoot])===t)}function Oc(t){return"[object ShadowRoot]"===Object.prototype.toString.call(t)}function Mc(t){try{const n=t.rules||t.cssRules;return n?((e=Array.from(n,$c).join("")).includes(" background-clip: text;")&&!e.includes(" -webkit-background-clip: text;")&&(e=e.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),e):null}catch(t){return null}var e}function $c(t){let e;if(function(t){return"styleSheet"in t}(t))try{e=Mc(t.styleSheet)||function(t){const{cssText:e}=t;if(e.split('"').length<3)return e;const n=["@import",`url(${JSON.stringify(t.href)})`];return""===t.layerName?n.push("layer"):t.layerName&&n.push(`layer(${t.layerName})`),t.supportsText&&n.push(`supports(${t.supportsText})`),t.media.length&&n.push(t.media.mediaText),n.join(" ")+";"}(t)}catch(t){}else if(function(t){return"selectorText"in t}(t)){let e=t.cssText;const n=t.selectorText.includes(":"),r="string"==typeof t.style.all&&t.style.all;if(r&&(e=function(t){let e="";for(let n=0;n<t.style.length;n++){const r=t.style,s=r[n],i=r.getPropertyPriority(s);e+=`${s}:${r.getPropertyValue(s)}${i?" !important":""};`}return`${t.selectorText} { ${e} }`}(t)),n&&(e=function(t){const e=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm;return t.replace(e,"$1\\$2")}(e)),n||r)return e}return e||t.cssText}!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(Ac||(Ac={}));class Dc{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(t){if(!t)return-1;return xc(Cc([this,"access",t=>t.getMeta,"call",e=>e(t),"optionalAccess",t=>t.id]),(()=>-1))}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){const e=this.getId(t);this.idNodeMap.delete(e),t.childNodes&&t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){const n=e.id;this.idNodeMap.set(n,t),this.nodeMetaMap.set(t,e)}replace(t,e){const n=this.getNode(t);if(n){const t=this.nodeMetaMap.get(n);t&&this.nodeMetaMap.set(e,t)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function Lc({maskInputOptions:t,tagName:e,type:n}){return"OPTION"===e&&(e="SELECT"),Boolean(t[e.toLowerCase()]||n&&t[n]||"password"===n||"INPUT"===e&&!n&&t.text)}function Nc({isMasked:t,element:e,value:n,maskInputFn:r}){let s=n||"";return t?(r&&(s=r(s,e)),"*".repeat(s.length)):s}function Fc(t){return t.toLowerCase()}function Pc(t){return t.toUpperCase()}const jc="__rrweb_original__";function Bc(t){const e=t.type;return t.hasAttribute("data-rr-is-password")?"password":e?Fc(e):null}function Uc(t,e,n){return"INPUT"!==e||"radio"!==n&&"checkbox"!==n?t.value:t.getAttribute("value")||""}function zc(t,e){let n;try{n=new URL(t,xc(e,(()=>window.location.href)))}catch(t){return null}return xc(Cc([n.pathname.match(/\.([0-9a-z]+)(?:$)/i),"optionalAccess",t=>t[1]]),(()=>null))}const qc={};function Hc(t){const e=qc[t];if(e)return e;const n=window.document;let r=window[t];if(n&&"function"==typeof n.createElement)try{const e=n.createElement("iframe");e.hidden=!0,n.head.appendChild(e);const s=e.contentWindow;s&&s[t]&&(r=s[t]),n.head.removeChild(e)}catch(t){}return qc[t]=r.bind(window)}function Wc(...t){return Hc("setTimeout")(...t)}function Jc(...t){return Hc("clearTimeout")(...t)}function Kc(t){try{return t.contentDocument}catch(t){}}let Yc=1;const Gc=new RegExp("[^a-z0-9-_:]"),Xc=-2;function Vc(){return Yc++}let Qc,Zc;const ta=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,ea=/^(?:[a-z+]+:)?\/\//i,na=/^www\..*/i,ra=/^(data:)([^,]*),(.*)/i;function sa(t,e){return(t||"").replace(ta,((t,n,r,s,i,o)=>{const c=r||i||o,a=n||s||"";if(!c)return t;if(ea.test(c)||na.test(c))return`url(${a}${c}${a})`;if(ra.test(c))return`url(${a}${c}${a})`;if("/"===c[0])return`url(${a}${function(t){let e="";return e=t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0],e=e.split("?")[0],e}(e)+c}${a})`;const u=e.split("/"),l=c.split("/");u.pop();for(const t of l)"."!==t&&(".."===t?u.pop():u.push(t));return`url(${a}${u.join("/")}${a})`}))}const ia=/^[^ \t\n\r\u000c]+/,oa=/^[, \t\n\r\u000c]+/;const ca=new WeakMap;function aa(t,e){return e&&""!==e.trim()?la(t,e):e}function ua(t){return Boolean("svg"===t.tagName||t.ownerSVGElement)}function la(t,e){let n=ca.get(t);if(n||(n=t.createElement("a"),ca.set(t,n)),e){if(e.startsWith("blob:")||e.startsWith("data:"))return e}else e="";return n.setAttribute("href",e),n.href}function fa(t,e,n,r,s,i){return r?"src"===n||"href"===n&&("use"!==e||"#"!==r[0])||"xlink:href"===n&&"#"!==r[0]?aa(t,r):"background"!==n||"table"!==e&&"td"!==e&&"th"!==e?"srcset"===n?function(t,e){if(""===e.trim())return e;let n=0;function r(t){let r;const s=t.exec(e.substring(n));return s?(r=s[0],n+=r.length,r):""}const s=[];for(;r(oa),!(n>=e.length);){let i=r(ia);if(","===i.slice(-1))i=aa(t,i.substring(0,i.length-1)),s.push(i);else{let r="";i=aa(t,i);let o=!1;for(;;){const t=e.charAt(n);if(""===t){s.push((i+r).trim());break}if(o)")"===t&&(o=!1);else{if(","===t){n+=1,s.push((i+r).trim());break}"("===t&&(o=!0)}r+=t,n+=1}}}return s.join(", ")}(t,r):"style"===n?sa(r,la(t)):"object"===e&&"data"===n?aa(t,r):"function"==typeof i?i(n,r,s):r:aa(t,r):r}function ha(t,e,n){return("video"===t||"audio"===t)&&"autoplay"===e}function da(t,e,n=1/0,r=0){return t?t.nodeType!==t.ELEMENT_NODE||r>n?-1:e(t)?r:da(t.parentNode,e,n,r+1):-1}function pa(t,e){return n=>{const r=n;if(null===r)return!1;try{if(t)if("string"==typeof t){if(r.matches(`.${t}`))return!0}else if(function(t,e){for(let n=t.classList.length;n--;){const r=t.classList[n];if(e.test(r))return!0}return!1}(r,t))return!0;return!(!e||!r.matches(e))}catch(t){return!1}}}function ma(t,e,n,r,s,i){try{const o=t.nodeType===t.ELEMENT_NODE?t:t.parentElement;if(null===o)return!1;if("INPUT"===o.tagName){const t=o.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(t))return!0}let c=-1,a=-1;if(i){if(a=da(o,pa(r,s)),a<0)return!0;c=da(o,pa(e,n),a>=0?a:1/0)}else{if(c=da(o,pa(e,n)),c<0)return!1;a=da(o,pa(r,s),c>=0?c:1/0)}return c>=0?!(a>=0)||c<=a:!(a>=0)&&!!i}catch(t){}return!!i}function ya(t,e){const{doc:n,mirror:r,blockClass:s,blockSelector:i,unblockSelector:o,maskAllText:c,maskAttributeFn:a,maskTextClass:u,unmaskTextClass:l,maskTextSelector:f,unmaskTextSelector:h,inlineStylesheet:d,maskInputOptions:p={},maskTextFn:m,maskInputFn:y,dataURLOptions:g={},inlineImages:v,recordCanvas:b,keepIframeSrcFn:w,newlyAddedElement:k=!1}=e,S=function(t,e){if(!e.hasNode(t))return;const n=e.getId(t);return 1===n?void 0:n}(n,r);switch(t.nodeType){case t.DOCUMENT_NODE:return"CSS1Compat"!==t.compatMode?{type:Ac.Document,childNodes:[],compatMode:t.compatMode}:{type:Ac.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:Ac.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:S};case t.ELEMENT_NODE:return function(t,e){const{doc:n,blockClass:r,blockSelector:s,unblockSelector:i,inlineStylesheet:o,maskInputOptions:c={},maskAttributeFn:a,maskInputFn:u,dataURLOptions:l={},inlineImages:f,recordCanvas:h,keepIframeSrcFn:d,newlyAddedElement:p=!1,rootId:m,maskAllText:y,maskTextClass:g,unmaskTextClass:v,maskTextSelector:b,unmaskTextSelector:w}=e,k=function(t,e,n,r){try{if(r&&t.matches(r))return!1;if("string"==typeof e){if(t.classList.contains(e))return!0}else for(let n=t.classList.length;n--;){const r=t.classList[n];if(e.test(r))return!0}if(n)return t.matches(n)}catch(t){}return!1}(t,r,s,i),S=function(t){if(t instanceof HTMLFormElement)return"form";const e=Fc(t.tagName);return Gc.test(e)?"div":e}(t);let _={};const T=t.attributes.length;for(let e=0;e<T;e++){const r=t.attributes[e];r.name&&!ha(S,r.name)&&(_[r.name]=fa(n,S,Fc(r.name),r.value,t,a))}if("link"===S&&o){const e=Array.from(n.styleSheets).find((e=>e.href===t.href));let r=null;e&&(r=Mc(e)),r&&(_.rel=null,_.href=null,_.crossorigin=null,_._cssText=sa(r,e.href))}if("style"===S&&t.sheet&&!(t.innerText||t.textContent||"").trim().length){const e=Mc(t.sheet);e&&(_._cssText=sa(e,la(n)))}if("input"===S||"textarea"===S||"select"===S||"option"===S){const e=t,n=Bc(e),r=Uc(e,Pc(S),n),s=e.checked;if("submit"!==n&&"button"!==n&&r){const t=ma(e,g,b,v,w,Lc({type:n,tagName:Pc(S),maskInputOptions:c}));_.value=Nc({isMasked:t,element:e,value:r,maskInputFn:u})}s&&(_.checked=s)}"option"===S&&(t.selected&&!c.select?_.selected=!0:delete _.selected);if("canvas"===S&&h)if("2d"===t.__context)(function(t){const e=t.getContext("2d");if(!e)return!0;for(let n=0;n<t.width;n+=50)for(let r=0;r<t.height;r+=50){const s=e.getImageData,i=jc in s?s[jc]:s;if(new Uint32Array(i.call(e,n,r,Math.min(50,t.width-n),Math.min(50,t.height-r)).data.buffer).some((t=>0!==t)))return!1}return!0})(t)||(_.rr_dataURL=t.toDataURL(l.type,l.quality));else if(!("__context"in t)){const e=t.toDataURL(l.type,l.quality),r=n.createElement("canvas");r.width=t.width,r.height=t.height;e!==r.toDataURL(l.type,l.quality)&&(_.rr_dataURL=e)}if("img"===S&&f){Qc||(Qc=n.createElement("canvas"),Zc=Qc.getContext("2d"));const e=t,r=e.currentSrc||e.getAttribute("src")||"<unknown-src>",s=e.crossOrigin,i=()=>{e.removeEventListener("load",i);try{Qc.width=e.naturalWidth,Qc.height=e.naturalHeight,Zc.drawImage(e,0,0),_.rr_dataURL=Qc.toDataURL(l.type,l.quality)}catch(t){if("anonymous"!==e.crossOrigin)return e.crossOrigin="anonymous",void(e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i));console.warn(`Cannot inline img src=${r}! Error: ${t}`)}"anonymous"===e.crossOrigin&&(s?_.crossOrigin=s:e.removeAttribute("crossorigin"))};e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i)}"audio"!==S&&"video"!==S||(_.rr_mediaState=t.paused?"paused":"played",_.rr_mediaCurrentTime=t.currentTime);p||(t.scrollLeft&&(_.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(_.rr_scrollTop=t.scrollTop));if(k){const{width:e,height:n}=t.getBoundingClientRect();_={class:_.class,rr_width:`${e}px`,rr_height:`${n}px`}}"iframe"!==S||d(_.src)||(k||Kc(t)||(_.rr_src=_.src),delete _.src);let I;try{customElements.get(S)&&(I=!0)}catch(t){}return{type:Ac.Element,tagName:S,attributes:_,childNodes:[],isSVG:ua(t)||void 0,needBlock:k,rootId:m,isCustom:I}}(t,{doc:n,blockClass:s,blockSelector:i,unblockSelector:o,inlineStylesheet:d,maskAttributeFn:a,maskInputOptions:p,maskInputFn:y,dataURLOptions:g,inlineImages:v,recordCanvas:b,keepIframeSrcFn:w,newlyAddedElement:k,rootId:S,maskAllText:c,maskTextClass:u,unmaskTextClass:l,maskTextSelector:f,unmaskTextSelector:h});case t.TEXT_NODE:return function(t,e){const{maskAllText:n,maskTextClass:r,unmaskTextClass:s,maskTextSelector:i,unmaskTextSelector:o,maskTextFn:c,maskInputOptions:a,maskInputFn:u,rootId:l}=e,f=t.parentNode&&t.parentNode.tagName;let h=t.textContent;const d="STYLE"===f||void 0,p="SCRIPT"===f||void 0,m="TEXTAREA"===f||void 0;if(d&&h){try{t.nextSibling||t.previousSibling||Cc([t,"access",t=>t.parentNode,"access",t=>t.sheet,"optionalAccess",t=>t.cssRules])&&(h=Mc(t.parentNode.sheet))}catch(e){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${e}`,t)}h=sa(h,la(e.doc))}p&&(h="SCRIPT_PLACEHOLDER");const y=ma(t,r,i,s,o,n);d||p||m||!h||!y||(h=c?c(h,t.parentElement):h.replace(/[\S]/g,"*"));m&&h&&(a.textarea||y)&&(h=u?u(h,t.parentNode):h.replace(/[\S]/g,"*"));if("OPTION"===f&&h){h=Nc({isMasked:ma(t,r,i,s,o,Lc({type:null,tagName:f,maskInputOptions:a})),element:t,value:h,maskInputFn:u})}return{type:Ac.Text,textContent:h||"",isStyle:d,rootId:l}}(t,{doc:n,maskAllText:c,maskTextClass:u,unmaskTextClass:l,maskTextSelector:f,unmaskTextSelector:h,maskTextFn:m,maskInputOptions:p,maskInputFn:y,rootId:S});case t.CDATA_SECTION_NODE:return{type:Ac.CDATA,textContent:"",rootId:S};case t.COMMENT_NODE:return{type:Ac.Comment,textContent:t.textContent||"",rootId:S};default:return!1}}function ga(t){return null==t?"":t.toLowerCase()}function va(t,e){const{doc:n,mirror:r,blockClass:s,blockSelector:i,unblockSelector:o,maskAllText:c,maskTextClass:a,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:f,skipChild:h=!1,inlineStylesheet:d=!0,maskInputOptions:p={},maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:b={},inlineImages:w=!1,recordCanvas:k=!1,onSerialize:S,onIframeLoad:_,iframeLoadTimeout:T=5e3,onStylesheetLoad:I,stylesheetLoadTimeout:E=5e3,keepIframeSrcFn:x=(()=>!1),newlyAddedElement:C=!1}=e;let{preserveWhiteSpace:A=!0}=e;const R=ya(t,{doc:n,mirror:r,blockClass:s,blockSelector:i,maskAllText:c,unblockSelector:o,maskTextClass:a,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:f,inlineStylesheet:d,maskInputOptions:p,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,dataURLOptions:b,inlineImages:w,recordCanvas:k,keepIframeSrcFn:x,newlyAddedElement:C});if(!R)return console.warn(t,"not serialized"),null;let O;O=r.hasNode(t)?r.getId(t):!function(t,e){if(e.comment&&t.type===Ac.Comment)return!0;if(t.type===Ac.Element){if(e.script&&("script"===t.tagName||"link"===t.tagName&&("preload"===t.attributes.rel||"modulepreload"===t.attributes.rel)||"link"===t.tagName&&"prefetch"===t.attributes.rel&&"string"==typeof t.attributes.href&&"js"===zc(t.attributes.href)))return!0;if(e.headFavicon&&("link"===t.tagName&&"shortcut icon"===t.attributes.rel||"meta"===t.tagName&&(ga(t.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===ga(t.attributes.name)||"icon"===ga(t.attributes.rel)||"apple-touch-icon"===ga(t.attributes.rel)||"shortcut icon"===ga(t.attributes.rel))))return!0;if("meta"===t.tagName){if(e.headMetaDescKeywords&&ga(t.attributes.name).match(/^description|keywords$/))return!0;if(e.headMetaSocial&&(ga(t.attributes.property).match(/^(og|twitter|fb):/)||ga(t.attributes.name).match(/^(og|twitter):/)||"pinterest"===ga(t.attributes.name)))return!0;if(e.headMetaRobots&&("robots"===ga(t.attributes.name)||"googlebot"===ga(t.attributes.name)||"bingbot"===ga(t.attributes.name)))return!0;if(e.headMetaHttpEquiv&&void 0!==t.attributes["http-equiv"])return!0;if(e.headMetaAuthorship&&("author"===ga(t.attributes.name)||"generator"===ga(t.attributes.name)||"framework"===ga(t.attributes.name)||"publisher"===ga(t.attributes.name)||"progid"===ga(t.attributes.name)||ga(t.attributes.property).match(/^article:/)||ga(t.attributes.property).match(/^product:/)))return!0;if(e.headMetaVerification&&("google-site-verification"===ga(t.attributes.name)||"yandex-verification"===ga(t.attributes.name)||"csrf-token"===ga(t.attributes.name)||"p:domain_verify"===ga(t.attributes.name)||"verify-v1"===ga(t.attributes.name)||"verification"===ga(t.attributes.name)||"shopify-checkout-api-token"===ga(t.attributes.name)))return!0}}return!1}(R,v)&&(A||R.type!==Ac.Text||R.isStyle||R.textContent.replace(/^\s+|\s+$/gm,"").length)?Vc():Xc;const M=Object.assign(R,{id:O});if(r.add(t,M),O===Xc)return null;S&&S(t);let $=!h;if(M.type===Ac.Element){$=$&&!M.needBlock,delete M.needBlock;const e=t.shadowRoot;e&&Oc(e)&&(M.isShadowHost=!0)}if((M.type===Ac.Document||M.type===Ac.Element)&&$){v.headWhitespace&&M.type===Ac.Element&&"head"===M.tagName&&(A=!1);const e={doc:n,mirror:r,blockClass:s,blockSelector:i,maskAllText:c,unblockSelector:o,maskTextClass:a,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:f,skipChild:h,inlineStylesheet:d,maskInputOptions:p,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:b,inlineImages:w,recordCanvas:k,preserveWhiteSpace:A,onSerialize:S,onIframeLoad:_,iframeLoadTimeout:T,onStylesheetLoad:I,stylesheetLoadTimeout:E,keepIframeSrcFn:x};for(const n of Array.from(t.childNodes)){const t=va(n,e);t&&M.childNodes.push(t)}if(function(t){return t.nodeType===t.ELEMENT_NODE}(t)&&t.shadowRoot)for(const n of Array.from(t.shadowRoot.childNodes)){const r=va(n,e);r&&(Oc(t.shadowRoot)&&(r.isShadow=!0),M.childNodes.push(r))}}return t.parentNode&&Rc(t.parentNode)&&Oc(t.parentNode)&&(M.isShadow=!0),M.type===Ac.Element&&"iframe"===M.tagName&&function(t,e,n){const r=t.contentWindow;if(!r)return;let s,i=!1;try{s=r.document.readyState}catch(t){return}if("complete"!==s){const r=Wc((()=>{i||(e(),i=!0)}),n);return void t.addEventListener("load",(()=>{Jc(r),i=!0,e()}))}const o="about:blank";if(r.location.href!==o||t.src===o||""===t.src)return Wc(e,0),t.addEventListener("load",e);t.addEventListener("load",e)}(t,(()=>{const e=Kc(t);if(e&&_){const n=va(e,{doc:e,mirror:r,blockClass:s,blockSelector:i,unblockSelector:o,maskAllText:c,maskTextClass:a,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:d,maskInputOptions:p,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:b,inlineImages:w,recordCanvas:k,preserveWhiteSpace:A,onSerialize:S,onIframeLoad:_,iframeLoadTimeout:T,onStylesheetLoad:I,stylesheetLoadTimeout:E,keepIframeSrcFn:x});n&&_(t,n)}}),T),M.type===Ac.Element&&"link"===M.tagName&&"string"==typeof M.attributes.rel&&("stylesheet"===M.attributes.rel||"preload"===M.attributes.rel&&"string"==typeof M.attributes.href&&"css"===zc(M.attributes.href))&&function(t,e,n){let r,s=!1;try{r=t.sheet}catch(t){return}if(r)return;const i=Wc((()=>{s||(e(),s=!0)}),n);t.addEventListener("load",(()=>{Jc(i),s=!0,e()}))}(t,(()=>{if(I){const e=va(t,{doc:n,mirror:r,blockClass:s,blockSelector:i,unblockSelector:o,maskAllText:c,maskTextClass:a,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:d,maskInputOptions:p,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:b,inlineImages:w,recordCanvas:k,preserveWhiteSpace:A,onSerialize:S,onIframeLoad:_,iframeLoadTimeout:T,onStylesheetLoad:I,stylesheetLoadTimeout:E,keepIframeSrcFn:x});e&&I(t,e)}}),E),M}function ba(t){let e,n=t[0],r=1;for(;r<t.length;){const s=t[r],i=t[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(e=n,n=i(n)):"call"!==s&&"optionalCall"!==s||(n=i(((...t)=>n.call(e,...t))),e=void 0)}return n}function wa(t,e,n=document){const r={capture:!0,passive:!0};return n.addEventListener(t,e,r),()=>n.removeEventListener(t,e,r)}const ka="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let Sa={map:{},getId:()=>(console.error(ka),-1),getNode:()=>(console.error(ka),null),removeNodeFromMap(){console.error(ka)},has:()=>(console.error(ka),!1),reset(){console.error(ka)}};function _a(t,e,n={}){let r=null,s=0;return function(...i){const o=Date.now();s||!1!==n.leading||(s=o);const c=e-(o-s),a=this;c<=0||c>e?(r&&(!function(...t){qa("clearTimeout")(...t)}(r),r=null),s=o,t.apply(a,i)):r||!1===n.trailing||(r=Ha((()=>{s=!1===n.leading?0:Date.now(),r=null,t.apply(a,i)}),c))}}function Ta(t,e,n,r,s=window){const i=s.Object.getOwnPropertyDescriptor(t,e);return s.Object.defineProperty(t,e,r?n:{set(t){Ha((()=>{n.set.call(this,t)}),0),i&&i.set&&i.set.call(this,t)}}),()=>Ta(t,e,i||{},!0)}function Ia(t,e,n){try{if(!(e in t))return()=>{};const r=t[e],s=n(r);return"function"==typeof s&&(s.prototype=s.prototype||{},Object.defineProperties(s,{__rrweb_original__:{enumerable:!1,value:r}})),t[e]=s,()=>{t[e]=r}}catch(t){return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(Sa=new Proxy(Sa,{get:(t,e,n)=>("map"===e&&console.error(ka),Reflect.get(t,e,n))}));let Ea=Date.now;function xa(t){const e=t.document;return{left:e.scrollingElement?e.scrollingElement.scrollLeft:void 0!==t.pageXOffset?t.pageXOffset:ba([e,"optionalAccess",t=>t.documentElement,"access",t=>t.scrollLeft])||ba([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.parentElement,"optionalAccess",t=>t.scrollLeft])||ba([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.scrollLeft])||0,top:e.scrollingElement?e.scrollingElement.scrollTop:void 0!==t.pageYOffset?t.pageYOffset:ba([e,"optionalAccess",t=>t.documentElement,"access",t=>t.scrollTop])||ba([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.parentElement,"optionalAccess",t=>t.scrollTop])||ba([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.scrollTop])||0}}function Ca(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function Aa(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function Ra(t){if(!t)return null;return t.nodeType===t.ELEMENT_NODE?t:t.parentElement}function Oa(t,e,n,r,s){if(!t)return!1;const i=Ra(t);if(!i)return!1;const o=pa(e,n);if(!s){const t=r&&i.matches(r);return o(i)&&!t}const c=da(i,o);let a=-1;return!(c<0)&&(r&&(a=da(i,pa(null,r))),c>-1&&a<0||c<a)}function Ma(t,e){return e.getId(t)===Xc}function $a(t,e){if(Rc(t))return!1;const n=e.getId(t);return!e.has(n)||(!t.parentNode||t.parentNode.nodeType!==t.DOCUMENT_NODE)&&(!t.parentNode||$a(t.parentNode,e))}function Da(t){return Boolean(t.changedTouches)}function La(t,e){return Boolean("IFRAME"===t.nodeName&&e.getMeta(t))}function Na(t,e){return Boolean("LINK"===t.nodeName&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&"stylesheet"===t.getAttribute("rel")&&e.getMeta(t))}function Fa(t){return Boolean(ba([t,"optionalAccess",t=>t.shadowRoot]))}/[1-9][0-9]{12}/.test(Date.now().toString())||(Ea=()=>(new Date).getTime());class Pa{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(t){return e=this.styleIDMap.get(t),n=()=>-1,null!=e?e:n();var e,n}has(t){return this.styleIDMap.has(t)}add(t,e){if(this.has(t))return this.getId(t);let n;return n=void 0===e?this.id++:e,this.styleIDMap.set(t,n),this.idStyleMap.set(n,t),n}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function ja(t){let e=null;return ba([t,"access",t=>t.getRootNode,"optionalCall",t=>t(),"optionalAccess",t=>t.nodeType])===Node.DOCUMENT_FRAGMENT_NODE&&t.getRootNode().host&&(e=t.getRootNode().host),e}function Ba(t){const e=t.ownerDocument;if(!e)return!1;const n=function(t){let e,n=t;for(;e=ja(n);)n=e;return n}(t);return e.contains(n)}function Ua(t){const e=t.ownerDocument;return!!e&&(e.contains(t)||Ba(t))}const za={};function qa(t){const e=za[t];if(e)return e;const n=window.document;let r=window[t];if(n&&"function"==typeof n.createElement)try{const e=n.createElement("iframe");e.hidden=!0,n.head.appendChild(e);const s=e.contentWindow;s&&s[t]&&(r=s[t]),n.head.removeChild(e)}catch(t){}return za[t]=r.bind(window)}function Ha(...t){return qa("setTimeout")(...t)}var Wa,Ja,Ka=(t=>(t[t.DomContentLoaded=0]="DomContentLoaded",t[t.Load=1]="Load",t[t.FullSnapshot=2]="FullSnapshot",t[t.IncrementalSnapshot=3]="IncrementalSnapshot",t[t.Meta=4]="Meta",t[t.Custom=5]="Custom",t[t.Plugin=6]="Plugin",t))(Ka||{}),Ya=(t=>(t[t.Mutation=0]="Mutation",t[t.MouseMove=1]="MouseMove",t[t.MouseInteraction=2]="MouseInteraction",t[t.Scroll=3]="Scroll",t[t.ViewportResize=4]="ViewportResize",t[t.Input=5]="Input",t[t.TouchMove=6]="TouchMove",t[t.MediaInteraction=7]="MediaInteraction",t[t.StyleSheetRule=8]="StyleSheetRule",t[t.CanvasMutation=9]="CanvasMutation",t[t.Font=10]="Font",t[t.Log=11]="Log",t[t.Drag=12]="Drag",t[t.StyleDeclaration=13]="StyleDeclaration",t[t.Selection=14]="Selection",t[t.AdoptedStyleSheet=15]="AdoptedStyleSheet",t[t.CustomElement=16]="CustomElement",t))(Ya||{}),Ga=(t=>(t[t.MouseUp=0]="MouseUp",t[t.MouseDown=1]="MouseDown",t[t.Click=2]="Click",t[t.ContextMenu=3]="ContextMenu",t[t.DblClick=4]="DblClick",t[t.Focus=5]="Focus",t[t.Blur=6]="Blur",t[t.TouchStart=7]="TouchStart",t[t.TouchMove_Departed=8]="TouchMove_Departed",t[t.TouchEnd=9]="TouchEnd",t[t.TouchCancel=10]="TouchCancel",t))(Ga||{}),Xa=(t=>(t[t.Mouse=0]="Mouse",t[t.Pen=1]="Pen",t[t.Touch=2]="Touch",t))(Xa||{});function Va(t){try{return t.contentDocument}catch(t){}}function Qa(t){let e,n=t[0],r=1;for(;r<t.length;){const s=t[r],i=t[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(e=n,n=i(n)):"call"!==s&&"optionalCall"!==s||(n=i(((...t)=>n.call(e,...t))),e=void 0)}return n}function Za(t){return"__ln"in t}!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(Wa||(Wa={})),function(t){t[t.PLACEHOLDER=0]="PLACEHOLDER",t[t.ELEMENT_NODE=1]="ELEMENT_NODE",t[t.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",t[t.TEXT_NODE=3]="TEXT_NODE",t[t.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",t[t.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",t[t.ENTITY_NODE=6]="ENTITY_NODE",t[t.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",t[t.COMMENT_NODE=8]="COMMENT_NODE",t[t.DOCUMENT_NODE=9]="DOCUMENT_NODE",t[t.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",t[t.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(Ja||(Ja={}));class tu{constructor(){this.length=0,this.head=null,this.tail=null}get(t){if(t>=this.length)throw new Error("Position outside of list range");let e=this.head;for(let n=0;n<t;n++)e=Qa([e,"optionalAccess",t=>t.next])||null;return e}addNode(t){const e={value:t,previous:null,next:null};if(t.__ln=e,t.previousSibling&&Za(t.previousSibling)){const n=t.previousSibling.__ln.next;e.next=n,e.previous=t.previousSibling.__ln,t.previousSibling.__ln.next=e,n&&(n.previous=e)}else if(t.nextSibling&&Za(t.nextSibling)&&t.nextSibling.__ln.previous){const n=t.nextSibling.__ln.previous;e.previous=n,e.next=t.nextSibling.__ln,t.nextSibling.__ln.previous=e,n&&(n.next=e)}else this.head&&(this.head.previous=e),e.next=this.head,this.head=e;null===e.next&&(this.tail=e),this.length++}removeNode(t){const e=t.__ln;this.head&&(e.previous?(e.previous.next=e.next,e.next?e.next.previous=e.previous:this.tail=e.previous):(this.head=e.next,this.head?this.head.previous=null:this.tail=null),t.__ln&&delete t.__ln,this.length--)}}const eu=(t,e)=>`${t}@${e}`;class nu{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=t=>{t.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const t=[],e=new Set,n=new tu,r=t=>{let e=t,n=Xc;for(;n===Xc;)e=e&&e.nextSibling,n=e&&this.mirror.getId(e);return n},s=s=>{if(!s.parentNode||!Ua(s))return;const i=Rc(s.parentNode)?this.mirror.getId(ja(s)):this.mirror.getId(s.parentNode),o=r(s);if(-1===i||-1===o)return n.addNode(s);const c=va(s,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:t=>{La(t,this.mirror)&&!Oa(t,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&this.iframeManager.addIframe(t),Na(t,this.mirror)&&this.stylesheetManager.trackLinkElement(t),Fa(s)&&this.shadowDomManager.addShadowRoot(s.shadowRoot,this.doc)},onIframeLoad:(t,e)=>{Oa(t,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(this.iframeManager.attachIframe(t,e),t.contentWindow&&this.canvasManager.addWindow(t.contentWindow),this.shadowDomManager.observeAttachShadow(t))},onStylesheetLoad:(t,e)=>{this.stylesheetManager.attachLinkElement(t,e)}});c&&(t.push({parentId:i,nextId:o,node:c}),e.add(c.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const t of this.movedSet)su(this.removes,t,this.mirror)&&!this.movedSet.has(t.parentNode)||s(t);for(const t of this.addedSet)iu(this.droppedSet,t)||su(this.removes,t,this.mirror)?iu(this.movedSet,t)?s(t):this.droppedSet.add(t):s(t);let i=null;for(;n.length;){let t=null;if(i){const e=this.mirror.getId(i.value.parentNode),n=r(i.value);-1!==e&&-1!==n&&(t=i)}if(!t){let e=n.tail;for(;e;){const n=e;if(e=e.previous,n){const e=this.mirror.getId(n.value.parentNode);if(-1===r(n.value))continue;if(-1!==e){t=n;break}{const e=n.value;if(e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const r=e.parentNode.host;if(-1!==this.mirror.getId(r)){t=n;break}}}}}}if(!t){for(;n.head;)n.removeNode(n.head.value);break}i=t.previous,n.removeNode(t.value),s(t.value)}const o={texts:this.texts.map((t=>({id:this.mirror.getId(t.node),value:t.value}))).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),attributes:this.attributes.map((t=>{const{attributes:e}=t;if("string"==typeof e.style){const n=JSON.stringify(t.styleDiff),r=JSON.stringify(t.$t);n.length<e.style.length&&(n+r).split("var(").length===e.style.split("var(").length&&(e.style=t.styleDiff)}return{id:this.mirror.getId(t.node),attributes:e}})).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),removes:this.removes,adds:t};(o.texts.length||o.attributes.length||o.removes.length||o.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(o))},this.processMutation=t=>{if(!Ma(t.target,this.mirror))switch(t.type){case"characterData":{const e=t.target.textContent;Oa(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||e===t.oldValue||this.texts.push({value:ma(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&e?this.maskTextFn?this.maskTextFn(e,Ra(t.target)):e.replace(/[\S]/g,"*"):e,node:t.target});break}case"attributes":{const e=t.target;let n=t.attributeName,r=t.target.getAttribute(n);if("value"===n){const n=Bc(e),s=e.tagName;r=Uc(e,s,n);const i=Lc({maskInputOptions:this.maskInputOptions,tagName:s,type:n});r=Nc({isMasked:ma(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,i),element:e,value:r,maskInputFn:this.maskInputFn})}if(Oa(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||r===t.oldValue)return;let s=this.attributeMap.get(t.target);if("IFRAME"===e.tagName&&"src"===n&&!this.keepIframeSrcFn(r)){if(Va(e))return;n="rr_src"}if(s||(s={node:t.target,attributes:{},styleDiff:{},$t:{}},this.attributes.push(s),this.attributeMap.set(t.target,s)),"type"===n&&"INPUT"===e.tagName&&"password"===(t.oldValue||"").toLowerCase()&&e.setAttribute("data-rr-is-password","true"),!ha(e.tagName,n)&&(s.attributes[n]=fa(this.doc,Fc(e.tagName),Fc(n),r,e,this.maskAttributeFn),"style"===n)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(t){this.unattachedDoc=this.doc}const n=this.unattachedDoc.createElement("span");t.oldValue&&n.setAttribute("style",t.oldValue);for(const t of Array.from(e.style)){const r=e.style.getPropertyValue(t),i=e.style.getPropertyPriority(t);r!==n.style.getPropertyValue(t)||i!==n.style.getPropertyPriority(t)?s.styleDiff[t]=""===i?r:[r,i]:s.$t[t]=[r,i]}for(const t of Array.from(n.style))""===e.style.getPropertyValue(t)&&(s.styleDiff[t]=!1)}break}case"childList":if(Oa(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;t.addedNodes.forEach((e=>this.genAdds(e,t.target))),t.removedNodes.forEach((e=>{const n=this.mirror.getId(e),r=Rc(t.target)?this.mirror.getId(t.target.host):this.mirror.getId(t.target);Oa(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||Ma(e,this.mirror)||!function(t,e){return-1!==e.getId(t)}(e,this.mirror)||(this.addedSet.has(e)?(ru(this.addedSet,e),this.droppedSet.add(e)):this.addedSet.has(t.target)&&-1===n||$a(t.target,this.mirror)||(this.movedSet.has(e)&&this.movedMap[eu(n,r)]?ru(this.movedSet,e):this.removes.push({parentId:r,id:n,isShadow:!(!Rc(t.target)||!Oc(t.target))||void 0})),this.mapRemoves.push(e))}))}},this.genAdds=(t,e)=>{if(!this.processedNodeManager.inOtherBuffer(t,this)&&!this.addedSet.has(t)&&!this.movedSet.has(t)){if(this.mirror.hasNode(t)){if(Ma(t,this.mirror))return;this.movedSet.add(t);let n=null;e&&this.mirror.hasNode(e)&&(n=this.mirror.getId(e)),n&&-1!==n&&(this.movedMap[eu(this.mirror.getId(t),n)]=!0)}else this.addedSet.add(t),this.droppedSet.delete(t);Oa(t,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(t.childNodes.forEach((t=>this.genAdds(t))),Fa(t)&&t.shadowRoot.childNodes.forEach((e=>{this.processedNodeManager.add(e,this),this.genAdds(e,t)})))}}}init(t){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((e=>{this[e]=t[e]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function ru(t,e){t.delete(e),e.childNodes.forEach((e=>ru(t,e)))}function su(t,e,n){return 0!==t.length&&function(t,e,n){let r=e.parentNode;for(;r;){const e=n.getId(r);if(t.some((t=>t.id===e)))return!0;r=r.parentNode}return!1}(t,e,n)}function iu(t,e){return 0!==t.size&&ou(t,e)}function ou(t,e){const{parentNode:n}=e;return!!n&&(!!t.has(n)||ou(t,n))}let cu;function au(t){cu=t}function uu(){cu=void 0}const lu=t=>{if(!cu)return t;return(...e)=>{try{return t(...e)}catch(t){if(cu&&!0===cu(t))return()=>{};throw t}}};function fu(t){let e,n=t[0],r=1;for(;r<t.length;){const s=t[r],i=t[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(e=n,n=i(n)):"call"!==s&&"optionalCall"!==s||(n=i(((...t)=>n.call(e,...t))),e=void 0)}return n}const hu=[];function du(t){try{if("composedPath"in t){const e=t.composedPath();if(e.length)return e[0]}else if("path"in t&&t.path.length)return t.path[0]}catch(t){}return t&&t.target}function pu(t,e){const n=new nu;hu.push(n),n.init(t);let r=window.MutationObserver||window.__rrMutationObserver;const s=fu([window,"optionalAccess",t=>t.Zone,"optionalAccess",t=>t.__symbol__,"optionalCall",t=>t("MutationObserver")]);s&&window[s]&&(r=window[s]);const i=new r(lu((e=>{t.onMutation&&!1===t.onMutation(e)||n.processMutations.bind(n)(e)})));return i.observe(e,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),i}function mu({mouseInteractionCb:t,doc:e,mirror:n,blockClass:r,blockSelector:s,unblockSelector:i,sampling:o}){if(!1===o.mouseInteraction)return()=>{};const c=!0===o.mouseInteraction||void 0===o.mouseInteraction?{}:o.mouseInteraction,a=[];let u=null;return Object.keys(Ga).filter((t=>Number.isNaN(Number(t))&&!t.endsWith("_Departed")&&!1!==c[t])).forEach((o=>{let c=Fc(o);const l=(e=>o=>{const c=du(o);if(Oa(c,r,s,i,!0))return;let a=null,l=e;if("pointerType"in o){switch(o.pointerType){case"mouse":a=Xa.Mouse;break;case"touch":a=Xa.Touch;break;case"pen":a=Xa.Pen}a===Xa.Touch&&(Ga[e]===Ga.MouseDown?l="TouchStart":Ga[e]===Ga.MouseUp&&(l="TouchEnd"))}else Da(o)&&(a=Xa.Touch);null!==a?(u=a,(l.startsWith("Touch")&&a===Xa.Touch||l.startsWith("Mouse")&&a===Xa.Mouse)&&(a=null)):Ga[e]===Ga.Click&&(a=u,u=null);const f=Da(o)?o.changedTouches[0]:o;if(!f)return;const h=n.getId(c),{clientX:d,clientY:p}=f;lu(t)({type:Ga[l],id:h,x:d,y:p,...null!==a&&{pointerType:a}})})(o);if(window.PointerEvent)switch(Ga[o]){case Ga.MouseDown:case Ga.MouseUp:c=c.replace("mouse","pointer");break;case Ga.TouchStart:case Ga.TouchEnd:return}a.push(wa(c,l,e))})),lu((()=>{a.forEach((t=>t()))}))}function yu({scrollCb:t,doc:e,mirror:n,blockClass:r,blockSelector:s,unblockSelector:i,sampling:o}){return wa("scroll",lu(_a(lu((o=>{const c=du(o);if(!c||Oa(c,r,s,i,!0))return;const a=n.getId(c);if(c===e&&e.defaultView){const n=xa(e.defaultView);t({id:a,x:n.left,y:n.top})}else t({id:a,x:c.scrollLeft,y:c.scrollTop})})),o.scroll||100)),e)}const gu=["INPUT","TEXTAREA","SELECT"],vu=new WeakMap;function bu({inputCb:t,doc:e,mirror:n,blockClass:r,blockSelector:s,unblockSelector:i,ignoreClass:o,ignoreSelector:c,maskInputOptions:a,maskInputFn:u,sampling:l,userTriggeredOnInput:f,maskTextClass:h,unmaskTextClass:d,maskTextSelector:p,unmaskTextSelector:m}){function y(t){let n=du(t);const l=t.isTrusted,y=n&&Pc(n.tagName);if("OPTION"===y&&(n=n.parentElement),!n||!y||gu.indexOf(y)<0||Oa(n,r,s,i,!0))return;const v=n;if(v.classList.contains(o)||c&&v.matches(c))return;const b=Bc(n);let w=Uc(v,y,b),k=!1;const S=Lc({maskInputOptions:a,tagName:y,type:b}),_=ma(n,h,p,d,m,S);"radio"!==b&&"checkbox"!==b||(k=n.checked),w=Nc({isMasked:_,element:n,value:w,maskInputFn:u}),g(n,f?{text:w,isChecked:k,userTriggered:l}:{text:w,isChecked:k});const T=n.name;"radio"===b&&T&&k&&e.querySelectorAll(`input[type="radio"][name="${T}"]`).forEach((t=>{if(t!==n){const e=Nc({isMasked:_,element:t,value:Uc(t,y,b),maskInputFn:u});g(t,f?{text:e,isChecked:!k,userTriggered:!1}:{text:e,isChecked:!k})}}))}function g(e,r){const s=vu.get(e);if(!s||s.text!==r.text||s.isChecked!==r.isChecked){vu.set(e,r);const s=n.getId(e);lu(t)({...r,id:s})}}const v=("last"===l.input?["change"]:["input","change"]).map((t=>wa(t,lu(y),e))),b=e.defaultView;if(!b)return()=>{v.forEach((t=>t()))};const w=b.Object.getOwnPropertyDescriptor(b.HTMLInputElement.prototype,"value"),k=[[b.HTMLInputElement.prototype,"value"],[b.HTMLInputElement.prototype,"checked"],[b.HTMLSelectElement.prototype,"value"],[b.HTMLTextAreaElement.prototype,"value"],[b.HTMLSelectElement.prototype,"selectedIndex"],[b.HTMLOptionElement.prototype,"selected"]];return w&&w.set&&v.push(...k.map((t=>Ta(t[0],t[1],{set(){lu(y)({target:this,isTrusted:!1})}},!1,b)))),lu((()=>{v.forEach((t=>t()))}))}function wu(t){return function(t,e){if(Tu("CSSGroupingRule")&&t.parentRule instanceof CSSGroupingRule||Tu("CSSMediaRule")&&t.parentRule instanceof CSSMediaRule||Tu("CSSSupportsRule")&&t.parentRule instanceof CSSSupportsRule||Tu("CSSConditionRule")&&t.parentRule instanceof CSSConditionRule){const n=Array.from(t.parentRule.cssRules).indexOf(t);e.unshift(n)}else if(t.parentStyleSheet){const n=Array.from(t.parentStyleSheet.cssRules).indexOf(t);e.unshift(n)}return e}(t,[])}function ku(t,e,n){let r,s;return t?(t.ownerNode?r=e.getId(t.ownerNode):s=n.getId(t),{styleId:s,id:r}):{}}function Su({mirror:t,stylesheetManager:e},n){let r=null;r="#document"===n.nodeName?t.getId(n):t.getId(n.host);const s="#document"===n.nodeName?fu([n,"access",t=>t.defaultView,"optionalAccess",t=>t.Document]):fu([n,"access",t=>t.ownerDocument,"optionalAccess",t=>t.defaultView,"optionalAccess",t=>t.ShadowRoot]),i=fu([s,"optionalAccess",t=>t.prototype])?Object.getOwnPropertyDescriptor(fu([s,"optionalAccess",t=>t.prototype]),"adoptedStyleSheets"):void 0;return null!==r&&-1!==r&&s&&i?(Object.defineProperty(n,"adoptedStyleSheets",{configurable:i.configurable,enumerable:i.enumerable,get(){return fu([i,"access",t=>t.get,"optionalAccess",t=>t.call,"call",t=>t(this)])},set(t){const n=fu([i,"access",t=>t.set,"optionalAccess",t=>t.call,"call",e=>e(this,t)]);if(null!==r&&-1!==r)try{e.adoptStyleSheets(t,r)}catch(t){}return n}}),lu((()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:i.configurable,enumerable:i.enumerable,get:i.get,set:i.set})}))):()=>{}}function _u(t,e={}){const n=t.doc.defaultView;if(!n)return()=>{};let r;t.recordDOM&&(r=pu(t,t.doc));const s=function({mousemoveCb:t,sampling:e,doc:n,mirror:r}){if(!1===e.mousemove)return()=>{};const s="number"==typeof e.mousemove?e.mousemove:50,i="number"==typeof e.mousemoveCallback?e.mousemoveCallback:500;let o,c=[];const a=_a(lu((e=>{const n=Date.now()-o;t(c.map((t=>(t.timeOffset-=n,t))),e),c=[],o=null})),i),u=lu(_a(lu((t=>{const e=du(t),{clientX:n,clientY:s}=Da(t)?t.changedTouches[0]:t;o||(o=Ea()),c.push({x:n,y:s,id:r.getId(e),timeOffset:Ea()-o}),a("undefined"!=typeof DragEvent&&t instanceof DragEvent?Ya.Drag:t instanceof MouseEvent?Ya.MouseMove:Ya.TouchMove)})),s,{trailing:!1})),l=[wa("mousemove",u,n),wa("touchmove",u,n),wa("drag",u,n)];return lu((()=>{l.forEach((t=>t()))}))}(t),i=mu(t),o=yu(t),c=function({viewportResizeCb:t},{win:e}){let n=-1,r=-1;return wa("resize",lu(_a(lu((()=>{const e=Ca(),s=Aa();n===e&&r===s||(t({width:Number(s),height:Number(e)}),n=e,r=s)})),200)),e)}(t,{win:n}),a=bu(t),u=function({mediaInteractionCb:t,blockClass:e,blockSelector:n,unblockSelector:r,mirror:s,sampling:i,doc:o}){const c=lu((o=>_a(lu((i=>{const c=du(i);if(!c||Oa(c,e,n,r,!0))return;const{currentTime:a,volume:u,muted:l,playbackRate:f}=c;t({type:o,id:s.getId(c),currentTime:a,volume:u,muted:l,playbackRate:f})})),i.media||500))),a=[wa("play",c(0),o),wa("pause",c(1),o),wa("seeked",c(2),o),wa("volumechange",c(3),o),wa("ratechange",c(4),o)];return lu((()=>{a.forEach((t=>t()))}))}(t);let l=()=>{},f=()=>{},h=()=>{},d=()=>{};t.recordDOM&&(l=function({styleSheetRuleCb:t,mirror:e,stylesheetManager:n},{win:r}){if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype)return()=>{};const s=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(s,{apply:lu(((r,s,i)=>{const[o,c]=i,{id:a,styleId:u}=ku(s,e,n.styleMirror);return(a&&-1!==a||u&&-1!==u)&&t({id:a,styleId:u,adds:[{rule:o,index:c}]}),r.apply(s,i)}))});const i=r.CSSStyleSheet.prototype.deleteRule;let o,c;r.CSSStyleSheet.prototype.deleteRule=new Proxy(i,{apply:lu(((r,s,i)=>{const[o]=i,{id:c,styleId:a}=ku(s,e,n.styleMirror);return(c&&-1!==c||a&&-1!==a)&&t({id:c,styleId:a,removes:[{index:o}]}),r.apply(s,i)}))}),r.CSSStyleSheet.prototype.replace&&(o=r.CSSStyleSheet.prototype.replace,r.CSSStyleSheet.prototype.replace=new Proxy(o,{apply:lu(((r,s,i)=>{const[o]=i,{id:c,styleId:a}=ku(s,e,n.styleMirror);return(c&&-1!==c||a&&-1!==a)&&t({id:c,styleId:a,replace:o}),r.apply(s,i)}))})),r.CSSStyleSheet.prototype.replaceSync&&(c=r.CSSStyleSheet.prototype.replaceSync,r.CSSStyleSheet.prototype.replaceSync=new Proxy(c,{apply:lu(((r,s,i)=>{const[o]=i,{id:c,styleId:a}=ku(s,e,n.styleMirror);return(c&&-1!==c||a&&-1!==a)&&t({id:c,styleId:a,replaceSync:o}),r.apply(s,i)}))}));const a={};Iu("CSSGroupingRule")?a.CSSGroupingRule=r.CSSGroupingRule:(Iu("CSSMediaRule")&&(a.CSSMediaRule=r.CSSMediaRule),Iu("CSSConditionRule")&&(a.CSSConditionRule=r.CSSConditionRule),Iu("CSSSupportsRule")&&(a.CSSSupportsRule=r.CSSSupportsRule));const u={};return Object.entries(a).forEach((([r,s])=>{u[r]={insertRule:s.prototype.insertRule,deleteRule:s.prototype.deleteRule},s.prototype.insertRule=new Proxy(u[r].insertRule,{apply:lu(((r,s,i)=>{const[o,c]=i,{id:a,styleId:u}=ku(s.parentStyleSheet,e,n.styleMirror);return(a&&-1!==a||u&&-1!==u)&&t({id:a,styleId:u,adds:[{rule:o,index:[...wu(s),c||0]}]}),r.apply(s,i)}))}),s.prototype.deleteRule=new Proxy(u[r].deleteRule,{apply:lu(((r,s,i)=>{const[o]=i,{id:c,styleId:a}=ku(s.parentStyleSheet,e,n.styleMirror);return(c&&-1!==c||a&&-1!==a)&&t({id:c,styleId:a,removes:[{index:[...wu(s),o]}]}),r.apply(s,i)}))})})),lu((()=>{r.CSSStyleSheet.prototype.insertRule=s,r.CSSStyleSheet.prototype.deleteRule=i,o&&(r.CSSStyleSheet.prototype.replace=o),c&&(r.CSSStyleSheet.prototype.replaceSync=c),Object.entries(a).forEach((([t,e])=>{e.prototype.insertRule=u[t].insertRule,e.prototype.deleteRule=u[t].deleteRule}))}))}(t,{win:n}),f=Su(t,t.doc),h=function({styleDeclarationCb:t,mirror:e,ignoreCSSAttributes:n,stylesheetManager:r},{win:s}){const i=s.CSSStyleDeclaration.prototype.setProperty;s.CSSStyleDeclaration.prototype.setProperty=new Proxy(i,{apply:lu(((s,o,c)=>{const[a,u,l]=c;if(n.has(a))return i.apply(o,[a,u,l]);const{id:f,styleId:h}=ku(fu([o,"access",t=>t.parentRule,"optionalAccess",t=>t.parentStyleSheet]),e,r.styleMirror);return(f&&-1!==f||h&&-1!==h)&&t({id:f,styleId:h,set:{property:a,value:u,priority:l},index:wu(o.parentRule)}),s.apply(o,c)}))});const o=s.CSSStyleDeclaration.prototype.removeProperty;return s.CSSStyleDeclaration.prototype.removeProperty=new Proxy(o,{apply:lu(((s,i,c)=>{const[a]=c;if(n.has(a))return o.apply(i,[a]);const{id:u,styleId:l}=ku(fu([i,"access",t=>t.parentRule,"optionalAccess",t=>t.parentStyleSheet]),e,r.styleMirror);return(u&&-1!==u||l&&-1!==l)&&t({id:u,styleId:l,remove:{property:a},index:wu(i.parentRule)}),s.apply(i,c)}))}),lu((()=>{s.CSSStyleDeclaration.prototype.setProperty=i,s.CSSStyleDeclaration.prototype.removeProperty=o}))}(t,{win:n}),t.collectFonts&&(d=function({fontCb:t,doc:e}){const n=e.defaultView;if(!n)return()=>{};const r=[],s=new WeakMap,i=n.FontFace;n.FontFace=function(t,e,n){const r=new i(t,e,n);return s.set(r,{family:t,buffer:"string"!=typeof e,descriptors:n,fontSource:"string"==typeof e?e:JSON.stringify(Array.from(new Uint8Array(e)))}),r};const o=Ia(e.fonts,"add",(function(e){return function(n){return Ha(lu((()=>{const e=s.get(n);e&&(t(e),s.delete(n))})),0),e.apply(this,[n])}}));return r.push((()=>{n.FontFace=i})),r.push(o),lu((()=>{r.forEach((t=>t()))}))}(t)));const p=function(t){const{doc:e,mirror:n,blockClass:r,blockSelector:s,unblockSelector:i,selectionCb:o}=t;let c=!0;const a=lu((()=>{const t=e.getSelection();if(!t||c&&fu([t,"optionalAccess",t=>t.isCollapsed]))return;c=t.isCollapsed||!1;const a=[],u=t.rangeCount||0;for(let e=0;e<u;e++){const o=t.getRangeAt(e),{startContainer:c,startOffset:u,endContainer:l,endOffset:f}=o;Oa(c,r,s,i,!0)||Oa(l,r,s,i,!0)||a.push({start:n.getId(c),startOffset:u,end:n.getId(l),endOffset:f})}o({ranges:a})}));return a(),wa("selectionchange",a)}(t),m=function({doc:t,customElementCb:e}){const n=t.defaultView;return n&&n.customElements?Ia(n.customElements,"define",(function(t){return function(n,r,s){try{e({define:{name:n}})}catch(t){}return t.apply(this,[n,r,s])}})):()=>{}}(t),y=[];for(const e of t.plugins)y.push(e.observer(e.callback,n,e.options));return lu((()=>{hu.forEach((t=>t.reset())),fu([r,"optionalAccess",t=>t.disconnect,"call",t=>t()]),s(),i(),o(),c(),a(),u(),l(),f(),h(),d(),p(),m(),y.forEach((t=>t()))}))}function Tu(t){return void 0!==window[t]}function Iu(t){return Boolean(void 0!==window[t]&&window[t].prototype&&"insertRule"in window[t].prototype&&"deleteRule"in window[t].prototype)}class Eu{constructor(t){this.generateIdFn=t,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(t,e,n,r){const s=n||this.getIdToRemoteIdMap(t),i=r||this.getRemoteIdToIdMap(t);let o=s.get(e);return o||(o=this.generateIdFn(),s.set(e,o),i.set(o,e)),o}getIds(t,e){const n=this.getIdToRemoteIdMap(t),r=this.getRemoteIdToIdMap(t);return e.map((e=>this.getId(t,e,n,r)))}getRemoteId(t,e,n){const r=n||this.getRemoteIdToIdMap(t);if("number"!=typeof e)return e;const s=r.get(e);return s||-1}getRemoteIds(t,e){const n=this.getRemoteIdToIdMap(t);return e.map((e=>this.getRemoteId(t,e,n)))}reset(t){if(!t)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(t),this.iframeRemoteIdToIdMap.delete(t)}getIdToRemoteIdMap(t){let e=this.iframeIdToRemoteIdMap.get(t);return e||(e=new Map,this.iframeIdToRemoteIdMap.set(t,e)),e}getRemoteIdToIdMap(t){let e=this.iframeRemoteIdToIdMap.get(t);return e||(e=new Map,this.iframeRemoteIdToIdMap.set(t,e)),e}}function xu(t){let e,n=t[0],r=1;for(;r<t.length;){const s=t[r],i=t[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(e=n,n=i(n)):"call"!==s&&"optionalCall"!==s||(n=i(((...t)=>n.call(e,...t))),e=void 0)}return n}class Cu{constructor(t){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new Eu(Vc),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=t.mutationCb,this.wrappedEmit=t.wrappedEmit,this.stylesheetManager=t.stylesheetManager,this.recordCrossOriginIframes=t.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new Eu(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=t.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(t){this.iframes.set(t,!0),t.contentWindow&&this.crossOriginIframeMap.set(t.contentWindow,t)}addLoadListener(t){this.loadListener=t}attachIframe(t,e){this.mutationCb({adds:[{parentId:this.mirror.getId(t),nextId:null,node:e}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),xu([this,"access",t=>t.loadListener,"optionalCall",e=>e(t)]);const n=Va(t);n&&n.adoptedStyleSheets&&n.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(n.adoptedStyleSheets,this.mirror.getId(n))}handleMessage(t){const e=t;if("rrweb"!==e.data.type||e.origin!==e.data.origin)return;if(!t.source)return;const n=this.crossOriginIframeMap.get(t.source);if(!n)return;const r=this.transformCrossOriginEvent(n,e.data.event);r&&this.wrappedEmit(r,e.data.isCheckout)}transformCrossOriginEvent(t,e){switch(e.type){case Ka.FullSnapshot:{this.crossOriginIframeMirror.reset(t),this.crossOriginIframeStyleMirror.reset(t),this.replaceIdOnNode(e.data.node,t);const n=e.data.node.id;return this.crossOriginIframeRootIdMap.set(t,n),this.patchRootIdOnNode(e.data.node,n),{timestamp:e.timestamp,type:Ka.IncrementalSnapshot,data:{source:Ya.Mutation,adds:[{parentId:this.mirror.getId(t),nextId:null,node:e.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case Ka.Meta:case Ka.Load:case Ka.DomContentLoaded:return!1;case Ka.Plugin:return e;case Ka.Custom:return this.replaceIds(e.data.payload,t,["id","parentId","previousId","nextId"]),e;case Ka.IncrementalSnapshot:switch(e.data.source){case Ya.Mutation:return e.data.adds.forEach((e=>{this.replaceIds(e,t,["parentId","nextId","previousId"]),this.replaceIdOnNode(e.node,t);const n=this.crossOriginIframeRootIdMap.get(t);n&&this.patchRootIdOnNode(e.node,n)})),e.data.removes.forEach((e=>{this.replaceIds(e,t,["parentId","id"])})),e.data.attributes.forEach((e=>{this.replaceIds(e,t,["id"])})),e.data.texts.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case Ya.Drag:case Ya.TouchMove:case Ya.MouseMove:return e.data.positions.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case Ya.ViewportResize:return!1;case Ya.MediaInteraction:case Ya.MouseInteraction:case Ya.Scroll:case Ya.CanvasMutation:case Ya.Input:return this.replaceIds(e.data,t,["id"]),e;case Ya.StyleSheetRule:case Ya.StyleDeclaration:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleId"]),e;case Ya.Font:return e;case Ya.Selection:return e.data.ranges.forEach((e=>{this.replaceIds(e,t,["start","end"])})),e;case Ya.AdoptedStyleSheet:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleIds"]),xu([e,"access",t=>t.data,"access",t=>t.styles,"optionalAccess",t=>t.forEach,"call",e=>e((e=>{this.replaceStyleIds(e,t,["styleId"])}))]),e}}return!1}replace(t,e,n,r){for(const s of r)(Array.isArray(e[s])||"number"==typeof e[s])&&(Array.isArray(e[s])?e[s]=t.getIds(n,e[s]):e[s]=t.getId(n,e[s]));return e}replaceIds(t,e,n){return this.replace(this.crossOriginIframeMirror,t,e,n)}replaceStyleIds(t,e,n){return this.replace(this.crossOriginIframeStyleMirror,t,e,n)}replaceIdOnNode(t,e){this.replaceIds(t,e,["id","rootId"]),"childNodes"in t&&t.childNodes.forEach((t=>{this.replaceIdOnNode(t,e)}))}patchRootIdOnNode(t,e){t.type===Ac.Document||t.rootId||(t.rootId=e),"childNodes"in t&&t.childNodes.forEach((t=>{this.patchRootIdOnNode(t,e)}))}}class Au{constructor(t){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=t.mutationCb,this.scrollCb=t.scrollCb,this.bypassOptions=t.bypassOptions,this.mirror=t.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(t,e){if(!Oc(t))return;if(this.shadowDoms.has(t))return;this.shadowDoms.add(t),this.bypassOptions.canvasManager.addShadowRoot(t);const n=pu({...this.bypassOptions,doc:e,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},t);this.restoreHandlers.push((()=>n.disconnect())),this.restoreHandlers.push(yu({...this.bypassOptions,scrollCb:this.scrollCb,doc:t,mirror:this.mirror})),Ha((()=>{t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(t.adoptedStyleSheets,this.mirror.getId(t.host)),this.restoreHandlers.push(Su({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},t))}),0)}observeAttachShadow(t){const e=Va(t),n=function(t){try{return t.contentWindow}catch(t){}}(t);e&&n&&this.patchAttachShadow(n.Element,e)}patchAttachShadow(t,e){const n=this;this.restoreHandlers.push(Ia(t.prototype,"attachShadow",(function(t){return function(r){const s=t.call(this,r);return this.shadowRoot&&Ua(this)&&n.addShadowRoot(this.shadowRoot,e),s}})))}reset(){this.restoreHandlers.forEach((t=>{try{t()}catch(t){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet,this.bypassOptions.canvasManager.resetShadowRoots()}}class Ru{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}addWindow(){}addShadowRoot(){}resetShadowRoots(){}}class Ou{constructor(t){this.trackedLinkElements=new WeakSet,this.styleMirror=new Pa,this.mutationCb=t.mutationCb,this.adoptedStyleSheetCb=t.adoptedStyleSheetCb}attachLinkElement(t,e){"_cssText"in e.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:e.id,attributes:e.attributes}]}),this.trackLinkElement(t)}trackLinkElement(t){this.trackedLinkElements.has(t)||(this.trackedLinkElements.add(t),this.trackStylesheetInLinkElement(t))}adoptStyleSheets(t,e){if(0===t.length)return;const n={id:e,styleIds:[]},r=[];for(const e of t){let t;this.styleMirror.has(e)?t=this.styleMirror.getId(e):(t=this.styleMirror.add(e),r.push({styleId:t,rules:Array.from(e.rules||CSSRule,((t,e)=>({rule:$c(t),index:e})))})),n.styleIds.push(t)}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(t){}}class Mu{constructor(){this.nodeMap=new WeakMap,this.active=!1}inOtherBuffer(t,e){const n=this.nodeMap.get(t);return n&&Array.from(n).some((t=>t!==e))}add(t,e){this.active||(this.active=!0,function(...t){qa("requestAnimationFrame")(...t)}((()=>{this.nodeMap=new WeakMap,this.active=!1}))),this.nodeMap.set(t,(this.nodeMap.get(t)||new Set).add(e))}destroy(){}}let $u,Du;try{if(2!==Array.from([1],(t=>2*t))[0]){const t=document.createElement("iframe");document.body.appendChild(t),Array.from=ys([t,"access",t=>t.contentWindow,"optionalAccess",t=>t.Array,"access",t=>t.from])||Array.from,document.body.removeChild(t)}}catch(t){console.debug("Unable to override Array.from",t)}const Lu=new Dc;function Nu(t={}){const{emit:e,checkoutEveryNms:n,checkoutEveryNth:r,blockClass:s="rr-block",blockSelector:i=null,unblockSelector:o=null,ignoreClass:c="rr-ignore",ignoreSelector:a=null,maskAllText:u=!1,maskTextClass:l="rr-mask",unmaskTextClass:f=null,maskTextSelector:h=null,unmaskTextSelector:d=null,inlineStylesheet:p=!0,maskAllInputs:m,maskInputOptions:y,slimDOMOptions:g,maskAttributeFn:v,maskInputFn:b,maskTextFn:w,maxCanvasSize:k=null,packFn:S,sampling:_={},dataURLOptions:T={},mousemoveWait:I,recordDOM:E=!0,recordCanvas:x=!1,recordCrossOriginIframes:C=!1,recordAfter:A=("DOMContentLoaded"===t.recordAfter?t.recordAfter:"load"),userTriggeredOnInput:R=!1,collectFonts:O=!1,inlineImages:M=!1,plugins:$,keepIframeSrcFn:D=(()=>!1),ignoreCSSAttributes:L=new Set([]),errorHandler:N,onMutation:F,getCanvasManager:P}=t;au(N);const j=!C||window.parent===window;let B=!1;if(!j)try{window.parent.document&&(B=!1)}catch(t){B=!0}if(j&&!e)throw new Error("emit function is required");if(!j&&!B)return()=>{};void 0!==I&&void 0===_.mousemove&&(_.mousemove=I),Lu.reset();const U=!0===m?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==y?y:{},z=!0===g||"all"===g?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===g,headMetaDescKeywords:"all"===g}:g||{};let q;!function(t=window){"NodeList"in t&&!t.NodeList.prototype.forEach&&(t.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach&&(t.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...t)=>{let e=t[0];if(!(0 in t))throw new TypeError("1 argument is required");do{if(this===e)return!0}while(e=e&&e.parentNode);return!1})}();let H=0;const W=t=>{for(const e of $||[])e.eventProcessor&&(t=e.eventProcessor(t));return S&&!B&&(t=S(t)),t};$u=(t,s)=>{const i=t;if(i.timestamp=Ea(),!ys([hu,"access",t=>t[0],"optionalAccess",t=>t.isFrozen,"call",t=>t()])||i.type===Ka.FullSnapshot||i.type===Ka.IncrementalSnapshot&&i.data.source===Ya.Mutation||hu.forEach((t=>t.unfreeze())),j)ys([e,"optionalCall",t=>t(W(i),s)]);else if(B){const t={type:"rrweb",event:W(i),origin:window.location.origin,isCheckout:s};window.parent.postMessage(t,"*")}if(i.type===Ka.FullSnapshot)q=i,H=0;else if(i.type===Ka.IncrementalSnapshot){if(i.data.source===Ya.Mutation&&i.data.isAttachIframe)return;H++;const t=r&&H>=r,e=n&&q&&i.timestamp-q.timestamp>n;(t||e)&&tt(!0)}};const J=t=>{$u({type:Ka.IncrementalSnapshot,data:{source:Ya.Mutation,...t}})},K=t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.Scroll,...t}}),Y=t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.CanvasMutation,...t}}),G=new Ou({mutationCb:J,adoptedStyleSheetCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.AdoptedStyleSheet,...t}})}),X=new Cu({mirror:Lu,mutationCb:J,stylesheetManager:G,recordCrossOriginIframes:C,wrappedEmit:$u});for(const t of $||[])t.getMirror&&t.getMirror({nodeMirror:Lu,crossOriginIframeMirror:X.crossOriginIframeMirror,crossOriginIframeStyleMirror:X.crossOriginIframeStyleMirror});const V=new Mu,Q=function(t,e){try{return t?t(e):new Ru}catch(t){return console.warn("Unable to initialize CanvasManager"),new Ru}}(P,{mirror:Lu,win:window,mutationCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.CanvasMutation,...t}}),recordCanvas:x,blockClass:s,blockSelector:i,unblockSelector:o,maxCanvasSize:k,sampling:_.canvas,dataURLOptions:T,errorHandler:N}),Z=new Au({mutationCb:J,scrollCb:K,bypassOptions:{onMutation:F,blockClass:s,blockSelector:i,unblockSelector:o,maskAllText:u,maskTextClass:l,unmaskTextClass:f,maskTextSelector:h,unmaskTextSelector:d,inlineStylesheet:p,maskInputOptions:U,dataURLOptions:T,maskAttributeFn:v,maskTextFn:w,maskInputFn:b,recordCanvas:x,inlineImages:M,sampling:_,slimDOMOptions:z,iframeManager:X,stylesheetManager:G,canvasManager:Q,keepIframeSrcFn:D,processedNodeManager:V},mirror:Lu}),tt=(t=!1)=>{if(!E)return;$u({type:Ka.Meta,data:{href:window.location.href,width:Aa(),height:Ca()}},t),G.reset(),Z.init(),hu.forEach((t=>t.lock()));const e=function(t,e){const{mirror:n=new Dc,blockClass:r="rr-block",blockSelector:s=null,unblockSelector:i=null,maskAllText:o=!1,maskTextClass:c="rr-mask",unmaskTextClass:a=null,maskTextSelector:u=null,unmaskTextSelector:l=null,inlineStylesheet:f=!0,inlineImages:h=!1,recordCanvas:d=!1,maskAllInputs:p=!1,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOM:v=!1,dataURLOptions:b,preserveWhiteSpace:w,onSerialize:k,onIframeLoad:S,iframeLoadTimeout:_,onStylesheetLoad:T,stylesheetLoadTimeout:I,keepIframeSrcFn:E=(()=>!1)}=e||{};return va(t,{doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:i,maskAllText:o,maskTextClass:c,unmaskTextClass:a,maskTextSelector:u,unmaskTextSelector:l,skipChild:!1,inlineStylesheet:f,maskInputOptions:!0===p?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===p?{}:p,maskAttributeFn:m,maskTextFn:y,maskInputFn:g,slimDOMOptions:!0===v||"all"===v?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===v,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===v?{}:v,dataURLOptions:b,inlineImages:h,recordCanvas:d,preserveWhiteSpace:w,onSerialize:k,onIframeLoad:S,iframeLoadTimeout:_,onStylesheetLoad:T,stylesheetLoadTimeout:I,keepIframeSrcFn:E,newlyAddedElement:!1})}(document,{mirror:Lu,blockClass:s,blockSelector:i,unblockSelector:o,maskAllText:u,maskTextClass:l,unmaskTextClass:f,maskTextSelector:h,unmaskTextSelector:d,inlineStylesheet:p,maskAllInputs:U,maskAttributeFn:v,maskInputFn:b,maskTextFn:w,slimDOM:z,dataURLOptions:T,recordCanvas:x,inlineImages:M,onSerialize:t=>{La(t,Lu)&&X.addIframe(t),Na(t,Lu)&&G.trackLinkElement(t),Fa(t)&&Z.addShadowRoot(t.shadowRoot,document)},onIframeLoad:(t,e)=>{X.attachIframe(t,e),t.contentWindow&&Q.addWindow(t.contentWindow),Z.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{G.attachLinkElement(t,e)},keepIframeSrcFn:D});if(!e)return console.warn("Failed to snapshot the document");$u({type:Ka.FullSnapshot,data:{node:e,initialOffset:xa(window)}}),hu.forEach((t=>t.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&G.adoptStyleSheets(document.adoptedStyleSheets,Lu.getId(document))};Du=tt;try{const t=[],e=t=>lu(_u)({onMutation:F,mutationCb:J,mousemoveCb:(t,e)=>$u({type:Ka.IncrementalSnapshot,data:{source:e,positions:t}}),mouseInteractionCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.MouseInteraction,...t}}),scrollCb:K,viewportResizeCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.ViewportResize,...t}}),inputCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.Input,...t}}),mediaInteractionCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.MediaInteraction,...t}}),styleSheetRuleCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.StyleSheetRule,...t}}),styleDeclarationCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.StyleDeclaration,...t}}),canvasMutationCb:Y,fontCb:t=>$u({type:Ka.IncrementalSnapshot,data:{source:Ya.Font,...t}}),selectionCb:t=>{$u({type:Ka.IncrementalSnapshot,data:{source:Ya.Selection,...t}})},customElementCb:t=>{$u({type:Ka.IncrementalSnapshot,data:{source:Ya.CustomElement,...t}})},blockClass:s,ignoreClass:c,ignoreSelector:a,maskAllText:u,maskTextClass:l,unmaskTextClass:f,maskTextSelector:h,unmaskTextSelector:d,maskInputOptions:U,inlineStylesheet:p,sampling:_,recordDOM:E,recordCanvas:x,inlineImages:M,userTriggeredOnInput:R,collectFonts:O,doc:t,maskAttributeFn:v,maskInputFn:b,maskTextFn:w,keepIframeSrcFn:D,blockSelector:i,unblockSelector:o,slimDOMOptions:z,dataURLOptions:T,mirror:Lu,iframeManager:X,stylesheetManager:G,shadowDomManager:Z,processedNodeManager:V,canvasManager:Q,ignoreCSSAttributes:L,plugins:ys([$,"optionalAccess",t=>t.filter,"call",t=>t((t=>t.observer)),"optionalAccess",t=>t.map,"call",t=>t((t=>({observer:t.observer,options:t.options,callback:e=>$u({type:Ka.Plugin,data:{plugin:t.name,payload:e}})})))])||[]},{});X.addLoadListener((n=>{try{t.push(e(n.contentDocument))}catch(t){console.warn(t)}}));const n=()=>{tt(),t.push(e(document))};return"interactive"===document.readyState||"complete"===document.readyState?n():(t.push(wa("DOMContentLoaded",(()=>{$u({type:Ka.DomContentLoaded,data:{}}),"DOMContentLoaded"===A&&n()}))),t.push(wa("load",(()=>{$u({type:Ka.Load,data:{}}),"load"===A&&n()}),window))),()=>{t.forEach((t=>t())),V.destroy(),Du=void 0,uu()}}catch(t){console.warn(t)}}Nu.mirror=Lu,Nu.takeFullSnapshot=function(t){if(!Du)throw new Error("please take full snapshot after start recording");Du(t)};const Fu=3;function Pu(t){return t>9999999999?t:1e3*t}function ju(t){return t>9999999999?t/1e3:t}function Bu(t,e){"sentry.transaction"!==e.category&&(["ui.click","ui.input"].includes(e.category)?t.triggerUserActivity():t.checkAndHandleExpiredSession(),t.addUpdate((()=>(t.throttledAddEvent({type:Ka.Custom,timestamp:1e3*(e.timestamp||0),data:{tag:"breadcrumb",payload:Qe(e,10,1e3)}}),"console"===e.category))))}const Uu="button,a";function zu(t){return t.closest(Uu)||t}function qu(t){const e=Hu(t);return e&&e instanceof Element?zu(e):e}function Hu(t){return function(t){return"object"==typeof t&&!!t&&"target"in t}(t)?t.target:t}let Wu;function Ju(t){return Wu||(Wu=[],Z(bc,"open",(function(t){return function(...e){if(Wu)try{Wu.forEach((t=>t()))}catch(t){}return t.apply(bc,e)}}))),Wu.push(t),()=>{const e=Wu?Wu.indexOf(t):-1;e>-1&&Wu.splice(e,1)}}const Ku=new Set([Ya.Mutation,Ya.StyleSheetRule,Ya.StyleDeclaration,Ya.AdoptedStyleSheet,Ya.CanvasMutation,Ya.Selection,Ya.MediaInteraction]);class Yu{constructor(t,e,n=Bu){this.Dt=0,this.Lt=0,this.Nt=[],this.Ft=e.timeout/1e3,this.Pt=e.threshold/1e3,this.jt=e.scrollTimeout/1e3,this._replay=t,this.Bt=e.ignoreSelector,this.Ut=n}addListeners(){const t=Ju((()=>{this.Dt=Xu()}));this.zt=()=>{t(),this.Nt=[],this.Dt=0,this.Lt=0}}removeListeners(){this.zt&&this.zt(),this.qt&&clearTimeout(this.qt)}handleClick(t,e){if(function(t,e){if(!Gu.includes(t.tagName))return!0;if("INPUT"===t.tagName&&!["submit","button"].includes(t.getAttribute("type")||""))return!0;if("A"===t.tagName&&(t.hasAttribute("download")||t.hasAttribute("target")&&"_self"!==t.getAttribute("target")))return!0;if(e&&t.matches(e))return!0;return!1}(e,this.Bt)||!function(t){return!(!t.data||"number"!=typeof t.data.nodeId||!t.timestamp)}(t))return;const n={timestamp:ju(t.timestamp),clickBreadcrumb:t,clickCount:0,node:e};this.Nt.some((t=>t.node===n.node&&Math.abs(t.timestamp-n.timestamp)<1))||(this.Nt.push(n),1===this.Nt.length&&this.Ht())}registerMutation(t=Date.now()){this.Dt=ju(t)}registerScroll(t=Date.now()){this.Lt=ju(t)}registerClick(t){const e=zu(t);this.Wt(e)}Wt(t){this.Jt(t).forEach((t=>{t.clickCount++}))}Jt(t){return this.Nt.filter((e=>e.node===t))}Kt(){const t=[],e=Xu();this.Nt.forEach((n=>{!n.mutationAfter&&this.Dt&&(n.mutationAfter=n.timestamp<=this.Dt?this.Dt-n.timestamp:void 0),!n.scrollAfter&&this.Lt&&(n.scrollAfter=n.timestamp<=this.Lt?this.Lt-n.timestamp:void 0),n.timestamp+this.Ft<=e&&t.push(n)}));for(const e of t){const t=this.Nt.indexOf(e);t>-1&&(this.Yt(e),this.Nt.splice(t,1))}this.Nt.length&&this.Ht()}Yt(t){const e=this._replay,n=t.scrollAfter&&t.scrollAfter<=this.jt,r=t.mutationAfter&&t.mutationAfter<=this.Pt,s=!n&&!r,{clickCount:i,clickBreadcrumb:o}=t;if(s){const n=1e3*Math.min(t.mutationAfter||this.Ft,this.Ft),r=n<1e3*this.Ft?"mutation":"timeout",s={type:"default",message:o.message,timestamp:o.timestamp,category:"ui.slowClickDetected",data:{...o.data,url:bc.location.href,route:e.getCurrentRoute(),timeAfterClickMs:n,endReason:r,clickCount:i||1}};this.Ut(e,s)}else if(i>1){const t={type:"default",message:o.message,timestamp:o.timestamp,category:"ui.multiClick",data:{...o.data,url:bc.location.href,route:e.getCurrentRoute(),clickCount:i,metric:!0}};this.Ut(e,t)}}Ht(){this.qt&&clearTimeout(this.qt),this.qt=yo((()=>this.Kt()),1e3)}}const Gu=["A","BUTTON","INPUT"];function Xu(){return Date.now()/1e3}function Vu(t,e){try{if(!function(t){return t.type===Fu}(e))return;const{source:n}=e.data;if(Ku.has(n)&&t.registerMutation(e.timestamp),n===Ya.Scroll&&t.registerScroll(e.timestamp),function(t){return t.data.source===Ya.MouseInteraction}(e)){const{type:n,id:r}=e.data,s=Nu.mirror.getNode(r);s instanceof HTMLElement&&n===Ga.Click&&t.registerClick(s)}}catch(t){}}function Qu(t){return{timestamp:Date.now()/1e3,type:"default",...t}}var Zu;!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(Zu||(Zu={}));const tl=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]);function el(t){const e={};!t["data-sentry-component"]&&t["data-sentry-element"]&&(t["data-sentry-component"]=t["data-sentry-element"]);for(const n in t)if(tl.has(n)){let r=n;"data-testid"!==n&&"data-test-id"!==n||(r="testId"),e[r]=t[n]}return e}const nl=t=>e=>{if(!t.isEnabled())return;const n=function(t){const{target:e,message:n}=function(t){const e="click"===t.name;let n,r=null;try{r=e?qu(t.event):Hu(t.event),n=W(r,{maxStringLength:200})||"<unknown>"}catch(t){n="<unknown>"}return{target:r,message:n}}(t);return Qu({category:`ui.${t.name}`,...rl(e,n)})}(e);if(!n)return;const r="click"===e.name,s=r?e.event:void 0;var i,o,c;!(r&&t.clickDetector&&s&&s.target)||s.altKey||s.metaKey||s.ctrlKey||s.shiftKey||(i=t.clickDetector,o=n,c=qu(e.event),i.handleClick(o,c)),Bu(t,n)};function rl(t,e){const n=Nu.mirror.getId(t),r=n&&Nu.mirror.getNode(n),s=r&&Nu.mirror.getMeta(r),i=s&&function(t){return t.type===Zu.Element}(s)?s:null;return{message:e,data:i?{nodeId:n,node:{id:n,tagName:i.tagName,textContent:Array.from(i.childNodes).map((t=>t.type===Zu.Text&&t.textContent)).filter(Boolean).map((t=>t.trim())).join(""),attributes:el(i.attributes)}}:{}}}function sl(t,e){if(!t.isEnabled())return;t.updateUserActivity();const n=function(t){const{metaKey:e,shiftKey:n,ctrlKey:r,altKey:s,key:i,target:o}=t;if(!o||function(t){return"INPUT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable}(o)||!i)return null;const c=e||r||s,a=1===i.length;if(!c&&a)return null;const u=W(o,{maxStringLength:200})||"<unknown>",l=rl(o,u);return Qu({category:"ui.keyDown",message:u,data:{...l.data,metaKey:e,shiftKey:n,ctrlKey:r,altKey:s,key:i}})}(e);n&&Bu(t,n)}const il={resource:function(t){const{entryType:e,initiatorType:n,name:r,responseEnd:s,startTime:i,decodedBodySize:o,encodedBodySize:c,responseStatus:a,transferSize:u}=t;if(["fetch","xmlhttprequest"].includes(n))return null;return{type:`${e}.${n}`,start:al(i),end:al(s),name:r,data:{size:u,statusCode:a,decodedBodySize:o,encodedBodySize:c}}},paint:function(t){const{duration:e,entryType:n,name:r,startTime:s}=t,i=al(s);return{type:n,name:r,start:i,end:i+e,data:void 0}},navigation:function(t){const{entryType:e,name:n,decodedBodySize:r,duration:s,domComplete:i,encodedBodySize:o,domContentLoadedEventStart:c,domContentLoadedEventEnd:a,domInteractive:u,loadEventStart:l,loadEventEnd:f,redirectCount:h,startTime:d,transferSize:p,type:m}=t;if(0===s)return null;return{type:`${e}.${m}`,start:al(d),end:al(i),name:n,data:{size:p,decodedBodySize:r,encodedBodySize:o,duration:s,domInteractive:u,domContentLoadedEventStart:c,domContentLoadedEventEnd:a,loadEventStart:l,loadEventEnd:f,domComplete:i,redirectCount:h}}}};function ol(t,e){return({metric:n})=>{e.replayPerformanceEntries.push(t(n))}}function cl(t){const e=il[t.entryType];return e?e(t):null}function al(t){return((ft||bc.performance.timeOrigin)+t)/1e3}function ul(t){const e=t.entries[t.entries.length-1];return pl(t,"largest-contentful-paint",e&&e.element?[e.element]:void 0)}function ll(t){return void 0!==t.sources}function fl(t){const e=[],n=[];for(const r of t.entries)if(ll(r)){const t=[];for(const e of r.sources)if(e.node){n.push(e.node);const r=Nu.mirror.getId(e.node);r&&t.push(r)}e.push({value:r.value,nodeIds:t.length?t:void 0})}return pl(t,"cumulative-layout-shift",n,e)}function hl(t){const e=t.entries[t.entries.length-1];return pl(t,"first-input-delay",e&&e.target?[e.target]:void 0)}function dl(t){const e=t.entries[t.entries.length-1];return pl(t,"interaction-to-next-paint",e&&e.target?[e.target]:void 0)}function pl(t,e,n,r){const s=t.value,i=t.rating,o=al(s);return{type:"web-vital",name:e,start:o,end:o,data:{value:s,size:s,rating:i,nodeIds:n?n.map((t=>Nu.mirror.getId(t))):void 0,attributions:r}}}const ml='var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,e){for(var i=new n(31),a=0;a<31;++a)i[a]=e+=1<<t[a-1];var s=new r(i[30]);for(a=1;a<30;++a)for(var o=i[a];o<i[a+1];++o)s[o]=o-i[a]<<5|a;return{b:i,r:s}},o=s(e,2),f=o.b,h=o.r;f[28]=258,h[258]=28;for(var l=s(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,a=0,s=new n(r);a<i;++a)t[a]&&++s[t[a]-1];var o,f=new n(r);for(a=1;a<r;++a)f[a]=f[a-1]+s[a-1]<<1;if(e){o=new n(1<<r);var h=15-r;for(a=0;a<i;++a)if(t[a])for(var l=a<<4|t[a],c=r-t[a],v=f[t[a]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>h]=l}else for(o=new n(i),a=0;a<i;++a)t[a]&&(o[a]=u[f[t[a]-1]++]>>15-t[a]);return o},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var w=new t(32);for(c=0;c<32;++c)w[c]=5;var p=d(g,9,0),y=d(w,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},_=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},x=function(r,e){for(var i=[],a=0;a<r.length;++a)r[a]&&i.push({s:a,f:r[a]});var s=i.length,o=i.slice();if(!s)return{t:F,l:0};if(1==s){var f=new t(i[0].s+1);return f[i[0].s]=1,{t:f,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var h=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:h.f+l.f,l:h,r:l};c!=s-1;)h=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:h.f+l.f,l:h,r:l};var d=o[0].s;for(a=1;a<s;++a)o[a].s>d&&(d=o[a].s);var g=new n(d+1),w=A(i[c-1],g,0);if(w>e){a=0;var p=0,y=w-e,m=1<<y;for(o.sort((function(t,n){return g[n.s]-g[t.s]||t.f-n.f}));a<s;++a){var b=o[a].s;if(!(g[b]>e))break;p+=m-(1<<w-g[b]),g[b]=e}for(p>>=y;p>0;){var M=o[a].s;g[M]<e?p-=1<<e-g[M]++-1:++a}for(;a>=0&&p;--a){var E=o[a].s;g[E]==e&&(--g[E],++p)}w=e}return{t:new t(g),l:w}},A=function(t,n,r){return-1==t.s?Math.max(A(t.l,n,r+1),A(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,a=t[0],s=1,o=function(t){e[i++]=t},f=1;f<=r;++f)if(t[f]==a&&f!=r)++s;else{if(!a&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(a),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(a);s=1,a=t[f]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<e;++a)t[i+a+4]=r[a];return 8*(i+4+e)},U=function(t,r,s,o,f,h,l,u,c,v,m){z(r,m++,s),++f[256];for(var b=x(f,15),M=b.t,E=b.l,A=x(h,15),U=A.t,C=A.l,F=D(M),I=F.c,S=F.n,L=D(U),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=x(q,7),H=G.t,J=G.l,K=19;K>4&&!H[a[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(f,g)+T(h,w)+l,X=T(f,M)+T(h,U)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(U,C,0),R=U;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[a[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=p,P=g,Q=y,R=w;for(B=0;B<u;++B){var rt=o[B];if(rt>255){_(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;_(r,m,Q[et]),m+=R[et],et>3&&(_(r,m,rt>>5&8191),m+=i[et])}else _(r,m,N[rt]),m+=P[rt]}return _(r,m,N[256]),m+P[256]},C=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}},L=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,a=0|r.length,s=0;s!=a;){for(var o=Math.min(s+2655,a);s<o;++s)i+=e+=r[s];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},O=function(a,s,o,f,u){if(!u&&(u={l:1},s.dictionary)){var c=s.dictionary.subarray(-32768),v=new t(c.length+a.length);v.set(c),v.set(a,c.length),a=v,u.w=c.length}return function(a,s,o,f,u,c){var v=c.z||a.length,d=new t(f+v+5*(1+Math.ceil(v/7e3))+u),g=d.subarray(f,d.length-u),w=c.l,p=7&(c.r||0);if(s){p&&(g[0]=c.r>>3);for(var y=C[s-1],M=y>>13,E=8191&y,z=(1<<o)-1,_=c.p||new n(32768),x=c.h||new n(z+1),A=Math.ceil(o/3),D=2*A,T=function(t){return(a[t]^a[t+1]<<A^a[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=x[H];if(_[J]=K,x[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!w)){p=U(a,g,0,F,I,S,O,q,G,j-G,p),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(a[j+Q]==a[j+Q-W]){for(var $=0;$<Z&&a[j+$]==a[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-_[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=_[J])&32767}if(R){F[q++]=268435456|h[Q]<<18|l[R];var it=31&h[Q],at=31&l[R];O+=e[it]+i[at],++I[257+it],++S[at],B=j+Q,++L}else F[q++]=a[j],++I[a[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=a[j],++I[a[j]];p=U(a,g,w,F,I,S,O,q,G,j-G,p),w||(c.r=7&p|g[p/8|0]<<3,p-=7,c.h=x,c.p=_,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+w;j+=65535){var st=j+65535;st>=v&&(g[p/8|0]=w,st=v),p=k(g,p+1,a.subarray(j,st))}c.i=v}return b(d,0,f+m(p)+u)}(a,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):12+s.mem,o,f,u)},j=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},q=function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&j(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}},B=function(t){return 10+(t.filename?t.filename.length+1:0)},G=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(O(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;a&&(this.b.set(n.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(a),32768),this.s.z=n.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n}();var H=function(){function t(t,n){this.c=L(),this.v=1,G.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),G.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=O(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=L();i.p(n.dictionary),j(t,2,i.d())}}(r,this.o),this.v=0),n&&j(r,r.length-4,this.c.d()),this.ondata(r,n)},t}(),J="undefined"!=typeof TextEncoder&&new TextEncoder,K="undefined"!=typeof TextDecoder&&new TextDecoder;try{K.decode(F,{stream:!0})}catch(t){}var N=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(P(t),this.d=n||!1)},t}();function P(n,r){if(J)return J.encode(n);for(var e=n.length,i=new t(n.length+(n.length>>1)),a=0,s=function(t){i[a++]=t},o=0;o<e;++o){if(a+5>i.length){var f=new t(a+8+(e-o<<1));f.set(i),i=f}var h=n.charCodeAt(o);h<128||r?s(h):h<2048?(s(192|h>>6),s(128|63&h)):h>55295&&h<57344?(s(240|(h=65536+(1047552&h)|1023&n.charCodeAt(++o))>>18),s(128|h>>12&63),s(128|h>>6&63),s(128|63&h)):(s(224|h>>12),s(128|h>>6&63),s(128|63&h))}return b(i,0,a)}function Q(t){return function(t,n){n||(n={});var r=S(),e=t.length;r.p(t);var i=O(t,n,B(n),8),a=i.length;return q(i,n),j(i,a-8,r.d()),j(i,a-4,e),i}(P(t))}const R=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(const r of t)n+=r.length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new H,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new N(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},V={clear:()=>{R.clear()},addEvent:t=>R.addEvent(t),finish:()=>R.finish(),compress:t=>Q(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in V&&"function"==typeof V[n])try{const t=V[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});';class yl extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class gl{constructor(){this.events=[],this.Gt=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(t){const e=JSON.stringify(t).length;if(this.Gt+=e,this.Gt>Ic)throw new yl;this.events.push(t)}finish(){return new Promise((t=>{const e=this.events;this.clear(),t(JSON.stringify(e))}))}clear(){this.events=[],this.Gt=0,this.hasCheckout=!1}getEarliestTimestamp(){const t=this.events.map((t=>t.timestamp)).sort()[0];return t?Pu(t):null}}class vl{constructor(t){this.Xt=t,this.Vt=0}ensureReady(){return this.Qt||(this.Qt=new Promise(((t,e)=>{this.Xt.addEventListener("message",(({data:n})=>{n.success?t():e()}),{once:!0}),this.Xt.addEventListener("error",(t=>{e(t)}),{once:!0})}))),this.Qt}destroy(){this.Xt.terminate()}postMessage(t,e){const n=this.Zt();return new Promise(((r,s)=>{const i=({data:e})=>{const o=e;o.method===t&&o.id===n&&(this.Xt.removeEventListener("message",i),o.success?r(o.response):s(new Error("Error in compression worker")))};this.Xt.addEventListener("message",i),this.Xt.postMessage({id:n,method:t,arg:e})}))}Zt(){return this.Vt++}}class bl{constructor(t){this.Xt=new vl(t),this.te=null,this.Gt=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return!!this.te}get type(){return"worker"}ensureReady(){return this.Xt.ensureReady()}destroy(){this.Xt.destroy()}addEvent(t){const e=Pu(t.timestamp);(!this.te||e<this.te)&&(this.te=e);const n=JSON.stringify(t);return this.Gt+=n.length,this.Gt>Ic?Promise.reject(new yl):this.ee(n)}finish(){return this.ne()}clear(){this.te=null,this.Gt=0,this.hasCheckout=!1,this.Xt.postMessage("clear").then(null,(t=>{}))}getEarliestTimestamp(){return this.te}ee(t){return this.Xt.postMessage("addEvent",t)}async ne(){const t=await this.Xt.postMessage("finish");return this.te=null,this.Gt=0,t}}class wl{constructor(t){this.re=new gl,this.se=new bl(t),this.ie=this.re,this.oe=this.ce()}get waitForCheckout(){return this.ie.waitForCheckout}get type(){return this.ie.type}get hasEvents(){return this.ie.hasEvents}get hasCheckout(){return this.ie.hasCheckout}set hasCheckout(t){this.ie.hasCheckout=t}set waitForCheckout(t){this.ie.waitForCheckout=t}destroy(){this.re.destroy(),this.se.destroy()}clear(){return this.ie.clear()}getEarliestTimestamp(){return this.ie.getEarliestTimestamp()}addEvent(t){return this.ie.addEvent(t)}async finish(){return await this.ensureWorkerIsLoaded(),this.ie.finish()}ensureWorkerIsLoaded(){return this.oe}async ce(){try{await this.se.ensureReady()}catch(t){return}await this.ae()}async ae(){const{events:t,hasCheckout:e,waitForCheckout:n}=this.re,r=[];for(const e of t)r.push(this.se.addEvent(e));this.se.hasCheckout=e,this.se.waitForCheckout=n,this.ie=this.se;try{await Promise.all(r),this.re.clear()}catch(t){}}}function kl({useCompression:t,workerUrl:e}){if(t&&window.Worker){const t=function(t){try{const e=t||function(){if("undefined"==typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__)return function(){const t=new Blob([ml]);return URL.createObjectURL(t)}();return""}();if(!e)return;const n=new Worker(e);return new wl(n)}catch(t){}}(e);if(t)return t}return new gl}function Sl(){try{return"sessionStorage"in bc&&!!bc.sessionStorage}catch(t){return!1}}function _l(t){!function(){if(!Sl())return;try{bc.sessionStorage.removeItem(wc)}catch(t){}}(),t.session=void 0}function Tl(t){return void 0!==t&&Math.random()<t}function Il(t){const e=Date.now();return{id:t.id||ht(),started:t.started||e,lastActivity:t.lastActivity||e,segmentId:t.segmentId||0,sampled:t.sampled,previousSessionId:t.previousSessionId}}function El(t){if(Sl())try{bc.sessionStorage.setItem(wc,JSON.stringify(t))}catch(t){}}function xl({sessionSampleRate:t,allowBuffering:e,stickySession:n=!1},{previousSessionId:r}={}){const s=function(t,e){return Tl(t)?"session":!!e&&"buffer"}(t,e),i=Il({sampled:s,previousSessionId:r});return n&&El(i),i}function Cl(t,e,n=+new Date){return null===t||void 0===e||e<0||0!==e&&t+e<=n}function Al(t,{maxReplayDuration:e,sessionIdleExpire:n,targetTime:r=Date.now()}){return Cl(t.started,e,r)||Cl(t.lastActivity,n,r)}function Rl(t,{sessionIdleExpire:e,maxReplayDuration:n}){return!!Al(t,{sessionIdleExpire:e,maxReplayDuration:n})&&("buffer"!==t.sampled||0!==t.segmentId)}function Ol({sessionIdleExpire:t,maxReplayDuration:e,previousSessionId:n},r){const s=r.stickySession&&function(){if(!Sl())return null;try{const t=bc.sessionStorage.getItem(wc);return t?Il(JSON.parse(t)):null}catch(t){return null}}();return s?Rl(s,{sessionIdleExpire:t,maxReplayDuration:e})?xl(r,{previousSessionId:s.id}):s:xl(r,{previousSessionId:n})}function Ml(t,e,n){return!!Dl(t,e)&&($l(t,e,n),!0)}async function $l(t,e,n){const{eventBuffer:r}=t;if(!r||r.waitForCheckout&&!n)return null;const s="buffer"===t.recordingMode;try{n&&s&&r.clear(),n&&(r.hasCheckout=!0,r.waitForCheckout=!1);const i=function(t,e){try{if("function"==typeof e&&function(t){return t.type===Ka.Custom}(t))return e(t)}catch(t){return null}return t}(e,t.getOptions().beforeAddRecordingEvent);if(!i)return;return await r.addEvent(i)}catch(e){const n=e&&e instanceof yl,i=n?"addEventSizeExceeded":"addEvent";if(n&&s)return r.clear(),r.waitForCheckout=!0,null;t.handleException(e),await t.stop({reason:i});const o=zt();o&&o.recordDroppedEvent("internal_sdk_error","replay")}}function Dl(t,e){if(!t.eventBuffer||t.isPaused()||!t.isEnabled())return!1;const n=Pu(e.timestamp);return!(n+t.timeouts.sessionIdlePause<Date.now())&&!(n>t.getContext().initialTimestamp+t.getOptions().maxReplayDuration)}function Ll(t){return!t.type}function Nl(t){return"transaction"===t.type}function Fl(t){return"feedback"===t.type}function Pl(t){return(e,n)=>{if(!t.isEnabled()||!Ll(e)&&!Nl(e))return;const r=n&&n.statusCode;!r||r<200||r>=300||(Nl(e)?function(t,e){const n=t.getContext();e.contexts&&e.contexts.trace&&e.contexts.trace.trace_id&&n.traceIds.size<100&&n.traceIds.add(e.contexts.trace.trace_id)}(t,e):function(t,e){const n=t.getContext();e.event_id&&n.errorIds.size<100&&n.errorIds.add(e.event_id);if("buffer"!==t.recordingMode||!e.tags||!e.tags.replayId)return;const{beforeErrorSampling:r}=t.getOptions();if("function"==typeof r&&!r(e))return;yo((async()=>{try{await t.sendBufferedReplayOrFlush()}catch(e){t.handleException(e)}}))}(t,e))}}function jl(t){return e=>{t.isEnabled()&&Ll(e)&&function(t,e){const n=e.exception&&e.exception.values&&e.exception.values[0]&&e.exception.values[0].value;if("string"!=typeof n)return;if(n.match(/(reactjs\.org\/docs\/error-decoder\.html\?invariant=|react\.dev\/errors\/)(418|419|422|423|425)/)||n.match(/(does not match server-rendered HTML|Hydration failed because)/i)){Bu(t,Qu({category:"replay.hydrate-error",data:{url:K()}}))}}(t,e)}}function Bl(t){const e=zt();e&&e.on("beforeAddBreadcrumb",(e=>function(t,e){if(!t.isEnabled()||!Ul(e))return;const n=function(t){if(!Ul(t)||["fetch","xhr","sentry.event","sentry.transaction"].includes(t.category)||t.category.startsWith("ui."))return null;if("console"===t.category)return function(t){const e=t.data&&t.data.arguments;if(!Array.isArray(e)||0===e.length)return Qu(t);let n=!1;const r=e.map((t=>{if(!t)return t;if("string"==typeof t)return t.length>Tc?(n=!0,`${t.slice(0,Tc)}…`):t;if("object"==typeof t)try{const e=Qe(t,7);return JSON.stringify(e).length>Tc?(n=!0,`${JSON.stringify(e,null,2).slice(0,Tc)}…`):e}catch(t){}return t}));return Qu({...t,data:{...t.data,arguments:r,...n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(t);return Qu(t)}(e);n&&Bu(t,n)}(t,e)))}function Ul(t){return!!t.category}function zl(){const t=Pt().getPropagationContext().dsc;t&&delete t.replay_id;const e=Ae();if(e){delete Je(e).replay_id}}function ql(t){return Object.assign(((e,n)=>{if(!t.isEnabled()||t.isPaused())return e;if(function(t){return"replay_event"===t.type}(e))return delete e.breadcrumbs,e;if(!Ll(e)&&!Nl(e)&&!Fl(e))return e;if(!t.checkAndHandleExpiredSession())return zl(),e;if(Fl(e))return t.flush(),e.contexts.feedback.replay_id=t.getSessionId(),function(t,e){t.triggerUserActivity(),t.addUpdate((()=>!e.timestamp||(t.throttledAddEvent({type:Ka.Custom,timestamp:1e3*e.timestamp,data:{tag:"breadcrumb",payload:{timestamp:e.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:e.event_id}}}}),!1)))}(t,e),e;if(function(t,e){return!(t.type||!t.exception||!t.exception.values||!t.exception.values.length||!e.originalException||!e.originalException.__rrweb__)}(e,n)&&!t.getOptions()._experiments.captureExceptions)return null;const r=function(t,e){return"buffer"===t.recordingMode&&e.message!==Sc&&!(!e.exception||e.type)&&Tl(t.getOptions().errorSampleRate)}(t,e);return(r||"session"===t.recordingMode)&&(e.tags={...e.tags,replayId:t.getSessionId()}),e}),{id:"Replay"})}function Hl(t,e){return e.map((({type:e,start:n,end:r,name:s,data:i})=>{const o=t.throttledAddEvent({type:Ka.Custom,timestamp:n,data:{tag:"performanceSpan",payload:{op:e,description:s,startTimestamp:n,endTimestamp:r,data:i}}});return"string"==typeof o?Promise.resolve(null):o}))}function Wl(t){return e=>{if(!t.isEnabled())return;const n=function(t){const{from:e,to:n}=t,r=Date.now()/1e3;return{type:"navigation.push",start:r,end:r,name:n,data:{previous:e}}}(e);null!==n&&(t.getContext().urls.push(n.name),t.triggerUserActivity(),t.addUpdate((()=>(Hl(t,[n]),!1))))}}function Jl(t,e){t.isEnabled()&&(null!==e&&(Sr(e.name,zt())||t.addUpdate((()=>(Hl(t,[e]),!0)))))}function Kl(t){if(!t)return;const e=new TextEncoder;try{if("string"==typeof t)return e.encode(t).length;if(t instanceof URLSearchParams)return e.encode(t.toString()).length;if(t instanceof FormData){const n=ef(t);return e.encode(n).length}if(t instanceof Blob)return t.size;if(t instanceof ArrayBuffer)return t.byteLength}catch(t){}}function Yl(t){if(!t)return;const e=parseInt(t,10);return isNaN(e)?void 0:e}function Gl(t){try{if("string"==typeof t)return[t];if(t instanceof URLSearchParams)return[t.toString()];if(t instanceof FormData)return[ef(t)];if(!t)return[void 0]}catch(t){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}function Xl(t,e){if(!t)return{headers:{},size:void 0,_meta:{warnings:[e]}};const n={...t._meta},r=n.warnings||[];return n.warnings=[...r,e],t._meta=n,t}function Vl(t,e){if(!e)return null;const{startTimestamp:n,endTimestamp:r,url:s,method:i,statusCode:o,request:c,response:a}=e;return{type:t,start:n/1e3,end:r/1e3,name:s,data:ot({method:i,statusCode:o,request:c,response:a})}}function Ql(t){return{headers:{},size:t,_meta:{warnings:["URL_SKIPPED"]}}}function Zl(t,e,n){if(!e&&0===Object.keys(t).length)return;if(!e)return{headers:t};if(!n)return{headers:t,size:e};const r={headers:t,size:e},{body:s,warnings:i}=function(t){if(!t||"string"!=typeof t)return{body:t};const e=t.length>_c,n=function(t){const e=t[0],n=t[t.length-1];return"["===e&&"]"===n||"{"===e&&"}"===n}(t);if(e){const e=t.slice(0,_c);return n?{body:e,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${e}…`,warnings:["TEXT_TRUNCATED"]}}if(n)try{return{body:JSON.parse(t)}}catch(t){}return{body:t}}(n);return r.body=s,i&&i.length>0&&(r._meta={warnings:i}),r}function tf(t,e){return Object.entries(t).reduce(((n,[r,s])=>{const i=r.toLowerCase();return e.includes(i)&&t[r]&&(n[i]=s),n}),{})}function ef(t){return new URLSearchParams(t).toString()}function nf(t,e){const n=function(t,e=bc.document.baseURI){if(t.startsWith("http://")||t.startsWith("https://")||t.startsWith(bc.location.origin))return t;const n=new URL(t,e);if(n.origin!==new URL(e).origin)return t;const r=n.href;if(!t.endsWith("/")&&r.endsWith("/"))return r.slice(0,-1);return r}(t);return Q(n,e)}async function rf(t,e,n){try{const r=await async function(t,e,n){const r=Date.now(),{startTimestamp:s=r,endTimestamp:i=r}=e,{url:o,method:c,status_code:a=0,request_body_size:u,response_body_size:l}=t.data,f=nf(o,n.networkDetailAllowUrls)&&!nf(o,n.networkDetailDenyUrls),h=f?function({networkCaptureBodies:t,networkRequestHeaders:e},n,r){const s=n?function(t,e){if(1===t.length&&"string"!=typeof t[0])return cf(t[0],e);if(2===t.length)return cf(t[1],e);return{}}(n,e):{};if(!t)return Zl(s,r,void 0);const i=sf(n),[o,c]=Gl(i),a=Zl(s,r,o);if(c)return Xl(a,c);return a}(n,e.input,u):Ql(u),d=await async function(t,{networkCaptureBodies:e,networkResponseHeaders:n},r,s){if(!t&&void 0!==s)return Ql(s);const i=r?of(r.headers,n):{};if(!r||!e&&void 0!==s)return Zl(i,s,void 0);const[o,c]=await async function(t){const e=function(t){try{return t.clone()}catch(t){}}(t);if(!e)return[void 0,"BODY_PARSE_ERROR"];try{const t=await function(t){return new Promise(((e,n)=>{const r=yo((()=>n(new Error("Timeout while trying to read response body"))),500);(async function(t){return await t.text()})(t).then((t=>e(t)),(t=>n(t))).finally((()=>clearTimeout(r)))}))}(e);return[t]}catch(t){return t instanceof Error&&t.message.indexOf("Timeout")>-1?[void 0,"BODY_PARSE_TIMEOUT"]:[void 0,"BODY_PARSE_ERROR"]}}(r),a=function(t,{networkCaptureBodies:e,responseBodySize:n,captureDetails:r,headers:s}){try{const i=t&&t.length&&void 0===n?Kl(t):n;return r?Zl(s,i,e?t:void 0):Ql(i)}catch(t){return Zl(s,n,void 0)}}(o,{networkCaptureBodies:e,responseBodySize:s,captureDetails:t,headers:i});if(c)return Xl(a,c);return a}(f,n,e.response,l);return{startTimestamp:s,endTimestamp:i,url:o,method:c,statusCode:a,request:h,response:d}}(t,e,n),s=Vl("resource.fetch",r);Jl(n.replay,s)}catch(t){}}function sf(t=[]){if(2===t.length&&"object"==typeof t[1])return t[1].body}function of(t,e){const n={};return e.forEach((e=>{t.get(e)&&(n[e]=t.get(e))})),n}function cf(t,e){if(!t)return{};const n=t.headers;return n?n instanceof Headers?of(n,e):Array.isArray(n)?{}:tf(n,e):{}}async function af(t,e,n){try{const r=function(t,e,n){const r=Date.now(),{startTimestamp:s=r,endTimestamp:i=r,input:o,xhr:c}=e,{url:a,method:u,status_code:l=0,request_body_size:f,response_body_size:h}=t.data;if(!a)return null;if(!c||!nf(a,n.networkDetailAllowUrls)||nf(a,n.networkDetailDenyUrls)){return{startTimestamp:s,endTimestamp:i,url:a,method:u,statusCode:l,request:Ql(f),response:Ql(h)}}const d=c[go],p=d?tf(d.request_headers,n.networkRequestHeaders):{},m=tf(function(t){const e=t.getAllResponseHeaders();if(!e)return{};return e.split("\r\n").reduce(((t,e)=>{const[n,r]=e.split(": ");return r&&(t[n.toLowerCase()]=r),t}),{})}(c),n.networkResponseHeaders),[y,g]=n.networkCaptureBodies?Gl(o):[void 0],[v,b]=n.networkCaptureBodies?function(t){try{return[t.responseText]}catch(t){}try{return function(t,e){try{if("string"==typeof t)return[t];if(t instanceof Document)return[t.body.outerHTML];if("json"===e&&t&&"object"==typeof t)return[JSON.stringify(t)];if(!t)return[void 0]}catch(t){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}(t.response,t.responseType)}catch(t){}return[void 0]}(c):[void 0],w=Zl(p,f,y),k=Zl(m,h,v);return{startTimestamp:s,endTimestamp:i,url:a,method:u,statusCode:l,request:g?Xl(w,g):w,response:b?Xl(k,b):k}}(t,e,n),s=Vl("resource.xhr",r);Jl(n.replay,s)}catch(t){}}function uf(t,e){const{xhr:n,input:r}=e;if(!n)return;const s=Kl(r),i=n.getResponseHeader("content-length")?Yl(n.getResponseHeader("content-length")):function(t,e){try{return Kl("json"===e&&t&&"object"==typeof t?JSON.stringify(t):t)}catch(t){return}}(n.response,n.responseType);void 0!==s&&(t.data.request_body_size=s),void 0!==i&&(t.data.response_body_size=i)}function lf(t){const e=zt();try{const{networkDetailAllowUrls:n,networkDetailDenyUrls:r,networkCaptureBodies:s,networkRequestHeaders:i,networkResponseHeaders:o}=t.getOptions(),c={replay:t,networkDetailAllowUrls:n,networkDetailDenyUrls:r,networkCaptureBodies:s,networkRequestHeaders:i,networkResponseHeaders:o};e&&e.on("beforeAddBreadcrumb",((t,e)=>function(t,e,n){if(!e.data)return;try{(function(t){return"xhr"===t.category})(e)&&function(t){return t&&t.xhr}(n)&&(uf(e,n),af(e,n,t)),function(t){return"fetch"===t.category}(e)&&function(t){return t&&t.response}(n)&&(!function(t,e){const{input:n,response:r}=e,s=Kl(n?sf(n):void 0),i=r?Yl(r.headers.get("content-length")):void 0;void 0!==s&&(t.data.request_body_size=s),void 0!==i&&(t.data.response_body_size=i)}(e,n),rf(e,n,t))}catch(t){}}(c,t,e)))}catch(t){}}function ff(t){const{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:r}=t,s=Date.now()/1e3;return{type:"memory",name:"memory",start:s,end:s,data:{memory:{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:r}}}}const hf=n.navigator;function df(t){let e=!1;return(n,r)=>{if(!t.checkAndHandleExpiredSession())return;const s=r||!e;e=!0,t.clickDetector&&Vu(t.clickDetector,n),t.addUpdate((()=>{if("buffer"===t.recordingMode&&s&&t.setInitialState(),!Ml(t,n,s))return!0;if(!s)return!1;const e=t.session;if(function(t,e){if(!e||!t.session||0!==t.session.segmentId)return;Ml(t,function(t){const e=t.getOptions();return{type:Ka.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:t.isRecordingCanvas(),sessionSampleRate:e.sessionSampleRate,errorSampleRate:e.errorSampleRate,useCompressionOption:e.useCompression,blockAllMedia:e.blockAllMedia,maskAllText:e.maskAllText,maskAllInputs:e.maskAllInputs,useCompression:!!t.eventBuffer&&"worker"===t.eventBuffer.type,networkDetailHasUrls:e.networkDetailAllowUrls.length>0,networkCaptureBodies:e.networkCaptureBodies,networkRequestHasHeaders:e.networkRequestHeaders.length>0,networkResponseHasHeaders:e.networkResponseHeaders.length>0}}}}(t),!1)}(t,s),"buffer"===t.recordingMode&&e&&t.eventBuffer){const n=t.eventBuffer.getEarliestTimestamp();n&&(e.started=n,t.getOptions().stickySession&&El(e))}return e&&e.previousSessionId||"session"===t.recordingMode&&t.flush(),!0}))}}async function pf({recordingData:t,replayId:e,segmentId:n,eventContext:r,timestamp:s,session:i}){const o=function({recordingData:t,headers:e}){let n;const r=`${JSON.stringify(e)}\n`;if("string"==typeof t)n=`${r}${t}`;else{const e=(new TextEncoder).encode(r);n=new Uint8Array(e.length+t.length),n.set(e),n.set(t,e.length)}return n}({recordingData:t,headers:{segment_id:n}}),{urls:c,errorIds:a,traceIds:u,initialTimestamp:l}=r,f=zt(),h=Pt(),d=f&&f.getTransport(),p=f&&f.getDsn();if(!(f&&d&&p&&i.sampled))return bt({});const m={type:kc,replay_start_timestamp:l/1e3,timestamp:s/1e3,error_ids:a,trace_ids:u,urls:c,replay_id:e,segment_id:n,replay_type:i.sampled},y=await async function({client:t,scope:e,replayId:n,event:r}){const s={event_id:n,integrations:"object"!=typeof t._integrations||null===t._integrations||Array.isArray(t._integrations)?void 0:Object.keys(t._integrations)};t.emit("preprocessEvent",r,s);const i=await Un(t.getOptions(),r,s,e,t,jt());if(!i)return null;i.platform=i.platform||"javascript";const o=t.getSdkMetadata(),{name:c,version:a}=o&&o.sdk||{};return i.sdk={...i.sdk,name:c||"sentry.javascript.unknown",version:a||"0.0.0"},i}({scope:h,client:f,replayId:e,event:m});if(!y)return f.recordDroppedEvent("event_processor","replay",m),bt({});delete y.sdkProcessingMetadata;const g=function(t,e,n,r){return en(hn(t,fn(t),r,n),[[{type:"replay_event"},t],[{type:"replay_recording",length:"string"==typeof e?(new TextEncoder).encode(e).length:e.length},e]])}(y,o,p,f.getOptions().tunnel);let v;try{v=await d.send(g)}catch(t){const e=new Error(Sc);try{e.cause=t}catch(t){}throw e}if("number"==typeof v.statusCode&&(v.statusCode<200||v.statusCode>=300))throw new mf(v.statusCode);const b=vr({},v);if(gr(b,"replay"))throw new yf(b);return v}class mf extends Error{constructor(t){super(`Transport returned status code ${t}`)}}class yf extends Error{constructor(t){super("Rate limit hit"),this.rateLimits=t}}async function gf(t,e={count:0,interval:5e3}){const{recordingData:n,onError:r}=t;if(n.length)try{return await pf(t),!0}catch(n){if(n instanceof mf||n instanceof yf)throw n;if(Wn("Replays",{ue:e.count}),r&&r(n),e.count>=3){const t=new Error(`${Sc} - max retries exceeded`);try{t.cause=n}catch(t){}throw t}return e.interval*=++e.count,new Promise(((n,r)=>{yo((async()=>{try{await gf(t,e),n(!0)}catch(t){r(t)}}),e.interval)}))}}const vf="__THROTTLED";function bf(t,e,n){const r=new Map;let s=!1;return(...i)=>{const o=Math.floor(Date.now()/1e3);if((t=>{const e=t-n;r.forEach(((t,n)=>{n<e&&r.delete(n)}))})(o),[...r.values()].reduce(((t,e)=>t+e),0)>=e){const t=s;return s=!0,t?"__SKIPPED":vf}s=!1;const c=r.get(o)||0;return r.set(o,c+1),t(...i)}}class wf{constructor({options:t,recordingOptions:e}){wf.prototype.__init.call(this),wf.prototype.__init2.call(this),wf.prototype.__init3.call(this),wf.prototype.__init4.call(this),wf.prototype.__init5.call(this),wf.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this.le=Date.now(),this.bt=!1,this.fe=!1,this.he=!1,this.de=!1,this.pe={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this.me=e,this.ct=t,this.ye=function(t,e,n){let r,s,i;const o=n&&n.maxWait?Math.max(n.maxWait,e):0;function c(){return a(),r=t(),r}function a(){void 0!==s&&clearTimeout(s),void 0!==i&&clearTimeout(i),s=i=void 0}function u(){return s&&clearTimeout(s),s=yo(c,e),o&&void 0===i&&(i=yo(c,o)),r}return u.cancel=a,u.flush=function(){return void 0!==s||void 0!==i?c():r},u}((()=>this.ge()),this.ct.flushMinDelay,{maxWait:this.ct.flushMaxDelay}),this.ve=bf(((t,e)=>function(t,e,n){return Dl(t,e)?$l(t,e,n):Promise.resolve(null)}(this,t,e)),300,5);const{slowClickTimeout:n,slowClickIgnoreSelectors:r}=this.getOptions(),s=n?{threshold:Math.min(3e3,n),timeout:n,scrollTimeout:300,ignoreSelector:r?r.join(","):""}:void 0;s&&(this.clickDetector=new Yu(this,s))}getContext(){return this.pe}isEnabled(){return this.bt}isPaused(){return this.fe}isRecordingCanvas(){return Boolean(this._canvas)}getOptions(){return this.ct}handleException(t){this.ct.onError&&this.ct.onError(t)}initializeSampling(t){const{errorSampleRate:e,sessionSampleRate:n}=this.ct,r=e<=0&&n<=0;this.he=r,r||(this.be(t),this.session&&!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",this.we()))}start(){if(this.bt&&"session"===this.recordingMode)return;if(this.bt&&"buffer"===this.recordingMode)return;this.ke();const t=Ol({maxReplayDuration:this.ct.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire},{stickySession:this.ct.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=t,this.we()}startBuffering(){if(this.bt)return;const t=Ol({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this.ct.maxReplayDuration},{stickySession:this.ct.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=t,this.recordingMode="buffer",this.we()}startRecording(){try{const t=this._canvas;this.Se=Nu({...this.me,..."buffer"===this.recordingMode?{checkoutEveryNms:6e4}:this.ct._experiments.continuousCheckout&&{checkoutEveryNms:Math.max(36e4,this.ct._experiments.continuousCheckout)},emit:df(this),.../iPhone|iPad|iPod/i.test(hf&&hf.userAgent||"")||/Macintosh/i.test(hf&&hf.userAgent||"")&&hf&&hf.maxTouchPoints&&hf.maxTouchPoints>1?{sampling:{mousemove:!1}}:{},onMutation:this._e,...t?{recordCanvas:t.recordCanvas,getCanvasManager:t.getCanvasManager,sampling:t.sampling,dataURLOptions:t.dataURLOptions}:{}})}catch(t){this.handleException(t)}}stopRecording(){try{return this.Se&&(this.Se(),this.Se=void 0),!0}catch(t){return this.handleException(t),!1}}async stop({forceFlush:t=!1,reason:e}={}){if(this.bt){this.bt=!1;try{zl(),this.Te(),this.stopRecording(),this.ye.cancel(),t&&await this.ge({force:!0}),this.eventBuffer&&this.eventBuffer.destroy(),this.eventBuffer=null,_l(this)}catch(t){this.handleException(t)}}}pause(){this.fe||(this.fe=!0,this.stopRecording())}resume(){this.fe&&this.Ie()&&(this.fe=!1,this.startRecording())}async sendBufferedReplayOrFlush({continueRecording:t=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();const e=Date.now();await this.flushImmediate();const n=this.stopRecording();t&&n&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this.ke(e),this.Ee(e),this.xe()),this.startRecording())}addUpdate(t){const e=t();"buffer"!==this.recordingMode&&!0!==e&&this.ye()}triggerUserActivity(){if(this.ke(),this.Se)this.checkAndHandleExpiredSession(),this.Ee();else{if(!this.Ie())return;this.resume()}}updateUserActivity(){this.ke(),this.Ee()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this.ye()}flushImmediate(){return this.ye(),this.ye.flush()}cancelFlush(){this.ye.cancel()}getSessionId(){return this.session&&this.session.id}checkAndHandleExpiredSession(){if(!(this.le&&Cl(this.le,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled))return!!this.Ie();this.pause()}setInitialState(){const t=`${bc.location.pathname}${bc.location.hash}${bc.location.search}`,e=`${bc.location.origin}${t}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this.Ce(),this.pe.initialUrl=e,this.pe.initialTimestamp=Date.now(),this.pe.urls.push(e)}throttledAddEvent(t,e){const n=this.ve(t,e);if(n===vf){const t=Qu({category:"replay.throttled"});this.addUpdate((()=>!Ml(this,{type:5,timestamp:t.timestamp||0,data:{tag:"breadcrumb",payload:t,metric:!0}})))}return n}getCurrentRoute(){const t=this.lastActiveSpan||Ae(),e=t&&Ce(t),n=(e&&ke(e).data||{})[Jt];if(e&&n&&["route","custom"].includes(n))return ke(e).description}we(){this.setInitialState(),this.Ee(),this.eventBuffer=kl({useCompression:this.ct.useCompression,workerUrl:this.ct.workerUrl}),this.Te(),this.Ae(),this.bt=!0,this.fe=!1,this.startRecording()}be(t){const e=this.ct.errorSampleRate>0,n=Ol({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this.ct.maxReplayDuration,previousSessionId:t},{stickySession:this.ct.stickySession,sessionSampleRate:this.ct.sessionSampleRate,allowBuffering:e});this.session=n}Ie(){if(!this.session)return!1;const t=this.session;return!Rl(t,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this.ct.maxReplayDuration})||(this.Re(t),!1)}async Re(t){this.bt&&(await this.stop({reason:"refresh session"}),this.initializeSampling(t.id))}Ae(){try{bc.document.addEventListener("visibilitychange",this.Oe),bc.addEventListener("blur",this.Me),bc.addEventListener("focus",this.$e),bc.addEventListener("keydown",this.De),this.clickDetector&&this.clickDetector.addListeners(),this.de||(!function(t){const e=zt();co(nl(t)),lo(Wl(t)),Bl(t),lf(t),Qn(ql(t)),e&&(e.on("beforeSendEvent",jl(t)),e.on("afterSendEvent",Pl(t)),e.on("createDsc",(e=>{const n=t.getSessionId();n&&t.isEnabled()&&"session"===t.recordingMode&&t.checkAndHandleExpiredSession()&&(e.replay_id=n)})),e.on("spanStart",(e=>{t.lastActiveSpan=e})),e.on("spanEnd",(e=>{t.lastActiveSpan=e})),e.on("beforeSendFeedback",((e,n)=>{const r=t.getSessionId();n&&n.includeReplay&&t.isEnabled()&&r&&e.contexts&&e.contexts.feedback&&(e.contexts.feedback.replay_id=r)})))}(this),this.de=!0)}catch(t){this.handleException(t)}this.Le=function(t){function e(e){t.performanceEntries.includes(e)||t.performanceEntries.push(e)}function n({entries:t}){t.forEach(e)}const r=[];return["navigation","paint","resource"].forEach((t=>{r.push(Oi(t,n))})),r.push(Ci(ol(ul,t)),xi(ol(fl,t)),Ai(ol(hl,t)),Ri(ol(dl,t))),()=>{r.forEach((t=>t()))}}(this)}Te(){try{bc.document.removeEventListener("visibilitychange",this.Oe),bc.removeEventListener("blur",this.Me),bc.removeEventListener("focus",this.$e),bc.removeEventListener("keydown",this.De),this.clickDetector&&this.clickDetector.removeListeners(),this.Le&&this.Le()}catch(t){this.handleException(t)}}__init(){this.Oe=()=>{"visible"===bc.document.visibilityState?this.Ne():this.Fe()}}__init2(){this.Me=()=>{const t=Qu({category:"ui.blur"});this.Fe(t)}}__init3(){this.$e=()=>{const t=Qu({category:"ui.focus"});this.Ne(t)}}__init4(){this.De=t=>{sl(this,t)}}Fe(t){if(!this.session)return;Al(this.session,{maxReplayDuration:this.ct.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(t&&this.Pe(t),this.conditionalFlush())}Ne(t){if(!this.session)return;this.checkAndHandleExpiredSession()&&t&&this.Pe(t)}ke(t=Date.now()){this.le=t}Ee(t=Date.now()){this.session&&(this.session.lastActivity=t,this.xe())}Pe(t){this.addUpdate((()=>{this.throttledAddEvent({type:Ka.Custom,timestamp:t.timestamp||0,data:{tag:"breadcrumb",payload:t}})}))}je(){let t=(e=this.performanceEntries,e.map(cl).filter(Boolean)).concat(this.replayPerformanceEntries);var e;if(this.performanceEntries=[],this.replayPerformanceEntries=[],this.he){const e=this.pe.initialTimestamp/1e3;t=t.filter((t=>t.start>=e))}return Promise.all(Hl(this,t))}Ce(){this.pe.errorIds.clear(),this.pe.traceIds.clear(),this.pe.urls=[]}Be(){const{session:t,eventBuffer:e}=this;if(!t||!e||this.he)return;if(t.segmentId)return;const n=e.getEarliestTimestamp();n&&n<this.pe.initialTimestamp&&(this.pe.initialTimestamp=n)}Ue(){const t={initialTimestamp:this.pe.initialTimestamp,initialUrl:this.pe.initialUrl,errorIds:Array.from(this.pe.errorIds),traceIds:Array.from(this.pe.traceIds),urls:this.pe.urls};return this.Ce(),t}async ze(){const t=this.getSessionId();if(this.session&&this.eventBuffer&&t&&(await this.je(),this.eventBuffer&&this.eventBuffer.hasEvents&&(await async function(t){try{return Promise.all(Hl(t,[ff(bc.performance.memory)]))}catch(t){return[]}}(this),this.eventBuffer&&t===this.getSessionId())))try{this.Be();const e=Date.now();if(e-this.pe.initialTimestamp>this.ct.maxReplayDuration+3e4)throw new Error("Session is too long, not sending replay");const n=this.Ue(),r=this.session.segmentId++;this.xe();const s=await this.eventBuffer.finish();await gf({replayId:t,recordingData:s,segmentId:r,eventContext:n,session:this.session,timestamp:e,onError:t=>this.handleException(t)})}catch(t){this.handleException(t),this.stop({reason:"sendReplay"});const e=zt();if(e){const n=t instanceof yf?"ratelimit_backoff":"send_error";e.recordDroppedEvent(n,"replay")}}}__init5(){this.ge=async({force:t=!1}={})=>{if(!this.bt&&!t)return;if(!this.checkAndHandleExpiredSession())return;if(!this.session)return;const e=this.session.started,n=Date.now()-e;this.ye.cancel();const r=n<this.ct.minReplayDuration,s=n>this.ct.maxReplayDuration+5e3;if(r||s)return void(r&&this.ye());const i=!!this.qe;this.qe||(this.qe=this.ze());try{await this.qe}catch(t){this.handleException(t)}finally{this.qe=void 0,i&&this.ye()}}}xe(){this.session&&this.ct.stickySession&&El(this.session)}__init6(){this._e=t=>{const e=t.length,n=this.ct.mutationLimit,r=n&&e>n;if(e>this.ct.mutationBreadcrumbLimit||r){const t=Qu({category:"replay.mutations",data:{count:e,limit:r}});this.Pe(t)}return!r||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}}function kf(t,e){return[...t,...e].join(",")}const Sf='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',_f=["content-length","content-type","accept"];let Tf=!1;class If{static __initStatic(){this.id="Replay"}constructor({flushMinDelay:t=5e3,flushMaxDelay:e=5500,minReplayDuration:n=4999,maxReplayDuration:r=36e5,stickySession:s=!0,useCompression:i=!0,workerUrl:o,_experiments:c={},maskAllText:a=!0,maskAllInputs:u=!0,blockAllMedia:l=!0,mutationBreadcrumbLimit:f=750,mutationLimit:h=1e4,slowClickTimeout:d=7e3,slowClickIgnoreSelectors:p=[],networkDetailAllowUrls:m=[],networkDetailDenyUrls:y=[],networkCaptureBodies:g=!0,networkRequestHeaders:v=[],networkResponseHeaders:b=[],mask:w=[],maskAttributes:k=["title","placeholder"],unmask:S=[],block:_=[],unblock:T=[],ignore:I=[],maskFn:E,beforeAddRecordingEvent:x,beforeErrorSampling:C,onError:A}={}){this.name=If.id;const R=function({mask:t,unmask:e,block:n,unblock:r,ignore:s}){return{maskTextSelector:kf(t,[".sentry-mask","[data-sentry-mask]"]),unmaskTextSelector:kf(e,[]),blockSelector:kf(n,[".sentry-block","[data-sentry-block]","base","iframe[srcdoc]:not([src])"]),unblockSelector:kf(r,[]),ignoreSelector:kf(s,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'])}}({mask:w,unmask:S,block:_,unblock:T,ignore:I});if(this.me={maskAllInputs:u,maskAllText:a,maskInputOptions:{password:!0},maskTextFn:E,maskInputFn:E,maskAttributeFn:(t,e,n)=>function({el:t,key:e,maskAttributes:n,maskAllText:r,privacyOptions:s,value:i}){return r?s.unmaskTextSelector&&t.matches(s.unmaskTextSelector)?i:n.includes(e)||"value"===e&&"INPUT"===t.tagName&&["submit","button"].includes(t.getAttribute("type")||"")?i.replace(/[\S]/g,"*"):i:i}({maskAttributes:k,maskAllText:a,privacyOptions:R,key:t,value:e,el:n}),...R,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:t=>{try{t.__rrweb__=!0}catch(t){}}},this.He={flushMinDelay:t,flushMaxDelay:e,minReplayDuration:Math.min(n,15e3),maxReplayDuration:Math.min(r,Ec),stickySession:s,useCompression:i,workerUrl:o,blockAllMedia:l,maskAllInputs:u,maskAllText:a,mutationBreadcrumbLimit:f,mutationLimit:h,slowClickTimeout:d,slowClickIgnoreSelectors:p,networkDetailAllowUrls:m,networkDetailDenyUrls:y,networkCaptureBodies:g,networkRequestHeaders:Ef(v),networkResponseHeaders:Ef(b),beforeAddRecordingEvent:x,beforeErrorSampling:C,onError:A,_experiments:c},this.He.blockAllMedia&&(this.me.blockSelector=this.me.blockSelector?`${this.me.blockSelector},${Sf}`:Sf),this.We&&ps())throw new Error("Multiple Sentry Session Replay instances are not supported");this.We=!0}get We(){return Tf}set We(t){Tf=t}afterAllSetup(t){ps()&&!this._replay&&(this.Je(t),this.Ke(t))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(t){return this._replay?this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(t):(this._replay.start(),Promise.resolve()):Promise.resolve()}getReplayId(){if(this._replay&&this._replay.isEnabled())return this._replay.getSessionId()}getRecordingMode(){if(this._replay&&this._replay.isEnabled())return this._replay.recordingMode}Ke(t){this._replay&&(this.Ye(t),this._replay.initializeSampling())}Je(t){const e=function(t,e){const n=e.getOptions(),r={sessionSampleRate:0,errorSampleRate:0,...ot(t)},s=Ke(n.replaysSessionSampleRate),i=Ke(n.replaysOnErrorSampleRate);null==s&&null==i&&o((()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}));null!=s&&(r.sessionSampleRate=s);null!=i&&(r.errorSampleRate=i);return r}(this.He,t);this._replay=new wf({options:e,recordingOptions:this.me})}Ye(t){try{const e=t.getIntegrationByName("ReplayCanvas");if(!e)return;this._replay._canvas=e.getOptions()}catch(t){}}}function Ef(t){return[..._f,...t.map((t=>t.toLowerCase()))]}return If.__initStatic(),$e(),t.BrowserClient=Ls,t.SDK_VERSION=e,t.SEMANTIC_ATTRIBUTE_SENTRY_OP=Yt,t.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN=Gt,t.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE=Kt,t.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE=Jt,t.Scope=Ot,t.WINDOW=gs,t.addBreadcrumb=Er,t.addEventProcessor=Qn,t.addIntegration=function(t){const e=zt();e&&e.addIntegration(t)},t.breadcrumbsIntegration=qo,t.browserApiErrorsIntegration=Wo,t.browserTracingIntegration=(t={})=>{$e();const{enableInp:e,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:i,enableStandaloneClsSpans:o},beforeStartSpan:c,idleTimeout:a,finalTimeout:u,childSpanTimeout:l,markBackgroundSpan:f,traceFetch:h,traceXHR:d,trackFetchStreamPerformance:p,shouldCreateSpanForRequest:m,enableHTTPTimings:y,instrumentPageLoad:g,instrumentNavigation:v}={...hc,...t},b=Qi({recordClsStandaloneSpans:o||!1});e&&So(),s&&n.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver((t=>{const e=Ae();if(e)for(const n of t.getEntries()){if(!n.scripts[0])continue;const t=Wi(ft+n.startTime),{start_timestamp:r,op:s}=ke(e);if("navigation"===s&&r&&t<r)continue;const i=Wi(n.duration),o={[Gt]:"auto.ui.browser.metrics"},c=n.scripts[0],{invoker:a,invokerType:u,sourceURL:l,sourceFunctionName:f,sourceCharPosition:h}=c;o["browser.script.invoker"]=a,o["browser.script.invoker_type"]=u,l&&(o["code.filepath"]=l),f&&(o["code.function"]=f),-1!==h&&(o["browser.script.source_char_position"]=h),zi(e,t,t+i,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:o})}})).observe({type:"long-animation-frame",buffered:!0}):r&&Oi("longtask",(({entries:t})=>{const e=Ae();if(!e)return;const{op:n,start_timestamp:r}=ke(e);for(const s of t){const t=Wi(ft+s.startTime),i=Wi(s.duration);"navigation"===n&&r&&t<r||zi(e,t,t+i,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[Gt]:"auto.ui.browser.metrics"}})}})),i&&Oi("event",(({entries:t})=>{const e=Ae();if(e)for(const n of t)if("click"===n.name){const t=Wi(ft+n.startTime),r=Wi(n.duration),s={name:W(n.target),op:`ui.interaction.${n.name}`,startTime:t,attributes:{[Gt]:"auto.ui.browser.metrics"}},i=Y(n.target);i&&(s.attributes["ui.component_name"]=i),zi(e,t,t+r,s)}}));const w={name:void 0,source:void 0};function k(t,e){const n="pageload"===e.op,r=c?c(e):e,s=r.attributes||{};e.name!==r.name&&(s[Jt]="custom",r.attributes=s),w.name=r.name,w.source=s[Jt];const i=$n(r,{idleTimeout:a,finalTimeout:u,childSpanTimeout:l,disableAutoFinish:n,beforeSpanEnd:t=>{b(),Zi(t,{recordClsOnPageloadSpan:!o})}});function f(){["interactive","complete"].includes(gs.document.readyState)&&t.emit("idleSpanEnableAutoFinish",i)}return n&&gs.document&&(gs.document.addEventListener("readystatechange",(()=>{f()})),f()),i}return{name:"BrowserTracing",afterAllSetup(t){let n,r=gs.location&&gs.location.href;function s(){n&&!ke(n).timestamp&&n.end()}t.on("startNavigationSpan",(e=>{zt()===t&&(s(),n=k(t,{op:"navigation",...e}))})),t.on("startPageLoadSpan",((e,r={})=>{if(zt()!==t)return;s();const i=he(r.sentryTrace||mc("sentry-trace"),r.baggage||mc("baggage"));Pt().setPropagationContext(i),n=k(t,{op:"pageload",...e})})),t.on("spanEnd",(t=>{const e=ke(t).op;if(t!==Ce(t)||"navigation"!==e&&"pageload"!==e)return;const n=Pt(),r=n.getPropagationContext();n.setPropagationContext({...r,sampled:void 0!==r.sampled?r.sampled:Se(t),dsc:r.dsc||Je(t)})})),gs.location&&(g&&dc(t,{name:gs.location.pathname,startTime:ft?ft/1e3:void 0,attributes:{[Jt]:"url",[Gt]:"auto.pageload.browser"}}),v&&lo((({to:e,from:n})=>{void 0===n&&r&&-1!==r.indexOf(e)?r=void 0:n!==e&&(r=void 0,pc(t,{name:gs.location.pathname,attributes:{[Jt]:"url",[Gt]:"auto.navigation.browser"}}))}))),f&&gs&&gs.document&&gs.document.addEventListener("visibilitychange",(()=>{const t=Ae();if(!t)return;const e=Ce(t);if(gs.document.hidden&&e){const t="cancelled",{op:n,status:r}=ke(e);r||e.setStatus({code:re,message:t}),e.setAttribute("sentry.cancellation_reason","document.hidden"),e.end()}})),i&&function(t,e,n,r){let s;const i=()=>{const i="ui.action.click",o=Ae(),c=o&&Ce(o);if(c){const t=ke(c).op;if(["navigation","pageload"].includes(t))return}s&&(s.setAttribute(Xt,"interactionInterrupted"),s.end(),s=void 0),r.name&&(s=$n({name:r.name,op:i,attributes:{[Jt]:r.source||"url"}},{idleTimeout:t,finalTimeout:e,childSpanTimeout:n}))};gs.document&&addEventListener("click",i,{once:!1,capture:!0})}(a,u,l,w),e&&function(t){const e=({entries:t})=>{const e=Ae(),n=e&&Ce(e);t.forEach((t=>{if(!function(t){return"duration"in t}(t)||!n)return;const e=t.interactionId;if(null!=e&&!ko.has(e)){if(wo.length>10){const t=wo.shift();ko.delete(t)}wo.push(e),ko.set(e,n)}}))};Oi("event",e),Oi("first-input",e)}(),ac(t,{traceFetch:h,traceXHR:d,trackFetchStreamPerformance:p,tracePropagationTargets:t.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:m,enableHTTPTimings:y})}}},t.captureEvent=Hn,t.captureException=captureException,t.captureMessage=function(t,e){const n="string"==typeof e?e:void 0,r="string"!=typeof e?{captureContext:e}:void 0;return Pt().captureMessage(t,n,r)},t.captureSession=nr,t.captureUserFeedback=function(t){const e=zt();e&&e.captureUserFeedback(t)},t.chromeStackLineParser=Ao,t.close=async function(t){const e=zt();return e?e.close(t):Promise.resolve(!1)},t.continueTrace=(t,e)=>{const n=Ft(C());if(n.continueTrace)return n.continueTrace(t,e);const{sentryTrace:r,baggage:s}=t;return Ut((t=>{const n=he(r,s);return t.setPropagationContext(n),e()}))},t.createTransport=wr,t.createUserFeedbackEnvelope=Ds,t.dedupeIntegration=Br,t.defaultStackLineParsers=jo,t.defaultStackParser=Bo,t.endSession=tr,t.eventFromException=As,t.eventFromMessage=Rs,t.exceptionFromError=ks,t.feedbackAsyncIntegration=vc,t.feedbackIntegration=vc,t.flush=async function(t){const e=zt();return e?e.flush(t):Promise.resolve(!1)},t.forceLoad=function(){},t.functionToStringIntegration=Ar,t.geckoStackLineParser=Mo,t.getActiveSpan=Ae,t.getClient=zt,t.getCurrentHub=ss,t.getCurrentScope=Pt,t.getDefaultIntegrations=ec,t.getGlobalScope=Bt,t.getIsolationScope=jt,t.getReplay=function(){const t=zt();return t&&t.getIntegrationByName("Replay")},t.getRootSpan=Ce,t.getSpanDescendants=xe,t.globalHandlersIntegration=Vo,t.httpContextIntegration=Zo,t.inboundFiltersIntegration=Or,t.init=function(t={}){const e=function(t={}){const e={defaultIntegrations:ec(t),release:"string"==typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:gs.SENTRY_RELEASE&&gs.SENTRY_RELEASE.id?gs.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return null==t.defaultIntegrations&&delete t.defaultIntegrations,{...e,...t}}(t);if(!e.skipBrowserExtensionCheck&&function(){const t=void 0!==gs.window&&gs;if(!t)return!1;const e=t[t.chrome?"chrome":"browser"],n=e&&e.runtime&&e.runtime.id,r=gs.location&&gs.location.href||"",s=!!n&&gs===gs.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some((t=>r.startsWith(`${t}//`))),i=void 0!==t.nw;return!!n&&!s&&!i}())return void o((()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));const n={...e,stackParser:(r=e.stackParser||Bo,Array.isArray(r)?h(...r):r),integrations:cr(e),transport:e.transport||To};var r;return function(t,e){!0===e.debug&&o((()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),Pt().update(e.initialScope);const n=new t(e);return pr(n),n.init(),n}(Ls,n)},t.isInitialized=function(){return!!zt()},t.lastEventId=Vn,t.lazyLoadIntegration=async function(t,n){const r=nc[t],s=rc.Sentry=rc.Sentry||{};if(!r)throw new Error(`Cannot lazy load integration: ${t}`);const i=s[t];if("function"==typeof i&&!("_isShim"in i))return i;const o=function(t){const n=zt(),r=n&&n.getOptions(),s=r&&r.cdnBaseUrl||"https://browser.sentry-cdn.com";return new URL(`/${e}/${t}.min.js`,s).toString()}(r),c=gs.document.createElement("script");c.src=o,c.crossOrigin="anonymous",c.referrerPolicy="origin",n&&c.setAttribute("nonce",n);const a=new Promise(((t,e)=>{c.addEventListener("load",(()=>t())),c.addEventListener("error",e)})),u=gs.document.currentScript,l=gs.document.body||gs.document.head||u&&u.parentElement;if(!l)throw new Error(`Could not find parent element to insert lazy-loaded ${t} script`);l.appendChild(c);try{await a}catch(e){throw new Error(`Error when loading integration: ${t}`)}const f=s[t];if("function"!=typeof f)throw new Error(`Could not load integration: ${t}`);return f},t.linkedErrorsIntegration=tc,t.makeFetchTransport=To,t.metrics=sc,t.onLoad=function(t){t()},t.opera10StackLineParser=No,t.opera11StackLineParser=Po,t.parameterize=function(t,...e){const n=new String(String.raw(t,...e));return n.__sentry_template_string__=t.join("\0").replace(/%/g,"%%").replace(/\0/g,"%s"),n.__sentry_template_values__=e,n},t.replayIntegration=t=>new If(t),t.setContext=Wn,t.setCurrentClient=pr,t.setExtra=Kn,t.setExtras=Jn,t.setMeasurement=dn,t.setTag=Gn,t.setTags=Yn,t.setUser=Xn,t.showReportDialog=function(t={}){if(!gs.document)return;const e=Pt(),n=e.getClient(),r=n&&n.getDsn();if(!r)return;if(e&&(t.user={...e.getUser(),...t.user}),!t.eventId){const e=Vn();e&&(t.eventId=e)}const s=gs.document.createElement("script");s.async=!0,s.crossOrigin="anonymous",s.src=function(t,e){const n=Ve(t);if(!n)return"";const r=`${sr(n)}embed/error-page/`;let s=`dsn=${Ge(n)}`;for(const t in e)if("dsn"!==t&&"onClose"!==t)if("user"===t){const t=e.user;if(!t)continue;t.name&&(s+=`&name=${encodeURIComponent(t.name)}`),t.email&&(s+=`&email=${encodeURIComponent(t.email)}`)}else s+=`&${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`;return`${r}?${s}`}(r,t),t.onLoad&&(s.onload=t.onLoad);const{onClose:i}=t;if(i){const t=e=>{if("__sentry_reportdialog_closed__"===e.data)try{i()}finally{gs.removeEventListener("message",t)}};gs.addEventListener("message",t)}const o=gs.document.head||gs.document.body;o&&o.appendChild(s)},t.spanToBaggageHeader=function(t){return ue(Je(t))},t.spanToJSON=ke,t.spanToTraceHeader=ve,t.startBrowserTracingNavigationSpan=pc,t.startBrowserTracingPageLoadSpan=dc,t.startInactiveSpan=wn,t.startNewTrace=function(t){return Ut((e=>(e.setPropagationContext({traceId:Tt()}),kn(null,t))))},t.startSession=Zn,t.startSpan=function(t,e){const n=Tn();if(n.startSpan)return n.startSpan(t,e);const r=_n(t),{forceTransaction:s,parentSpan:i}=t;return Ut(t.scope,(()=>xn(i)((()=>{const n=Pt(),i=En(n),o=t.onlyIfParent&&!i?new je:Sn({parentSpan:i,spanArguments:r,forceTransaction:s,scope:n});return Ct(n,o),Be((()=>e(o)),(()=>{const{status:t}=ke(o);!o.isRecording()||t&&"ok"!==t||o.setStatus({code:re,message:"internal_error"})}),(()=>o.end()))}))))},t.startSpanManual=bn,t.suppressTracing=function(t){const e=Tn();return e.suppressTracing?e.suppressTracing(t):Ut((e=>(e.setSDKProcessingMetadata({[vn]:!0}),t())))},t.updateSpanName=function(t,e){t.updateName(e),t.setAttributes({[Jt]:"custom",[Zt]:e})},t.winjsStackLineParser=Do,t.withActiveSpan=kn,t.withIsolationScope=function(...t){const e=Ft(C());if(2===t.length){const[n,r]=t;return n?e.withSetIsolationScope(n,r):e.withIsolationScope(r)}return e.withIsolationScope(t[0])},t.withScope=Ut,t}({});
//# sourceMappingURL=bundle.tracing.replay.min.js.map
