mobile-navigation {
  display: block;
}
mobile-navigation .site-nav {
  padding: 0;
}
mobile-navigation .site-nav nav {
  transition: transform 300ms ease-in-out;
}
mobile-navigation .site-nav li > a {
  position: relative;
  padding: 1.5rem var(--sidebar-gutter);
  display: flex;
  align-items: center;
  width: 100%;
}
mobile-navigation .site-nav > .site-nav-container > nav > ul > li:not(:first-child) > a {
  border-top: 1px solid var(--color-borders-main);
}
mobile-navigation .site-nav > .site-nav-container > nav > ul > li.has-submenu.opened > a {
  border-bottom: 1px solid var(--color-borders-main);
}
mobile-navigation .site-nav li > a > .icon {
  width: 44px;
  height: 44px;
  border: 1px solid var(--color-borders-main);
  border-radius: var(--border-radius-buttons);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  position: absolute;
  right: var(--sidebar-gutter);
  top: 50%;
  transform: translateY(-50%);
}
html[dir=rtl] mobile-navigation .site-nav li > a > .icon {
  right: auto;
  left: var(--sidebar-gutter);
}
mobile-navigation .site-nav li > a > .icon::after {
  content: '';
  width: 8px;
  height: 8px;
  border-right: 2px solid currentColor;
  border-bottom: 2px solid currentColor;
  transform: rotate(-45deg) translate(1.5px, -0.5px);
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -5px 0 0 -5px;
}
html[dir=rtl] mobile-navigation .site-nav li > a > .icon::after {
  transform: rotate(135deg) translate(-1.5px, -0.5px);
}
mobile-navigation li.has-submenu.opened > a > .icon::after,
mobile-navigation li.has-babymenu.opened > a > .icon::after {
  transform: rotate(45deg) translate(0, 0);
}
html[dir=rtl] mobile-navigation li.has-submenu.opened > a > .icon::after,
html[dir=rtl] mobile-navigation li.has-babymenu.opened > a > .icon::after {
  transform: rotate(45deg) translate(0, 0);
}
mobile-navigation .site-nav .submenu, mobile-navigation .site-nav .babymenu {
  width: 100%;
  position: static;
  top: auto;
  transition: max-height 300ms ease-in-out;
  display: block;
  max-height: 0;
  overflow: hidden;
}
html[dir=rtl] mobile-navigation .site-nav .submenu, html[dir=rtl] mobile-navigation .site-nav .babymenu {
  right: 0;
}
html[dir=ltr] mobile-navigation .site-nav .submenu, html[dir=ltr] mobile-navigation .site-nav .babymenu {
  left: 0;
}
mobile-navigation .site-nav .has-promotion {
  padding: var(--sidebar-gutter);
}
mobile-navigation .site-nav .text-animation--underline-in-header {
  padding-bottom: 0;
  background: none;
}
mobile-navigation .site-nav .submenu li,
mobile-navigation .site-nav .babymenu li {
  padding-left: 1rem;
}
mobile-navigation .site-nav .button__icon svg * {
  fill: none;
}
mobile-navigation .menu-link svg, mobile-navigation .button svg {
  margin-inline-end: 0.75rem;
}
mobile-navigation .menu-link svg path, mobile-navigation .button--outline svg path {
  stroke: var(--color-text-main);
}
mobile-navigation.opened-first-submenu .site-nav,
mobile-navigation.opened-first-submenu .site-nav-container, mobile-navigation.opened-second-submenu .site-nav,
mobile-navigation.opened-second-submenu .site-nav-container {
  height: 0;
}
mobile-navigation li.has-submenu.opened .submenu,
mobile-navigation li.has-babymenu.opened .babymenu {
  max-height: unset;
}
mobile-navigation .header-actions {
  background-color: var(--color-third-background-main);
  border-bottom: 1px solid var(--color-borders-main);
  margin: 0 !important;
  width: 100%;
  padding: 1.25rem calc(var(--sidebar-gutter) / 2) 0;
  justify-content: space-between;
  display: flex !important;
}
mobile-navigation .header-actions > * {
  display: inline-flex;
  margin: 0 calc(var(--sidebar-gutter) / 2) 1.25rem;
  flex: initial;
}
mobile-navigation .header-actions .header-info-block {
  height: auto;
}
mobile-navigation .header-actions .header-info-block__image {
  margin-inline-end: 0.625rem;
}
mobile-navigation .header-actions .header-info-block__image svg, mobile-navigation .header-actions .header-info-block__image img {
  width: 1.75rem !important;
  height: 1.75rem !important;
}
mobile-navigation .header-actions .header-info-block__title {
  font-size: calc(14px / 16 * var(--base-body-size) + 0px);
  font-weight: var(--font-weight-body-bold);
}
mobile-navigation .header-actions .header-info-block__text .icon {
  margin-inline-start: 0.3125rem;
  transform: scale(0.9);
}

/* Style arrow container to be touch-friendly */
mobile-navigation .site-nav li.has-submenu > a,
mobile-navigation .site-nav li.has-babymenu > a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 0; /* Remove right padding to accommodate the arrow */
}

.site-menu-sidebar-footer {
  display: none !important;
}
