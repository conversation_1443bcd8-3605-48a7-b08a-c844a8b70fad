.product-item {
  overflow: visible;
}

.product-item__image-figure {
  border-radius: var(--border-radius-cards) var(--border-radius-cards) 0 0;
  transition: opacity 0.3s linear, visibility 0.3s linear !important;
}
.product-item__image .lazy-image:before, .product-item__image .lazy-image:after {
  border-radius: var(--border-radius-cards) 0 0;
}
.product-item__image--no-text .product-item__image-figure {
  border-radius: var(--border-radius-cards);
}
.product-item__image-figure--secondary {
  opacity: 0;
  visibility: hidden;
}
.no-touchevents .product-item__image--has-secondary:hover .product-item__image-figure--primary {
  opacity: 0;
  visibility: hidden;
}
.no-touchevents .product-item__image--has-secondary:hover .product-item__image-figure--secondary {
  opacity: 1;
  visibility: visible;
}

div + .equalize-white-space {
  margin-top: -0.1875rem;
}

.product-item__price span {
  width: auto;
}
.product-item__price span:not(:first-child) {
  padding-inline-start: 0.5rem;
}
.product-item__price span:empty {
  display: none;
}
.product-item__price .product-price--unit {
  display: block;
  padding-inline-start: 0 !important;
  opacity: 0.62;
  font-size: 80%;
  margin-top: -0.125rem;
  margin-bottom: 0.125rem;
}

.product-item__text {
  display: flex;
  flex-direction: column;
  justify-content: stretch;
}
.product-item__text.text-align--center {
  align-items: center;
}
.product-item__text.text-align--right {
  align-items: flex-end;
}
.product-item__text:empty {
  display: none;
}

.product-item__quick-buy {
  width: 100%;
}
.product-item__quick-buy .add-to-cart-preloader svg circle {
  stroke: var(--color-text-cards);
}
.product-item__title {
  overflow-wrap: normal;
  word-break: normal;
}
.product-item__title + .product-item__quick-buy {
  margin-top: 0.5rem;
}
.product-item__quick-buy .button {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.product-item__title:last-child {
  margin-bottom: 0.5rem;
}

.product-item__text + .product-item__quick-buy {
  margin-top: 0.25rem;
}

.product-item__local-availability .alert {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
}
.product-item__local-availability .alert:before {
  width: 0.625rem;
  height: 0.625rem;
  margin-inline-end: 0.3125rem;
  top: 0.0625rem;
}
.product-item__local-availability .alert.alert--note:before {
  border-color: var(--color-text-cards);
}

.product-item__badges {
  position: absolute;
  top: var(--gutter-small);
  width: calc(100% - (var(--gutter-small) * 2));
  pointer-events: none;
}
html[dir=rtl] .product-item__badges {
  left: var(--gutter-small);
}
html[dir=ltr] .product-item__badges {
  right: var(--gutter-small);
}
.product-text .product-item__badges {
  position: static;
  display: flex;
  gap: 0.75rem;
  width: auto;
}

.product-item__badge {
  clear: both;
  float: right;
  margin-bottom: calc(var(--gutter-small) / 2);
  width: auto;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-inline-start: 0.4375rem;
  padding-inline-end: 0.4375rem;
  height: 1.5rem;
  border-radius: clamp(0px, var(--border-radius-buttons), 3px);
  background-color: #bbb;
  line-height: 1.5rem;
  text-transform: uppercase;
}
.product-text .product-item__badge {
  margin-bottom: 0;
  max-width: none;
  float: none;
  height: 1.75rem;
  line-height: 1.75rem;
  padding-inline-start: 0.5625rem;
  padding-inline-end: 0.5625rem;
}

.product-item__icons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem 0;
}

.star-rating {
  font-size: 0;
  width: 100%;
  height: 1rem;
  display: grid;
  grid-template-columns: max-content auto;
  align-items: center;
  gap: 0.625rem;
}
.star-rating__stars {
  background-repeat: repeat-x;
  font-size: 0;
  display: inline-block;
  text-align: start;
}
.star-rating__stars-active {
  display: inline-block;
  font-size: 0;
  height: 0.875rem;
  background-repeat: repeat-x;
}

.main-content--align-product-items .product-item {
  display: flex;
  flex-direction: column;
}
.main-content--align-product-items .product-item__text:first-of-type {
  flex-grow: 1;
}

.product-item .stamped-badge-caption {
  margin-inline-start: 8px;
  font-size: 80%;
}
.product-item .yotpo .yotpo-bottomline .yotpo-icon-star, .product-item .yotpo .yotpo-bottomline .yotpo-icon-half-star, .product-item .yotpo .yotpo-bottomline .yotpo-icon-empty-star {
  color: var(--color-text-cards);
}
.product-item .yotpo a, .product-item .yotpo a:hover {
  color: var(--color-text-cards) !important;
  margin-inline-start: 8px;
  pointer-events: none !important;
}
.product-item .yotpo .standalone-bottomline.star-clickable, .product-item .yotpo .standalone-bottomline .star-clickable {
  cursor: default !important;
}

.collection-tabs {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}
.collection-tabs_tab.active .button {
  background-color: var(--color-text-main);
  border-color: var(--color-text-main);
  color: var(--color-foreground-main);
}
.collection-tabs_panel {
  display: none;
}
.collection-tabs_panel.active {
  display: flex;
  gap: var(--grid-gap);
  align-items: stretch;
  justify-content: space-between;
}

.collections-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--grid-gap);
}

.profile-collection-wrapper {
  /* display: flex;
  flex: 1 1 auto; */
  max-width: 75%;
}

div.profile-collection-image-holder {
  display: flex;
  flex: 1 1 auto;
  height: auto;
  max-width: 25%;
  border-radius: 10px;
  position: relative;
  transition: bottom 150ms ease-in-out;
}

div.profile-collection-image-holder a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

img.profile-collection-image {
  height: auto;
  border-radius: var(--border-radius-cards);
  object-fit: cover;
  overflow-x: hidden;
}

.profile-details {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 25px;
  left: 25px;
}

.profile-title {
  display: flex;
  flex: 1;
  color: #F5F5F5;
  font-weight: 700;
  font-size: 40px;
}

.profile-link {
  display: flex;
  flex: 1;
  color: #F5F5F5;
  line-height: 1;
  font-weight: 400;
  font-size: 28px;
  border: var(--border-width-buttons) solid #F5F5F5;
  border-radius: calc(var(--border-radius-buttons) / 2);
  text-decoration: none;
  padding: 8px 16px;
}

@media screen and (max-width: 767px) {
  .collection-tabs_panel.active {
    overflow: hidden;
  }
  div.profile-collection-image-holder {
    max-width: calc(50% - var(--grid-gap) / 2) !important;
  }
  .profile-collection-wrapper {
    max-width: calc(50% - var(--grid-gap) / 2) !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  div.profile-collection-image-holder {
    max-width: 35% !important;
  }
  .profile-collection-wrapper {
    max-width: 65% !important;
  }
}

@media screen and (max-width: 1023px) {
  .profile-details {
    left: 1rem;
  }
  .profile-title {
    font-size: 30px;
  }
  .profile-link {
    font-size: 18px;
  }
}

@media screen and (max-width: 1023px) {
  [name="add"]:not(.add-to-cart--mobile-large) {
    font-size: calc(16px / 22 * var(--base-body-size) + 0px);
  }
  [name="add"].add-to-cart--mobile-large {
    font-size: calc(18px / 12 * var(--base-body-size) + 0px);
  }
}