.image-with-hotspots {
  position: relative;
  border-radius: var(--border-radius-cards);
  overflow: hidden;
}
.image-with-hotspots-wrapper {
  position: relative;
}
.image-with-hotspots .onboarding-svg {
  position: relative;
  height: 40vh;
}

.image-hotspots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.image-hotspots__spot {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.image-hotspots__spot--content {
  display: none;
  position: absolute;
  width: auto;
  min-width: 220px;
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-top {
    left: 50%;
    top: calc(50% - 40px / 2 - 10px * 1.8);
    transform: translate(-50%, -100%);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-top-mobile {
    left: 50%;
    top: calc(50% - 40px / 2 - 10px * 1.8);
    transform: translate(-50%, -100%);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-top-mobile .hotspot-tooltip::after {
    bottom: 0;
    left: 50%;
    border-color: transparent transparent white white;
    transform-origin: 0 bottom;
    transform: rotate(-45deg) translate(-50%, 48%);
  }
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-top .hotspot-tooltip::after {
    bottom: 0;
    left: 50%;
    border-color: transparent transparent white white;
    transform-origin: 0 bottom;
    transform: rotate(-45deg) translate(-50%, 48%);
  }
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-bottom {
    left: 50%;
    top: calc(50% + 40px / 2 + 10px * 1.8);
    transform: translate(-50%, 0);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-bottom-mobile {
    left: 50%;
    top: calc(50% + 40px / 2 + 10px * 1.8);
    transform: translate(-50%, 0);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-bottom-mobile .hotspot-tooltip::after {
    top: 0;
    transform-origin: 0 0;
    left: 50%;
    border-color: white white transparent transparent;
    transform: rotate(-45deg) translate(-50%, -50%);
  }
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-bottom .hotspot-tooltip::after {
    top: 0;
    left: 50%;
    border-color: white white transparent transparent;
    transform: rotate(-45deg) translate(-50%, -50%);
  }
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-left {
    left: calc(10px * -1.8);
    top: 50%;
    transform: translate(-100%, -50%);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-left-mobile {
    left: calc(10px * -1.8);
    top: 50%;
    transform: translate(-100%, -50%);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-left-mobile .hotspot-tooltip::after {
    top: 50%;
    left: 100%;
    transform-origin: 0 0;
    border-color: transparent white white transparent;
    transform: rotate(-45deg) translate(-50%, -50%);
  }
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-left .hotspot-tooltip::after {
    top: 50%;
    left: 100%;
    border-color: transparent white white transparent;
    transform: rotate(-45deg) translate(-50%, -50%);
  }
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-right {
    left: calc(50% + 40px / 2 + 10px * 1.8);
    top: 50%;
    transform: translate(0, -50%);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-right-mobile {
    left: calc(50% + 40px / 2 + 10px * 1.8);
    top: 50%;
    transform: translate(0, -50%);
  }
}
@media screen and (max-width: 767px) {
  .image-hotspots__spot--content-right-mobile .hotspot-tooltip::after {
    top: 50%;
    left: 0;
    transform-origin: 0 0;
    border-color: white transparent transparent white;
    transform: rotate(-45deg) translate(-50%, -50%);
  }
}
@media screen and (min-width: 768px) {
  .image-hotspots__spot--content-right .hotspot-tooltip::after {
    top: 50%;
    left: 0;
    border-color: white transparent transparent white;
    transform: rotate(-45deg) translate(-50%, -50%);
  }
}
.image-hotspots__spot--bullet {
  display: block;
  background-color: rgba(0, 0, 0, 0.2);
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  text-indent: -999px;
  cursor: pointer;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.2);
  padding: 0;
  border: solid transparent 10px;
  transition: ease all 250ms;
}
.image-hotspots__spot--bullet:after {
  content: "";
  width: calc(28px - 8px * 2);
  height: calc(28px - 8px * 2);
  border-radius: 50%;
  display: block;
  background-color: white;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: ease 250ms all;
}
.image-hotspots__spot:hover .image-hotspots__spot--bullet, .image-hotspots__spot--bullet.active {
  width: 40px;
  height: 40px;
}
.image-hotspots__spot:hover .image-hotspots__spot--bullet:after, .image-hotspots__spot--bullet.active:after {
  width: calc(40px - 6px * 2);
  height: calc(40px - 6px * 2);
}

.hotspot-tooltip {
  display: inline-block;
  width: 100%;
  background-color: white;
  border-radius: var(--border-radius-cards);
  padding: 14px;
  align-items: center;
  box-shadow: 0 0 15px 5px rgba(0, 0, 0, 0.12);
}
.hotspot-tooltip:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 10px solid black;
  transform-origin: 0 0;
}
.hotspot-tooltip--product {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}
.hotspot-tooltip--product .title {
  display: block;
  width: 100%;
  font-weight: var(--font-weight-body-bold);
}
.hotspot-tooltip--product .title .underline-animation {
  background-position: 0 calc(1em + 2px);
}
.hotspot-tooltip--product .price {
  display: block;
  width: 100%;
  line-height: 1.1;
  margin-bottom: 0.3125rem;
}
.hotspot-tooltip--product .price del {
  opacity: 0.6;
  padding: 0 0.3125rem;
}
.hotspot-tooltip--image {
  width: 4.375rem;
  height: auto;
}
@media screen and (max-width: 474px) {
  .hotspot-tooltip--image {
    width: 3.125rem;
  }
}
.hotspot-tooltip--image svg {
  background: rgba(0, 0, 0, 0.08);
  display: block;
}
.hotspot-tooltip--rte .title {
  font-weight: var(--font-weight-body-bold);
  display: block;
  width: 100%;
}
.hotspot-tooltip--rte .description {
  display: inline-block;
  width: 100%;
}
.hotspot-tooltip--rte .description:not(:first-child) {
  margin-top: 0.3125rem;
}

.image-hotspots__spot--bullet.active + .image-hotspots__spot--content {
  display: inline-block;
}