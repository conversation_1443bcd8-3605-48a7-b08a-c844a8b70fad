if (typeof NutritionalInfo !== 'function') {

  class NutritionalInfo extends HTMLElement {
    constructor() {
      super();
      const content = this.innerHTML.replace(/<\/p>|<\/div>/g, '').split('<p>');
      let contentDOM = '';
      let titleFlag = false;

      for (let i = 1; i < content.length; i++) {
        const contentArray = content[i].split(/<br>|<br\/>|\n/g);
        contentDOM += '<div class="table">';

        if (this.dataset.titleLabelFirst != '' || this.dataset.titleLabelSecond != '' || this.dataset.titleLabelThird != '') {
          if (!titleFlag) {
            titleFlag = true;
            contentDOM += `<span class="table-line table-line--heading">`;
            if (this.dataset.titleLabelFirst != '') {
              contentDOM += `<span>${this.dataset.titleLabelFirst}</span>`;
            }
            contentDOM += `<span>${this.dataset.titleLabelSecond}</span>`;
            if (this.dataset.titleLabelThird != '') {
              contentDOM += `<span>${this.dataset.titleLabelThird}</span>`;
            }
            contentDOM += `</span>`;
          }
        }

        for (let j = 0; j < contentArray.length; j++) {
          if (contentArray[j].length > 0 && contentArray[j].replace(/\s/g, '').length != 0) {
            const contentBlockArrayPrepare = contentArray[j].replaceAll(';', '#SD*@N!SA');
            const contentBlockArray = contentBlockArrayPrepare.split('#SD*@N!SA');
            contentDOM += `<span class="table-line ${(contentArray[j].startsWith('-') ? 'table-line--indent' : '')}">`;
            for (let k = 0; k < contentBlockArray.length; k++) {
              const row = contentBlockArray[k].trim().replace(/^-/, '');
              if (row.length > 0 && row.replace(/\s/g, '').length != 0) {
                contentDOM += `<span>${row}</span>`;
              }
            }
            if (contentBlockArray.length == 1 && this.dataset.titleLabelSecond != '') {
              contentDOM += `<span></span>`;
            }
            if (contentBlockArray.length == 2 && this.dataset.titleLabelThird != '') {
              contentDOM += `<span></span>`;
            }
            contentDOM += `</span>`;
          }
        }

        contentDOM += `</div>`;
      }
      
      this.innerHTML = contentDOM;
    }
  }

  if (typeof customElements.get('nutritional-info') == 'undefined') {
    customElements.define('nutritional-info', NutritionalInfo);
  }

}
