.account-widget__head, .account-table .thead {
  background-color: rgba(0, 0, 0, 0.05);
  height: 3.4375rem;
  border-bottom: solid var(--color-borders-main) var(--border-width-cards);
}

.account-widget, .account-table {
  width: 100%;
  border: solid var(--color-borders-main) var(--border-width-cards);
  border-radius: var(--border-radius-cards);
  overflow: hidden;
  margin-bottom: var(--gutter-regular);
}

.account .form-actions__login {
  display: flex;
  gap: 14px;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.account-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--gutter-regular);
}
@media screen and (max-width: 1023px) {
  .account-layout {
    grid-template-columns: 100%;
  }
}
.account .pagination {
  padding-top: 0;
  text-align: center;
}
.account input:not([type=checkbox]) {
  width: 100%;
}
.account label {
  padding-bottom: 0.25rem;
  display: inline-block;
}
.account__form-block {
  max-width: 25.6875rem;
}
.account__form-row {
  padding-bottom: var(--gutter-small);
}
.account__form-row--flex {
  display: flex;
  align-items: center;
  gap: var(--gutter-small);
}
.account__form-row--flex input {
  width: auto;
}
.account__form-row--flex > * {
  flex: auto;
  width: auto;
}
.account__form-row a:not(.button) {
  text-decoration: underline;
}

@media screen and (max-width: 767px) {
  .account-table {
    border: 0;
  }
}
@media screen and (max-width: 767px) {
  .account-table .thead {
    display: none;
  }
}
.account-table .thead .tr {
  height: 100%;
}
.account-table a:not(.button) {
  text-decoration: underline;
}
.account-table .tr {
  display: grid;
  gap: var(--gutter-regular);
  align-items: center;
  padding: 0 var(--gutter-small);
}
.account-table .tr.orders {
  grid-template-columns: 5.625rem 9.375rem 1fr 1fr 5.625rem;
}
@media screen and (max-width: 767px) {
  .account-table .tr.orders {
    grid-template-columns: 100%;
  }
}
.account-table .tr.order {
  grid-template-columns: 4fr 1fr 1fr 5.625rem;
}
@media screen and (max-width: 767px) {
  .account-table .tr.order {
    grid-template-columns: 100%;
  }
}
@media screen and (max-width: 767px) {
  .account-table .tr {
    grid-template-columns: 100%;
    padding: 0 0 var(--gutter-regular) 0;
    border: solid var(--color-borders-main) var(--border-width-cards);
    border-radius: var(--border-radius-cards);
    margin-bottom: var(--gutter-large);
    gap: 0;
  }
}
.account-table .tr:not(:last-child) {
  border-bottom: solid var(--color-borders-main) var(--border-width-cards);
}
.account-table .th {
  align-self: center;
  white-space: nowrap;
}
.account-table .td {
  padding: var(--gutter-small) 0;
}
@media screen and (max-width: 767px) {
  .account-table .td {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    padding: var(--gutter-small) var(--gutter-regular) 0;
    text-align: end;
  }
}
.account-table .td.mobile-cta {
  grid-template-columns: 100%;
  padding-top: var(--gutter-regular);
  padding-bottom: var(--gutter-small);
}
@media screen and (max-width: 767px) {
  .account-table .td .label {
    text-align: start;
  }
}
@media screen and (max-width: 767px) {
  .account-table .td:first-child {
    background-color: rgba(0, 0, 0, 0.05);
    padding-top: 0;
    min-height: 3.4375rem;
    height: auto;
    border-bottom: solid var(--color-borders-main) var(--border-width-cards);
  }
}
@media screen and (min-width: 768px) {
  .account-table .label {
    display: none;
  }
}
.account-table .tfoot {
  text-align: end;
  padding: var(--gutter-regular);
  border-top: solid var(--color-borders-main) var(--border-width-cards);
}
@media screen and (max-width: 767px) {
  .account-table .tfoot {
    border-top: 0;
    padding-top: 0;
  }
}
.account-table .tfoot .tr {
  border: 0;
}

.account-widget__head {
  padding: 0 var(--gutter-regular);
  display: flex;
  align-items: center;
}
.account-widget__body {
  padding: var(--gutter-small) var(--gutter-regular);
}

.mobile-cta {
  width: 100%;
}
@media screen and (min-width: 768px) {
  .mobile-cta {
    display: none;
  }
}