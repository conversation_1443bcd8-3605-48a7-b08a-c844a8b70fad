@media screen and (max-width: 767px) {
  .shipping-calculator__cell {
    padding-bottom: var(--gutter-small);
  }
}
.shipping-calculator__head {
  background-color: rgba(0, 0, 0, 0.05);
  border-bottom: solid var(--color-borders-main) var(--border-width-cards);
  height: 3.4375rem;
  padding: 0 var(--gutter-regular);
  display: flex;
  align-items: center;
}
.shipping-calculator__content {
  display: flex;
  padding: var(--gutter-regular);
  gap: var(--gutter-regular);
  align-items: end;
}
@media screen and (max-width: 767px) {
  .shipping-calculator__content {
    display: block;
  }
}
.shipping-calculator label {
  width: 100%;
  display: inline-block;
  padding-bottom: 0.3125rem;
}
.shipping-calculator input,
.shipping-calculator select {
  width: 100%;
}
@media screen and (min-width: 768px) {
  .shipping-calculator input[name=zipcode] {
    max-width: 150px;
  }
}
.shipping-calculator .button {
  min-width: 200px;
}

.shipping-estimator__results {
  padding-top: 0;
}
.shipping-estimator__results-content-heading {
  display: inline-block;
  width: 100%;
}
.shipping-estimator__results-content-list {
  list-style-type: disc;
  margin: var(--gutter-small);
}
.shipping-estimator__results li:not(:last-child) {
  margin-bottom: 0.375rem;
}
.shipping-estimator__results .alert {
  display: none;
}