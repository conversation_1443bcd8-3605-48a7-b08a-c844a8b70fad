.breadcrumb-container {
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.breadcrumb-container::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
}

@media screen and (max-width: 1023px) {
  .breadcrumb {
    font-size: calc( 					12px / 16 * var(--base-body-size) + 0px 				);
  }
}
@media screen and (min-width: 1024px) {
  .breadcrumb {
    font-size: calc( 				14px / 16 * var(--base-body-size) + 0px 			);
  }
}

.breadcrumb {
  display: flex;
  width: auto;
  margin: 0;
  flex-shrink: 0;
  line-height: 1.1;
}
.breadcrumb-main {
  position: relative;
  z-index: 9;
  position: absolute;
  top: calc(clamp(2.5rem, var(--container-vertical-space), 5rem) * -1);
  height: clamp(2.5rem, var(--container-vertical-space), 5rem);
  width: 100%;
}
html[dir=rtl] .breadcrumb-main {
  right: 0;
}
html[dir=ltr] .breadcrumb-main {
  left: 0;
}
.breadcrumb-main:after, .breadcrumb-main:before {
  content: "";
  position: absolute;
  top: 0;
  height: 100%;
  background: var(--color-background-main);
}
.breadcrumb-main:after {
  right: 0;
  width: calc(var(--gutter-regular) * 2);
  background: linear-gradient(-90deg, var(--color-background-main) 50%, var(--color-opacity-background-main) 100%);
}
.breadcrumb-main:before {
  left: -var(--gutter-regular);
  width: var(--gutter-regular);
  background: linear-gradient(90deg, var(--color-background-main) 50%, var(--color-opacity-background-main) 100%);
}
.breadcrumb-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow-x: auto;
  height: 100%;
  width: 100%;
  max-width: 1360px;
  padding: 0 var(--gutter-container);
  margin: auto;
}
.breadcrumb span:not(:last-child) {
  margin-inline-end: 0.6875rem;
}
.breadcrumb span:last-child {
  margin-inline-end: var(--gutter-regular);
}
.breadcrumb__link a {
  border-bottom: none !important;
  transition: all 100ms linear;
}
.no-touchevents .breadcrumb__link a:hover {
  color: var(--color-accent-main);
}