{"store": {"id": ***********, "plan": "premium", "theme": "webshop/main", "primaryLocale": "de", "adminMode": false, "headless": false, "storefrontRootDomain": "", "checkoutRootDomain": "", "storefrontAccessToken": ""}, "tsPublished": **********, "declaration": {"declDays": "", "declName": "", "declPath": "", "declType": "", "isActive": false, "showType": true, "declHours": "", "declYears": "", "declDomain": "", "declMonths": "", "declMinutes": "", "declPurpose": "", "declSeconds": "", "declSession": "", "showPurpose": false, "declProvider": "", "showProvider": true, "declIntroText": "", "declRetention": "", "declFirstParty": "", "declThirdParty": "", "showDateGenerated": true}, "language": {"unpublished": [], "languageMode": "Single", "fallbackLanguage": "de", "languageDetection": "locale", "languagesSupported": []}, "texts": {"managed": {"headerText": {"de": "Wir respektieren deine Privatsphäre"}, "consentText": {"de": "Diese Website verwendet Cookies, um Ihnen das beste Erlebnis zu bieten."}, "linkText": {"de": "Datenschutzerklärung"}, "imprintText": {"de": "Impressum"}, "allowButtonText": {"de": "<PERSON><PERSON><PERSON>"}, "denyButtonText": {"de": "<PERSON><PERSON><PERSON><PERSON>"}, "dismissButtonText": {"de": "Okay"}, "leaveSiteButtonText": {"de": "Diese Seite verlassen"}, "preferencesButtonText": {"de": "Einstellungen"}, "cookiePolicyText": {"de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "preferencesPopupTitleText": {"de": "Einwilligungseinstellungen verwalten"}, "preferencesPopupIntroText": {"de": "Wir verwenden Cookies, um die Funktionalität der Website zu optimieren, die Leistung zu analysieren und Ihnen ein personalisiertes Erlebnis zu bieten. Einige Cookies sind für den ordnungsgemäßen Betrieb der Website unerlässlich. Diese Cookies können nicht deaktiviert werden. In diesem Fenster können Sie Ihre Präferenzen für Cookies verwalten."}, "preferencesPopupSaveButtonText": {"de": "Auswahl speichern"}, "preferencesPopupCloseButtonText": {"de": "Schließen"}, "preferencesPopupAcceptAllButtonText": {"de": "Alles Akzeptieren"}, "preferencesPopupRejectAllButtonText": {"de": "<PERSON><PERSON>"}, "cookiesDetailsText": {"de": "<PERSON><PERSON>-Det<PERSON>"}, "preferencesPopupAlwaysAllowedText": {"de": "<PERSON><PERSON> erlaubt"}, "accessSectionParagraphText": {"de": "<PERSON>e haben das Recht, jederzeit auf Ihre Daten zuzugreifen."}, "accessSectionTitleText": {"de": "Datenübertragbarkeit"}, "accessSectionAccountInfoActionText": {"de": "persön<PERSON> Daten"}, "accessSectionDownloadReportActionText": {"de": "Alle Daten an<PERSON>"}, "accessSectionGDPRRequestsActionText": {"de": "An<PERSON>gen betroffener Personen"}, "accessSectionOrdersRecordsActionText": {"de": "Aufträge"}, "rectificationSectionParagraphText": {"de": "<PERSON>e haben das Recht, die Aktualisierung Ihrer Daten zu verlangen, wann immer Sie dies für angemessen halten."}, "rectificationSectionTitleText": {"de": "Datenberichtigung"}, "rectificationCommentPlaceholder": {"de": "Beschreibe<PERSON> Sie, was Sie aktualisieren möchten"}, "rectificationCommentValidationError": {"de": "Kommentar ist erforderlich"}, "rectificationSectionEditAccountActionText": {"de": "Aktualisierung anfordern"}, "erasureSectionTitleText": {"de": "Recht auf Löschung"}, "erasureSectionParagraphText": {"de": "<PERSON>e haben das Recht, die Löschung aller Ihrer Daten zu verlangen. <PERSON><PERSON> können Sie nicht mehr auf Ihr Konto zugreifen."}, "erasureSectionRequestDeletionActionText": {"de": "Löschung personenbezogener Daten anfordern"}, "consentDate": {"de": "Zustimmungsdatum"}, "consentId": {"de": "Einwilligungs-ID"}, "consentSectionChangeConsentActionText": {"de": "Einwilligungspräferenz ändern"}, "consentSectionConsentedText": {"de": "Sie haben der Cookie-Richtlinie dieser Website zugestimmt am"}, "consentSectionNoConsentText": {"de": "Sie haben der Cookie-Richtlinie dieser Website nicht zugestimmt."}, "consentSectionTitleText": {"de": "<PERSON><PERSON><PERSON>Einwilligung"}, "consentStatus": {"de": "Einwilligungspräferenz"}, "confirmationFailureMessage": {"de": "Ihre Anfrage wurde nicht bestätigt. Bitte versuchen Si<PERSON> es erneut und wenn das Problem weiterhin besteht, wenden Si<PERSON> sich an den Ladenbesitzer, um Hilfe zu erhalten"}, "confirmationFailureTitle": {"de": "Ein Problem ist aufgetreten"}, "confirmationSuccessMessage": {"de": "Wir werden uns in Kürze zu Ihrem Anliegen bei Ihnen melden."}, "confirmationSuccessTitle": {"de": "<PERSON>hre Anfrage wurde bestätigt"}, "guestsSupportEmailFailureMessage": {"de": "Ihre Anfrage wurde nicht übermittelt. Bitte versuchen Si<PERSON> es erneut und wenn das Problem weiterhin besteht, wenden Sie sich an den Shop-Inhaber, um Hilfe zu erhalten."}, "guestsSupportEmailFailureTitle": {"de": "Ein Problem ist aufgetreten"}, "guestsSupportEmailPlaceholder": {"de": "E-Mail-Addresse"}, "guestsSupportEmailSuccessMessage": {"de": "Wenn Sie als Kunde dieses Shops registriert sind, erhalten Sie in Kürze eine E-Mail mit Anweisungen zum weiteren Vorgehen."}, "guestsSupportEmailSuccessTitle": {"de": "Vielen Dank für die Anfrage"}, "guestsSupportEmailValidationError": {"de": "Email ist ungültig"}, "guestsSupportInfoText": {"de": "<PERSON>te loggen Sie sich mit Ihrem Kundenkonto ein, um fortzufahren."}, "submitButton": {"de": "e<PERSON>re<PERSON>n"}, "submittingButton": {"de": "Senden..."}, "cancelButton": {"de": "Abbrechen"}, "declIntroText": {"de": "Wir verwenden Cookies, um die Funktionalität der Website zu optimieren, die Leistung zu analysieren und Ihnen ein personalisiertes Erlebnis zu bieten. Einige Cookies sind für den ordnungsgemäßen Betrieb der Website unerlässlich. Diese Cookies können nicht deaktiviert werden. In diesem Fenster können Sie Ihre Präferenzen für Cookies verwalten."}, "declName": {"de": "Name"}, "declPurpose": {"de": "Zwe<PERSON>"}, "declType": {"de": "<PERSON><PERSON>"}, "declRetention": {"de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "declProvider": {"de": "<PERSON><PERSON><PERSON>"}, "declFirstParty": {"de": "Erstanbieter"}, "declThirdParty": {"de": "Dritt<PERSON><PERSON><PERSON>"}, "declSeconds": {"de": "Sekunden"}, "declMinutes": {"de": "Minuten"}, "declHours": {"de": "Std."}, "declDays": {"de": "Tage"}, "declMonths": {"de": "Monate"}, "declYears": {"de": "Jahre"}, "declSession": {"de": "Sit<PERSON>ng"}, "declDomain": {"de": "Domain"}, "declPath": {"de": "Weg"}}, "categories": {"strictlyNecessaryCookiesTitleText": {"de": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>"}, "strictlyNecessaryCookiesDescriptionText": {"de": "Diese Cookies sind une<PERSON><PERSON><PERSON><PERSON>, damit <PERSON> sich auf der Website bewegen und ihre Funktionen nutzen können, z. B. den Zugriff auf sichere Bereiche der Website. Ohne diese Cookies kann die Website nicht richtig funktionieren."}, "functionalityCookiesTitleText": {"de": "Funktionale Cookies"}, "functionalityCookiesDescriptionText": {"de": "Diese Cookies ermöglichen es der Website, verbesserte Funktionalität und Personalisierung bereitzustellen. <PERSON><PERSON> könne<PERSON> von uns oder von Drittanbietern gesetzt werden, deren Dienste wir auf unseren Seiten hinzugefügt haben. Wenn Sie diese Cookies nicht zulassen, funktionieren einige oder alle dieser Dienste möglicherweise nicht richtig."}, "performanceCookiesTitleText": {"de": "Performance-Cookies"}, "performanceCookiesDescriptionText": {"de": "Diese Cookies ermöglichen es uns, die Leistung unserer Website zu überwachen und zu verbessern. Sie ermöglichen es uns beispielsweise, <PERSON><PERSON><PERSON> zu z<PERSON>hlen, Verkehrsquellen zu identifizieren und zu sehen, welche Teile der Website am beliebtesten sind."}, "targetingCookiesTitleText": {"de": "Targeting-Cookies"}, "targetingCookiesDescriptionText": {"de": "Diese Cookies können von unseren Werbepartnern über unsere Website gesetzt werden. Sie können von diesen Unternehmen verwendet werden, um ein Profil Ihrer Interessen zu erstellen und Ihnen relevante Werbung auf anderen Websites anzuzeigen. Sie speichern keine direkten personenbezogenen Daten, sondern basieren auf der eindeutigen Identifizierung Ihres Browsers und Ihres Internetgeräts. Wenn Sie diese Cookies nicht zulassen, erleben Sie weniger zielgerichtete Werbung."}, "unclassifiedCookiesTitleText": {"de": "Unklassifizierte Cookies"}, "unclassifiedCookiesDescriptionText": {"de": "Unklassifizierte Cookies sind Cookies, die wir gerade zusammen mit den Anbietern einzelner Cookies klassifizieren."}}, "auto": {}}, "library": {"previewMode": false, "fadeInTimeout": 0, "defaultBlocked": 7, "showLink": true, "showImprintLink": true, "showGoogleLink": false, "enabled": true, "cookie": {"expiryDays": 365, "secure": true, "domain": ""}, "dismissOnScroll": false, "dismissOnWindowClick": false, "dismissOnTimeout": false, "palette": {"popup": {"background": "#FFFFFF", "backgroundForCalculations": {"a": 1, "b": 255, "g": 255, "r": 255}, "text": "#000000"}, "button": {"background": "transparent", "backgroundForCalculations": {"a": 1, "b": 255, "g": 255, "r": 255}, "text": "#000000", "textForCalculation": {"a": 1, "b": 0, "g": 0, "r": 0}, "border": "#000000"}}, "content": {"href": "/policies/privacy-policy", "imprintHref": "", "close": "&#10005;", "target": "", "logo": "<img class=\"cc-banner-logo\" style=\"max-height: 40px;\" src=\"https://04739c-df.myshopify.com/cdn/shop/files/pandectes-banner-logo.png\" alt=\"logo\" />"}, "window": "<div role=\"dialog\" aria-live=\"polite\" aria-label=\"cookieconsent\" aria-describedby=\"cookieconsent:desc\" id=\"pandectes-banner\" class=\"cc-window-wrapper cc-overlay-wrapper\"><div class=\"pd-cookie-banner-window cc-window {{classes}}\"><!--googleoff: all-->{{children}}<!--googleon: all--></div></div>", "compliance": {"custom": "<div class=\"cc-compliance cc-highlight\">{{preferences}}{{allow}}</div>"}, "type": "custom", "layouts": {"basic": "{{logo}}{{messagelink}}{{compliance}}{{close}}"}, "position": "overlay", "theme": "wired", "revokable": true, "animateRevokable": false, "revokableReset": false, "revokableLogoUrl": "https://04739c-df.myshopify.com/cdn/shop/files/pandectes-reopen-logo.png", "revokablePlacement": "bottom-left", "revokableMarginHorizontal": 15, "revokableMarginVertical": 15, "static": false, "autoAttach": true, "hasTransition": true, "blacklistPage": [""], "elements": {"close": "<button aria-label=\"dismiss cookie message\" type=\"button\" tabindex=\"0\" class=\"cc-close\">{{close}}</button>", "dismiss": "<button aria-label=\"dismiss cookie message\" type=\"button\" tabindex=\"0\" class=\"cc-btn cc-btn-decision cc-dismiss\">{{dismiss}}</button>", "allow": "<button aria-label=\"allow cookies\" type=\"button\" tabindex=\"0\" class=\"cc-btn cc-btn-decision cc-allow\">{{allow}}</button>", "deny": "<button aria-label=\"deny cookies\" type=\"button\" tabindex=\"0\" class=\"cc-btn cc-btn-decision cc-deny\">{{deny}}</button>", "preferences": "<button aria-label=\"settings cookies\" tabindex=\"0\" type=\"button\" class=\"cc-btn cc-settings\" onclick=\"Pandectes.fn.openPreferences()\">{{preferences}}</button>"}}, "geolocation": {"auOnly": false, "brOnly": false, "caOnly": false, "chOnly": false, "euOnly": false, "jpOnly": false, "nzOnly": false, "thOnly": false, "zaOnly": false, "canadaOnly": false, "globalVisibility": true}, "dsr": {"guestsSupport": false, "accessSectionDownloadReportAuto": false}, "banner": {"resetTs": 1741061817, "extraCss": "        .cc-banner-logo {max-width: 24em!important;}    @media(min-width: 768px) {.cc-window.cc-floating{max-width: 24em!important;width: 24em!important;}}    .cc-message, .pd-cookie-banner-window .cc-header, .cc-logo {text-align: left}    .cc-window-wrapper{z-index: 2147483647;-webkit-transition: opacity 1s ease;  transition: opacity 1s ease;}    .cc-window{z-index: 2147483647;font-family: inherit;}    .pd-cookie-banner-window .cc-header{font-family: inherit;}    .pd-cp-ui{font-family: inherit; background-color: #FFFFFF;color:#000000;}    button.pd-cp-btn, a.pd-cp-btn{}    input + .pd-cp-preferences-slider{background-color: rgba(0, 0, 0, 0.3)}    .pd-cp-scrolling-section::-webkit-scrollbar{background-color: rgba(0, 0, 0, 0.3)}    input:checked + .pd-cp-preferences-slider{background-color: rgba(0, 0, 0, 1)}    .pd-cp-scrolling-section::-webkit-scrollbar-thumb {background-color: rgba(0, 0, 0, 1)}    .pd-cp-ui-close{color:#000000;}    .pd-cp-preferences-slider:before{background-color: #FFFFFF}    .pd-cp-title:before {border-color: #000000!important}    .pd-cp-preferences-slider{background-color:#000000}    .pd-cp-toggle{color:#000000!important}    @media(max-width:699px) {.pd-cp-ui-close-top svg {fill: #000000}}    .pd-cp-toggle:hover,.pd-cp-toggle:visited,.pd-cp-toggle:active{color:#000000!important}    .pd-cookie-banner-window {box-shadow: 0 0 18px rgb(0 0 0 / 20%);}  ", "customJavascript": {"useButtons": true}, "showPoweredBy": false, "logoHeight": 40, "revokableTrigger": false, "hybridStrict": false, "cookiesBlockedByDefault": "7", "isActive": true, "implicitSavePreferences": true, "cookieIcon": false, "blockBots": false, "showCookiesDetails": true, "hasTransition": true, "blockingPage": false, "showOnlyLandingPage": false, "leaveSiteUrl": "https://pandectes.io", "linkRespectStoreLang": false}, "cookies": {"0": [{"name": "keep_alive", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "30 minute(s)", "expires": 30, "unit": "declMinutes", "purpose": {"de": "Wird im Zusammenhang mit der Käuferlokalisierung verwendet."}}, {"name": "secure_customer_sig", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Wird im Zusammenhang mit dem Kundenlogin verwendet."}}, {"name": "cart_currency", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "2 week(s)", "expires": 2, "unit": "declWeeks", "purpose": {"de": "Das Cookie ist für die sichere Checkout- und Zahlungsfunktion auf der Website erforderlich. Diese Funktion wird von shopify.com bereitgestellt."}}, {"name": "localization", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Lokalisierung von Shopify-Shops"}}, {"name": "_tracking_consent", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Tracking-Einstellungen."}}, {"name": "shopify_pay_redirect", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "1 hour(s)", "expires": 1, "unit": "declHours", "purpose": {"de": "Das Cookie ist für die sichere Checkout- und Zahlungsfunktion auf der Website erforderlich. Diese Funktion wird von shopify.com bereitgestellt."}}, {"name": "_shopify_essential", "type": "http", "domain": "shopify.com", "path": "/***********", "provider": "Shopify", "firstParty": false, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Wird auf der Kontenseite verwendet."}}, {"name": "auth_state_*", "type": "http", "domain": "shopify.com", "path": "/***********/account", "provider": "Shopify", "firstParty": false, "retention": "25 minute(s)", "expires": 25, "unit": "declMinutes", "purpose": {"de": ""}}, {"name": "customer_account_locale", "type": "http", "domain": "shopify.com", "path": "/***********", "provider": "Shopify", "firstParty": false, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Used to keep track of a customer account locale when a redirection occurs from checkout or the storefront to customer accounts."}}, {"name": "_pandectes_gdpr", "type": "http", "domain": ".www.dnatural.de", "path": "/", "provider": "Pandectes", "firstParty": true, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Wird für die Funktionalität des Cookies-Zustimmungsbanners verwendet."}}], "1": [], "2": [{"name": "_shopify_y", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Shopify-Ana<PERSON><PERSON>."}}, {"name": "_ga", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Google", "firstParty": false, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "<PERSON><PERSON> wird von Google Analytics mit unbekannter Funktionalität gesetzt"}}, {"name": "_shopify_sa_t", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "30 minute(s)", "expires": 30, "unit": "declMinutes", "purpose": {"de": "Shopify-Analysen in Bezug auf Marketing und Empfehlungen."}}, {"name": "_shopify_sa_p", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "30 minute(s)", "expires": 30, "unit": "declMinutes", "purpose": {"de": "Shopify-Analysen in Bezug auf Marketing und Empfehlungen."}}, {"name": "_shopify_s", "type": "http", "domain": "de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "Session", "expires": 1, "unit": "declSeconds", "purpose": {"de": "Shopify-Ana<PERSON><PERSON>."}}, {"name": "_shopify_sa_p", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "30 minute(s)", "expires": 30, "unit": "declMinutes", "purpose": {"de": "Shopify-Analysen in Bezug auf Marketing und Empfehlungen."}}, {"name": "_shopify_s", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "30 minute(s)", "expires": 30, "unit": "declMinutes", "purpose": {"de": "Shopify-Ana<PERSON><PERSON>."}}, {"name": "_orig_referrer", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "2 week(s)", "expires": 2, "unit": "declWeeks", "purpose": {"de": "<PERSON><PERSON><PERSON><PERSON><PERSON>."}}, {"name": "_shopify_s", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "30 minute(s)", "expires": 30, "unit": "declMinutes", "purpose": {"de": "Shopify-Ana<PERSON><PERSON>."}}, {"name": "_shopify_sa_t", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Shopify", "firstParty": true, "retention": "30 minute(s)", "expires": 30, "unit": "declMinutes", "purpose": {"de": "Shopify-Analysen in Bezug auf Marketing und Empfehlungen."}}, {"name": "_landing_page", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Shopify", "firstParty": false, "retention": "2 week(s)", "expires": 2, "unit": "declWeeks", "purpose": {"de": "<PERSON><PERSON><PERSON><PERSON><PERSON>."}}, {"name": "_ga_*", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Google", "firstParty": false, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}], "4": [{"name": "klaviyoOnsite", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "<PERSON><PERSON><PERSON><PERSON>", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "klaviyoPagesVisitCount", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "<PERSON><PERSON><PERSON><PERSON>", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "_fbp", "type": "http", "domain": ".dnatural.de", "path": "/", "provider": "Facebook", "firstParty": false, "retention": "3 month(s)", "expires": 3, "unit": "declMonths", "purpose": {"de": "<PERSON><PERSON> wird von Facebook platziert, um Besuche auf Websites zu verfolgen."}}, {"name": "lastExternal<PERSON><PERSON>errer", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "Facebook", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "<PERSON><PERSON><PERSON><PERSON>, wie der Benutzer auf die Website gelangt ist, indem er seine letzte URL-Adresse registriert."}}, {"name": "lastExternalReferrerTime", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "Facebook", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "Enthält den Zeitstempel der letzten Aktualisierung des lastExternalReferrer-Cookies."}}, {"name": "__kl_key", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "<PERSON><PERSON><PERSON><PERSON>", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "_kla_test", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "<PERSON><PERSON><PERSON><PERSON>", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "__kla_viewed", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "<PERSON><PERSON><PERSON><PERSON>", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "__kla_id", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "<PERSON><PERSON><PERSON><PERSON>", "firstParty": true, "retention": "1 year(s)", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wenn jemand durch eine Klaviyo-E-Mail auf Ihre Website klickt"}}], "8": [{"name": "wpm-test-cookie", "type": "http", "domain": "dnatural.de", "path": "/", "provider": "Unknown", "firstParty": false, "retention": "Session", "expires": 1, "unit": "declSeconds", "purpose": {"de": ""}}, {"name": "wpm-test-cookie", "type": "http", "domain": "www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "declSeconds", "purpose": {"de": ""}}, {"name": "wpm-test-cookie", "type": "http", "domain": "de", "path": "/", "provider": "Unknown", "firstParty": false, "retention": "Session", "expires": 1, "unit": "declSeconds", "purpose": {"de": ""}}, {"name": "reviews_io_tracking_page_view_id", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "reviews_io_tracking_replay_url", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "reviews_io_tracking_enable_recording", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "reviews_io_tracking_id", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "reviews_io_tracking_sid", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "sentryReplaySession", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "reviews_io_tracking_progress", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "__storage_test__", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "session-storage-test", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "local-storage-test", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "kl-uid", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "kl-bloom-local", "type": "html_local", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Local Storage", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}, {"name": "kl-bloom-session", "type": "html_session", "domain": "https://www.dnatural.de", "path": "/", "provider": "Unknown", "firstParty": true, "retention": "Session", "expires": 1, "unit": "decl<PERSON><PERSON>s", "purpose": {"de": ""}}]}, "blocker": {"isActive": false, "googleConsentMode": {"id": "", "analyticsId": "", "adwordsId": "", "isActive": false, "adStorageCategory": 4, "analyticsStorageCategory": 2, "personalizationStorageCategory": 1, "functionalityStorageCategory": 1, "customEvent": true, "securityStorageCategory": 0, "redactData": false, "urlPassthrough": false, "dataLayerProperty": "dataLayer", "waitForUpdate": 500, "useNativeChannel": false}, "facebookPixel": {"id": "", "isActive": false, "ldu": false}, "microsoft": {"isActive": false, "uetTags": ""}, "rakuten": {"isActive": false, "cmp": false, "ccpa": false}, "klaviyoIsActive": true, "gpcIsActive": true, "defaultBlocked": 7, "patterns": {"whiteList": [], "blackList": {"1": [], "2": [], "4": [], "8": []}, "iframesWhiteList": [], "iframesBlackList": {"1": [], "2": [], "4": [], "8": []}, "beaconsWhiteList": [], "beaconsBlackList": {"1": [], "2": [], "4": [], "8": []}}}}