.popup {
  position: fixed;
  bottom: var(--gutter-large);
  width: calc(100% - (var(--gutter-large) * 2));
  background: var(--color-background-main);
  color: var(--color-text-main);
  border: 1px solid var(--color-borders-main);
  transform: translateY(3.125rem);
  opacity: 0;
  transition: all 300ms linear 50ms;
  z-index: 999;
  overflow: hidden;
  visibility: hidden;
}
html[dir=rtl] .popup {
  left: var(--gutter-large);
}
html[dir=ltr] .popup {
  right: var(--gutter-large);
}
.popup.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.popup-size--small {
  max-width: 21.25rem;
}
.popup-size--large {
  max-width: 33.75rem;
}
.popup--newsletter input {
  padding-inline-end: 3.125rem;
}
.popup--newsletter button[type=submit] {
  top: var(--gutter-small);
  position: absolute;
  width: 50px;
  height: calc(100% - var(--gutter-small));
  padding-top: 9px;
}
html[dir=rtl] .popup--newsletter button[type=submit] {
  left: 0;
}
html[dir=ltr] .popup--newsletter button[type=submit] {
  right: 0;
}
.no-touchevents .popup--newsletter button[type=submit]:hover svg {
  transform: scale(1.1);
}
.popup--newsletter button[type=submit] svg {
  width: 70%;
  height: 70%;
}
.popup svg path {
  stroke: var(--color-text-main);
}

.popup-age-verification,
.popup-common {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  opacity: 0;
  transition: all 300ms linear 50ms;
  overflow: hidden;
  visibility: hidden;
  top: 0;
  z-index: 1001;
  text-align: center;
  padding: var(--gutter-regular);
}
html[dir=rtl] .popup-age-verification,
html[dir=rtl] .popup-common {
  right: 0;
}
html[dir=ltr] .popup-age-verification,
html[dir=ltr] .popup-common {
  left: 0;
}
.popup-age-verification__content,
.popup-common__content {
  position: absolute;
  width: 100%;
  max-width: 33.75rem;
  background: var(--color-background-main);
  color: var(--color-text-main);
  border: 1px solid var(--color-borders-main);
  top: 50%;
  left: 50%;
  padding: var(--gutter-regular);
  transform: translate(-50%, -50%);
}
.popup-age-verification .button,
.popup-common .button {
  width: auto;
}
.popup-age-verification.active,
.popup-common.active {
  opacity: 1;
  visibility: visible;
}

.popup-common > div {
  z-index: 9;
}

.popup-close {
  position: absolute;
  top: 1.25rem;
  z-index: 999;
  cursor: pointer;
  transition: all 150ms linear;
}
html[dir=rtl] .popup-close {
  left: 1.25rem;
}
html[dir=ltr] .popup-close {
  right: 1.25rem;
}
.no-touchevents .popup-close:hover {
  transform: scale(1.1);
}

.stacked-popups {
  position: fixed;
  bottom: var(--gutter-regular);
  display: grid;
  grid-template-columns: 100%;
  gap: var(--gutter-regular);
  justify-items: end;
  z-index: 999;
  pointer-events: none;
  width: calc(100% - (var(--gutter-regular) * 2));
}
.stacked-popups--position-left {
  width: auto;
  padding-inline-end: var(--gutter-regular);
}
html[dir=rtl] .stacked-popups--position-left {
  right: var(--gutter-regular);
}
html[dir=ltr] .stacked-popups--position-left {
  left: var(--gutter-regular);
}
html[dir=rtl] .stacked-popups--position-right {
  left: var(--gutter-regular);
}
html[dir=ltr] .stacked-popups--position-right {
  right: var(--gutter-regular);
}
.stacked-popups .popup {
  pointer-events: all;
  position: relative;
  bottom: 0;
  right: 0 !important;
  left: 0 !important;
  width: 100%;
}
@media screen and (max-width: 474px) {
  .stacked-popups .popup {
    max-width: 100%;
  }
}
.stacked-popups .popup-title {
  display: block;
  padding-inline-end: 1.875rem;
}

.popup-text--no-content {
  background-color: rgba(0, 0, 0, 0.04);
}
.popup-text--no-content svg {
  width: 100%;
  height: 100%;
}

.cta-stacked {
  display: flex;
  flex-wrap: wrap;
  gap: 0.9375rem;
}