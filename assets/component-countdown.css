.countdown {
  z-index: 9;
  position: relative;
  display: flex;
  justify-content: center;
}
.countdown.hide {
  visibility: hidden;
}
.align-content--horizontal-left .countdown {
  justify-content: flex-start;
}
html[dir=rtl] .align-content--horizontal-left .countdown {
  justify-content: flex-end;
}
.align-content--horizontal-center .countdown {
  justify-content: center;
}
.align-content--horizontal-right .countdown {
  justify-content: flex-end;
}
html[dir=rtl] .align-content--horizontal-right .countdown {
  justify-content: flex-start;
}
.countdown .time {
  display: flex;
}
.countdown .time-holder {
  display: flex;
  flex-direction: column;
  margin-left: 1.25rem;
  margin-right: 1.25rem;
  text-align: center;
}
.rich-text__container--countdown--compact .countdown .time-holder {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
@media screen and (max-width: 1023px) {
  .countdown .time-holder {
    margin-left: 0.9375rem;
    margin-right: 0.9375rem;
  }
}
@media screen and (max-width: 474px) {
  .countdown .time-holder {
    margin-left: 0.625rem;
    margin-right: 0.625rem;
  }
}
.countdown .time-holder .time-data {
  display: flex;
  direction: ltr;
}
.countdown .time-holder > span > span {
  width: 2.6875rem;
  height: 3.75rem;
  background: var(--main-background);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: -0.0625rem;
  border-radius: 0.375rem;
  font-size: 3.625rem;
  font-weight: bold;
}
.rich-text__container--countdown--compact .countdown .time-holder > span > span {
  font-size: 2rem;
  width: 1.4375rem;
  height: 2.5rem;
}
@media screen and (max-width: 1023px) {
  .countdown .time-holder > span > span {
    width: 1.25rem;
    height: 3.125rem;
    font-size: 1.875rem;
  }
  .rich-text__container--countdown--compact .countdown .time-holder > span > span {
    font-size: 1.875rem;
  }
}
@media screen and (max-width: 767px) {
  .countdown .time-holder > span > span {
    width: 1.25rem;
    height: 2.5rem;
    font-size: 1.75rem;
  }
  .rich-text__container--countdown--compact .countdown .time-holder > span > span {
    font-size: 1.75rem;
  }
}
.countdown .time-holder:not(:first-child) .time-data {
  position: relative;
}
.countdown .time-holder:not(:first-child) .time-data:after {
  content: ":";
  font-size: 3.625rem;
  line-height: 1;
  display: inline-block;
  position: absolute;
  left: -1.4375rem;
  top: 0;
}
.rich-text__container--countdown--compact .countdown .time-holder:not(:first-child) .time-data:after {
  font-size: 2rem;
  top: 0.1875rem;
  left: -0.6875rem;
}
@media screen and (max-width: 1023px) {
  .countdown .time-holder:not(:first-child) .time-data:after {
    font-size: 1.875rem;
    top: 0.4375rem;
    left: -1.125rem;
  }
}
@media screen and (max-width: 767px) {
  .countdown .time-holder:not(:first-child) .time-data:after {
    font-size: 1.25rem;
    top: 0.5rem;
    left: -0.8125rem;
  }
}
.countdown .time-helper {
  opacity: 0.6;
  margin-top: 0.625rem;
  text-align: center;
  text-transform: uppercase;
}
@media screen and (max-width: 767px) {
  .countdown .time-helper {
    margin-top: 0;
  }
}
.rich-text__container--countdown--compact .countdown .time-helper {
  margin-top: 0;
  font-size: 0.6875rem;
}

@media screen and (max-width: 767px) {
  .rich-text__container--has-countdown .rich-text__text {
    text-align: center;
    justify-content: center;
  }
}

.rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-right .rich-text__countdown {
  padding-inline-end: calc((100vw - 1280px) / 2);
}
@media screen and (max-width: 1360px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-right .rich-text__countdown {
    padding-inline-end: var(--gutter-small);
  }
}
@media screen and (max-width: 767px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-right .rich-text__countdown {
    padding: var(--gutter-regular);
  }
}
@media screen and (min-width: 1361px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-right .countdown {
    justify-content: flex-end;
  }
}
.rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-left .rich-text__countdown {
  padding-inline-start: calc((100vw - 1280px) / 2);
}
@media screen and (max-width: 1360px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-left .rich-text__countdown {
    padding-inline-start: var(--gutter-small);
  }
}
@media screen and (max-width: 767px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-left .rich-text__countdown {
    padding: var(--gutter-regular);
  }
}
@media screen and (min-width: 1361px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--countdown-left .countdown {
    justify-content: flex-start;
  }
}
.rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--has-countdown-compact .rich-text__text {
  padding-top: var(--gutter-regular) !important;
  padding-bottom: var(--gutter-regular) !important;
}
@media screen and (min-width: 768px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--has-countdown-compact {
    grid-template-columns: 1fr 1fr;
  }
}
@media screen and (max-width: 767px) {
  .rich-text__container--fullwidth.rich-text__container--has-countdown.rich-text__container--has-countdown-compact .rich-text__text {
    padding: var(--gutter-regular);
  }
}