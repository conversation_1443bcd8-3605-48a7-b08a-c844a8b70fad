.no-js-hidden,
#site-store-selector-handle,
.shopify-payment-button {
  display: none !important;
}

.qty-selector {
  border-style: solid !important;
  border-width: var(--border-width-forms) !important;
  border-color: var(--color-borders-forms-primary) !important;
  border-radius: var(--border-radius-forms);
}

.product-quantity {
  min-width: 0 !important;
}

.header-actions search-form button {
  display: block !important;
  font-size: 0;
  width: 3.125rem;
  height: 100%;
  position: absolute;
  top: 0;
}
html[dir=rtl] .header-actions search-form button {
  right: 0;
}
html[dir=ltr] .header-actions search-form button {
  left: 0;
}

.lazy-image img {
  opacity: 1 !important;
}

.lazy-image:before,
.lazy-image:after {
  display: none !important;
}