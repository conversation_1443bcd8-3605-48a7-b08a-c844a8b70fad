{%- comment -%}
  This snippet structures the micro-data using JSON-LD specification. For product and article pages, the data is 100% outputted by Shopify. The theme only offers schema for the organization and breadcrumb.
{%- endcomment -%}

<script type="application/ld+json">
  [
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "url": {{ shop.url | json }}
    },
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": {{ shop.name | json }},
      "url": {{ shop.url | json }}
    }
  ]
</script>

<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": {{ 'general.breadcrumb.homepage' | t | json }},
      "item": "{{ shop.url }}"
    }
    {%- if request.page_type == 'product' -%}
      {%- assign product_position = '2' -%}
      {%- if collection -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ collection.title | json }},
          "item": "{{ shop.url }}{{ collection.url }}"
        }
        {%- assign product_position = '3' -%}
      {%- endif -%}
      ,{
        "@type": "ListItem",
        "position": {{ product_position }},
        "name": {{ product.title | json }},
        "item": "{{ shop.url }}{{ product.url }}"
      }
    {%- elsif request.page_type == 'collection' -%}
      ,{
        "@type": "ListItem",
        "position": 2,
        "name": {{ collection.title | json }},
        "item": "{{ shop.url }}{{ collection.url }}"
      }
    {%- elsif request.page_type == 'blog' -%}
      ,{
        "@type": "ListItem",
        "position": 2,
        "name": {{ blog.title | json }},
        "item": "{{ shop.url }}{{ blog.url }}"
      }
    {%- elsif request.page_type == 'article' -%}
      ,{
        "@type": "ListItem",
        "position": 2,
        "name": {{ blog.title | json }},
        "item": "{{ shop.url }}{{ blog.url }}"
      }, {
        "@type": "ListItem",
        "position": 3,
        "name": {{ blog.title | json }},
        "item": "{{ shop.url }}{{ article.url }}"
      }
    {%- elsif request.page_type == 'page' -%}
      ,{
        "@type": "ListItem",
        "position": 2,
        "name": {{ page.title | json }},
        "item": "{{ shop.url }}{{ page.url }}"
      }
    {%- endif -%}
  ]
}
</script>

{%- if request.page_type == 'product' -%}
  <script type="application/ld+json">{{ product | structured_data }}</script>
{%- elsif request.page_type == 'article' -%}
  <script type="application/ld+json">{{ article | structured_data }}</script>
{%- endif -%}