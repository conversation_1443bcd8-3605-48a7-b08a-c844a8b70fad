{% comment %}
  Renders the product quantity selector.

  Accepts:
  - variant: {Object} the selected product variant object.
  - default_to_first_variant: {<PERSON><PERSON><PERSON>} if false, the quantity selector will be hidden by default until a variant is selected (via js).
  - id: {String} unique identifier for the quantity selector id's.

  Usage:
  {% render 'product-quantity', variant: product.selected_or_first_available_variant, default_to_first_variant: true, id: section.id %}
{% endcomment %}

<product-quantity class="product-quantity" 
  {% unless variant.available %} style="display:none" {% endunless %} 
  {% unless default_to_first_variant %} style="display:none" {% endunless %} 
data-js-product-quantity>

  <button class="product-quantity__minus qty-minus no-js-hidden" aria-label="{{ 'general.accessibility_labels.decrease_quantity' | t }}" role="button" controls="qty-{{ id }}">
    {%- render 'theme-symbols', icon: 'minus' -%}
  </button>

  <label for="qty-{{ id }}" class="visually-hidden">{{ 'general.accessibility_labels.quantity' | t }}</label>
  <input type="number" name="quantity" value="1" min="1" max="999" class="product-quantity__selector qty-selector text-size--xlarge" id="qty-{{ id }}">

  <button class="product-quantity__plus qty-plus no-js-hidden" aria-label="{{ 'general.accessibility_labels.increase_quantity' | t }}" role="button" controls="qty-{{ id }}">
    {%- render 'theme-symbols', icon: 'plus' -%}
  </button>

</product-quantity>