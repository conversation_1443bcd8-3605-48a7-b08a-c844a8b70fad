{% comment %}
  Renders a placeholder svg.

  Accepts:
  - image: {String} a valid Shopify placeholder name value.ded).
	- ratio: {String} image aspect ratio (needs to be a valid width/height ratio).
  - class: {String} additional classes to add to the figure element.

  Usage:
	{% render 'lazy-svg', image: 'product-1', class: 'svg-placeholder svg-placeholder--background' %}
{% endcomment %}

<span 
	class="{{ class }}" 
	{% unless ratio == blank %} style="padding-top: {{ 100 | divided_by: ratio }}%" {% endunless %}
>
	{{ image | placeholder_svg_tag }}	
</span>

{%- assign ratio = blank -%}