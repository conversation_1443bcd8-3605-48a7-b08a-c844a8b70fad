{% comment %}
  Renders a simplified product item.

  Accepts:
  - product: {Object} Product object
  - show_title: {<PERSON>olean} Show product title
  - show_price: {<PERSON>olean} Show product price
  - show_vendor: {<PERSON>olean} Show product vendor
  - show_rating: {<PERSON><PERSON>an} Show product rating
  - show_quick_buy: {<PERSON><PERSON><PERSON>} Show quick buy button
  - show_local_availability: {<PERSON><PERSON><PERSON>} Show local availability
  - show_icons: {Boolean} Show product icons
  - blank_product: {Boolean} Flag to indicate if the product is blank (for onboarding)
  - layout: {String} Desktop grid classes
  - mobile_layout: {String} Mobile grid classes
  - preload: {Boolean} Flag to indicate if the thumbnail should be preloaded
  - show_badges: {<PERSON>olean} Show badges
  - product_icon_1_image: {String} Metafield for icon 1 image
  - product_icon_1_label: {String} Metafield for icon 1 label
  - product_icon_2_image: {String} Metafield for icon 2 image
  - product_icon_2_label: {String} Metafield for icon 2 label
  - product_icon_3_image: {String} Metafield for icon 3 image
  - product_icon_3_label: {String} Metafield for icon 3 label
  - product_icon_4_image: {String} Metafield for icon 4 image
  - product_icon_4_label: {String} Metafield for icon 4 label
  - product_icon_5_image: {String} Metafield for icon 5 image
  - product_icon_5_label: {String} Metafield for icon 5 label
  - product_icon_6_image: {String} Metafield for icon 6 image
  - product_icon_6_label: {String} Metafield for icon 6 label

  Usage:
  {% render 'simple-product-item',
    product: product,
    show_title: true,
    show_price: true,
    show_vendor: false,
    show_rating: false,
    show_quick_buy: true,
    show_local_availability: false,
    show_icons: false,
    blank_product: false,
    layout: 'grid-1',
    mobile_layout: 'palm-1',
    preload: true,
    show_badges: true,
    product_icon_1_image: 'custom.icon1_image',
    product_icon_1_label: 'custom.icon1_label',
    product_icon_2_image: 'custom.icon2_image',
    product_icon_2_label: 'custom.icon2_label',
    product_icon_3_image: 'custom.icon3_image',
    product_icon_3_label: 'custom.icon3_label',
    product_icon_4_image: 'custom.icon4_image',
    product_icon_4_label: 'custom.icon4_label',
    product_icon_5_image: 'custom.icon5_image',
    product_icon_5_label: 'custom.icon5_label',
    product_icon_6_image: 'custom.icon6_image',
    product_icon_6_label: 'custom.icon6_label'
  %}
{% endcomment %}

{%- assign product_variant = product.selected_or_first_available_variant -%}

<div id="product-item-{{ product.id }}" class="product-item card" data-js-product-item>
  {%- liquid
    if settings.product_card_aspect_ratio == 'natural'
      if product.media.size == 0 or blank_product
        assign aspect_ratio = 1
      else
        unless product.featured_media.aspect_ratio > 0
          assign aspect_ratio = 1
        else
          assign aspect_ratio = product.featured_media.aspect_ratio
        endunless
      endif
    else
      assign aspect_ratio = settings.product_card_aspect_ratio
    endif

    if settings.within_filter_enabled and product_collection
      assign product_url = product.url | within: product_collection
    else
      assign product_url = product.url
    endif
  -%}

  <a
    href="{{ product_url }}"
    class="
      card__image product-item__image
      {% if settings.product_card_show_secondary_image and product.media.size >= 2 %} product-item__image--has-secondary {% endif %}
    "
    style="padding-top: {{ 100 | divided_by: aspect_ratio }}%"
  >
    {%- liquid
      unless layout == 'shop'
        if layout contains 'grid-4'
          assign sizes = '(max-width: 1023px) calc((100vw - 100px) / 3), (max-width: 1280px) calc((100vw - 120px) / 4), 300px"'
        else
          assign sizes = '(max-width: 1280px) calc((100vw - 120px) / 3), 420px"'
        endif
        assign sizes = sizes | prepend: ' (max-width: 767px) calc((100vw - 50px) / 2), '
        if mobile_layout contains 'palm-1'
          assign sizes = sizes | prepend: 'sizes="(max-width: 474px) calc(100vw - 30px), '
        else
          assign sizes = sizes | prepend: 'sizes="(max-width: 474px) calc(50vw - 20px), '
        endif
      endunless

      if product.media.size == 0 or blank_product
        echo 'image' | placeholder_svg_tag
      else
        render 'lazy-image', image: product.featured_media, alt: product.title, ratio: aspect_ratio, fit: settings.product_card_aspect_ratio_fit, type: 'background', class: 'product-item__image-figure product-item__image-figure--primary lazy-image--animation', sizes: sizes, preload: preload
      endif

      if settings.product_card_show_secondary_image and product.media.size >= 2
        render 'lazy-image', image: product.media[1], ratio: aspect_ratio, fit: settings.product_card_aspect_ratio_fit, type: 'background', class: 'product-item__image-figure product-item__image-figure--secondary lazy-image--animation', sizes: sizes
      endif
    -%}
  </a>

  <div class="card__text product-item__text gutter--regular spacing--xlarge remove-empty-space text-align--{{ settings.product_card_text_alignment }}">
    {%- if show_title -%}
      <a
        class="product-item__title"
        href="{{ product_url }}"
        title="{{ product.title | escape }}"
      >
        <div class="remove-line-height-space--small">
          <span class="text-animation--underline text-size--{{ settings.product_card_text_size }} text-line-height--small text-weight--bold text-animation--underline">
            {%- liquid
              unless blank_product
                echo product.title
              else
                echo 'general.onboarding.product_title' | t
              endunless
            -%}
          </span>
        </div>
      </a>
    {%- endif -%}

    {%- if show_price -%}
      <div class="product-item__price text-size--large equalize-white-space">
        <div class="remove-line-height-space">
          {% unless blank_product %}
            {%- render 'product-price',
              target: product,
              variant: product_variant,
              product_price_varies: product.price_varies
            -%}
          {%- else -%}
            {{ 9999 | money }}
          {%- endunless -%}
        </div>
      </div>
    {%- endif -%}

    {%- if show_vendor -%}
      <div class="product-item__vendor text-size--large text-color--opacity equalize-white-space">
        <div class="remove-line-height-space">
          {% unless blank_product %}
            {{ product.vendor }}
          {% else %}
            DNATURAL
          {% endunless %}
        </div>
      </div>
    {%- endif -%}

    {%- if show_rating -%}
      <div class="product-item__ratings">
        {%- render 'rating',
          vendor: settings.reviews_app,
          product: product,
          hide_no_reviews_on_mobile: true
        -%}
      </div>
    {%- endif -%}

    {%- if show_quick_buy -%}
      {%- if product.available or blank_product -%}
        <div class="product-item__quick-buy">
          {%- render 'quick-buy',
            product: product,
            button_classes: 'button--small button--fullwidth add-to-cart--mobile-large',
            rendered_from_info_article: rendered_from_info_article,
            layout: layout
          -%}
        </div>
      {%- endif -%}
    {%- endif -%}

    {%- if show_local_availability -%}
      <pickup-availability-compact
        class="no-js-hidden product-item__local-availability text-size--xsmall"
        data-base-url="{{ shop.url }}{{ rou.root_url }}"
        data-variant-id="{{ product_variant.id }}"
        data-has-only-default-variant="{{ product.has_only_default_variant }}"
      >
        <span class="alert alert--note alert--circle alert--circle-loading">
          {{ 'store_availability.compact_widget.checking_availability' | t }}
        </span>
      </pickup-availability-compact>
    {%- endif -%}

    {%- if show_icons -%}
      <div class="product-item__icons">
        {% # theme-check-disable VariableName %}
        {%- liquid
          # Icon 1
          if product_icon_1_image != blank or product_icon_1_label != blank
            assign image_metafield_keys = product_icon_1_image | split: '.'
            assign icon_1_image = product.metafields[image_metafield_keys[0]][image_metafield_keys[1]] | image_url
            assign label_metafield_keys = product_icon_1_label | split: '.'
            assign icon_1_image_label = product.metafields[label_metafield_keys[0]][label_metafield_keys[1]]
            render 'product-icon-label', icon: icon_1_image, label: icon_1_image_label, style: 'tooltip'
          endif

          # Icon 2
          if product_icon_2_image != blank or product_icon_2_label != blank
            assign image_metafield_keys = product_icon_2_image | split: '.'
            assign icon_2_image = product.metafields[image_metafield_keys[0]][image_metafield_keys[1]] | image_url
            assign label_metafield_keys = product_icon_2_label | split: '.'
            assign icon_2_image_label = product.metafields[label_metafield_keys[0]][label_metafield_keys[1]]
            render 'product-icon-label', icon: icon_2_image, label: icon_2_image_label, style: 'tooltip'
          endif

          # Icon 3
          if product_icon_3_image != blank or product_icon_3_label != blank
            assign image_metafield_keys = product_icon_3_image | split: '.'
            assign icon_3_image = product.metafields[image_metafield_keys[0]][image_metafield_keys[1]] | image_url
            assign label_metafield_keys = product_icon_3_label | split: '.'
            assign icon_3_image_label = product.metafields[label_metafield_keys[0]][label_metafield_keys[1]]
            render 'product-icon-label', icon: icon_3_image, label: icon_3_image_label, style: 'tooltip'
          endif

          # Icon 4
          if product_icon_4_image != blank or product_icon_4_label != blank
            assign image_metafield_keys = product_icon_4_image | split: '.'
            assign icon_4_image = product.metafields[image_metafield_keys[0]][image_metafield_keys[1]] | image_url
            assign label_metafield_keys = product_icon_4_label | split: '.'
            assign icon_4_image_label = product.metafields[label_metafield_keys[0]][label_metafield_keys[1]]
            render 'product-icon-label', icon: icon_4_image, label: icon_4_image_label, style: 'tooltip'
          endif

          # Icon 5
          if product_icon_5_image != blank or product_icon_5_label != blank
            assign image_metafield_keys = product_icon_5_image | split: '.'
            assign icon_5_image = product.metafields[image_metafield_keys[0]][image_metafield_keys[1]] | image_url
            assign label_metafield_keys = product_icon_5_label | split: '.'
            assign icon_5_image_label = product.metafields[label_metafield_keys[0]][label_metafield_keys[1]]
            render 'product-icon-label', icon: icon_5_image, label: icon_5_image_label, style: 'tooltip'
          endif

          # Icon 6
          if product_icon_6_image != blank or product_icon_6_label != blank
            assign image_metafield_keys = product_icon_6_image | split: '.'
            assign icon_6_image = product.metafields[image_metafield_keys[0]][image_metafield_keys[1]] | image_url
            assign label_metafield_keys = product_icon_6_label | split: '.'
            assign icon_6_image_label = product.metafields[label_metafield_keys[0]][label_metafield_keys[1]]
            render 'product-icon-label', icon: icon_6_image, label: icon_6_image_label, style: 'tooltip'
          endif
        -%}
        {% # theme-check-disable VariableName %}
      </div>
    {%- endif -%}
  </div>

  {%- liquid
    unless blank_product
      if show_badges
        render 'product-badges', product: product, text_class: 'text-size--xsmall'
      endif
    endunless
  -%}
</div>
