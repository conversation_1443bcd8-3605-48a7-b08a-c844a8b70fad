{% comment %}
  Renders the product price.

  Accepts:
  - target: {Object} the target from where we extract the price (can be either a product or the selected variant) object.
  - variant: {Object} the selected product variant object.
  - show_saving: {<PERSON>olean} show saving amount.
  - class: {String} additional class.

  Usage:
  {% render 'product-price', target: product, variant: product.selected_or_first_available_variant, show_saving: true, class: 'product-price--highlight' %}
{% endcomment %}

<div class="product-price {{ class }}">

  {%- liquid
    if settings.show_currency_codes
      assign iso_code = localization.country.currency.iso_code | prepend: ' ' 
    endif
  -%}

  {%- if product_price_varies -%}

    <span class="product-price--original">
      {%- liquid
        assign price = target.price | money | append: iso_code
        if target.compare_at_price > target.price or target.compare_at_price_varies
          echo 'products.grid.on_sale_from_html' | t: price: price 
        else
          echo 'products.grid.from_text_html' | t: price: price 
        endif
      -%}
    </span>
    
    <del class="product-price--compare"></del>

  {%- else -%}

    <span class="product-price--original {{ text_size_class }}">{{ target.price | money | append: iso_code }}</span>
    
    <del class="product-price--compare">
      {%- liquid 
        if target.compare_at_price > target.price
          echo target.compare_at_price | money | append: iso_code | prepend: '<span>' | append: '</span>'
          if show_saving
            assign saving = target.compare_at_price | minus: target.price | money | append: iso_code
            echo 'products.page.sales_amount_html' | t: amount: saving | prepend: '<span>' | append: '</span>'
          endif
        endif
      -%}
    </del>

  {%- endif -%}

  <span class="product-price--unit text-size--regular">
    {%- liquid
      if variant.unit_price_measurement
        echo variant.unit_price | money | append: iso_code
        echo ' / '
        if variant.unit_price_measurement.reference_value != 1
          echo variant.unit_price_measurement.reference_value
        endif
        echo variant.unit_price_measurement.reference_unit
      endif
    -%}
  </span>

</div>