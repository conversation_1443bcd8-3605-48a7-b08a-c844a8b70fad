{% comment %}
  Renders section headings

  Accepts:
  - heading: {String} the heading text.
  - subheading: {String} the subheading text.
  - button: {<PERSON><PERSON><PERSON>} if true, it will render a button.
  - button_label: {<PERSON><PERSON><PERSON>} the button label.
  - link: {String} the button link.
  - heading_tag: {String} the HTML heading tag (H1-H6, span, etc.).
  - layout: {String} the layout of the section heading (can be "section-heading--left" or "section-heading--center").

  Usage:
  {% render 'section-heading', heading: 'Heading', button: true, button_label: 'View more', heading_tag: 'h1', layout: 'section-heading--center' %}
{% endcomment %}

{%- unless heading == blank and subheading == blank and button == blank -%}

  <div class="section-heading {{ layout }} gutter-bottom--page">
    
    {%- unless heading == blank and subheading == blank -%}

      <div class="section-heading__text remove-empty-space">

        {%- unless heading == blank -%}
          <{{ heading_tag }} class="section-heading__title h2">{{ heading | escape }}</{{ heading_tag }}>
        {%- endunless -%}

        {%- unless subheading == blank -%}
          <span class="section-heading__subheading text-size--xlarge">{{ subheading | escape }}</span>
        {%- endunless -%}

      </div>

    {%- endunless -%}
    
    {%- unless button == blank or layout contains 'center' -%}

      <div class="section-heading__actions">
        {%- if button -%}
          <a class="text-size--large text-link" href="{{ link }}">{{ button_label | t }}</a>
        {%- endif -%}
      </div>

    {%- endunless -%}
  
  </div>

{%- endunless -%}