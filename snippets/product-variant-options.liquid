{% comment %}
  Renders product variant options

  Accepts:
  - product: {Object} the product object.
  - option: {Object} the product option object.
  - current_variant: {Object} the current selected variant.
  - type: {String} type of the option (select or radio).
  - default_to_first_variant: {<PERSON><PERSON>an} if true, the first available variant will be selected by default.
  - reload_product: {String} how to handle combined product's reload (can be 'page' or null).
  - no_history: {<PERSON><PERSON><PERSON>} if true, URL will not be updated on variant change.
  - id: {String} unique identifier for the option id's.

  Usage:
  {% render 'product-variant-options', product: product, option: option, type: 'select', id: section.id, default_to_first_variant: true, reload_product: product.url %}
{% endcomment %}

{%- assign id = id | default: section.id -%}

{%- for value in option.values -%}

  {%- capture option_id -%}{{ product.handle }}-option-{{ option.name | escape | downcase | strip | handleize }}-{{ forloop.index }}-{{ id }}{%- endcapture -%} 
  {%- capture option_name -%}product-{{ option.name | escape | downcase | strip | handleize }}-{{ id }}{%- endcapture -%}

  {%- capture data_input_set -%}
    data-available="{{ value.variant.available }}"
    {% if value.product_url %} data-product-url="{{ value.product_url }}" {% if reload_product != blank %} data-product-custom-url="{{ reload_product }}" {% endif %} {% endif %}
    data-option-value-id="{{ value.id }}"
    data-variant-id="{{ value.variant.id }}"
    {% if option.selected_value == value %} data-selected {% endif %}
  {%- endcapture -%}

  {%- liquid

    assign unavailable_class = ""
    unless value.variant.available
      assign unavailable_class = "disabled"
    endunless 

    if value.product_url != blank
      assign unavailable_class = ""
    endif

    assign first_variant_product_url = false
    if value.product_url
      assign first_variant_product_url = true
    endif

  -%}

  {%- if type == 'select' -%}

    <option class="product-variant-value" value="{{ value | escape }}" 
      {% if default_to_first_variant and option.selected_value == value %}selected{% endif %} 
      {% if default_to_first_variant == false and first_variant_product_url and option.selected_value == value %} data-silent-selected {% endif %}
      {{ data_input_set }}
    >
      {{ value | escape }}
    </option>

  {%- else  -%} 

    <div class="product-variant__item {{ unavailable_class }}">
                  
      <input type="radio" name="{{ option_name }}" id="{{ option_id }}" value="{{ value | escape }}" 
        {% if default_to_first_variant and option.selected_value == value %}checked{% endif %} 
        {% if default_to_first_variant == false and first_variant_product_url and option.selected_value == value %} data-silent-selected {% endif %}
        class="product-variant__input product-variant-value {{ unavailable_class }}"
        {{ data_input_set }}
      >

      <label for="{{ option_id }}" class="product-variant__label">

        {%- if value.swatch.image -%}
          {%- assign color_swatch_image = value.swatch.image -%}
          <img class="product-variant__item-swatch product-variant__item-swatch--color"
            src="{{ color_swatch_image | image_url: width: 28, height: 28, crop: 'center' }}"
            srcset="
              {{ color_swatch_image | image_url: width: 28, height: 28, crop: 'center' }} 28w,
              {{ color_swatch_image | image_url: width: 56, height: 56, crop: 'center' }} 56w,
              {{ color_swatch_image | image_url: width: 84, height: 84, crop: 'center' }} 84w
            "
            sizes="30px" width="30" height="30"
            loading="lazy"
            alt="{{ color_swatch_image.alt | escape }}"
          />

        {%- elsif value.swatch.color -%}

          {%- assign color_swatch_background = 'rgb(' | append: value.swatch.color.rgb | append: ')' -%}
          <span class="product-variant__item-swatch product-variant__item-swatch--image" style="background-color:{{ color_swatch_background }}"></span>

        {%- endif -%}

        {{ value | escape }}

      </label>

    </div>

  {%- endif -%}

{%- endfor -%}