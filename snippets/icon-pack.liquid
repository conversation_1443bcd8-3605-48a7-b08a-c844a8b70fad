{% comment %}
  Renders an icon from our selected icon pack.

  Accepts:
  - icon: {String} the selected icon.

  Usage:
  {% render 'icon-pack', icon: 'appointment' %}
{% endcomment %}

{%- if icon == "appointment" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-linecap:round;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round"><circle cx="32" cy="32" r="30"/><path d="m32 2v6m0 48v6m-30-30h6m48 0h6"/><path d="m48 20-16 16-11-9"/></g></svg>
{%- elsif icon == "archive-box" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m58 18v40h-52v-40"/><path d="m2 6h60v12h-60z"/><path d="m20 30h24"/></g></svg>
{%- elsif icon == "box" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m2 62v-42l12-18h36l12 18v42zm0-42h60"/><path d="m46 34a4 4 0 0 1 -4 4h-20a4 4 0 0 1 -4-4 4 4 0 0 1 4-4h20a4 4 0 0 1 4 4z"/></g></svg>  
{%- elsif icon == "checkout-cart" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m30 22h-18m-10-16h6l10 40h32l3.2-9.7"/><circle cx="20" cy="54" r="4"/><circle cx="46" cy="54" r="4"/><circle cx="46" cy="22" r="16"/><path d="m53 18-8 9-5-5"/></g></svg>
{%- elsif icon == "currency" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><ellipse cx="42" cy="33" rx="12" ry="4"/><path d="m54 41c0 2.2-5.4 4-12 4s-12-1.8-12-4m24 8c0 2.2-5.4 4-12 4s-12-1.8-12-4"/><path d="m30 39h-28v-36h60v36h-8"/><circle cx="32" cy="21" r="8"/><path d="m12 21h4m32 0h4m-40-18a10 10 0 0 1 -10 10m60 0a10 10 0 0 1 -10-10m-50 26a10 10 0 0 1 10 10"/><path d="m30 33v24c0 2.2 5.4 4 12 4s12-1.8 12-4v-24"/></g></svg>
{%- elsif icon == "delivery" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m17 15h26c2.3 0 2.1 1.6 1.7 3.1s-3.7 14.9-3.7 14.9h10.1l4-2 3.9 2v8c0 1.3-.5 2-2 2h-8m-40 0h6.6m9.4 0h14.6"/><path d="m43.6 23h5.4l6.1 8m-24.1-8h-22m18 8h-22"/><path d="m24.8 44a6.9 6.9 0 0 1 -6.2 5c-2.7 0-4.2-2.2-3.4-5a6.9 6.9 0 0 1 6.2-5c2.6 0 4.2 2.2 3.4 5zm24 0a6.9 6.9 0 0 1 -6.2 5c-2.7 0-4.2-2.2-3.4-5a6.9 6.9 0 0 1 6.2-5c2.6 0 4.2 2.2 3.4 5z"/></g></svg>
{%- elsif icon == "delivery-time" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m62 46v-5l-8-7h-8"/><circle cx="24" cy="54" r="4"/><circle cx="54" cy="54" r="4"/><path d="m50 54h-22m-8 0h-4v-8h46v8h-4m-33.5-30h21.5v22m-30 0v-16.2m-14 8.2h6m-2 8h2"/><circle cx="14" cy="18" r="12"/><path d="m14 12v8h6"/></g></svg>
{%- elsif icon == "giftbox" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m56 34v28h-48v-28m-4-12h56v12h-56z"/><path d="m24 22v40m16-40v40m-13.8-57a7.4 7.4 0 0 0 -6.2-3 8 8 0 0 0 -5.3 14c4.2 3.7 17.3 6 17.3 6 0-6-3.4-14.3-5.8-17zm11.6 0a7.4 7.4 0 0 1 6.2-3 8 8 0 0 1 5.3 14c-4.2 3.7-17.3 6-17.3 6 0-6 3.4-14.3 5.8-17z"/></g></svg>
{%- elsif icon == "label" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m24.5 62-22.5-22.5 29.6-29.6 21.1 1.4 1.4 21.1z"/><circle cx="42.8" cy="21.2" r="2" transform="matrix(.70710678 -.70710678 .70710678 .70710678 -2.399784 36.50041)"/><path d="m45.7 5.7c4.2-4.3 10.5-4.9 14-1.4s2.9 9.8-1.4 14.1-10.6 4.9-14.1 1.4"/></g></svg>
{%- elsif icon == "open-24-hours" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="stroke-width:2;stroke-miterlimit:10;stroke:#202020;fill:none;stroke-linejoin:round;stroke-linecap:round"><path d="m56.418 31.908 1.582-7.908m-51.932 0 5.952 30h13.98m0-50-17.932 20m29.932-20 18 20m-54 0h60"/><path d="m54.004 40-6 14h13.996m-3.996-6v12m-26.004-18.45a6 6 0 0 1 8.637 8.277l-8.637 10.173h12"/></g></svg>
{%- elsif icon == "open-box" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="stroke-width:2;stroke-miterlimit:10;stroke:#202020;fill:none;stroke-linejoin:round;stroke-linecap:round"><path d="m10 36.125v14.037l22 11.58 22-11.58v-14.037"/><path d="m54 23.246 7-8.549-21.742-11.42-7.324 8.42z"/><path d="m32 61.742v-27"/><path d="m31.934 11.704-7.258-8.42-21.676 11.485 7 8.742z"/><path d="m32 34.742-8.584 8.929-20.449-11.676 7.033-8.484zm22-11.496 7 8.742-20.324 11.743-8.676-8.989z"/></g></svg>
{%- elsif icon == "paper-bag" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m8 22h48v40h-48z"/><path d="m22 26v-14a10 10 0 0 1 10-10 10 10 0 0 1 10 10v14"/></g></svg>
{%- elsif icon == "shipping-truck" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m2 40v-20h16m0 20v-30h24v8"/><path d="m42 40v-22h10l10 14v16h-4"/><circle cx="14" cy="48" r="6"/><circle cx="52" cy="48" r="6"/><path d="m8 48h-6v-8h40v8h-22m22 0h4"/></g></svg>
{%- elsif icon == "store" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m36 36h12v22h-12z"/><path d="m56 30v28h-48v-28"/><path d="m16 36h14v12h-14z"/><path d="m52.6 6h-41.2l-9.4 16.5a7.5 7.5 0 0 0 15 0 7.5 7.5 0 0 0 15 0 7.5 7.5 0 0 0 15 0 7.5 7.5 0 0 0 15 0zm-50.6 16h60m-30-16v16.5m-15 0 5-16.5m25 16.5-5-16.5"/></g></svg>
{%- elsif icon == "time" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-linecap:round;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round"><circle cx="32" cy="32" r="30"/><path d="m32 10v23l13 7"/></g></svg>
{%- elsif icon == "time-limit" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-linecap:round;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round"><path d="m54 6v10h-10m-12 43a27 27 0 1 1 21.751-43m-8.766 39.678a26.819 26.819 0 0 1 -6.985 2.653m15.751-10.331a27.159 27.159 0 0 1 -4.711 4.945m8.751-12.932a26.821 26.821 0 0 1 -1.58 3.952"/><circle cx="32" cy="32" r="3"/><path d="m33.961 34.261 10.039 7.739m-12-30v17"/></g></svg>
{%- elsif icon == "wallet" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m58 42v13a3 3 0 0 1 -3 3h-50a3 3 0 0 1 -3-3v-45"/><path d="m54 14v-5a3 3 0 0 0 -3-3h-45a4 4 0 0 0 -4 4 4 4 0 0 0 4 4h49a3 3 0 0 1 3 3v13"/><rect height="12" rx="2" width="20" x="42" y="30"/><path d="m12 20h2m8 0h2m8 0h2m8 0h2m-28 32h2m8 0h2m8 0h2m8 0h2"/><circle cx="49" cy="36" r="1"/></g></svg>
{%- elsif icon == "alcohol-bottle" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m44 38v-4c0-6-8-10-8-14v-8m-8 0v8c0 4-8 8-8 14v4m0 14v7c0 1.7 1.2 3 7 3h10c5.5 0 7-1.3 7-3v-7"/><path d="m20 38h24v14h-24zm8-36h8v10h-8z"/></g></svg>
{%- elsif icon == "apple" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m23 6c5.2 0 10 3.8 10 12v2m4-6c0-6 2-12 12-12 0 6-2 12-12 12z"/><path d="m33 20c-3.4 0-4.9-2-12-2s-14 6.8-14 18 8.2 26 16.9 26c5.3 0 5.7-4 8.6-4s5.2 4 8.7 4 15.8-9.5 15.8-26-10.4-18-14-18-6.1 2-10 2z"/></g></svg>
{%- elsif icon == "asian-cuisine" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m46 2-13.9 28m25.9-20-18 20m-18 32h20"/><path d="m2.1 30a30 30 0 0 0 59.8 0z"/></g></svg>
{%- elsif icon == "beer-bottle" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m28 6c-.3 1.7-1 7.6-1.4 12h10.8c-.4-4.4-1.1-10.3-1.4-12"/><path d="m39.4 28.3c-1.2-1.1-1.7-3.1-1.8-6.8 0-.9-.1-2.2-.2-3.6h-10.8c-.1 1.4-.2 2.7-.2 3.6-.1 3.7-.5 5.7-1.8 6.8a8.2 8.2 0 0 0 -2.6 5.7v24c0 2 1.8 4 5.6 4h8.8c3.8 0 5.6-2 5.6-4v-24a8.2 8.2 0 0 0 -2.6-5.7z"/><ellipse cx="32" cy="40" rx="6" ry="8"/><path d="m38 6v-2a2 2 0 0 0 -2-2h-8a2 2 0 0 0 -2 2v2z"/></g></svg>
{%- elsif icon == "birthday" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m58 50v9m-52 0v-7.9m-4 7.9h60m-30-38v-8m0-6v-2"/><path d="m12 41v-5a5 5 0 0 1 5-5h30a5 5 0 0 1 5 5v5m-34-10v-5a5 5 0 0 1 5-5h18a5 5 0 0 1 5 5v5m-40 20.1v-5.1a5 5 0 0 1 5-5h42a5 5 0 0 1 5 5v4m0 0a10.2 10.2 0 0 1 -3.9 1c-1.9 0-4-2-6.1-2s-4 2-6 2-3.8-2-6-2-4 2-6 2-4.1-2-6-2-4 2-6 2-4.1-2-6-2-4 2-6 2"/></g></svg>
{%- elsif icon == "brewed-coffee" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m19 32h26v14c0 8-8.191 12-8.191 12h-9.809s-8-4-8-12zm26 2h2a6 6 0 0 1 0 12h-2"/><path d="m37 28c0-12 12-14 12-26m-22 26c0-6.125 6-8.9 6-14m25 46a2 2 0 0 1 -2 2h-48a2 2 0 0 1 -2-2 2 2 0 0 1 2-2h48a2 2 0 0 1 2 2z"/></g></svg>
{%- elsif icon == "can" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m47 24c0-.6-.6-1.1-.9-1.3l-1.1-.7c-1.5-1-4-3-4-6m-24 40.9a2.1 2.1 0 0 0 .4 1.4l3.5 3.2c.3.3.5.5.8.5h20.5c.4 0 .6-.2.8-.5l3.5-3.2a2.1 2.1 0 0 0 .4-1.4v-16.9m-23.9-24c0 4-4.8 6.3-5.1 6.6s-.9.7-.9 1.3v12.1m4-26 6 6m-6 0h22"/><path d="m17 56c0-7.9 30-11.8 30-16m-2-18c-6.7 3.5-28 7.4-28 14m30 4v-16c0-.6-.6-1.1-.9-1.3l-1.1-.7m-28 14v20.9"/><circle cx="34" cy="3" r="1"/><circle cx="42" cy="7" r="1"/><circle cx="36" cy="11" r="1"/></g></svg>
{%- elsif icon == "cheese" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><circle cx="47" cy="38" r="3"/><circle cx="35" cy="47" r="5"/><circle cx="26.1" cy="36" r="2"/><circle cx="16" cy="46" r="2"/><circle cx="12" cy="34" r="4"/><path d="m62 30-32-28s-28 3.3-28 20z"/><path d="m2 22v32l60 8v-6.2l-1.5.3a5 5 0 0 1 0-10l1.5.3v-16.4"/></g></svg>
{%- elsif icon == "coffee-beans" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m54 18c-3.7 4.9-5.9 5.8-12 6-4.4.1-7.7.9-9.9 3.1"/><path d="m40 42c9-.1 18-6.1 21-14.5s-2-17.9-11.9-19.3-20.7 5-24.1 14.3l-.2.7"/><path d="m10 32c3.7 4.9 5.9 5.8 12 6s9.9 1.6 12 6"/><path d="m39 36.5c3.4 9.3-2 17.9-11.9 19.3s-20.7-5-24.1-14.3 2-17.9 11.9-19.3 20.7 5 24.1 14.3z"/></g></svg>
{%- elsif icon == "dinner" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><circle cx="32.7" cy="33" r="19"/><circle cx="32.7" cy="33" r="11"/><path d="m61.7 34.2v13.8a2 2 0 0 1 -2 2 2 2 0 0 1 -2-2v-13.9m-51-22.1v9.5"/><path d="m61.7 34.2v-22.2a6.3 6.3 0 0 0 -6.3 6.3v12.7a3.2 3.2 0 0 0 3.2 3.2zm-50.5-9.5-.7-12.7h-7.6l-.6 12.7a3.5 3.5 0 0 0 2.4 3.5v19.8a2 2 0 1 0 4 0v-19.9a3.5 3.5 0 0 0 2.5-3.4z"/></g></svg>
{%- elsif icon == "dome-plate" -%}
<svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/sv  g"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m6 45v-2a26 26 0 0 1 52 0v2m-29.4-27.9a4 4 0 1 1 6.7.1"/><path d="m2 45h60m-2 0-2 5a4.2 4.2 0 0 1 -4 3h-44c-1.7 0-3.2-1.3-4-3l-2-5"/></g></svg>
{%- elsif icon == "french-bread" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-linecap:round;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round"><path d="m4 35c0-6.428 7.599-20 28-20 20.404 0 28 9.083 28 20 0 10.595-9.981 10.002-21.999 10.002h-24c-2.983 0-10.001-2.548-10.001-10.002zm15.056-17.793c2.7 1.77 6.945 5.676 6.945 11.792m4-13.999c3 1.383 5.44 6.318 5.999 12m4-11.46c2.701 1.769 5.44 5.78 5.999 11.46"/><path d="m2 51h60"/></g></svg>
{%- elsif icon == "ice-pop" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m32 46v16"/><path d="m44 46v-32a12 12 0 0 0 -12-12 12 12 0 0 0 -12 12v32zm-16-32v24m8-24v24"/></g></svg>
{%- elsif icon == "margarita" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m26.5 34v28m-10 0h20"/><path d="m33.5 12a11 11 0 1 1 7 11.3"/><path d="m44.1 18a18.9 18.9 0 0 0 .4-4v-2h-36v2a18.9 18.9 0 0 0 .4 4"/><path d="m8.9 18c.8 3.6 2.8 5.3 5.6 6s5 2.8 5.4 4.8 1.4 5.2 6.6 5.2 6.2-3.3 6.7-5.3 1.7-3.9 5.4-4.8 4.8-2.4 5.6-6z"/></g></svg>
{%- elsif icon == "microwave" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><rect height="40" rx="3" width="60" x="2" y="12"/><circle cx="52" cy="41" r="3"/><rect height="25.99" rx="3" width="34" x="8" y="20"/><path d="m50 22h4m-4 8h4"/></g></svg>
{%- elsif icon == "milk-carton" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m56 46v16h-48v-48l12-12h24l12 12v8m-24-8h24m-24 38v10"/><path d="m20 2 12 12v14m-12-26v60"/><path d="m56 46-24 6v-24l24-6z"/></g></svg>
{%- elsif icon == "orange-slice" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><circle cx="32" cy="32" r="30"/><circle cx="32" cy="32" r="26"/><path d="m32 10v44m22-22h-44m37.6-15.6-31.2 31.2m31.2 0-31.2-31.2"/></g></svg>
{%- elsif icon == "pizza-slice" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><circle cx="28.999" cy="23.999" r="4"/><circle cx="34.999" cy="33.999" r="2"/><circle cx="20.999" cy="37.499" r="2.5"/><path d="m49.979 35.087 7.021-3.087c-5.989-13.913-17.87-24.433-32-30l-2.527 7.3"/><path d="m22.473 9.3-15.473 44.7 42.978-18.91c-5.145-11.962-15.36-21.003-27.505-25.79zm-5.474 40.299v8.4m8-11.92v15.92m16-22.907v12.907"/></g></svg>
{%- elsif icon == "spirit-glass" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m20 62h24m-12-16v16m21.9-34c-.8-11-7.9-26-7.9-26h-28s-7.1 14.3-7.9 26"/><path d="m10.1 28c0 .7-.1 1.4-.1 2 0 11.1 10.1 16 22 16s22-4.9 22-16c0-.7 0-1.3-.1-2z"/></g></svg>  
{%- elsif icon == "sushi-roll" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><ellipse cx="14" cy="22.014" rx="11.999" ry="5.999"/><path d="m2 22.014v12c0 3.315 5.371 6 12 6s12-2.685 12-6v-12"/><ellipse cx="34.009" cy="11.988" rx="11.999" ry="5.999"/><path d="m60.001 28.016-40 21.996m41.998-5.998-45.998 13.998"/><ellipse cx="14" cy="21.988" rx="2.571" ry=".857"/><ellipse cx="34.009" cy="11.961" rx="2.571" ry=".856"/><path d="m26 28.451a20 20 0 0 0 8.012 1.533c6.627 0 12-2.686 12-6v-12m-24.001.004v5.522"/></g></svg>
{%- elsif icon == "take-out-box" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m36 42h18m-40-28v-4a8 8 0 0 1 8-8h20a8 8 0 0 1 8 8v4"/><path d="m2 28 12-14h36l12 14v34h-60z"/><path d="m28 62v-34l-14-14v48m48-34h-34"/></g></svg>
{%- elsif icon == "wine-glass" -%}
  <svg height="64" viewBox="0 0 64 64" width="64" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="m47.8 18c-.5-6.7-1.8-16-1.8-16h-28s-1.3 9.3-1.8 16m15.8 21.1v22.9m-10 0h20"/><path d="m16.2 18c-.1 1.5-.2 2.9-.2 4 0 6 5.5 16 16.2 16s15.8-10 15.8-16c0-1.1-.1-2.5-.2-4z"/></g></svg>
{%- elsif icon == "ecological" -%}
  <svg fill="none" height="128" viewBox="0 0 128 128" width="128" xmlns="http://www.w3.org/2000/svg"><path d="m88.2385 68.9566c.3776.3776.8924.5947 1.4304.5947s1.0529-.2171 1.4304-.5947l3.0326-3.0325c9.1891-9.1893 10.0361-18.9389 2.4721-28.2203-1.5102-1.8535-2.6892-3.9823-3.5018-6.3285l-1.5223-4.4173c-.2291-.6522-.7782-1.1559-1.4536-1.3043-.6753-.1605-1.3849.0455-1.8766.538l-3.0325 3.0325c-10.116 10.116-10.116 26.5835 0 36.6987zm-.1716-36.8703.7551-.7552.4694 1.373c.961 2.7922 2.3692 5.3331 4.1883 7.5641 5.7791 7.0723 5.5844 13.9953-.6067 21.1132.4917-4.5547.3544-11.3292-2.5065-19.3396-.3776-1.0529-1.5334-1.5909-2.5752-1.2245-1.0529.3775-1.5909 1.5334-1.2245 2.5751 3.2272 9.04 2.6095 16.3638 1.8998 20.0835l-.4007-.4007c-8.5474-8.5362-8.5474-22.4414.0008-30.9893zm21.5141 42.3979c-.996-2.9638-4.212-4.577-7.13-3.5936l-11.0079 3.3876c-.0343.0112-.0798.0232-.1142.0455-1.8878.7097-3.8562 1.0872-5.8702 1.1215-1.9908.0575-4.0279-.4917-5.9165-1.5102l-.103-.0575c.2858-.1716.5492-.3664.7783-.5835.0112-.0112.0232-.0231.0343-.0343 1.0298-1.0529 1.6021-2.4379 1.6021-3.9137 0-1.4879-.5835-2.8952-1.6364-3.948l-8.9712-8.9489c-3.4341-3.4555-8.1715-5.4009-13.0239-5.4009h-22.5763v-1.911c0-1.1103-.8469-1.9796-1.9564-1.9796h-12.4397c-1.1103 0-2.0139.8924-2.0139 2.0028 0 1.1103.9044 2.0027 2.0139 2.0027l10.3907-.0008v26.8921h-10.3907c-1.1103 0-2.0139.9499-2.0139 2.0594 0 1.1104.9044 2.0594 2.0139 2.0594h12.4276c1.1103 0 1.9565-.961 1.9565-2.0714v-4.5316c3.5472.3775 5.756.8357 6.6715 1.2819 2.2662 1.6947 17.3822 12.7596 23.7329 12.7596h15.3683c2.5752 0 5.104-.4917 7.5065-1.4072l17.4287-6.6485c.023-.0111.045-.0231.069-.0343 2.701-1.1447 4.097-4.2339 3.169-7.0379zm-4.726 3.2959-17.3822 6.6029c-1.9453.7439-3.9823 1.1103-6.065 1.1103l-15.3691-.0008c-3.8794 0-15.1967-7.3581-21.4115-12.0037-.0687-.0575-.1717-.0918-.2515-.1373-1.4304-.8015-3.9249-1.3506-8.7311-1.8423v-16.4555h22.5783c3.7876 0 7.4842 1.5334 10.1616 4.2226l8.9712 8.9713c.2977.2977.4574.6753.4574 1.0872 0 .4007-.1485.7782-.435 1.076-.6865.5723-3.6049.5947-4.2682.0798l-7.0835-7.0835c-.7895-.7894-2.0594-.7894-2.8496 0-.7895.7895-.7895 2.0594 0 2.8496l7.1178 7.1178c.3775.3664.87.6634 1.4192.8925.0455.0343.103.0574.1485.0918l5.7448 3.1122c2.4945 1.3506 5.2301 2.0251 7.9072 2.0027 2.4602-.0343 4.8747-.4917 7.1753-1.3506l10.9864-3.3758c.858-.2857 1.785.1828 2.071 1.0297.264.7895-.126 1.6707-.892 2.0028z" fill="#000"/></svg>
{%- elsif icon == "leaf" -%}
  <svg fill="none" height="128" viewBox="0 0 128 128" width="128" xmlns="http://www.w3.org/2000/svg"><g fill="#000"><path d="m101.02 53.4123c3.918-9.4784 5.813-19.5272 5.635-29.8745l-.013-.7682c-.013-.7732-.636-1.3988-1.411-1.4117l-.769-.0129c-10.3464-.1783-20.3955 1.7174-29.8742 5.6355-9.139 3.7784-17.3375 9.2587-24.3655 16.285-7.0292 7.0265-12.5066 15.2241-16.285 24.3655-3.9181 9.4785-5.8138 19.5272-5.6355 29.8746l.0029.1575-6.5504 6.5499c-.5613.562-.5613 1.472 0 2.033.5612.561 1.4711.561 2.0324 0l6.5504-6.5527.1575.0029c10.3468.1782 20.3959-1.7175 29.8745-5.6355 9.1391-3.7785 17.3376-9.2587 24.3655-16.2851 7.0293-7.0265 12.5074-15.224 16.2854-24.3636zm-69.8402 41.3765c.1682-9.0102 1.9837-17.767 5.4128-26.0626 1.6058-3.8873 3.5351-7.5928 5.7673-11.107l-.0007 25.9893zm14.053-14.053v-27.2997c2.1234-2.8657 4.4664-5.5832 7.0214-8.1411 2.0174-2.0174 4.1349-3.8974 6.3434-5.6427v27.7175zm16.2379-16.2385v-27.0028c4.1637-2.9638 8.6301-5.4694 13.365-7.4997v21.1345zm16.2385-16.2379v-19.421c7.6909-2.9123 15.7547-4.4607 24.0378-4.6161zm26.0698-22.0051c-.155 8.2808-1.704 16.3466-4.616 24.0375h-19.421zm-26.911 26.9111h21.1345c-2.0273 4.735-4.5337 9.2043-7.4997 13.3651h-27.0027zm-17.5959 38.2426c-8.2966 3.4291-17.0504 5.2446-26.0608 5.4128l11.1801-11.18h25.9874c-3.5121 2.2322-7.2203 4.1614-11.1069 5.7672zm15.2578-8.6402h-27.2648l13.365-13.365h27.7175c-1.7482 2.2113-3.6282 4.329-5.6427 6.3435-2.5579 2.5579-5.2783 4.9009-8.1461 7.0236-.0107.0007-.0179-.0022-.0286-.0022z"/><path clip-rule="evenodd" d="m101.05 53.3395c-.01.0241-.02.0483-.03.0724s-.02.0485-.03.0726c-3.7771 9.1104-9.2447 17.2831-16.2554 24.291-7.0279 7.0263-15.2264 12.5066-24.3655 16.285-9.4786 3.9181-19.5277 5.8138-29.8745 5.6355l-.1575-.0028-6.5504 6.5528c-.5613.561-1.4712.561-2.0324 0-.5613-.562-.5613-1.471 0-2.033l6.5504-6.5501-.0029-.1575c-.1783-10.3473 1.7174-20.3961 5.6355-29.8745 3.7784-9.1414 9.2558-17.339 16.285-24.3655 7.028-7.0263 15.2265-12.5066 24.3655-16.285 9.4786-3.9181 19.5278-5.8138 29.8742-5.6355l.769.0129c.775.0128 1.398.6385 1.411 1.4117l.013.7681c.178 10.3209-1.708 20.3448-5.605 29.8019zm4.189-32.515c1.064.0177 1.919.8759 1.937 1.936l.012.768c.18 10.4196-1.729 20.5409-5.675 30.0874-3.8051 9.2046-9.3227 17.461-16.4013 24.5368-7.0773 7.0757-15.3346 12.5953-24.5388 16.4007-9.5257 3.9376-19.6239 5.8466-30.0188 5.6766l-6.3902 6.393c-.7695.769-2.0172.769-2.7868 0-.7695-.77-.7695-2.017.0001-2.787l6.3902-6.39c-.17-10.3955 1.7394-20.4933 5.677-30.0188 3.8054-9.2066 9.3222-17.4631 16.4009-24.539 7.0773-7.0756 15.3345-12.5953 24.5387-16.4007 9.5467-3.9462 19.6686-5.8554 30.088-5.6759m-67.8794 47.9145c-3.331 8.0583-5.1394 16.5516-5.3943 25.2898-.0075.2573-.0137.5149-.0185.7727l11.1794-11.1802.0007-25.9893c-.1798.283-.3576.5672-.5334.8527-2.0074 3.2594-3.7574 6.68-5.2339 10.2543zm5.2339-9.2271c-1.8034 3.0097-3.3879 6.1552-4.7409 9.4307-3.234 7.8236-5.0221 16.0601-5.3354 24.5342l10.0756-10.0764zm17.3476 31.9493c-8.03 3.3052-16.4873 5.1-25.19 5.3539-.2574.0075-.515.0137-.7728.0185l11.1801-11.1801h25.9874c-.2828.1798-.5668.3576-.8522.5334-3.2285 1.9891-6.6185 3.7254-10.1567 5.1933-.0326.0135-.0652.027-.0978.0405s-.0654.0271-.098.0405zm-.1059-.5333c3.2751-1.3532 6.4224-2.9376 9.4314-4.741h-23.8873l-10.0764 10.0764c8.4741-.3134 16.7079-2.1014 24.5323-5.3354zm15.462-8.1474c.0107 0 .0179.0028.0286.0021 2.8678-2.1226 5.5882-4.4657 8.1461-7.0235 1.852-1.852 3.5903-3.7912 5.217-5.8102.1427-.1771.2846-.3549.4257-.5333h-27.7175l-13.365 13.365zm-.1449-.5332c2.7937-2.0787 5.446-4.3689 7.9425-6.8654 1.7358-1.7358 3.371-3.5488 4.9076-5.433h-26.3844l-12.2984 12.2984zm-29.1525-28.7984c2.1234-2.8658 4.4664-5.5833 7.0214-8.1411 1.8545-1.8545 3.7935-3.5928 5.8101-5.2172.1772-.1427.355-.2845.5333-.4255v27.7175l-13.3648 13.3659zm.5333 26.012v-25.8356c2.0799-2.7926 4.3707-5.4431 6.8654-7.9406 1.738-1.738 3.5509-3.3733 5.4328-4.9076v26.3846zm15.7046-14.9508 13.365-13.368v-21.1345c-.1781.0764-.3559.1535-.5333.2312-4.5373 1.9883-8.8247 4.4162-12.8317 7.2685zm.5334-1.2878 12.2983-12.3011v-20.0997c-4.3406 1.9188-8.449 4.2438-12.2983 6.9615zm15.7051-14.9502 24.0378-24.0371c-.258.0049-.516.0111-.773.0186-8.0118.2346-15.8133 1.7759-23.2648 4.5976zm.5333-1.2875 22.1795-22.1791c-7.6306.283-15.0643 1.7625-22.1795 4.4151zm25.5365-20.7175-24.037 24.0375h19.421c2.821-7.4514 4.363-15.2548 4.598-23.2647.007-.2573.013-.515.018-.7728zm-.57 1.3248c-.283 7.6288-1.764 15.0642-4.4154 22.1793h-17.764zm-26.1201 26.1197-12.3011 12.2983h25.4396c2.7195-3.8467 5.0453-7.9575 6.9616-12.2983zm-.2209-.5334-13.3679 13.365h27.0027c2.8544-4.0041 5.2831-8.2942 7.2688-12.8316.0777-.1774.1546-.3552.2309-.5334z" fill-rule="evenodd"/></g></svg>
{%- elsif icon == "organic" -%}
  <svg fill="none" height="128" viewBox="0 0 128 128" width="128" xmlns="http://www.w3.org/2000/svg"><path d="m33.8468 51.2236c.334.1458.5958.4184.7268.757 1.0137 2.5298 2.1834 4.995 3.5028 7.3814 2.7496 4.92 5.9742 9.5616 9.6279 13.8591 1.9371 2.2833 3.6166 4.0462 4.7467 5.1786zm0 0c-.334-.1456-.7131-.1524-1.0522-.0184zm40.9812 29.7165c-.2919.2178-.4847.5418-.5364.9012-.0516.3593.0424.7244.2612 1.0148.2191.2902.5447.4824.9059.5337.3612.0514.728-.0421 1.0196-.2601 5.3512-3.9119 10.1206-8.5566 14.1669-13.7953m-15.8172 11.6057 1.7831 2.3712c.0003-.0002.0006-.0005.001-.0007 5.3659-3.9228 10.1488-8.5802 14.2069-13.8332m-15.991 11.4627c5.142-3.7606 9.7288-8.2198 13.6259-13.2478m-13.6259 13.2478 13.4424-13.3784m2.3748 1.7727.169.1485c.0016-.0018.0032-.0037.0048-.0055m-.1738-.143.178.1375c-.0014.0018-.0028.0036-.0042.0055m-.1738-.143c.2516-.2863.3723-.6635.3327-1.0419-.0394-.3784-.2353-.7232-.5406-.9519m.3817 2.1368c.2896-.3326.4287-.7696.3826-1.2083m0 0c-.0459-.441-.2741-.8425-.6294-1.1086m.6294 1.1086-.2209.0231.2209-.023zm-.7643-.9285.135-.18s-.0001-.0001-.0001-.0001m-.1349.1801.1349-.1801m-.1349.1801c-.3053-.229-.6925-.321-1.0689-.2542-.3761.0668-.7077.2863-.9145.6059m2.1183-.5318c-.3553-.2664-.8055-.3733-1.2431-.2957l.0348.196-.0348-.196c-.434.0772-.8174.3291-1.0587.6969m0 0c.0019-.0024.0037-.0049.0056-.0073l.1779.1379m-.1835-.1306c-.0018.0027-.0036.0055-.0054.0083l.1889.1223m11.9221-20.9962-.002-.0007c-.5129-.1784-1.0819-.0867-1.5126.244l-.0002-.0003-.0104.009-.0004.0003c-.0078.0061-.1252.0968-.3529.262-.2308.1673-.567.4054-.9963.6951-.8587.5794-2.0894 1.3648-3.5936 2.2035-3.0104 1.6783-7.1069 3.5644-11.5041 4.4432h-.0002c-7.3083 1.4672-12.351 4.4104-15.3564 9.0209l-.0003.0005c-3.5823 5.5261-4.2123 13.2774-1.9928 23.6659-3.3019 3.0562-6.2319 6.1135-8.5074 8.5665.6514-5.0335.0402-10.1525-1.7831-14.8992 5.4751-10.8263 7.0865-19.5518 4.7355-26.6066l-.211.0703.211-.0703c-1.9509-5.8516-6.5916-10.4704-14.126-14.1363-9.0417-4.3999-15.6562-12.7659-15.9165-13.0951-.0025-.0031-.0043-.0055-.0057-.0072-.3312-.4273-.8568-.6581-1.3962-.6146h-.0004c-.5387.0445-1.0193.3549-1.2792.8274l-.0003.0006c-12.3041 22.5382-9.5763 35.7676-5.045 42.9188v.0001c5.0905 8.0269 15.0286 12.3305 28.1203 12.3305h.0036c.6769 0 1.3774 0 2.0807-.0438 1.8109 4.9747 2.133 10.3663.925 15.5217-.3091 1.1842-.7189 2.3407-1.2243 3.4557l-.002.005c-.2015.472-.1632 1.012.1024 1.452h.0001c.2656.44.7264.726 1.2392.771.5128.044 1.0165-.158 1.3546-.545l.0018-.003c4.0496-4.7544 8.356-9.2871 12.8988-13.5772 4.0336 1.2534 8.2283 1.9199 12.4546 1.9781h.0031c6.6101 0 12.2165-1.9391 16.3468-5.7902l.0002-.0002c5.5046-5.1445 10.8526-15.8947 5.3366-37.9825l.001-.0001-.002-.0063c-.139-.4941-.51-.89-.994-1.0635zm-75.9368 21.7976-.0003-.0005c-5.4557-8.6178-4.0605-21.8923 4.049-37.6129 2.6013 2.9304 8.2741 8.63 15.4866 12.1514l.0006.0003c6.7815 3.2877 10.8407 7.3097 12.4927 12.268v.0001c1.9695 5.8974.7062 13.3855-3.9124 22.8857-1.1256-1.1306-2.778-2.8689-4.6795-5.1102l-.0001-.0002c-3.6442-4.2863-6.8604-8.9157-9.6028-13.8229-1.3145-2.3777-2.48-4.8339-3.4901-7.3544-.1529-.3942-.4577-.7112-.8461-.8807l-.0001-.0001c-.3888-.1694-.8299-.1774-1.2247-.0214h-.0001c-.3943.156-.7113.4608-.8812.8487l-.0001.0001c-.1698.388-.1789.8271-.0249 1.2216l.0003.0008c4.8232 12.2201 13.1026 21.8196 17.3896 26.2669-11.7024-.1303-20.4146-3.9476-24.7564-10.8403zm58.6086-10.8373c5.6369-1.2858 10.9847-3.5979 15.775-6.8181 3.3092 15.0395 1.5262 26.5164-4.9751 32.5829-5.1485 4.7962-13.1968 6.0962-22.9731 3.8124.6848-.4278 1.4461-.9146 2.2986-1.4801l.001-.0007c.4747-.3186.7426-.8653.7026-1.4343-.04-.5693-.3822-1.0732-.8964-1.3226-.5142-.2493-1.1229-.2073-1.5975.1112l-.0005.0004c-1.3775.9304-2.5197 1.6742-3.4453 2.1949-1.7563-8.9488-1.1382-15.513 1.8066-20.0236l.0001-.0001c2.4945-3.8263 6.8206-6.2957 13.2991-7.6213v.0001z" fill="#000" stroke="#000" stroke-width=".45"/></svg>
{%- elsif icon == "tree" -%}
  <svg fill="none" height="128" viewBox="0 0 128 128" width="128" xmlns="http://www.w3.org/2000/svg"><path d="m40.1786 45.4534c0 1.3545.0744 2.7217.2205 4.0719-4.2096 5.2507-6.5158 11.812-6.5158 18.5596 0 15.7961 12.3926 28.7519 27.9658 29.6523v12.7788c0 .959.778 1.734 1.7372 1.734.9586 0 1.7372-.777 1.7372-1.734l-.0007-12.7788c15.5739-.9011 27.9657-13.8558 27.9657-29.6523v-.0001c-.0022-6.7473-2.3081-13.3083-6.5145-18.5589.1461-1.3502.2205-2.7148.2205-4.0725 0-16.3187-10.4481-29.7034-23.4079-29.7034-12.9597 0-23.408 13.3844-23.408 29.7034zm21.6591 5.9651.0022 19.6366c-4.8471-.8277-8.5467-5.0568-8.5467-10.1346 0-.9589-.7773-1.7372-1.7372-1.7372-.9596 0-1.7342.778-1.7342 1.7372 0 6.9976 5.2505 12.7867 12.0175 13.6439v19.6949c-13.6488-.8961-24.4867-12.2931-24.4867-26.1746 0-6.1824 2.1934-12.1879 6.18-16.9077l.0005-.0006c.312-.3716.4551-.8577.3964-1.3385l-.0002-.0015c-.1859-1.4469-.2785-2.9204-.2785-4.3809 0-7.2505 2.2485-13.8166 5.8677-18.5648 3.6191-4.748 8.5978-7.6671 14.0687-7.6671 5.4716 0 10.4503 2.9185 14.0692 7.6662 3.6191 4.7479 5.8673 11.314 5.8673 18.5657 0 1.4604-.0926 2.934-.2785 4.3806-.0623.4812.0812.9689.3966 1.3408 3.9839 4.72 6.18 10.7256 6.1801 16.9076-.0088 13.8805-10.8456 25.2776-24.5024 26.1747v-9.306c6.7677-.8571 12.0175-6.6462 12.0175-13.6439 0-.9589-.7773-1.7372-1.7372-1.7372-.9589 0-1.7372.7774-1.7372 1.7372 0 5.0768-3.7023 9.3067-8.5468 10.1347v-17.2692c6.7677-.8572 12.0175-6.6463 12.0175-13.6439 0-.959-.7773-1.7372-1.7372-1.7372-.9589 0-1.7372.7773-1.7372 1.7372 0 5.0768-3.7023 9.3067-8.5467 10.1346v-9.247c0-.9595-.778-1.7342-1.7373-1.7342-.9586 0-1.7372.777-1.7372 1.7342z" fill="#000" stroke="#000" stroke-width=".5"/></svg>
{%- elsif icon == "earth-globe" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><g style="stroke-width:2;stroke-miterlimit:10;stroke:#202020;fill:none;stroke-linejoin:round;stroke-linecap:round"><path d="m57.7 16.5a30 30 0 1 1 -14.3-12.2"/><path d="m32 12c0 4.2 6 1.7 6 6s-4.6 7.3-8 5-7.9-3.5-11.9 2.1-1.3 12 1.5 11.9 5.5-2.8 6.7.6 1.5 3.4 2.8 4.2 1.3 2.2.9 4.1 2 8 4 8 3.8-.8 4-4 2.6-3.3 3.8-4.2-.9-4.3 1.3-6.5 6.6-6.2 2.8-7.2-3.5-1.8-4-3.4-2-3.2 1-3.3a11.9 11.9 0 0 0 8.7-3.6c2.5-2.6 3.8-5.2 6.1-5.2a25.6 25.6 0 0 0 -6.5-7.5 30 30 0 0 0 -7.8-4.7c-6.7 3.2-11.4 3.5-11.4 7.7z"/></g></svg>
{%- elsif icon == "favorite" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><path d="m32 46-17 11.9 7-19.9-16-12h20l6-19 6 19h20l-15 12 6 20.1z" style="stroke-width:2;stroke-miterlimit:10;stroke-linecap:round;stroke:#202020;fill:none;stroke-linejoin:round"/></svg>
{%- elsif icon == "flag" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m8 3.8c13.3-6.6 31.7 7.5 48 .2v30c-15.7 7.7-34.7-6.5-48 .1"/><path d="m8 62v-59"/></g></svg>
{%- elsif icon == "helpline" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><path d="m52.3 48.8c1.2.8 3 1.9 2.7 4.3s-4 8.9-12 8.9-17.7-6.3-26.2-14.8-14.8-18.3-14.8-26.2 7-11.7 8.9-12 3.6 1.5 4.3 2.7l6 9.2a4.3 4.3 0 0 1 -1.1 5.8c-2.6 2.1-6.8 4.6 2.9 14.3s12.3 5.4 14.3 2.9a4.3 4.3 0 0 1 5.8-1.1z" style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"/></svg>
{%- elsif icon == "like-hand" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m18 54h2c4 0 12 4 16 4h16a4 4 0 0 0 0-8h2a4 4 0 0 0 0-8h2a4 4 0 0 0 0-8m0 0h2a4 4 0 1 0 0-8h-20a81.1 81.1 0 0 0 2-18 4 4 0 0 0 -8 0s-1.8 19.9-14 21.8"/><path d="m2 60 16-2v-32h-16z"/><circle cx="10" cy="50" r="1"/></g></svg>
{%- elsif icon == "map-marker" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><g style="stroke-width:2;stroke-miterlimit:10;stroke:#202020;fill:none;stroke-linejoin:round;stroke-linecap:round"><path d="m38.1 46h13.9l8 16h-56l8-16h13.9"/><path d="m32 2a18.1 18.1 0 0 0 -18.1 18.1c0 16.3 18.1 32.3 18.1 32.3s18.1-16 18.1-32.3a18.1 18.1 0 0 0 -18.1-18.1z"/><ellipse cx="32" cy="20" rx="6" ry="6"/></g></svg>
{%- elsif icon == "security-shield" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><g style="fill:none;stroke:#202020;stroke-miterlimit:10;stroke-width:2;stroke-linejoin:round;stroke-linecap:round"><path d="m6 2h52v22.064a41.973 41.973 0 0 1 -26.006 37.936 41.97 41.97 0 0 1 -25.994-37.936z"/><path d="m22 27 9 9 15-16"/></g></svg>
{%- elsif icon == "star" -%}
  <svg height="256" viewBox="0 0 64 64" width="256" xmlns="http://www.w3.org/2000/svg"><path d="m32 48-17 10 4-19-14-13 19-2 8-18 8 18 19 2-14 13 4 19z" style="stroke-width:2;stroke-miterlimit:10;stroke-linecap:round;stroke:#202020;fill:none;stroke-linejoin:round"/></svg>
{%- endif -%}