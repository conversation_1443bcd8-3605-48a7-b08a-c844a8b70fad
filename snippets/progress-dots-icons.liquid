{% comment %}
  Renders an icon from our selected icon pack (used in the product page progress dots block).

  Accepts:
  - icon: {String} the selected icon.
  - color_1: {String} the primary color.
  - color_2: {String} the secondary color.

  Usage:  
  {% render 'progress-dots-icons', icon: 'coffee-bean' %}
{% endcomment %}
 
{%- if icon == "coffee-bean" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="21" fill="none"><path fill="{{ color_1 }}" stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21.566 9.051c1.889 5.167-1.111 9.945-6.611 10.722-5.5.778-11.5-2.777-13.39-7.944C-.322 6.662 2.678 1.885 8.178 1.107c5.5-.778 11.5 2.778 13.389 7.944Z"/><path stroke="{{ color_2 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.455 6.551c2.056 2.722 3.278 3.222 6.667 3.334 3.389.11 5.5.889 6.666 3.333"/></svg>

{%- elsif icon == "circle" -%}
  
<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="none"><circle cx="5" cy="5" r="5" fill="{{ color_1 }}"/></svg>

{%- elsif icon == "circle-ghost" -%}
  
<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="{{ color_2 }}"><circle cx="5" cy="5" r="4.5" stroke="{{ color_1 }}" fill="{{ color_2 }}" /></svg>

{%- elsif icon == "circle-ghost-active" -%}
  
<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="{{ color_2 }}"><circle cx="5" cy="5" r="4.5" stroke="{{ color_1 }}" fill="{{ color_1 }}" /></svg>

{%- elsif icon == "glass-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19v12m-5 0h10"/><path fill="{{ color_1 }}" stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 1v9c0 2.571-1.286 9-9 9s-9-6.429-9-9V1h18Z"/></svg>

{%- elsif icon == "glass" -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19v12m-5 0h10M19 1v9c0 2.571-1.286 9-9 9s-9-6.429-9-9V1h18Z"/></svg>

{%- elsif icon == "drop-active" -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="30" fill="none"><path fill="{{ color_1 }}" d="M20.95 19.05a10.295 10.295 0 0 0-.5-2.45C18.45 9.4 11 0 11 0S3.55 9.4 1.5 16.6a10.3 10.3 0 0 0-.5 2.45c0 .25-.05.5-.05.75A10.1 10.1 0 0 0 11 30a10.1 10.1 0 0 0 10-10.2c0-.25 0-.5-.05-.75Z"/></svg>

{%- elsif icon == "drop" -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="23" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" d="M21.95 20.05a10.295 10.295 0 0 0-.5-2.45C19.45 10.4 12 1 12 1s-7.45 9.4-9.5 16.6a10.3 10.3 0 0 0-.5 2.45c0 .25-.05.5-.05.75A10.1 10.1 0 0 0 12 31a10.1 10.1 0 0 0 10-10.2c0-.25 0-.5-.05-.75Z"/></svg>

{%- elsif icon == "apple" -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.5 3c2.6 0 5 1.9 5 6v1m2-3c0-3 1-6 6-6 0 3-1 6-6 6Z"/><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.5 10c-1.7 0-2.45-1-6-1s-7 3.4-7 9 4.1 13 8.45 13c2.65 0 2.85-2 4.3-2 1.45 0 2.6 2 4.35 2 1.75 0 7.9-4.75 7.9-13s-5.2-9-7-9c-1.8 0-3.05 1-5 1Z"/></svg>
  
{%- elsif icon == "apple-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path fill="{{ color_1 }}" d="M24.5 1.5c-5 0-6 3-6 6 5 0 6-3 6-6Z"/><path fill="{{ color_1 }}" d="M21.5 8.5c-1.45 0-2.6.65-4 .9-.05-4.75-3.05-6.9-6-6.9a1 1 0 1 0 0 2c1.9 0 3.95 1.3 4 4.9-1.15-.25-2.2-.9-5-.9-3.55 0-7 3.4-7 9s4.1 13 8.45 13c2.65 0 2.85-2 4.3-2 1.45 0 2.6 2 4.35 2 1.75 0 7.9-4.75 7.9-13s-5.2-9-7-9Z"/></svg>

{%- elsif icon == "bottle" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 19v-2c0-3-4-5-4-7V6m-4 0v4c0 2-4 4-4 7v2m0 7v3.5c0 .85.6 1.5 3.5 1.5h5c2.75 0 3.5-.65 3.5-1.5V26M10 19v7m12-7v7M14 1h4v5h-4V1Z"/></svg>

{%- elsif icon == "bottle-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path fill="{{ color_1 }}" d="M18.5 1a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 0-.5.5v4h5V1ZM20.05 12.45c-.8-.9-1.55-1.75-1.55-2.45V7h-5v3c0 .7-.75 1.55-1.55 2.45-.8.9-2.45 2.7-2.45 4.55v12.5c0 1.75 2.15 2 4 2h5c1 0 4 0 4-2V17c0-1.85-1.3-3.3-2.45-4.55Z"/></svg>

{%- elsif icon == "chili-pepper" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.55 8.75a3 3 0 1 1 5.3-2.65M2 1c1.6 0 4.35 1.1 4.95 3.15"/><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M30 29a2 2 0 0 0-2-2c-4.2 0-6.75-1.3-9.25-7.8s-3.25-9.1-4.6-11l-.45-.55h-.05A5 5 0 0 0 5 11v.2c0 6 9.95 19.75 22.95 19.8h.1A2 2 0 0 0 30 29Z"/></svg>

{%- elsif icon == "chili-pepper-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path fill="{{ color_1 }}" d="M29 27.5c-4.2 0-6.75-1.3-9.25-7.8s-3.25-9.1-4.6-11l-.45-.55h-.05A5 5 0 0 0 6 11.5v.2c0 6 9.95 19.75 22.95 19.8h.1a2 2 0 1 0-.05-4ZM7.25 5.6l.4-.25a6.95 6.95 0 0 1 2.65-.85 3 3 0 0 0-2.3-1h-.45C6.45 1.55 3.75.5 2 .5a1 1 0 0 0 0 2c1.2 0 3.1.75 3.8 2a2.95 2.95 0 0 0-.55 3.1 7.05 7.05 0 0 1 2-2Z"/></svg>

{%- elsif icon == "diamond" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" d="M1 11h30M8 3l8 8m0 0 8-8m-8 8v18M24 3l7 8-15 18L1 11l7-8h16Z"/></svg>

{%- elsif icon == "diamond-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path fill="{{ color_1 }}" d="M22.6 3.15H9.4L16 9.7l6.6-6.55Zm-9 7L7.35 3.9 1.9 10.15h11.7ZM15 12.15H1.8L15 28.85v-16.7ZM18.4 10.15h11.75l-5.5-6.25-6.25 6.25ZM17 12.15v16.7l13.2-16.7H17Z"/></svg>

{%- elsif icon == "heart" -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="30" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" d="M23.5 1.5c-3.25 0-6.45 2.1-7.5 5-1.05-2.9-4.25-5-7.5-5A7.5 7.5 0 0 0 1 9c0 6.5 5.5 13 15 19.5C25.5 22 31 15.5 31 9a7.5 7.5 0 0 0-7.5-7.5Z"/></svg>

{%- elsif icon == "heart-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="28" fill="none"><path fill="{{ color_1 }}" d="M30 8A7.5 7.5 0 0 0 22.5.5c-3.25 0-6.45 2.1-7.5 5-1.05-2.95-4.25-5-7.5-5A7.5 7.5 0 0 0 0 7.8V8a7.5 7.5 0 0 0 .35 2.25C2.05 16.6 9.7 23.05 15 27.5c5.3-4.4 12.9-11.35 14.6-17.2.109-.343.192-.694.25-1.05V9.1c.058-.347.091-.698.1-1.05L30 8Z"/></svg>

{%- elsif icon == "lemon" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><g stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" clip-path="url(%23a)"><path d="M20.85 8.45a5.75 5.75 0 0 0-3.3-5.7M31 8.6c-2.5-4.55-7.65-3.7-9.45-.05 3.3 4.9 8.2 2.15 9.45.05Z"/><path d="M22.95 15.85c-.5-3.3-1.4-3.65-1.4-4.4 0-.75.55-2.2 0-2.75-.55-.55-1.95-.05-2.75 0s-1.15-.9-4.4-1.4c-3.25-.5-6.85 1.7-9.3 4.15-2.45 2.45-4 4.65-4.1 8.2-.1 3.55 1.25 3.15 1.35 ********-1.1 3.05 0 4.1 1.1 1.05 3.15-.15 4.1 0 .95.15 1.25 1.5 4.1 1.35 2.85-.15 5.2-1.1 8.2-4.1 3-3 4.7-5.95 4.2-9.25Z"/></g><defs><clipPath id="a"><path fill="{{ color_2 }}" d="M0 0h32v32H0z"/></clipPath></defs></svg>

{%- elsif icon == "lemon-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path fill="{{ color_1 }}" d="M21.3 11.7c.05-.8.55-2.2 0-2.75l-.2-.1a6.8 6.8 0 0 0-3.85-6.25 1.006 1.006 0 1 0-.9 1.8 4.75 4.75 0 0 1 2.75 4.5h-.55c-.8.05-1.15-.9-4.4-1.4-3.25-.5-6.85 1.75-9.3 4.2-2.45 2.45-4 4.7-4.1 8.2-.1 3.5 1.25 3.15 1.4 *********-1.1 3 0 4.1 1.1 1.1 3.15-.15 4.1 0 .95.15 1.25 1.5 4.1 1.35 2.85-.15 5.2-1.1 8.2-4.1 3-3 4.6-5.95 4.15-9.25-.45-3.3-1.45-3.65-1.4-4.4ZM21.8 8.35c3.35 4.85 8.2 2.1 9.45.05-2.5-4.6-7.65-3.75-9.45-.05Z"/></svg>

{%- elsif icon == "star" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" d="m16 24-8.5 5 2-9.5-7-6.5 9.5-1 4-9 4 9 9.5 1-7 6.5 2 9.5-8.5-5Z"/></svg>

{%- elsif icon == "star-active" -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path fill="{{ color_1 }}" d="m32.02 12.402-11.268-1.337L16 .755l-4.753 10.31L-.02 12.402l8.33 7.707-2.212 11.135L16 25.7l9.901 5.545L23.69 20.11l8.331-7.707Z"/></svg>

{%- elsif icon == "sun" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><g stroke="{{ color_1 }}" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" clip-path="url(%23a)"><path d="M16 1v8m0 14v8m15-15h-8M9 16H1M5.4 5.4l5.65 5.65m9.9 9.9 5.65 5.65m0-21.2-5.65 5.65m-9.9 9.9L5.4 26.6M16 23a7 7 0 1 0 0-14 7 7 0 0 0 0 14Z"/></g><defs><clipPath id="a"><path fill="{{ color_2 }}" d="M0 0h32v32H0z"/></clipPath></defs></svg>

{%- elsif icon == "sun-active" -%}
  
  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="none"><path fill="{{ color_1 }}" d="M31 15h-7.05a7.95 7.95 0 0 0-1.6-3.9l5-5a1 1 0 0 0-1.4-1.4l-5 5A7.95 7.95 0 0 0 17 8.05V1a1 1 0 0 0-2 0v7.1a7.95 7.95 0 0 0-3.9 1.6l-5-5a1 1 0 1 0-1.4 1.4l5 5A7.95 7.95 0 0 0 8.1 15H1a1 1 0 0 0 0 2h7.1a7.95 7.95 0 0 0 1.6 3.9l-5 5a1.001 1.001 0 1 0 1.4 1.4l5-5a7.95 7.95 0 0 0 3.9 1.65V31a1 1 0 0 0 2 0v-7.05a7.95 7.95 0 0 0 3.9-1.6l5 5a1.002 1.002 0 0 0 1.737-.25 1.001 1.001 0 0 0-.337-1.15l-5-5A7.95 7.95 0 0 0 23.95 17H31a1 1 0 0 0 0-2Z"/></svg>

{%- endif -%}