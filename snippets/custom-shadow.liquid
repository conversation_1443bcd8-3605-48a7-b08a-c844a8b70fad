{% comment %}
  Renders inline CSS style to fix custom shadows in sections & blocks.

  Accepts:
  - id: {String} section or block id.
  Usage:
  {% render 'custom-shadow', id: section.id %}
{% endcomment %}

{%- if settings.shadow_cards_x != 0 -%}
  {% style %}
    {%- if settings.shadow_cards_x < 0 -%}
      #css-slider-{{ section_id }} .css-slider-holder {
        scroll-padding-inline-start: {{ settings.shadow_cards_x | abs | plus: settings.shadow_cards_blur }}px;
      }
    {%- else -%}
      #css-slider-{{ section_id }} .css-slider-holder {
        scroll-padding-inline-end: {{ settings.shadow_cards_x | plus: settings.shadow_cards_blur }}px;
      }
    {%- endif -%}
    #css-slider-{{ section_id }} .grid:after {
      content: "";
    }
  {% endstyle %}
{%- endif -%}