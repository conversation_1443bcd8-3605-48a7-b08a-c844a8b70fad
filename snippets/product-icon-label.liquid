{% comment %}
  Renders a product icon with a label.
  
  Accepts:
  - icon: {Object} the image object.
  - label: {String} label text.
  - style: {String} icon style (tooltip, compact).
  
  Usage:
  {% render 'product-icon-label', icon: product.icon, label: product.title, style: 'tooltip' %}
{% endcomment %}

{%- liquid
  
  if icon == blank
    assign icon_name = label | handle | append: '.png'
    assign icon_image = images[icon_name] 
    if icon_image != blank  
      assign icon = icon_image
    endif
  endif
  
  assign show_icon = true
  if style == 'tooltip' and icon == blank
    assign show_icon = false
  elsif style == 'compact' and icon == blank and label == blank
    assign show_icon = false
  endif 
  
-%}

{%- if show_icon -%}

  <span class="text-with-icon text-with-icon--large text-with-icon--smaller-icon-on-small text-with-icon--{{ style }}" 
    {% if style == 'tooltip' %} {% unless label == blank %}style="cursor:pointer"{% endunless %} {% endif %}
  >

    {%- if icon != blank -%}
      <span class="text-with-icon__icon" aria-hidden="true">
        <img src="{{ icon | image_url: width: 32, height: 32, crop: 'center' }}"
          srcset="
            {{ icon | image_url: width: 32, height: 32, crop: 'center' }} 32w,
            {{ icon | image_url: width: 64, height: 64, crop: 'center' }} 64w,
            {{ icon | image_url: width: 96, height: 96, crop: 'center' }} 96w
          "
          sizes="32px" width="32" height="32"
          loading="lazy"
          alt="{{ icon.alt | escape }}"
        />
      </span>
    {%- endif -%}

    {%- unless label == blank -%}
      <span class="text-with-icon__label element--hide-on-small">{{ label | escape }}</span>
    {%- endunless -%}

  </span>

{%- endif -%}