{% comment %}
  Renders an icon from our selected icon pack.

  Accepts:
  - icon: {String} the selected icon.

  Usage:
  {% render 'theme-symbols', icon: 'cart' %}
{% endcomment %}

{%- if icon == "chevron" -%}
  
<svg width="13" height="8" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1.414.086 7.9 6.57 6.485 7.985 0 1.5 1.414.086Z" fill="#000"/><path d="M12.985 1.515 6.5 8 5.085 6.586 11.571.101l1.414 1.414Z" fill="#000"/></svg>

{%- elsif icon == "chevron-left" -%}
  
<svg width="10" height="16" viewBox="0 0 10 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.273 15.089 1.011 7.892l1.362-1.35 7.262 7.197-1.362 1.35Z" fill="#111"/><path d="M9.637 2.067 2.363 9.253.999 7.906 8.273.72l1.364 1.347Z" fill="#111"/></svg>

{%- elsif icon == "house" -%}

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 17 19" fill="none"><path d="m8.364 1 7.364 5.728v9a1.636 1.636 0 0 1-1.637 1.636H2.637A1.636 1.636 0 0 1 1 15.728v-9L8.364 1Zm2.455 8.182h-5v8h5v-8Z"  stroke-width="1.5" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"/></svg>

{%- elsif icon == "cart" -%}
  
  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-left:-2px"><path class="circle" d="M9.5 20C9.77614 20 10 19.7761 10 19.5C10 19.2239 9.77614 19 9.5 19C9.22386 19 9 19.2239 9 19.5C9 19.7761 9.22386 20 9.5 20Z" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path class="circle" d="M18.5 20C18.7761 20 19 19.7761 19 19.5C19 19.2239 18.7761 19 18.5 19C18.2239 19 18 19.2239 18 19.5C18 19.7761 18.2239 20 18.5 20Z" fill="white" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /><path d="M3 3H6.27273L8.46545 13.7117C8.54027 14.08 8.7452 14.4109 9.04436 14.6464C9.34351 14.8818 9.71784 15.0069 10.1018 14.9997H18.0545C18.4385 15.0069 18.8129 14.8818 19.112 14.6464C19.4112 14.4109 19.6161 14.08 19.6909 13.7117L21 6.9999H7.09091" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="fill:none !important"/></svg>

{%- elsif icon == "account" -%}

  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.3333 19.25V17.4167C18.3333 16.4442 17.947 15.5116 17.2593 14.8239C16.5717 14.1363 15.6391 13.75 14.6666 13.75H7.33329C6.36083 13.75 5.4282 14.1363 4.74057 14.8239C4.05293 15.5116 3.66663 16.4442 3.66663 17.4167V19.25" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="fill:none!important"/><path d="M11 10.0833C13.0251 10.0833 14.6667 8.44171 14.6667 6.41667C14.6667 4.39162 13.0251 2.75 11 2.75C8.975 2.75 7.33337 4.39162 7.33337 6.41667C7.33337 8.44171 8.975 10.0833 11 10.0833Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="fill:none!important"/></svg>
  
{%- elsif icon == "search" -%}

  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="10.5" cy="10.5" r="7.5" stroke="black" stroke-width="2" style="fill:none!important"/><path d="M17.1213 15.2929L16.4142 14.5858L15 16L15.7071 16.7071L17.1213 15.2929ZM19.2426 20.2426C19.6331 20.6332 20.2663 20.6332 20.6568 20.2426C21.0473 19.8521 21.0473 19.219 20.6568 18.8284L19.2426 20.2426ZM15.7071 16.7071L19.2426 20.2426L20.6568 18.8284L17.1213 15.2929L15.7071 16.7071Z" fill="black" style="stroke:none!important"/></svg>

{%- elsif icon == "burger" -%}

  <svg fill="none" height="16" viewBox="0 0 20 16" width="20" xmlns="http://www.w3.org/2000/svg"><g fill="#000"><path d="m0 0h20v2h-20z"/><path d="m0 7h20v2h-20z"/><path d="m0 14h20v2h-20z"/></g></svg>

{%- elsif icon == "close" -%}

  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17 1L1 17" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M1 1L17 17" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>

{%- elsif icon == "close-small" -%}

  <svg version="1.1" class="svg close" xmlns="//www.w3.org/2000/svg" xmlns:xlink="//www.w3.org  /1999/xlink" x="0px" y="0px" width="60px" height="60px" viewBox="0 0 60 60" enable-background="new 0 0 60 60" xml:space="preserve"><polygon points="38.936,23.561 36.814,21.439 30.562,27.691 24.311,21.439 22.189,23.561 28.441,29.812 22.189,36.064 24.311,38.186 30.562,31.934 36.814,38.186 38.936,36.064 32.684,29.812 "></polygon></svg>

{%- elsif icon == "plus" -%}

  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 0H7V12H5V0Z" fill="black" style="stroke:none"/><path d="M12 5V7H0L1.19209e-07 5L12 5Z" fill="black" style="stroke:none"/></svg>
  
{%- elsif icon == "minus" -%}

  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 5V7H0L1.19209e-07 5L12 5Z" fill="black" style="stroke:none"/></svg>

{%- elsif icon == "info" -%}

  <svg fill="none" height="13" viewBox="0 0 5 13" width="5" xmlns="http://www.w3.org/2000/svg"><path d="m4.30469 11.1484-.11719.5c-.47917.1927-.86198.3256-1.14844.3985-.28646.0781-.53125.1172-.73437.1172-.41667 0-.72917-.1068-.9375-.3203-.20834-.2188-.3125-.487-.3125-.8047 0-.1198.01041-.2448.03125-.375.02083-.1302.05468-.2943.10156-.4922l.82812-3.32034c.04167-.18229.08073-.3776.11719-.58594.04167-.20833.0625-.38541.0625-.53124 0-.29167-.04948-.48438-.14843-.57813-.09376-.09896-.28907-.14844-.58594-.14844-.11459 0-.26823.01823-.46094.05469-.1875.03646-.330729.06771-.429688.09375l.117188-.5c.40625-.18229.77083-.31771 1.09375-.40625s.57813-.13281.76563-.13281c.42708 0 .73697.10156.92968.30469.19271.20312.28906.47656.28906.82031 0 .09375-.01041.22135-.03124.38281-.02084.16146-.05209.32292-.09376.48438l-.83593 3.32031c-.05209.20312-.09896.40364-.14063.60151-.03646.1928-.05468.3516-.05468.4766 0 .2969.0651.5.19531.6094.13541.1094.35156.164.64843.164.09896 0 .23959-.013.42188-.039.1875-.0261.33073-.0573.42969-.0938zm.55469-9.98434c0 .29688-.09636.55729-.28907.78125-.19271.21875-.43229.32813-.71875.32813-.26562 0-.49479-.10417-.6875-.3125-.19271-.21354-.28906-.45573-.28906-.72656 0-.286463.09635-.536463.28906-.750005s.42188-.320313.6875-.320313c.29688 0 .53906.101563.72656.304688.1875.197917.28126.429688.28126.69531z" fill="#fff" style="stroke:none"/></svg>

  {%- elsif icon == "send" -%}

  <svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M47.5 2.5 22.75 27.25M47.5 2.5l-15.75 45-9-20.25-20.25-9 45-15.75Z" stroke="#fff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/></svg>

  {%- elsif icon == "check" -%}

    <svg width="20" height="15" viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 7.60645L1.41421 6.19223L8.48528 13.2633L7.07107 14.6775L0 7.60645Z" fill="black"/>  <path d="M18.2635 0.656697L19.6777 2.07091L7.07107 14.6775L5.65685 13.2633L18.2635 0.656697Z" fill="black"/></svg>

  {%- elsif icon == "cross" -%}

    <svg fill="none" height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="m.928827 2.34315 1.414213-1.414218 5.65685 5.656858 5.65681-5.656858 1.4143 1.414218-5.65689 5.65685 5.65689 5.6569-1.4143 1.4142-5.65681-5.65689-5.65685 5.65689-1.414213-1.4142 5.656853-5.6569z" fill="#000"/><path d="m.928827 2.34315 1.414213-1.414218 5.65685 5.656858 5.65681-5.656858 1.4143 1.414218-5.65689 5.65685 5.65689 5.6569-1.4143 1.4142-5.65681-5.65689-5.65685 5.65689-1.414213-1.4142 5.656853-5.6569z" stroke="#000"/></svg>

{%- elsif icon == 'model-button' -%}

  <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg"><g style="stroke-width:4;stroke-miterlimit:10;stroke:#202020;fill:none;stroke-linejoin:round;stroke-linecap:round"><path d="m62 49.999-30.001 10.001-29.999-10.001v-36l29.999-9.999 30.001 9.999z"/><path d="m62 13.999-30.001 10.001-29.999-10.001m29.999 10.001v36"/></g></svg>

{%- elsif icon == 'badge-model-thumbnail' -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26"><path class="path" d="M19.13,8.28,14,5.32a2,2,0,0,0-2,0l-5.12,3a2,2,0,0,0-1,1.76V16a2,2,0,0,0,1,1.76l5.12,3a2,2,0,0,0,2,0l5.12-3a2,2,0,0,0,1-1.76V10A2,2,0,0,0,19.13,8.28Zm-6.4,11.1-5.12-3A.53.53,0,0,1,7.35,16V10a.53.53,0,0,1,.27-.46l5.12-3a.53.53,0,0,1,.53,0l5.12,3-4.72,2.68a1.33,1.33,0,0,0-.67,1.2v6a.53.53,0,0,1-.26,0Z" style="isolation:isolate"/></svg>

{%- elsif icon == 'badge-video-thumbnail' -%}

  <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path class="path" fill-rule="evenodd" clip-rule="evenodd" d="M9.71814 6.71984C9.0517 6.31605 8.19995 6.79588 8.19995 7.5751V18.311C8.19995 19.1138 9.09826 19.5893 9.76217 19.1379L18.1123 13.4612C18.7146 13.0518 18.6912 12.1564 18.0682 11.779L9.71814 6.71984Z" fill="#3A3A3A"/></svg>

{%- elsif icon == 'play' -%}

	<svg class="svg symbol symbol--play" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 48 48" style="enable-background:new 0 0 48 48; width: 48px; height: 48px;" xml:space="preserve"><g><path d="M32.5,22l-12.6-7.8c-1.1-0.7-2.6,0.1-2.6,1.5v15.7c0,1.3,1.5,2.2,2.6,1.5L32.5,25C33.6,24.3,33.6,22.7,32.5,22z"/></g></svg>

{%- elsif icon == "facebook" -%}

  <svg fill="none" height="120" viewBox="0 0 120 120" width="120" xmlns="http://www.w3.org/2000/svg"><path d="m81.3942 66.8069 2.8527-18.2698h-17.8237v-11.8507c0-5.0051 2.4876-9.8755 10.4751-9.8755h8.1017v-15.5765s-7.3485-1.2344-14.4004-1.2344c-14.6743 0-24.2822 8.7533-24.2822 24.5991v13.938h-16.3174v18.2698h16.3174v44.1931h20.083v-44.1931z" fill="#000"/></svg>

{%- elsif icon == "twitter" -%}

  <svg viewBox="0 0 24 24" aria-hidden="true" class="r-1nao33i r-4qtqp9 r-yyyyoo r-16y2uox r-8kz0gk r-dnmrzs r-bnwqim r-1plcrui r-lrvibr r-lrsllp"><g><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path></g></svg>

{%- elsif icon == "pinterest" -%}

  <svg fill="none" height="120" viewBox="0 0 120 120" width="120" xmlns="http://www.w3.org/2000/svg"><path d="m59.9889 10c-27.6161 0-49.9889 22.3828-49.9889 50.0111 0 21.2047 13.1749 39.2754 31.7707 46.5439-.4221-3.957-.8442-10.0247.1778-14.3367.9109-3.912 5.8653-24.85 5.8653-24.85s-1.4885-3.0007-1.4885-7.4239c0-6.9571 4.0213-12.1582 9.0424-12.1582 4.2657 0 6.3319 3.2007 6.3319 7.0238 0 4.2898-2.7327 10.7134-4.1546 16.6259-1.1997 4.9789 2.4883 9.0464 7.3983 9.0464 8.887 0 15.7077-9.3798 15.7077-22.8939 0-11.9583-8.6203-20.3379-20.8621-20.3379-14.219 0-22.5505 10.669-22.5505 21.7159 0 4.3121 1.6441 8.9131 3.7103 11.4026.3999.489.4665.9335.3332 1.4447-.3777 1.5782-1.2219 4.9789-1.3997 5.668-.2221.9335-.7109 1.1113-1.6662.689-6.2431-2.9117-10.1311-12.0471-10.1311-19.3599 0-15.7812 11.4419-30.2511 33.0149-30.2511 17.3294 0 30.8153 12.3583 30.8153 28.8731 0 17.226-10.8642 31.118-25.9275 31.118-5.0656 0-9.8201-2.645-11.4419-5.7568 0 0-2.5106 9.5354-3.1105 11.8915-1.133 4.3565-4.1768 9.7795-6.2208 13.0915 4.6878 1.445 9.6423 2.223 14.7967 2.223 27.5939 0 49.9889-22.3828 49.9889-50.0111-.022-27.6061-22.395-49.9889-50.0111-49.9889z" fill="#000"/></svg>

{%- elsif icon == 'zoom-in' -%}

	<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="9.08008" cy="9" r="8" stroke="var(--main-text)" stroke-width="2" style="fill:none!important" /><rect x="14.2988" y="15.9062" width="1.98612" height="6.65426" transform="rotate(-45 14.2988 15.9062)" fill="#111111"/><path d="M8.08008 5H10.0801V13H8.08008V5Z" fill="#111111"/><path d="M13.0801 8V10L5.08008 10L5.08008 8L13.0801 8Z" fill="#111111"/></svg>

{%- elsif icon == 'zoom-out' -%}

	<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="9.08008" cy="9" r="8" stroke="var(--main-text)" stroke-width="2" style="fill:none!important" /><rect x="14.2988" y="15.9062" width="1.98612" height="6.65426" transform="rotate(-45 14.2988 15.9062)" fill="#111111"/><path d="M13.0801 8V10L5.08008 10L5.08008 8L13.0801 8Z" fill="#111111"/></svg>

{%- elsif icon == 'alert-error' -%}

  <svg width="7" height="8" viewBox="0 0 7 8" fill="none"><rect x="5.9751" y="0.818024" width="1" height="8" transform="rotate(45 5.9751 0.818024)" fill="white"/><rect x="6.68213" y="6.47488" width="1" height="8" transform="rotate(135 6.68213 6.47488)" fill="white"/></svg>

{%- elsif icon == 'alert-success' -%}

  <svg fill="none" height="7" viewBox="0 0 7 7" width="7" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><clipPath id="a"><path d="m0 0h7v7h-7z"/></clipPath><g clip-path="url(#a)" fill="#fff"><path d="m3.46762 5.27917-2.754073-2.72918-.71355643.70711 2.75406943 2.72918z"/><path d="m0 0h6.02588v1.00457h-6.02588z" transform="matrix(-.71031 .703889 .71031 .703889 6.32977 1.03186)"/></g></svg>

{%- elsif icon == 'locked' -%}

  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="24" height="24" fill="none" stroke="#202020" stroke-miterlimit="10" stroke-width="3" stroke-linejoin="round"><path d="M19 30V15a13 13 0 1 1 26 0v15m-35 0h44v32H10zm22 11v10"/></svg>

{%- elsif icon == 'table' -%}

  <svg fill="none" height="26" viewBox="0 0 26 26" width="26" xmlns="http://www.w3.org/2000/svg"><path d="m11.7808 23.2721h-9.9168c-.22915 0-.44891-.091-.61094-.253s-.25306-.3818-.25306-.6109v-17.95032c0-.22913.09103-.44887.25306-.61089s.38179-.25304.61094-.25304h3.3168m8.6832 0h3.36c.2291 0 .4489.09102.6109.25304.1621.16202.2531.38176.2531.61089v10.60692m-4.224-11.47085v2.59178h-8.64v-2.59178c0-.22913.09103-.44887.25306-.61089s.38179-.25304.61094-.25304h1.3392c.09915-.48824.36406-.9272.74984-1.24249s.86871-.48753 1.36696-.48753c.4982 0 .9812.17224 1.367.48753.3857.31529.6506.75425.7498 1.24249h1.3392c.2291 0 .4489.09102.6109.25304.1621.16202.2531.38176.2531.61089zm-9.792 6.04749h10.944m-10.944 4.31966h10.944m-10.944 4.3196h7.104m1.8144 1.8381 8.9856-8.9416 3.024 3.0237-8.9856 8.9417m-3.024-3.0238-1.9008 4.8812 4.9248-1.8574m-3.024-3.0238 3.024 3.0238" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="1.5"/></svg>

{%- elsif icon == 'arrow-left-right' -%}

  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="11" fill="none"><path fill="#fff" d="m17.018 9.714 5.667-5.5L24.018 5.5 18.352 11l-1.334-1.286Z"/><path fill="#fff" d="m18.351 0 5.667 5.5-1.333 1.286-5.667-5.5L18.351 0ZM7.018 1.286l-5.666 5.5L.018 5.5 5.685 0l1.333 1.286Z"/><path fill="#fff" d="M5.685 11 .02 5.5l1.333-1.286 5.667 5.5L5.684 11Z"/></svg>

{%- endif -%}