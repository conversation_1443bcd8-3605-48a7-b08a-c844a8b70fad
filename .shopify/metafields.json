{"article": [{"key": "cta_products", "namespace": "custom", "name": "CTA Products", "description": "", "type": {"name": "list.product_reference", "category": "REFERENCE"}}], "blog": [], "collection": [{"key": "image", "namespace": "profile", "name": "Profile Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "title", "namespace": "profile", "name": "Profile Title", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "subtitle", "namespace": "profile", "name": "Profile Subtitle", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "purpose", "namespace": "profile_page", "name": "Profile Purpose", "description": "z.B. \"maximale Ausdauer\" für Ausdauerathleten", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "company": [], "company_location": [], "location": [], "market": [], "order": [], "page": [], "product": [{"key": "dietary-preferences", "namespace": "shopify", "name": "Dietary preferences", "description": "Helps identify products suitable for specific diets, e.g. vegan, nut free", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "flavor", "namespace": "shopify", "name": "Flavor", "description": "Specifies the taste or flavor of a product, e.g. vanilla, mint", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "dietary-use", "namespace": "shopify", "name": "Dietary use", "description": "Classifies products based on dietary benefits, e.g. muscle building, low carb", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "breath-sprays-certifications", "namespace": "shopify", "name": "Breath sprays certifications", "description": "Specifies the standards a breath spray meets, such as fair trade or gluten-free", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "description", "namespace": "product_page_accordion", "name": "Description", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "application", "namespace": "product_page_accordion", "name": "Application", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "ingredients", "namespace": "product_page_accordion", "name": "Ingredients", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "literature", "namespace": "product_page_accordion", "name": "Literature", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "selling_points_heading", "namespace": "product_page_accordion", "name": "Selling Points Heading", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "selling_points_subheading", "namespace": "custom", "name": "Selling Points Subheading", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "banner_text", "namespace": "product_page", "name": "Banner Text", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "banner_image", "namespace": "product_page", "name": "Banner Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "banner_heading", "namespace": "product_page", "name": "Banner Heading", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "faq_item_1", "namespace": "product_page", "name": "FAQ Item #1", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "faq_item_2", "namespace": "product_page", "name": "FAQ Item #2", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "faq_item_3", "namespace": "product_page", "name": "FAQ Item #3", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "faq_item_4", "namespace": "product_page", "name": "FAQ Item #4", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "faq_item_5", "namespace": "product_page", "name": "FAQ Item #5", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "selling_point_1", "namespace": "product_page", "name": "Selling Point 1", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "selling_point_2", "namespace": "product_page", "name": "Selling Point 2", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "selling_point_3", "namespace": "product_page", "name": "Selling Point 3", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "selling_point_4", "namespace": "product_page", "name": "Selling Point 4", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "product_icon_1_label", "namespace": "custom", "name": "Product Icon 1 Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_icon_1_image", "namespace": "custom", "name": "Product Icon 1 Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "product_icon_2_label", "namespace": "custom", "name": "Product Icon 2 Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_icon_2_image", "namespace": "custom", "name": "Product Icon 2 Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "product_icon_3_label", "namespace": "custom", "name": "Product Icon 3 Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_icon_3_image", "namespace": "custom", "name": "Product Icon 3 Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "product_icon_4_label", "namespace": "custom", "name": "Product Icon 4 Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_icon_4_image", "namespace": "custom", "name": "Product Icon 4 Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "product_icon_5_label", "namespace": "custom", "name": "Product Icon 5 Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_icon_5_image", "namespace": "custom", "name": "Product Icon 5 Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "product_icon_6_label", "namespace": "custom", "name": "Product Icon 6 Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_icon_6_image", "namespace": "custom", "name": "Product Icon 6 Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "nutrition_right_label", "namespace": "product_page", "name": "Nutrition Second Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "nutrition_info", "namespace": "product_page", "name": "Nutrition Info", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "nutrition_extra_info", "namespace": "product_page", "name": "Nutrition Extra Info", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "selling_point_5", "namespace": "product_page", "name": "Selling Point 5", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "selling_point_6", "namespace": "product_page", "name": "Selling Point 6", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "footnotes", "namespace": "product_page", "name": "Footnotes", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "nutrition_third_label", "namespace": "product_page", "name": "Nutrition Third Label", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "test-method", "namespace": "shopify", "name": "Test method", "description": "Defines the procedure for conducting a medical test, e.g. at-home test, lab analysis", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "test-sample", "namespace": "shopify", "name": "Test sample", "description": "Specifies the type of sample required for health and medical tests, e.g. swab, saliva", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "info_article_url", "namespace": "custom", "name": "Info Article URL", "description": "", "type": {"name": "url", "category": "URL"}}, {"key": "product_page_blog_article", "namespace": "custom", "name": "Product Page Blog Article", "description": "", "type": {"name": "url", "category": "URL"}}, {"key": "preis_pro_stueck", "namespace": "custom", "name": "Preis pro <PERSON>ueck", "description": "", "type": {"name": "number_decimal", "category": "NUMBER"}}, {"key": "product_page_bottom_text", "namespace": "custom", "name": "Product Page Bottom Text", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "custom_product", "namespace": "mm-google-shopping", "name": "Google: Custom Product", "description": "Use to indicate whether or not the unique product identifiers (UPIs) GTIN, MPN, and brand are available for your product.", "type": {"name": "boolean", "category": "TRUE_FALSE"}}, {"key": "rating_count", "namespace": "reviews", "name": "Product rating count", "description": "Total number of ratings from customers", "type": {"name": "number_integer", "category": "NUMBER"}}, {"key": "rating", "namespace": "reviews", "name": "Product rating", "description": "Average rating from customers", "type": {"name": "rating", "category": "RATING"}}, {"key": "age-group", "namespace": "shopify", "name": "Age group", "description": "Defines the target age range for a product, such as adults or kids", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "product-certifications-standards", "namespace": "shopify", "name": "Product certifications & standards", "description": "Indicates the certifications or standards a product meets, such as organic or cruelty free", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "detailed-ingredients", "namespace": "shopify", "name": "Detailed ingredients", "description": "Lists specific components used in health and beauty, e.g. Vitamin C, probiotics", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "ingredient-category", "namespace": "shopify", "name": "Ingredient category", "description": "Classifies the type of ingredients in vitamins and supplements, e.g. fiber, herbs", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "bullet_points_product_page", "namespace": "custom", "name": "Bullet Points Product Page", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "allergen-test-type", "namespace": "shopify", "name": "Allergen test type", "description": "Specifies the allergen type the test kit is designed to detect, such as food or environmental", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "queries", "namespace": "shopify--discovery--product_search_boost", "name": "Search product boosts", "description": "List of search queries for which a product gets higher rank in search results", "type": {"name": "list.single_line_text_field", "category": "TEXT"}}, {"key": "related_products", "namespace": "shopify--discovery--product_recommendation", "name": "Related products", "description": "List of related products", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "related_products_display", "namespace": "shopify--discovery--product_recommendation", "name": "Related products settings", "description": "Determines how related products are displayed along with algorithmic product recommendations", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "complementary_products", "namespace": "shopify--discovery--product_recommendation", "name": "Complementary products", "description": "List of complementary products", "type": {"name": "list.product_reference", "category": "REFERENCE"}}], "variant": [{"key": "customsItemDescription", "namespace": "<PERSON><PERSON><PERSON><PERSON>", "name": "[DHL] customsItemDescription", "description": null, "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_4", "namespace": "mm-google-shopping", "name": "Google: Custom Label 4", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_3", "namespace": "mm-google-shopping", "name": "Google: Custom Label 3", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_2", "namespace": "mm-google-shopping", "name": "Google: Custom Label 2", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_1", "namespace": "mm-google-shopping", "name": "Google: Custom Label 1", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_0", "namespace": "mm-google-shopping", "name": "Google: Custom Label 0", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_system", "namespace": "mm-google-shopping", "name": "Google: Size System", "description": "The country of the size system used by your product.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_type", "namespace": "mm-google-shopping", "name": "Google: Size Type", "description": "Your apparel product’s cut.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "mpn", "namespace": "mm-google-shopping", "name": "Google: MPN", "description": "Your product’s Manufacturer Part Number (MPN).", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "gender", "namespace": "mm-google-shopping", "name": "Google: Gender", "description": "The gender for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "condition", "namespace": "mm-google-shopping", "name": "Google: Condition", "description": "The condition of your product at time of sale.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "age_group", "namespace": "mm-google-shopping", "name": "Google: Age Group", "description": "The demographic for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "shop": [{"key": "fastbundleconf", "namespace": "rbrfb", "name": "Fast Bundle Conf", "description": "App's config for the widgets", "type": {"name": "json", "category": "JSON"}}]}