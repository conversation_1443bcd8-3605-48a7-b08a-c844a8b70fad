name: Deploy Vendor Notifier

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

on:
  push:
    branches:
      - main

jobs:
  build:
    name: Build vendor notifier
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@master
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - name: Set up PNPM
        uses: pnpm/action-setup@v3
        with:
          version: 9 # Or your desired pnpm version
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Build vendor-notifier
        run: pnpm turbo run build --filter=vendor-notifier
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      - name: Run Drizzle Setup
        run: pnpm --filter=vendor-notifier run drizzle:migrate
        env:
          DATABASE_URL: ${{ secrets.VENDOR_NOTIFIER_DATABASE_URL }}

  deploy:
    name: Cloud Run Deployment
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Checkout
        uses: actions/checkout@master
      - name: Authenticate to Google Cloud
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets.GCP_SA_KEY }}'
      - name: Setup GCP Service Account
        uses: google-github-actions/setup-gcloud@v2
        with:
          version: 'latest'
      - name: Configure Docker
        run: |
          gcloud auth configure-docker europe-west9-docker.pkg.dev
      - name: Build
        run: |
          docker build \
            -t europe-west9-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/cloud-run-deployments/vendor-notifier:latest \
            -t europe-west9-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/cloud-run-deployments/vendor-notifier:${{ github.sha }} \
            -f Dockerfile.vendor-notifier .
      - name: Push
        run: |
          docker push europe-west9-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/cloud-run-deployments/vendor-notifier:latest
          docker push europe-west9-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/cloud-run-deployments/vendor-notifier:${{ github.sha }}
      - name: Deploy
        run: |
          gcloud run deploy vendor-notifier \
            --region europe-west9 \
            --image europe-west9-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/cloud-run-deployments/vendor-notifier:${{ github.sha }} \
            --platform managed \
            --allow-unauthenticated \
            --project ${{ secrets.GCP_PROJECT_ID }}
