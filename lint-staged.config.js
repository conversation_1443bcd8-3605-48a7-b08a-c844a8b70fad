import micromatch from 'micromatch';

const filtered = (files, fn) => {
  const filteredFiles = micromatch.not(files, '**/generated/**');
  return fn(filteredFiles);
};

export default {
  "apps/vendor-notifier/**/*.{ts,tsx}": (files) => filtered(files, (filteredFiles) => [
    `pnpm run --filter vendor-notifier lint:files -- ${filteredFiles.join(' ')}`
  ]),
  "apps/vendor-notifier/{package.json,**/*.{ts,tsx}}": () => [
    "pnpm run --filter vendor-notifier drizzle:generate"
  ],
  "**/*.{ts,tsx}": (files) => filtered(files, (filteredFiles) => [
    `prettier --write ${filteredFiles.join(' ')}`
  ])
};
