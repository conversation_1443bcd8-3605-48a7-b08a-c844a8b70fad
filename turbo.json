{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "globalEnv": ["CI", "SENTRY_AUTH_TOKEN", "NEXT_RUNTIME"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "build/**", "public/build/**"], "passThroughEnv": ["SENTRY_AUTH_TOKEN"]}, "vendor-notifier#build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["build/**", "public/build/**"], "passThroughEnv": ["SENTRY_AUTH_TOKEN"]}, "lint": {"dependsOn": ["^lint"]}, "lint:files": {"dependsOn": ["^lint:files"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "drizzle:generate": {"dependsOn": ["^drizzle:generate"]}}}