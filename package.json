{"name": "dnatural-turbo", "private": true, "type": "module", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "lint:files": "turbo run lint:files", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "generate-dockerfiles": "node --experimental-json-modules ./bin/generate-dockerfiles.js", "prepare": "husky"}, "devDependencies": {"husky": "^9.1.7", "lint-staged": "^16.1.2", "micromatch": "^4.0.8", "prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "5.8.3"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "pnpm": {"overrides": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5", "esbuild": "0.25.4"}, "onlyBuiltDependencies": ["@parcel/watcher", "@sentry/cli", "@tailwindcss/oxide", "esbuild", "maplibre-gl", "sharp", "unrs-resolver", "vue-demi"]}}