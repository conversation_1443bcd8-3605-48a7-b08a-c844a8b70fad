# @repo/eslint-config Package

## Overview

This package provides shared ESLint configurations for the monorepo, offering different presets for various types of projects. It uses ESLint v9's flat config format and includes TypeScript, React, and Next.js specific configurations.

## Available Configurations

### 1. Base Configuration (`./base`)
- **File**: `base.js`
- **Purpose**: Foundation configuration for all projects
- **Includes**:
  - ESLint recommended rules
  - TypeScript ESLint recommended rules
  - Prettier integration to avoid conflicts
  - Turborepo plugin for environment variable checks
  - Only-warn plugin (converts all errors to warnings)
- **Ignores**: `dist/**` directories

### 2. Next.js Configuration (`./next-js`)
- **File**: `next.js`
- **Purpose**: Configuration for Next.js applications
- **Extends**: Base configuration
- **Includes**:
  - All base configuration rules
  - React recommended rules
  - React Hooks rules
  - Next.js recommended rules
  - Next.js Core Web Vitals rules
  - Service Worker globals
- **Special Rules**:
  - Disables `react/react-in-jsx-scope` (not needed with new JSX transform)

### 3. React Internal Configuration (`./react-internal`)
- **File**: `react-internal.js`
- **Purpose**: Configuration for React libraries
- **Extends**: Base configuration
- **Includes**:
  - All base configuration rules
  - React recommended rules
  - React Hooks rules
  - Browser and Service Worker globals
- **Special Rules**:
  - Disables `react/react-in-jsx-scope` (not needed with new JSX transform)

## Package Exports

The package uses ES modules and exports three configurations:
```json
{
  "./base": "./base.js",
  "./next-js": "./next.js",
  "./react-internal": "./react-internal.js"
}
```

## Dependencies

### Core ESLint
- `@eslint/js`: ESLint core recommended rules
- `eslint`: ESLint engine (v9.25.0)

### TypeScript
- `typescript-eslint`: TypeScript ESLint plugin and parser
- `typescript`: TypeScript compiler

### React/Next.js
- `eslint-plugin-react`: React specific linting rules
- `eslint-plugin-react-hooks`: React Hooks linting rules
- `@next/eslint-plugin-next`: Next.js specific linting rules

### Utilities
- `eslint-config-prettier`: Disables ESLint rules that conflict with Prettier
- `eslint-plugin-turbo`: Turborepo specific rules
- `eslint-plugin-only-warn`: Converts errors to warnings
- `globals`: Global variable definitions

## Usage

In your project's ESLint configuration:

```javascript
// For a Next.js app
import { nextJsConfig } from "@repo/eslint-config/next-js";
export default nextJsConfig;

// For a React library
import { config } from "@repo/eslint-config/react-internal";
export default config;

// For a Node.js project
import { config } from "@repo/eslint-config/base";
export default config;
```

## Features

1. **Modern Config Format**: Uses ESLint v9's flat config format
2. **TypeScript Support**: Full TypeScript linting with type-aware rules
3. **Framework Specific**: Tailored configurations for different frameworks
4. **Prettier Compatible**: Automatically disables conflicting rules
5. **Monorepo Aware**: Includes Turborepo plugin for environment variables
6. **Warning Mode**: All errors can be converted to warnings via only-warn plugin

## Notes

- All configurations are ESM only (no CommonJS support)
- Configurations are composable and can be extended
- The base configuration is automatically included in framework-specific configs
- React version is automatically detected for React-based configurations