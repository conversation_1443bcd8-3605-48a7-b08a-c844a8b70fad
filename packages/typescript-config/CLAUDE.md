# @repo/typescript-config Package

## Overview

This package provides shared TypeScript configurations for the monorepo. It offers different presets optimized for various project types, ensuring consistent TypeScript settings across all applications and packages.

## Available Configurations

### 1. Base Configuration (`base.json`)
- **Purpose**: Foundation configuration with strict TypeScript settings
- **Key Settings**:
  - **Target**: ES2022
  - **Module**: NodeNext (Node.js native ESM support)
  - **Module Resolution**: NodeNext
  - **Strict Mode**: Enabled (all strict checks)
  - **Isolated Modules**: True (required for build tools like Vite/esbuild)
  - **Lib**: ES2022, DOM, DOM.Iterable
  - **Additional Safety**: `noUncheckedIndexedAccess: true`
  - **Declaration Files**: Generates `.d.ts` and `.d.ts.map` files

### 2. Next.js Configuration (`nextjs.json`)
- **Extends**: base.json
- **Purpose**: Optimized for Next.js applications
- **Overrides**:
  - **Module**: ESNext (for Next.js bundler)
  - **Module Resolution**: Bundler
  - **JSX**: preserve (Next.js handles JSX transformation)
  - **Allow JS**: true (JavaScript files allowed)
  - **No Emit**: true (Next.js handles compilation)
  - **Plugin**: Next.js TypeScript plugin

### 3. React Library Configuration (`react-library.json`)
- **Extends**: base.json
- **Purpose**: For React component libraries
- **Overrides**:
  - **JSX**: react-jsx (new JSX transform)

## Configuration Details

### Base Configuration Features

1. **Modern JavaScript Target**: ES2022 provides latest stable features
2. **Strict Type Checking**: All strict flags enabled for maximum type safety
3. **Module System**: NodeNext for proper ESM support in Node.js
4. **Declaration Maps**: Enables go-to-definition for library consumers
5. **Isolated Modules**: Ensures compatibility with modern build tools
6. **Index Access Checking**: Prevents undefined access on arrays/objects

### Framework-Specific Optimizations

#### Next.js
- Uses Bundler module resolution for webpack compatibility
- Preserves JSX for Next.js to transform
- Includes Next.js TypeScript plugin for enhanced DX
- No emit since Next.js handles the build process

#### React Library
- Uses the new JSX transform (no React import needed)
- Inherits all strict settings from base
- Generates declaration files for library consumers

## Usage

In your project's `tsconfig.json`:

```json
{
  "extends": "@repo/typescript-config/nextjs.json",
  "compilerOptions": {
    // Your project-specific overrides
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules"]
}
```

## File Structure

```
typescript-config/
├── package.json       # Package metadata
├── base.json         # Base configuration
├── nextjs.json       # Next.js specific config
└── react-library.json # React library config
```

## Best Practices

1. **Always extend** from one of the provided configurations
2. **Minimize overrides** to maintain consistency
3. **Use the appropriate preset** for your project type
4. **Include proper file patterns** in your tsconfig.json

## Notes

- All configurations use JSON Schema for better IDE support
- The package is private and not published to npm
- Configurations are designed to work with modern build tools
- Strict mode is enabled by default for better type safety