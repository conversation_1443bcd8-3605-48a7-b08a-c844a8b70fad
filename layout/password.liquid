<!doctype html>
<html class="no-js html-password" lang="{{ request.locale.iso_code }}" dir="{%- render 'lang-dir' -%}">
<head>

	<meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0">

	{%- if settings.favicon -%}
		<link rel="shortcut icon" href="{{ settings.favicon | image_url: width: 32, height: 32 }}" type="image/png" />
	{%- endif -%}

	{%- capture seo_title -%}
    {%- if template contains 'search' -%}
      {{ search.terms | replace: '*', '' | split: ' AND ' | last }} - {{ shop.name | escape }}
    {%- else -%}
      {{ page_title }}{% if current_tags %}{% assign meta_tags = current_tags | join: ', ' %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags }}{% endif %}{% if current_page != 1 %} &ndash; {{ 'general.meta.page' | t: page: current_page }}{% endif %}{% unless page_title contains shop.name %} &ndash; {{ shop.name | escape }}{% endunless %}
    {%- endif -%}
  {%- endcapture -%}

	<title>{{ seo_title }}</title>

	{%- if page_description -%}
		<meta name="description" content="{{ page_description | escape }}">
	{%- endif -%}

  <link rel="canonical" href="{{ canonical_url }}">

  <link rel="preconnect" href="https://cdn.shopify.com">
  {%- unless settings.headings_font.system? and settings.body_font.system? -%}
    <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
  {%- endunless -%}
    
  {%- render 'head-variables' -%}
  {%- render 'head-preloader' -%}

  {{ 'theme.css' | asset_url | stylesheet_tag }}

	{{ content_for_header }}

  <script>
    const rbi = [];
    const ribSetSize = (img) => {
      if ( img.offsetWidth / img.dataset.ratio < img.offsetHeight ) {
        img.setAttribute('sizes', `${Math.ceil(img.offsetHeight * img.dataset.ratio)}px`);
      } else {
        img.setAttribute('sizes', `${Math.ceil(img.offsetWidth)}px`);
      }
    }
    const debounce = (fn, wait) => {
      let t;
      return (...args) => {
        clearTimeout(t);
        t = setTimeout(() => fn.apply(this, args), wait);
      };
    }
    window.KEYCODES = {
      TAB: 9,
      ESC: 27,
      DOWN: 40,
      RIGHT: 39,
      UP: 38,
      LEFT: 37,
      RETURN: 13
    };
    window.addEventListener('resize', debounce(()=>{
      for ( let img of rbi ) {
        ribSetSize(img);
      }
    }, 250));
  </script>

  <noscript>
    <link rel="stylesheet" href="{{ 'theme-noscript.css' | asset_url }}">
  </noscript>

  <style>
    html {
      min-height: 100%;
    }
    body {
      display: grid;
      grid-template-rows: auto 1fr;
      height: 100vh;
    }
    .main-footer {
      align-self: end;
    }
  </style>

</head>

<body class="template-password no-touchevents">

  <script type="text/javascript">
    if ( 'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch ) { document.querySelector('body').classList.remove('no-touchevents'); document.querySelector('body').classList.add('touchevents'); } 
  </script>
  
  <a href="#content" class="visually-hidden skip-to-content" tabindex="0">{{ 'general.accessibility_labels.skip_to_content' | t }}</a>

  <div class="main-password-page">

    {{ 'section-header.css' | asset_url | stylesheet_tag }}
	  {%- section 'main-password-header' -%}

    {{ 'component-slider.css' | asset_url | stylesheet_tag }}

		<div id="content-holder">
			<main id="content" role="main">
			  <div id="page-content">{{ content_for_layout }}</div>
			</main>
		</div>

	</div>

	{%- section 'main-password-footer' -%}

  <script src="{{ 'component-modal.js' | asset_url }}" defer></script>
  <link rel="stylesheet" href="{{ 'component-modal.css' | asset_url }}" media="print" onload="this.media='all'">

	<script type="text/javascript">

    KROWN = {
      themeName: "Local",
      themeVersion: "2.5.0",
      settings: {
        shop_money_format: '{{ shop.money_format | strip_html }}',
        iso_code: '{% if settings.show_currency_codes %} {{ localization.country.currency.iso_code }}{% endif %}',
        google_maps_api_key: "{{ settings.google_maps_api_key }}",
        locales: {
          products_add_to_cart_button: `{{ 'products.page.add_to_cart_button' | t }}`,
          products_sold_out_variant: `{{ 'products.page.inventory.sold_out_variant' | t }}`,
          products_unavailable_variant: `{{ 'products.page.inventory.unavailable_variant' | t }}`,
          products_variant_required: `{{ 'products.grid.choose_variant_first' | t }}`,
          products_one_product: `{{ 'products.page.inventory.one_product' | t }}`,
          products_few_products: `{{ 'products.page.inventory.few_products' | t }}`,
          products_many_products: `{{ 'products.page.inventory.many_products' | t }}`,
          products_no_products: `{{ 'products.page.inventory.no_products' | t }}`,
          products_preorder: `{{ 'products.page.inventory.preorder' | t }}`,
          products_enough_products: ``,
          cart_general_error: `{{ 'cart.general_error' | t }}`,
          store_selector_title_default: `{{ 'store_availability.store_selector.picking_up' | t }}`,
          store_selector_title_selected: `{{ 'store_availability.store_selector.my_store' | t }}`,
          store_selector_label: `{{ 'store_availability.store_selector.select_store_label' | t }}`
        },
        symbols: {
          arrow: `{%- render 'theme-symbols', icon: 'arrow-long' -%}`,
          zoom_out: `{%- render 'theme-symbols', icon: 'zoom-out' -%}`,
          zoom_loader: `<svg class="zoom__loader-element" viewBox="25 25 50 50"><circle cx="50" cy="50" r="20" fill="none" stroke-width="4"/></svg>`,
          close: `{%- render 'theme-symbols', icon: 'close' -%}`,
          toggle_pack: `<span class="toggle__icon">{%- render 'theme-symbols', icon: 'chevron' -%}</span>`,
          toggle_pack_alternate: `<span class="toggle__icon-alternate"><span class="toggle__icon--plus">{%- render 'theme-symbols', icon: 'plus' -%}</span><span class="toggle__icon--minus">{%- render 'theme-symbols', icon: 'minus' -%}</span></span>`
        },
        predictive_search_enabled: "true",
        predictive_search_placeholder: `<div class="search-results"><div class="search-block"><a class="search-item search-item--blank"><div class="thumbnail"></div><div class="content"><span class="title"></span><span class="caption"></span></div></a><a class="search-item search-item--blank"><div class="thumbnail"></div><div class="content"><span class="title"></span><span class="caption"></span></div></a><a class="search-item search-item--blank"><div class="thumbnail"></div><div class="content"><span class="title"></span><span class="caption"></span></div></a></div></div>`
      }
    }

	</script>

  <script src="{{ 'component-pickup-availability.js' | asset_url }}" defer></script>
  <script src="{{ 'component-slider.js' | asset_url }}" defer></script>

  {%- if request.design_mode -%}
    <script src="{{ 'theme-editor.js' | asset_url }}" defer></script>
  {%- endif -%}

  {%- if request.design_mode -%}
    <script src="{{ 'section-announcement-bar.js' | asset_url }}" defer></script>
    <script src="{{ 'component-toggle.js' | asset_url }}" defer></script>
    <script src="{{ 'section-main-product.js' | asset_url }}" defer></script>
    <script src="{{ 'component-before-after.js' | asset_url }}" defer></script>
    <script src="{{ 'component-video.js' | asset_url }}" defer></script>
    <script src="{{ 'component-store-selector.js' | asset_url }}" defer></script>
    <script src="{{ 'component-interactive-map.js' | asset_url }}" defer></script>
    <script src="{{ 'component-image-hotspots.js' | asset_url }}" defer></script>
    <script src="{{ 'component-localization-form.js' | asset_url }}" defer></script>
    <script src="{{ 'component-predictive-search.js' | asset_url }}" defer></script>
    <script src="{{ 'component-shipping-calculator.js' | asset_url }}" defer></script>
    <script src="{{ 'component-scrolling-text.js' | asset_url }}" defer></script>
    <script src="{{ 'component-countdown-clock.js' | asset_url }}" defer></script>
  {%- endif -%}
  
  <div class="site-overlay" style="display:none"></div>
  
  <link rel="stylesheet" href="{{ 'section-main-product.css' | asset_url }}" media="print" onload="this.media='all';">

</body>
</html>