<!doctype html>
  <html class="no-js" lang="{{ request.locale.iso_code }}" dir="{%- render 'lang-dir' -%}">
  <head>
  
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0">
  
    {%- if settings.favicon -%}
      <link rel="shortcut icon" href="{{ settings.favicon | image_url: width: 32, height: 32 }}" type="image/png" />
    {%- endif -%}
  
    {%- capture seo_title -%}
      {%- if template contains 'search' -%}
        {{ search.terms | replace: '*', '' | split: ' AND ' | last }} - {{ shop.name | escape }}
      {%- else -%}
        {{ page_title }}{% if current_tags %}{% assign meta_tags = current_tags | join: ', ' %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags }}{% endif %}{% if current_page != 1 %} &ndash; {{ 'general.meta.page' | t: page: current_page }}{% endif %}{% unless page_title contains shop.name %} &ndash; {{ shop.name | escape }}{% endunless %}
      {%- endif -%}
    {%- endcapture -%}
  
    <title>{{ seo_title }}</title>
  
    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}
  
    {%- liquid
      render 'open-graph'
      unless settings.disable_microdata 
        render 'microdata-schema'
      endunless
    -%}
  
    <link rel="canonical" href="{{ canonical_url }}">
  
    <link rel="preconnect" href="https://cdn.shopify.com">
    {%- unless settings.headings_font.system? and settings.body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}
  
    {%- render 'head-preloader' -%}
    {%- render 'head-variables' -%}
  
    {{ 'theme.css' | asset_url | stylesheet_tag }}
  
    {{ content_for_header }}
  
    <script>
      const rbi = [];
      const ribSetSize = (img) => {
        if ( img.offsetWidth / img.dataset.ratio < img.offsetHeight ) {
          img.setAttribute('sizes', `${Math.ceil(img.offsetHeight * img.dataset.ratio)}px`);
        } else {
          img.setAttribute('sizes', `${Math.ceil(img.offsetWidth)}px`);
        }
      }
      const debounce = (fn, wait) => {
        let t;
        return (...args) => {
          clearTimeout(t);
          t = setTimeout(() => fn.apply(this, args), wait);
        };
      }
      window.KEYCODES = {
        TAB: 9,
        ESC: 27,
        DOWN: 40,
        RIGHT: 39,
        UP: 38,
        LEFT: 37,
        RETURN: 13
      };
      window.addEventListener('resize', debounce(()=>{
        for ( let img of rbi ) {
          ribSetSize(img);
        }
      }, 250));
    </script>
    
    {%- if template == 'customers/addresses' or request.design_mode -%}
      <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer></script>
    {%- endif -%}
  
    <noscript>
      <link rel="stylesheet" href="{{ 'theme-noscript.css' | asset_url }}">
    </noscript>
  
  </head>
  
  <body id="{{ page_title | handle }}" class="no-touchevents 
    {% if customer %} customer-logged-in {% endif %} 
    template-{{ template.name }} template-{{ template | replace: '.', ' ' | handle }} 
    {% if settings.shadow_cards_x < 0 %} has-negative-shadow-x {% endif %}
    {% if settings.shadow_cards_y < 0 %} has-negative-shadow-y {% endif %}
  ">

    <script type="text/javascript">
      if ( 'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch ) { document.querySelector('body').classList.remove('no-touchevents'); document.querySelector('body').classList.add('touchevents'); } 
    </script>
  
    <a href="#main" class="visually-hidden skip-to-content" tabindex="0" data-js-inert>{{ 'general.accessibility_labels.skip_to_content' | t }}</a>
  
    {{ 'component-slider.css' | asset_url | stylesheet_tag }}
    <script src="{{ 'component-product-form.js' | asset_url }}" defer></script>
    <style>
      .product-quick-view__header {
        display: none;
      }
    </style>
  
    <main id="main" class="main-content 
      {% if settings.product_card_align_buy %} main-content--align-product-items {% endif %}
    ">
      <div class="modal modal--product active" style="
        background: #666;  
      ">
        <div class="container--medium">
          <div class="modal-content" style="
            pointer-events: all;
            opacity: 1;
            visibility: visible;
            transform: translateY(0);"
          >
            {{ content_for_layout }}
          </div>
        </div>
      </div>
    </main>
    
    <script src="{{ 'component-modal.js' | asset_url }}" defer></script>
    <link rel="stylesheet" href="{{ 'component-modal.css' | asset_url }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ 'component-popup.css' | asset_url }}" media="print" onload="this.media='all'">
  
    <script type="text/javascript">
  
      KROWN = {
        themeName: "Local",
        themeVersion: "2.5.0",
        settings: {
          shop_money_format: '{{ shop.money_format | strip_html }}',
          iso_code: '{% if settings.show_currency_codes %} {{ localization.country.currency.iso_code }}{% endif %}',
          cart_action: "{{ settings.cart_action }}",
          google_maps_api_key: "{{ settings.google_maps_api_key }}",
          routes: {
            cart_url: "{{ routes.cart_url }}",
            cart_add_url: "{{ routes.cart_add_url }}",
            cart_change_url: "{{ routes.cart_change_url }}",
            cart_update_url: "{{ routes.cart_update_url }}",
            predictive_search_url: "{{ routes.predictive_search_url }}",
            product_recommendations_url: "{{ routes.product_recommendations_url }}"
          },
          locales: {
            products_add_to_cart_button: `{{ 'products.page.add_to_cart_button' | t }}`,
            products_sold_out_variant: `{{ 'products.page.inventory.sold_out_variant' | t }}`,
            products_unavailable_variant: `{{ 'products.page.inventory.unavailable_variant' | t }}`,
            products_variant_required: `{{ 'products.grid.choose_variant_first' | t }}`,
            products_one_product: `{{ 'products.page.inventory.one_product' | t }}`,
            products_few_products: `{{ 'products.page.inventory.few_products' | t }}`,
            products_many_products: `{{ 'products.page.inventory.many_products' | t }}`,
            products_no_products: `{{ 'products.page.inventory.no_products' | t }}`,
            products_preorder: `{{ 'products.page.inventory.preorder' | t }}`,
            products_enough_products: ``,
            cart_general_error: `{{ 'cart.general_error' | t }}`,
            store_selector_title_default: `{{ 'store_availability.store_selector.picking_up' | t }}`,
            store_selector_title_selected: `{{ 'store_availability.store_selector.my_store' | t }}`,
            store_selector_label: `{{ 'store_availability.store_selector.select_store_label' | t }}`,
            shipping_calculator_results_heading_one: `{{ 'cart.shipping_calculator.results_heading_one' | t }}`,
            shipping_calculator_results_heading_multiple: `{{ 'cart.shipping_calculator.results_heading_multiple' | t }}`,
            shipping_notice_remaining_to_free: `{{ 'cart.free_shipping_remaining_html' | t }}`,
            shipping_notice_eligible_for_free: `{{ 'cart.free_shipping_eligible' | t }}`
          },
          symbols: {
            zoom_out: `{%- render 'theme-symbols', icon: 'zoom-out' -%}`,
            zoom_loader: `<svg class="zoom__loader-element" viewBox="25 25 50 50"><circle cx="50" cy="50" r="20" fill="none" stroke-width="4"/></svg>`,
            close: `{%- render 'theme-symbols', icon: 'close' -%}`,
            toggle_pack: `<span class="toggle__icon">{%- render 'theme-symbols', icon: 'chevron' -%}</span>`,
            toggle_pack_alternate: `<span class="toggle__icon-alternate"><span class="toggle__icon--plus">{%- render 'theme-symbols', icon: 'plus' -%}</span><span class="toggle__icon--minus">{%- render 'theme-symbols', icon: 'minus' -%}</span></span>`
          },
          predictive_search_enabled: "true",
          predictive_search_script: "{{ 'component-predictive-search.js' | asset_url }}",
          predictive_search_placeholder: `<div class="search-results"><div class="search-block"><a class="search-item search-item--blank"><div class="thumbnail"></div><div class="content"><span class="title"></span><span class="caption"></span></div></a><a class="search-item search-item--blank"><div class="thumbnail"></div><div class="content"><span class="title"></span><span class="caption"></span></div></a><a class="search-item search-item--blank"><div class="thumbnail"></div><div class="content"><span class="title"></span><span class="caption"></span></div></a></div></div>`
        }
      }
      
    </script>
  
    <script src="{{ 'component-quick-buy.js' | asset_url }}" defer></script>
    <script src="{{ 'component-pickup-availability.js' | asset_url }}" defer></script>
  
    <script src="{{ 'component-slider.js' | asset_url }}" defer></script>
  
    {%- if request.design_mode -%}
      <script src="{{ 'theme-editor.js' | asset_url }}" defer></script>
    {%- endif -%}
    
    {%- unless settings.cart_action == "no-overlay" or template.name contains "cart" -%}
  
      <sidebar-drawer id="site-cart-sidebar" class="sidebar sidebar--right {% if cart.item_count == 0 %} cart-is-empty {% endif %}" tabindex="-1" role="dialog" aria-modal="true" data-cart-items="{{ cart.item_count }}" style="display:none" data-js-site-cart-sidebar>
        
        <div class="sidebar__header">
          <span class="sidebar__title h5">
            {{ 'cart.title' | t }}
            (<span data-header-cart-count>{{ cart.item_count }}</span>)
          </span>
          <button class="sidebar__close" data-js-close>
            <span class="visually-hidden">{{ 'general.accessibility_labels.close_sidebar' | t }}</span>
            <span aria-hidden="true" aria-role="img">{%- render 'theme-symbols', icon: 'close' -%}</span>
          </button>
        </div>
  
        <div class="sidebar__body">
  
          <div class="cart-form sidebar-large-padding">
            {%- render 'cart-form', type: 'sidebar' -%}
            <a href="{{ routes.all_products_collection_url }}" class="cart-continue button button--fullwidth button--solid button--regular">
              {{ 'cart.continue_browsing' | t }}
            </a>
          </div>
  
          {%- if settings.cart_recommendations -%}
            <cart-recommendations id="cart-recommendations" data-section="cart-recommendations" data-limit="6"></cart-recommendations>
            <script src="{{ 'component-cart-recommendations.js' | asset_url }}" defer></script>
          {%- endif -%}
  
          {%- if settings.cart_notes_enable -%}
            <div class="cart-instructions hide-if-empty-cart">
              <div class="form-field">
                <cart-note>
                  <label for="CartDrawer-Note">{{ 'cart.note' | t }}</label>
                  <textarea id="CartDrawer-Note" name="note">{{ cart.note }}</textarea>
                </cart-note> 
              </div>
            </div>
          {%- endif -%}
  
        </div>
  
        <div class="sidebar__footer hide-if-empty-cart">
          {%- render 'cart-subtotal', type: 'sidebar' -%}
        </div>
  
      </sidebar-drawer>
  
      <script>
        new MutationObserver((mutations, observer) => {
          if ( document.getElementById('cart').classList.contains('cart--empty') ) {
            document.getElementById('site-cart-sidebar').classList.add('cart-is-empty');
          } else {
            document.getElementById('site-cart-sidebar').classList.remove('cart-is-empty');
          }
        }).observe(document.querySelector('.cart-form'), {
          attributes: false, childList: true, subtree: true
        })
      </script>
  
    {%- endunless -%}
  
    <link rel="stylesheet" href="{{ 'component-cart.css' | asset_url }}" media="print" onload="this.media='all'">
  
    <script src="{{ 'component-quantity-selector.js' | asset_url }}" defer></script>
    {%- if settings.cart_action == "overlay" or template.name contains "cart" -%}
      <script src="{{ 'component-cart.js' | asset_url }}" defer></script>
    {%- endif -%}
  
    <link rel="stylesheet" href="{{ 'component-sidebar.css' | asset_url }}" media="print" onload="this.media='all';">
    <script src="{{ 'section-header.js' | asset_url }}" defer></script>
    
    {%- if request.design_mode -%}
      <script src="{{ 'section-announcement-bar.js' | asset_url }}" defer></script>
      <script src="{{ 'component-toggle.js' | asset_url }}" defer></script>
      <script src="{{ 'section-main-product.js' | asset_url }}" defer></script>
      <script src="{{ 'component-video.js' | asset_url }}" defer></script>
      <script src="{{ 'component-store-selector.js' | asset_url }}" defer></script>
      <script src="{{ 'component-interactive-map.js' | asset_url }}" defer></script>
      <script src="{{ 'component-localization-form.js' | asset_url }}" defer></script>
      <script src="{{ 'component-predictive-search.js' | asset_url }}" defer></script>
      <script src="{{ 'component-shipping-calculator.js' | asset_url }}" defer></script>
      <script src="{{ 'component-scrolling-text.js' | asset_url }}" defer></script>
    {%- endif -%}
  
    <div class="site-overlay" style="display:none"></div>
  
    <link rel="stylesheet" href="{{ 'section-main-product.css' | asset_url }}" media="print" onload="this.media='all';">
  
    {%- if settings.show_gotop -%}
      <span id="go-top" class="main-go-top"
        onclick="window.scrollTo({ top: 0, behavior: 'smooth' })"
      >
        <span class="visually-hidden">{{ 'general.accessibility_labels.go_to_top' | t }}</span>
        <span class="main-go-top__icon" aria-hidden="true">{%- render 'theme-symbols', icon: 'chevron' -%}</span>
      </span>
      <script>
        window.addEventListener('scroll', ()=>{
          if ( window.scrollY > 100 ) {
            document.getElementById('go-top').classList.add('show');
          } else {
            document.getElementById('go-top').classList.remove('show');
          }
        }, {passive: true});
      </script>
    {%- endif -%}
  
  </body>
  </html>