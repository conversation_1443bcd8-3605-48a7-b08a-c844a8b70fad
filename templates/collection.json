/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "banner": {
      "type": "main-collection-banner",
      "settings": {
        "collection_title_prefix": "",
        "show_collection_description": false,
        "show_collection_image": false,
        "alignment": "horizontal-left align-content--vertical-bottom",
        "color_background_main": "rgba(0,0,0,0)",
        "color_text_main": "rgba(0,0,0,0)"
      }
    },
    "product-grid": {
      "type": "main-collection-product-grid",
      "blocks": {
        "title": {
          "type": "title",
          "settings": {}
        },
        "vendor_eNa6Qj": {
          "type": "vendor",
          "settings": {}
        },
        "price": {
          "type": "price",
          "settings": {}
        },
        "rating_VrtqxE": {
          "type": "rating",
          "settings": {}
        },
        "quick_buy": {
          "type": "quick_buy",
          "settings": {}
        },
        "icons_3dgJ6A": {
          "type": "icons",
          "settings": {}
        }
      },
      "block_order": [
        "title",
        "vendor_eNa6Qj",
        "price",
        "rating_VrtqxE",
        "quick_buy",
        "icons_3dgJ6A"
      ],
      "settings": {
        "products_number": 24,
        "enable_filtering": true,
        "enable_sorting": true,
        "filters_position": "sidebar",
        "expand_filters_bydefault": true,
        "stick_sidebar_to_top": true,
        "image_filter_layout": "onecolumn",
        "layout_desktop": "four-columns",
        "layout_mobile": "grid-palm-2"
      }
    }
  },
  "order": [
    "banner",
    "product-grid"
  ]
}
