/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "settings": {
        "center": false
      }
    },
    "blog_posts_4pknni": {
      "type": "blog-posts",
      "blocks": {
        "info_3zjUpF": {
          "type": "info",
          "settings": {
            "show_date": true,
            "show_author": false,
            "show_comments_number": false
          }
        },
        "title_tPqbep": {
          "type": "title",
          "settings": {}
        }
      },
      "block_order": [
        "info_3zjUpF",
        "title_tPqbep"
      ],
      "settings": {
        "title": "Blogbeiträge",
        "subheading": "",
        "show_view_all": true,
        "section_heading_layout": "section-heading--left",
        "blog": "biohacking",
        "emphasize_first": true,
        "blog_show_image": true,
        "image_aspect_ratio": "1.33333",
        "section_background_color": "rgba(0,0,0,0)",
        "section_background_gradient": "",
        "section_heading_color": "rgba(0,0,0,0)",
        "remove_margin": false,
        "seo_h_tag": "h2"
      }
    },
    "rich_text_EYgaQM": {
      "type": "rich-text",
      "blocks": {
        "title_CwQW9P": {
          "type": "title",
          "settings": {
            "title": "Talk about your brand",
            "title_size": "h2",
            "seo_h_tag": "h2"
          }
        },
        "text_NkwYTD": {
          "type": "text",
          "settings": {
            "text": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements or welcome customers to your store.</p>",
            "text_size": "regular"
          }
        },
        "button_68GqBg": {
          "type": "button",
          "settings": {
            "button_label": "Show more",
            "link": "",
            "open_in_new_window": false,
            "button_size": "large",
            "button_style": "outline"
          }
        }
      },
      "block_order": [
        "title_CwQW9P",
        "text_NkwYTD",
        "button_68GqBg"
      ],
      "settings": {
        "image_position": "right",
        "height": "regular",
        "image_crop": true,
        "text_alignment": "horizontal-center",
        "is_fullwidth": false,
        "section_background_color": "rgba(0,0,0,0)",
        "section_background_gradient": "",
        "remove_margin": false,
        "color_background_main": "rgba(0,0,0,0)",
        "color_text_main": "rgba(0,0,0,0)",
        "color_accent_main": "rgba(0,0,0,0)",
        "color_borders_main": "rgba(0,0,0,0)",
        "color_shadow_main": "rgba(0,0,0,0)",
        "color_hide_borders": false,
        "color_hide_shadow": false
      }
    }
  },
  "order": [
    "main",
    "blog_posts_4pknni",
    "rich_text_EYgaQM"
  ]
}
