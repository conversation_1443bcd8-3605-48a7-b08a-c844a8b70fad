/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "vendor": {
          "type": "vendor",
          "settings": {
            "text_size": "text-size--regular"
          }
        },
        "klaviyo_reviews_average_rating_Vf3XYx": {
          "type": "shopify://apps/klaviyo-reviews/blocks/average-rating/db20e365-d984-4ac4-9655-e1588d951ca9",
          "settings": {
            "product": "{{product}}"
          }
        },
        "title": {
          "type": "title",
          "settings": {}
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "variants_style": "radio",
            "show_quantities": "no"
          }
        },
        "description": {
          "type": "description",
          "settings": {}
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_price": true,
            "show_quantity_selector": true,
            "show_preorder": false,
            "show_dynamic_checkout": false,
            "show_gift_card_recipient": false,
            "color_text_buttons": "rgba(0,0,0,0)",
            "color_accent_buttons": "rgba(0,0,0,0)",
            "style_buttons": "solid"
          }
        },
        "empty_space_3": {
          "type": "space",
          "settings": {}
        },
        "collapsible_tab_TgYYkh": {
          "type": "collapsible_tab",
          "settings": {
            "heading": "Beschreibung",
            "content": "{{ product.metafields.product_page_accordion.description | metafield_tag }}",
            "page": "",
            "hide_block_if": false
          }
        },
        "collapsible_tab_kxF4KE": {
          "type": "collapsible_tab",
          "disabled": true,
          "settings": {
            "heading": "Inhaltsstoffe",
            "content": "{{ product.metafields.product_page_accordion.ingredients | metafield_tag }}",
            "page": "",
            "hide_block_if": false
          }
        },
        "collapsible_tab_394wxr": {
          "type": "collapsible_tab",
          "settings": {
            "heading": "Anwendung",
            "content": "{{ product.metafields.product_page_accordion.application | metafield_tag }}",
            "page": "",
            "hide_block_if": false
          }
        },
        "collapsible_tab_XCnA3y": {
          "type": "collapsible_tab",
          "settings": {
            "heading": "Literatur",
            "content": "{{ product.metafields.product_page_accordion.literature | metafield_tag }}",
            "page": "",
            "hide_block_if": true
          }
        },
        "nutrition_DXfjVF": {
          "type": "nutrition",
          "settings": {
            "heading": "Inhaltsstoffe",
            "label_first": "Inhalt",
            "label_second": "{{ product.metafields.product_page.nutrition_right_label.value }}",
            "label_third": "{{ product.metafields.product_page.nutrition_third_label.value }}",
            "content": "{{ product.metafields.product_page.nutrition_info | metafield_tag }}",
            "content_extra": "{{ product.metafields.product_page.nutrition_extra_info | metafield_tag }}",
            "hide_block_if": true,
            "blend_in": true
          }
        }
      },
      "block_order": [
        "vendor",
        "klaviyo_reviews_average_rating_Vf3XYx",
        "title",
        "variant_picker",
        "description",
        "buy_buttons",
        "empty_space_3",
        "collapsible_tab_TgYYkh",
        "collapsible_tab_kxF4KE",
        "collapsible_tab_394wxr",
        "collapsible_tab_XCnA3y",
        "nutrition_DXfjVF"
      ],
      "settings": {
        "gallery_style": "slider",
        "gallery_ratio": "natural",
        "gallery_fit": false,
        "show_border": false,
        "gallery_card_design": false,
        "gallery_padding": 0,
        "gallery_pagination": "dots",
        "enable_zoom": true,
        "enable_video_looping": false,
        "default_to_first_variant": true,
        "sticky_add_to_cart": true,
        "sticky_atc_style": "wide",
        "sticky_bar_bgcolor": "#ffffff",
        "sticky_bar_txtcolor": "#113b28"
      }
    },
    "text_columns_images_AhkgYn": {
      "type": "text-columns-images",
      "blocks": {
        "image_6icDby": {
          "type": "image",
          "settings": {
            "image": "{{ product.metafields.product_page.selling_point_1.value.image.value }}",
            "title": "{{ product.metafields.product_page.selling_point_1.value.title.value }}",
            "caption": "",
            "link": "",
            "open_in_new_window": false,
            "seo_h_tag": "h3"
          }
        },
        "image_xR8Lr4": {
          "type": "image",
          "settings": {
            "image": "{{ product.metafields.product_page.selling_point_2.value.image.value }}",
            "title": "{{ product.metafields.product_page.selling_point_2.value.title.value }}",
            "caption": "",
            "link": "",
            "open_in_new_window": false,
            "seo_h_tag": "h3"
          }
        },
        "image_KytGzX": {
          "type": "image",
          "settings": {
            "image": "{{ product.metafields.product_page.selling_point_3.value.image.value }}",
            "title": "{{ product.metafields.product_page.selling_point_3.value.title.value }}",
            "caption": "",
            "link": "",
            "open_in_new_window": false,
            "seo_h_tag": "h3"
          }
        },
        "image_KYbLWe": {
          "type": "image",
          "settings": {
            "image": "{{ product.metafields.product_page.selling_point_4.value.image.value }}",
            "title": "{{ product.metafields.product_page.selling_point_4.value.title.value }}",
            "caption": "",
            "link": "",
            "open_in_new_window": false,
            "seo_h_tag": "h3"
          }
        },
        "image_TqY96G": {
          "type": "image",
          "settings": {
            "image": "{{ product.metafields.product_page.selling_point_5.value.image.value }}",
            "title": "{{ product.metafields.product_page.selling_point_5.value.title.value }}",
            "caption": "",
            "link": "",
            "open_in_new_window": false,
            "seo_h_tag": "h3"
          }
        },
        "image_jkD8qm": {
          "type": "image",
          "settings": {
            "image": "{{ product.metafields.product_page.selling_point_6.value.image.value }}",
            "title": "{{ product.metafields.product_page.selling_point_6.value.title.value }}",
            "caption": "",
            "link": "",
            "open_in_new_window": false,
            "seo_h_tag": "h3"
          }
        }
      },
      "block_order": [
        "image_6icDby",
        "image_xR8Lr4",
        "image_KytGzX",
        "image_KYbLWe",
        "image_TqY96G",
        "image_jkD8qm"
      ],
      "settings": {
        "heading": "{{ product.metafields.product_page_accordion.selling_points_heading.value }}",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "style": "slider",
        "layout": "grid-4 grid-portable-3 grid-lap-2",
        "border_radius": 200,
        "section_background_color": "rgba(0,0,0,0)",
        "section_background_gradient": "",
        "section_heading_color": "rgba(0,0,0,0)",
        "remove_margin": false,
        "seo_h_tag": "h2"
      }
    },
    "rich_text_zN7M8J": {
      "type": "rich-text",
      "blocks": {
        "title_z9ddzP": {
          "type": "title",
          "settings": {
            "title": "Warum Magnesium für Sportler unverzichtbar ist",
            "title_size": "h2",
            "seo_h_tag": "h2"
          }
        },
        "text_H6mBXV": {
          "type": "text",
          "settings": {
            "text": "<p><strong>- Unterstützt die Muskelfunktion<br/></strong>   Trägt zur normalen Muskelarbeit bei.</p><p><strong>- Fördert den Energiestoffwechsel</strong>  Wichtig für die Energiegewinnung in den Zellen.</p><p><strong>- Hilft bei der Regeneration</strong> <br/>Spielt eine Rolle im Erholungsprozess nach Belastung.</p><p><strong>- Unterstützt den Elektrolythaushalt</strong><br/> Trägt zum Gleichgewicht der Mineralstoffe bei.</p>",
            "text_size": "regular"
          }
        }
      },
      "block_order": [
        "title_z9ddzP",
        "text_H6mBXV"
      ],
      "settings": {
        "show_video": false,
        "video": "",
        "image_position": "top no-image",
        "height": "small",
        "image_crop": false,
        "heading": "",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "seo_h_tag": "h2",
        "text_alignment": "horizontal-left",
        "text_image_ratio": "3fr 1fr",
        "is_fullwidth": true,
        "emphasize_text_and_image_sep": false,
        "section_background_color": "rgba(0,0,0,0)",
        "section_background_gradient": "",
        "remove_margin": false,
        "color_background_main": "#0d2c1e",
        "color_text_main": "#ffffff",
        "color_accent_main": "rgba(0,0,0,0)",
        "color_borders_main": "rgba(0,0,0,0)",
        "color_shadow_main": "rgba(0,0,0,0)",
        "color_hide_borders": false,
        "color_hide_shadow": false
      }
    },
    "rich_text_KDgUWy": {
      "type": "rich-text",
      "blocks": {
        "title_xPea9f": {
          "type": "title",
          "settings": {
            "title": "<strong>{{ product.metafields.product_page.banner_heading.value }}</strong>",
            "title_size": "h2",
            "seo_h_tag": "h2"
          }
        },
        "text_HjrCVd": {
          "type": "text",
          "settings": {
            "text": "{{ product.metafields.product_page.banner_text | metafield_tag }}<p></p>",
            "text_size": "regular"
          }
        },
        "button_phhc9h": {
          "type": "button",
          "settings": {
            "button_label": "Zum Blog",
            "link": "{{ product.metafields.custom.product_page_blog_article.value }}",
            "open_in_new_window": false,
            "button_size": "large",
            "button_style": "outline"
          }
        }
      },
      "block_order": [
        "title_xPea9f",
        "text_HjrCVd",
        "button_phhc9h"
      ],
      "settings": {
        "show_video": false,
        "image": "{{ product.metafields.product_page.banner_image.value }}",
        "video": "",
        "image_position": "right",
        "height": "regular",
        "image_crop": true,
        "heading": "",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "seo_h_tag": "h2",
        "text_alignment": "horizontal-left",
        "text_image_ratio": "1fr 1fr",
        "is_fullwidth": false,
        "emphasize_text_and_image_sep": false,
        "section_background_color": "rgba(0,0,0,0)",
        "section_background_gradient": "",
        "remove_margin": false,
        "color_background_main": "rgba(0,0,0,0)",
        "color_text_main": "rgba(0,0,0,0)",
        "color_accent_main": "rgba(0,0,0,0)",
        "color_borders_main": "rgba(0,0,0,0)",
        "color_shadow_main": "rgba(0,0,0,0)",
        "color_hide_borders": false,
        "color_hide_shadow": false
      }
    },
    "content_toggles_pJ4LXw": {
      "type": "content-toggles",
      "blocks": {
        "text_EcQwgg": {
          "type": "text",
          "settings": {
            "title": "{{ product.metafields.product_page.faq_item_1.value.question.value }}",
            "subtitle": "",
            "icon": "no-icon",
            "button_label": "",
            "button_link": "",
            "body": "{{ product.metafields.product_page.faq_item_1.value.answer | metafield_tag }}<p></p>",
            "hide_block_if": true
          }
        },
        "text_iDQXyn": {
          "type": "text",
          "settings": {
            "title": "{{ product.metafields.product_page.faq_item_2.value.question.value }}",
            "subtitle": "",
            "icon": "no-icon",
            "button_label": "",
            "button_link": "",
            "body": "{{ product.metafields.product_page.faq_item_2.value.answer | metafield_tag }}<p></p>",
            "hide_block_if": true
          }
        },
        "text_qiLjYm": {
          "type": "text",
          "disabled": true,
          "settings": {
            "title": "Wie werden Proteine gebildet?",
            "subtitle": "",
            "icon": "no-icon",
            "button_label": "",
            "button_link": "",
            "body": "<p>Proteine sind die essenziellen Komponenten im menschlichen Körper. Sie werden für die Bildung von Strukturen, Funktionen sowie der Regulation jeder Körperzelle, jedes Gewebes und aller Organe benötigt. Dem Körper stehen zur Bildung der Proteine Aminosäuren zur Verfügung. Jedes Protein besteht aus einer spezifischen Aneinanderreihung dieser. Wie sie angeordnet werden, ist in der DNA als genetischer Code gespeichert.<br/></p>",
            "hide_block_if": false
          }
        },
        "text_arbTmk": {
          "type": "text",
          "disabled": true,
          "settings": {
            "title": "Was sind freie Aminosäuren?",
            "subtitle": "",
            "icon": "no-icon",
            "button_label": "",
            "button_link": "",
            "body": "<p>Aminosäuren sind die Bausteine für Eiweiße. Durch die Aufnahme von eiweißhaltiger Nahrung werden diese verdaut und in einzelne Aminosäuren aufgespalten. So können sie vom Körper aufgenommen und für eigene Bauprozesse verwendet werden. Freie Aminosäuren müssen nicht erst aufgespalten werden, sondern können direkt über die Schleimhäute aufgenommen werden und in die Blutbahn gelangen. </p>",
            "hide_block_if": false
          }
        },
        "text_8MApRW": {
          "type": "text",
          "settings": {
            "title": "{{ product.metafields.product_page.faq_item_3.value.question.value }}",
            "subtitle": "",
            "icon": "no-icon",
            "button_label": "",
            "button_link": "",
            "body": "{{ product.metafields.product_page.faq_item_3.value.answer | metafield_tag }}<p></p>",
            "hide_block_if": true
          }
        },
        "text_wWQc83": {
          "type": "text",
          "settings": {
            "title": "{{ product.metafields.product_page.faq_item_4.value.question.value }}",
            "subtitle": "",
            "icon": "no-icon",
            "button_label": "",
            "button_link": "",
            "body": "{{ product.metafields.product_page.faq_item_4.value.answer | metafield_tag }}<p></p>",
            "hide_block_if": true
          }
        },
        "text_9yUDKJ": {
          "type": "text",
          "settings": {
            "title": "{{ product.metafields.product_page.faq_item_5.value.question.value }}",
            "subtitle": "",
            "icon": "no-icon",
            "button_label": "",
            "button_link": "",
            "body": "{{ product.metafields.product_page.faq_item_5.value.answer | metafield_tag }}<p></p>",
            "hide_block_if": true
          }
        }
      },
      "block_order": [
        "text_EcQwgg",
        "text_iDQXyn",
        "text_qiLjYm",
        "text_arbTmk",
        "text_8MApRW",
        "text_wWQc83",
        "text_9yUDKJ"
      ],
      "settings": {
        "heading": "Häufig gestellte Fragen",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "open_first": false,
        "form_enable": false,
        "form_title": "Contact form",
        "seo_h_tag": "h2"
      }
    },
    "17406851034fab19e5": {
      "type": "apps",
      "blocks": {
        "klaviyo_reviews_product_reviews_fxVtxz": {
          "type": "shopify://apps/klaviyo-reviews/blocks/product-reviews/db20e365-d984-4ac4-9655-e1588d951ca9",
          "settings": {}
        }
      },
      "block_order": [
        "klaviyo_reviews_product_reviews_fxVtxz"
      ],
      "settings": {
        "heading": "",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "full_width": false,
        "remove_margin": false,
        "seo_h_tag": "h2"
      }
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "blocks": {
        "price": {
          "type": "price",
          "settings": {}
        },
        "title": {
          "type": "title",
          "settings": {}
        },
        "quick_buy": {
          "type": "quick_buy",
          "settings": {}
        }
      },
      "block_order": [
        "price",
        "title",
        "quick_buy"
      ],
      "settings": {
        "heading": "Dir könnte auch gefallen",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "layout": "grid-4 grid-portable-3 grid-lap-2 grid-tiny-1",
        "layout_mobile": "grid-palm-1",
        "products_number": 4,
        "section_background_color": "rgba(0,0,0,0)",
        "section_background_gradient": "",
        "section_heading_color": "rgba(0,0,0,0)",
        "remove_margin": false
      }
    },
    "rich_text_GUeUkY": {
      "type": "rich-text",
      "blocks": {
        "text_UUPiMk": {
          "type": "text",
          "settings": {
            "text": "<p>{{ product.metafields.custom.product_page_bottom_text | metafield_tag }}</p>",
            "text_size": "regular"
          }
        }
      },
      "block_order": [
        "text_UUPiMk"
      ],
      "settings": {
        "show_video": false,
        "video": "",
        "image_position": "top no-image",
        "height": "regular",
        "image_crop": true,
        "heading": "",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "seo_h_tag": "h2",
        "text_alignment": "horizontal-left",
        "text_image_ratio": "1fr 1fr",
        "is_fullwidth": false,
        "emphasize_text_and_image_sep": false,
        "section_background_color": "rgba(0,0,0,0)",
        "section_background_gradient": "",
        "remove_margin": false,
        "color_background_main": "rgba(0,0,0,0)",
        "color_text_main": "rgba(0,0,0,0)",
        "color_accent_main": "rgba(0,0,0,0)",
        "color_borders_main": "rgba(0,0,0,0)",
        "color_shadow_main": "rgba(0,0,0,0)",
        "color_hide_borders": false,
        "color_hide_shadow": false
      }
    },
    "custom_liquid_RgN7mJ": {
      "type": "custom-liquid",
      "settings": {
        "heading": "",
        "subheading": "",
        "section_heading_layout": "section-heading--left",
        "custom_liquid": "{%- if product.metafields.product_page.footnotes != blank -%}<div style=\"background-color: #FBE6E6;\"><div class=\"container--large\" style=\"padding-top: 15px; padding-bottom: 15px; font-size: 0.75rem;\">{{ product.metafields.product_page.footnotes }}</div></div>{%-endif-%}",
        "full_width": true,
        "remove_margin": false,
        "seo_h_tag": "h2"
      }
    }
  },
  "order": [
    "main",
    "text_columns_images_AhkgYn",
    "rich_text_zN7M8J",
    "rich_text_KDgUWy",
    "content_toggles_pJ4LXw",
    "17406851034fab19e5",
    "product-recommendations",
    "rich_text_GUeUkY",
    "custom_liquid_RgN7mJ"
  ]
}
